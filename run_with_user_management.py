#!/usr/bin/env python3
"""
MemeMaster AI 用户管理系统启动脚本
"""

import asyncio
import sys
import os
import signal
import threading
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    MemeMaster AI                             ║
║                  用户管理系统 v1.0                           ║
║                                                              ║
║  🚀 分层收费模式 | 💰 加密货币支付 | 🔐 JWT认证              ║
║  📊 使用跟踪     | 🎯 功能权限控制 | 📈 实时分析              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def check_dependencies():
    """检查依赖项"""
    print("🔍 检查系统依赖...")
    
    required_modules = [
        'fastapi', 'uvicorn', 'pyjwt', 'bcrypt', 
        'passlib', 'aiosqlite', 'pydantic'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少依赖模块: {', '.join(missing_modules)}")
        print("请运行: pip install " + " ".join(missing_modules))
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

async def initialize_system():
    """初始化系统"""
    print("🔧 初始化用户管理系统...")
    
    try:
        # 初始化数据库
        from src.database.connection import init_database
        await init_database()
        print("✅ 数据库初始化完成")
        
        # 检查管理员账户
        from src.auth.auth_manager import auth_manager
        from src.database.user_db import UserDatabase
        from src.models.user import UserRole
        
        user_db = UserDatabase()
        admin_user = await user_db.get_user_by_email("<EMAIL>")
        
        if not admin_user:
            # 创建管理员账户
            admin_user = await auth_manager.register_user(
                email="<EMAIL>",
                username="admin",
                password="Admin123!@#",
                full_name="系统管理员"
            )
            await user_db.update_user(admin_user.id, {"role": UserRole.ENTERPRISE})
            print("✅ 管理员账户创建完成")
        else:
            print("ℹ️  管理员账户已存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return False

def start_task_scheduler():
    """启动定时任务调度器"""
    print("⏰ 启动定时任务调度器...")
    
    async def run_scheduler():
        try:
            from src.tasks.subscription_tasks import TaskScheduler
            scheduler = TaskScheduler()
            await scheduler.start_scheduler()
        except Exception as e:
            print(f"❌ 定时任务调度器启动失败: {e}")
    
    def scheduler_thread():
        asyncio.run(run_scheduler())
    
    scheduler_thread = threading.Thread(target=scheduler_thread, daemon=True)
    scheduler_thread.start()
    print("✅ 定时任务调度器已启动")

def start_web_server():
    """启动Web服务器"""
    print("🌐 启动Web服务器...")
    
    try:
        import uvicorn
        from web_app import app
        
        # 配置服务器
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            reload=False  # 生产环境建议关闭
        )
        
        server = uvicorn.Server(config)
        return server
        
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        return None

def setup_signal_handlers(server):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n🛑 收到信号 {signum}，正在关闭服务器...")
        if server:
            server.should_exit = True
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def print_startup_info():
    """打印启动信息"""
    print("\n" + "="*60)
    print("🎉 MemeMaster AI 用户管理系统启动成功！")
    print("="*60)
    
    print("\n📋 系统信息:")
    print("   • 数据库: SQLite (mememaster.db)")
    print("   • 认证方式: JWT Token")
    print("   • 支付方式: Solana (SOL)")
    print("   • 订阅层级: 免费版 / Pro版 / 机构版")
    
    print("\n🔑 默认账户:")
    print("   管理员: <EMAIL> / Admin123!@#")
    
    print("\n🌐 访问地址:")
    print("   • 主应用: http://localhost:8001/")
    print("   • 用户登录: http://localhost:8001/login")
    print("   • 用户中心: http://localhost:8001/user-dashboard")
    print("   • 订阅管理: http://localhost:8001/subscription")
    print("   • 管理员: http://localhost:8001/admin")
    
    print("\n📚 API文档:")
    print("   • Swagger UI: http://localhost:8001/docs")
    print("   • ReDoc: http://localhost:8001/redoc")
    
    print("\n💡 提示:")
    print("   • 使用 Ctrl+C 停止服务器")
    print("   • 查看日志了解系统运行状态")
    print("   • 定时任务会自动处理订阅和使用限制")
    
    print("\n" + "="*60)

async def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not await check_dependencies():
        sys.exit(1)
    
    # 初始化系统
    if not await initialize_system():
        sys.exit(1)
    
    # 启动定时任务调度器
    start_task_scheduler()
    
    # 启动Web服务器
    server = start_web_server()
    if not server:
        sys.exit(1)
    
    # 设置信号处理器
    setup_signal_handlers(server)
    
    # 打印启动信息
    print_startup_info()
    
    # 运行服务器
    try:
        await server.serve()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器运行错误: {e}")
        sys.exit(1)

def run_quick_test():
    """快速测试系统功能"""
    print("🧪 运行快速功能测试...")
    
    async def test_system():
        try:
            # 测试数据库连接
            from src.database.connection import db_connection
            async with db_connection.get_connection() as conn:
                cursor = await conn.execute("SELECT COUNT(*) FROM users")
                user_count = (await cursor.fetchone())[0]
                print(f"✅ 数据库连接正常，用户数量: {user_count}")
            
            # 测试认证系统
            from src.auth.auth_manager import auth_manager
            test_token = auth_manager.create_access_token(data={"sub": "1"})
            if test_token:
                print("✅ 认证系统正常")
            
            # 测试订阅计划
            from src.models.subscription import SUBSCRIPTION_PLANS
            print(f"✅ 订阅计划配置正常，共 {len(SUBSCRIPTION_PLANS)} 个计划")
            
            print("🎉 系统功能测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 系统功能测试失败: {e}")
            return False
    
    return asyncio.run(test_system())

if __name__ == "__main__":
    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            command = sys.argv[1]
            
            if command == "test":
                # 运行测试
                success = run_quick_test()
                sys.exit(0 if success else 1)
            
            elif command == "init":
                # 仅初始化系统
                async def init_only():
                    return await initialize_system()
                
                success = asyncio.run(init_only())
                if success:
                    print("✅ 系统初始化完成")
                sys.exit(0 if success else 1)
            
            elif command == "help":
                print("""
MemeMaster AI 用户管理系统启动脚本

使用方法:
    python run_with_user_management.py [命令]

命令:
    (无参数)  - 启动完整系统（默认）
    test     - 运行系统功能测试
    init     - 仅初始化系统
    help     - 显示此帮助信息

示例:
    python run_with_user_management.py
    python run_with_user_management.py test
    python run_with_user_management.py init
                """)
                sys.exit(0)
            
            else:
                print(f"❌ 未知命令: {command}")
                print("使用 'python run_with_user_management.py help' 查看帮助")
                sys.exit(1)
        
        # 默认启动完整系统
        asyncio.run(main())
        
    except KeyboardInterrupt:
        print("\n⚠️  启动被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
