# MemeMaster AI 版本控制指南

## 🌟 版本分支结构

### 主要分支

#### `main` - 主分支
- **用途**: 生产环境稳定版本
- **保护**: 只能通过Pull Request合并
- **部署**: 自动部署到生产环境

#### `develop` - 开发分支  
- **用途**: 开发环境集成分支
- **功能**: 所有新功能的集成点
- **测试**: 持续集成测试

#### `release/v1.0.0` - 发布分支
- **用途**: 准备发布的版本
- **功能**: 版本发布前的最后调整
- **合并**: 完成后合并到main和develop

### 功能分支

#### `feature/ui-optimization` - UI优化功能
- **用途**: UI/UX改进和优化
- **基于**: develop分支
- **合并**: 完成后合并回develop

#### `hotfix/layout-fixes` - 布局修复
- **用途**: 紧急布局问题修复
- **基于**: main分支
- **合并**: 修复后合并到main和develop

## 📋 版本历史

### v2.0.0 (2024-12-19)
**重大功能更新版本**

#### ✨ 新功能
- 全新策略管理中心界面重构
- 增强的热点监控和预测系统
- 改进的AI情感分析引擎
- 优化的用户界面和交互体验
- 完善的响应式设计系统
- 增强的布局一致性和视觉效果

#### 🔧 改进
- 策略卡片布局一致性修复
- 文字方向冲突解决方案
- 页面标题样式优化
- 组件响应式设计改进
- 用户体验优化

#### 🐛 修复
- 修复策略管理器布局问题
- 解决CSS文字方向冲突
- 修复页面标题显示异常
- 优化移动端适配问题

### v1.0.0 (2024-06-14)
**初始发布版本**

#### ✨ 新功能
- React-based 专业交易仪表板
- 策略管理中心与参数优化
- 热点监控和AI情感分析
- 一键代币部署系统
- 流动性管理和动态退出策略
- 基于SOL的用户管理系统
- 专业紫色渐变主题UI
- 响应式设计和布局优化

#### 🐛 修复问题
- 策略卡片高度一致性问题
- 侧边栏文字垂直显示问题
- CSS文字方向冲突问题
- 监控统计卡片布局问题
- 响应式设计兼容性问题

#### 🔧 技术改进
- 使用Flexbox布局系统
- 添加comprehensive CSS修复
- 优化网格布局系统
- 增强跨设备兼容性
- 改进组件结构和样式管理

## 🚀 分支工作流程

### 1. 功能开发流程
```bash
# 从develop创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 开发完成后
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# 创建Pull Request到develop
```

### 2. 热修复流程
```bash
# 从main创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/urgent-fix

# 修复完成后
git add .
git commit -m "fix: urgent issue"
git push origin hotfix/urgent-fix

# 合并到main和develop
```

### 3. 发布流程
```bash
# 从develop创建发布分支
git checkout develop
git checkout -b release/v1.1.0

# 发布准备完成后
git checkout main
git merge release/v1.1.0
git tag -a v1.1.0 -m "Release v1.1.0"
git checkout develop
git merge release/v1.1.0
```

## 📝 提交信息规范

### 提交类型
- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

### 提交格式
```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### 示例
```
feat(strategy): add parameter optimization panel

- Integrated OptimizationPanel component
- Added new tab for parameter control
- Enhanced strategy management workflow

Closes #123
```

## 🏷️ 版本标签

### 标签命名规范
- `v1.0.0` - 主版本.次版本.修订版本
- `v1.0.0-beta.1` - 预发布版本
- `v1.0.0-rc.1` - 发布候选版本

### 版本号规则
- **主版本**: 不兼容的API修改
- **次版本**: 向下兼容的功能性新增
- **修订版本**: 向下兼容的问题修正

## 🔄 当前状态

- **当前版本**: v1.0.0
- **活跃分支**: main, develop, feature/ui-optimization
- **下一版本**: v1.1.0 (计划中)
- **主要改进方向**: 
  - 性能优化
  - 新功能集成
  - 用户体验提升
  - 安全性增强

## 📞 联系信息

如有版本控制相关问题，请联系开发团队。
