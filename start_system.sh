#!/bin/bash

# MemeMaster AI 完整系统启动脚本

echo "🚀 MemeMaster AI 完整系统启动"
echo "================================"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

echo "📦 检查系统依赖..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi
echo "✅ Python $(python3 --version) 已安装"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi
echo "✅ npm $(npm --version) 已安装"

# 安装Python依赖（如果需要）
echo "🐍 检查Python依赖..."
python3 -c "import fastapi, uvicorn" 2>/dev/null || {
    echo "📦 安装Python依赖..."
    pip3 install fastapi uvicorn pyjwt bcrypt aiosqlite psutil
}

# 安装Node.js依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装Node.js依赖..."
    npm install
else
    echo "✅ Node.js依赖已存在"
fi

echo ""
echo "🚀 启动系统服务..."

# 启动后端服务器（后台运行）
echo "📡 启动后端API服务器..."
python3 working_server.py &
BACKEND_PID=$!
echo "   后端服务器 PID: $BACKEND_PID"

# 等待后端启动
sleep 3

# 检查后端是否启动成功
if curl -s http://localhost:8001/health > /dev/null 2>&1; then
    echo "   ✅ 后端服务器启动成功"
else
    echo "   ❌ 后端服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端应用（后台运行）
echo "🎨 启动React前端应用..."
npm run frontend:dev &
FRONTEND_PID=$!
echo "   前端应用 PID: $FRONTEND_PID"

# 等待前端启动
sleep 5

echo ""
echo "🎉 MemeMaster AI 系统启动完成！"
echo "=================================="
echo ""
echo "🌐 访问地址:"
echo "   • 前端界面: http://localhost:3000/"
echo "   • 后端API: http://localhost:8001/"
echo "   • API文档: http://localhost:8001/docs"
echo "   • 健康检查: http://localhost:8001/health"
echo ""
echo "📱 功能模块:"
echo "   • 🤖 AI智能分析 - 实时热点监测"
echo "   • ⚡ 自动化交易 - 策略管理系统"
echo "   • 💰 用户管理 - 分层收费模式"
echo "   • 📊 数据可视化 - React仪表盘"
echo ""
echo "🔧 技术栈:"
echo "   • 前端: React 18 + Vite + Tailwind CSS"
echo "   • 后端: FastAPI + Python"
echo "   • 数据库: SQLite + aiosqlite"
echo "   • 认证: JWT Token + bcrypt"
echo ""
echo "💡 使用提示:"
echo "   • 前端开发: 修改frontend/目录下的文件"
echo "   • 后端开发: 修改src/目录下的文件"
echo "   • 用户管理: 访问 /login 或 /user-dashboard"
echo "   • 管理员: 访问 /admin (需要企业版权限)"
echo ""
echo "⚠️ 按 Ctrl+C 停止所有服务"
echo ""

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "✅ 所有服务已停止"; exit 0' INT

# 保持脚本运行
while true; do
    sleep 1
done
