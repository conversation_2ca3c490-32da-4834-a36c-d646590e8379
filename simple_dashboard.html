<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 智能仪表盘</title>
    
    <!-- 字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f1f5f9;
            min-height: 100vh;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(71, 85, 105, 0.5);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 10;
        }

        .logo {
            padding: 24px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.5);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo i {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .logo h1 {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin: 4px 16px;
            border-radius: 8px;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(71, 85, 105, 0.5);
            color: white;
        }

        .nav-item.active {
            background: rgba(99, 102, 241, 0.2);
            color: #a5b4fc;
            border: 1px solid rgba(99, 102, 241, 0.3);
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 260px;
            padding: 24px;
        }

        .welcome-banner {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            text-align: center;
        }

        .welcome-banner h2 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 12px;
            color: white;
        }

        .welcome-banner p {
            color: #cbd5e1;
            font-size: 16px;
            line-height: 1.6;
        }

        /* 统计卡片 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .stat-card:hover {
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-title {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: white;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .icon-ai {
            background: rgba(139, 92, 246, 0.2);
            color: #a78bfa;
        }

        .icon-trade {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }

        .icon-sub {
            background: rgba(245, 158, 11, 0.2);
            color: #fbbf24;
        }

        .icon-wallet {
            background: rgba(16, 185, 129, 0.2);
            color: #34d399;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
            color: #10b981;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-top: 32px;
        }

        .feature-card {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .feature-card:hover {
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin: 0 auto 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        .feature-desc {
            color: #94a3b8;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #3b82f6 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5b21b6 0%, #1d4ed8 100%);
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            border: 1px solid rgba(71, 85, 105, 0.5);
            color: #94a3b8;
        }

        .btn-outline:hover {
            background: rgba(71, 85, 105, 0.3);
            color: white;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-robot"></i>
                <h1>MemeMaster AI</h1>
            </div>
            
            <a href="#" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>仪表盘</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>市场分析</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>交易策略</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>自动化交易</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>支付与订阅</span>
            </a>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 欢迎横幅 -->
            <div class="welcome-banner fade-in">
                <h2>🎉 MemeMaster AI 智能仪表盘</h2>
                <p>欢迎使用现代化的智能交易系统！系统检测到3个新的交易机会，市场情绪指数为 <strong style="color: #10b981;">+78.2</strong>，适合积极交易。</p>
            </div>
            
            <!-- 统计卡片 -->
            <div class="stats-container">
                <div class="stat-card fade-in">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">AI分析使用</div>
                            <div class="stat-value">42/∞</div>
                        </div>
                        <div class="stat-icon icon-ai">
                            <i class="fas fa-brain"></i>
                        </div>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>+12% 本周使用率</span>
                    </div>
                </div>
                
                <div class="stat-card fade-in">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">活跃策略</div>
                            <div class="stat-value">7</div>
                        </div>
                        <div class="stat-icon icon-trade">
                            <i class="fas fa-robot"></i>
                        </div>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>今日收益: +3.2 SOL</span>
                    </div>
                </div>
                
                <div class="stat-card fade-in">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">订阅状态</div>
                            <div class="stat-value">PRO 会员</div>
                        </div>
                        <div class="stat-icon icon-sub">
                            <i class="fas fa-crown"></i>
                        </div>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-calendar"></i>
                        <span>到期日: 2023-12-15</span>
                    </div>
                </div>
                
                <div class="stat-card fade-in">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">钱包余额</div>
                            <div class="stat-value">48.75 SOL</div>
                        </div>
                        <div class="stat-icon icon-wallet">
                            <i class="fas fa-wallet"></i>
                        </div>
                    </div>
                    <div class="stat-trend">
                        <i class="fas fa-sync-alt"></i>
                        <span>≈ $1,250 USD</span>
                    </div>
                </div>
            </div>
            
            <!-- 功能卡片 -->
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon" style="background: rgba(239, 68, 68, 0.2); color: #f87171;">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="feature-title">热点检测</div>
                    <div class="feature-desc">实时监测社交媒体趋势，识别新兴Meme币机会</div>
                    <button class="btn btn-outline">立即扫描</button>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon" style="background: rgba(59, 130, 246, 0.2); color: #60a5fa;">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="feature-title">策略生成</div>
                    <div class="feature-desc">AI驱动的交易策略，自动优化风险回报比</div>
                    <button class="btn btn-outline">创建策略</button>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon" style="background: rgba(139, 92, 246, 0.2); color: #a78bfa;">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="feature-title">一键部署</div>
                    <div class="feature-desc">自动化部署代币到多个区块链网络</div>
                    <button class="btn btn-primary">立即部署</button>
                </div>
                
                <div class="feature-card fade-in">
                    <div class="feature-icon" style="background: rgba(16, 185, 129, 0.2); color: #34d399;">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="feature-title">流动性管理</div>
                    <div class="feature-desc">智能管理资金池，优化交易执行</div>
                    <button class="btn btn-outline">管理流动性</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加交互效果
        document.querySelectorAll('.stat-card, .feature-card').forEach(card => {
            card.addEventListener('click', function() {
                console.log('点击了:', this.querySelector('.stat-title, .feature-title').textContent);
                
                // 添加点击动画
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.fade-in').forEach((element, index) => {
                element.style.animationDelay = `${index * 0.1}s`;
            });
        });

        console.log('🎉 MemeMaster AI 仪表盘已加载完成！');
    </script>
</body>
</html>
