<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 专业数据分析平台</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="frontend/styles/design-system.css">
    <link rel="stylesheet" href="frontend/styles/professional-layout.css">
    <link rel="stylesheet" href="frontend/styles/professional-animations.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="app-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <a href="#" class="sidebar-logo">
                    <div class="w-8 h-8 bg-gradient-blue rounded-lg flex items-center justify-center">
                        <i data-lucide="brain-circuit" class="w-5 h-5 text-white"></i>
                    </div>
                    <span>MemeMaster AI</span>
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">核心功能</div>
                    <a href="#" class="nav-item active">
                        <i data-lucide="layout-dashboard" class="nav-icon"></i>
                        <span>仪表盘</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i data-lucide="trending-up" class="nav-icon"></i>
                        <span>舆情监控</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i data-lucide="workflow" class="nav-icon"></i>
                        <span>业务流程</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i data-lucide="target" class="nav-icon"></i>
                        <span>策略管理</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">交易管理</div>
                    <a href="#" class="nav-item">
                        <i data-lucide="wallet" class="nav-icon"></i>
                        <span>钱包管理</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i data-lucide="droplets" class="nav-icon"></i>
                        <span>流动性控制</span>
                    </a>
                    <a href="#" class="nav-item">
                        <i data-lucide="log-out" class="nav-icon"></i>
                        <span>动态策略</span>
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">系统</div>
                    <a href="#" class="nav-item">
                        <i data-lucide="settings" class="nav-icon"></i>
                        <span>系统设置</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <header class="main-header">
                <div class="header-content">
                    <div>
                        <h1 class="page-title">数据分析仪表盘</h1>
                        <p class="page-subtitle">实时监控 • 智能分析 • 专业决策</p>
                    </div>
                    <div class="header-actions">
                        <div class="status-indicator status-online">
                            <div class="status-dot"></div>
                            <span>系统正常</span>
                        </div>
                        <button class="btn btn-primary">
                            <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                            刷新数据
                        </button>
                    </div>
                </div>
            </header>

            <div class="main-body">
                <!-- 关键指标 -->
                <div class="grid grid-cols-4 mb-8">
                    <div class="data-card hover-lift fade-in">
                        <div class="flex items-center justify-between mb-4">
                            <div class="metric-label">总资产价值</div>
                            <div class="p-2 bg-gradient-blue rounded-lg">
                                <i data-lucide="dollar-sign" class="w-5 h-5 text-white"></i>
                            </div>
                        </div>
                        <div class="metric-value text-primary">$2,847,392</div>
                        <div class="metric-change positive">
                            <i data-lucide="trending-up" class="w-4 h-4"></i>
                            <span>+12.5%</span>
                        </div>
                    </div>

                    <div class="data-card hover-lift fade-in" style="animation-delay: 0.1s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="metric-label">活跃策略</div>
                            <div class="p-2 bg-gradient-green rounded-lg">
                                <i data-lucide="zap" class="w-5 h-5 text-white"></i>
                            </div>
                        </div>
                        <div class="metric-value text-success">24</div>
                        <div class="metric-change positive">
                            <i data-lucide="plus" class="w-4 h-4"></i>
                            <span>+3 今日</span>
                        </div>
                    </div>

                    <div class="data-card hover-lift fade-in" style="animation-delay: 0.2s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="metric-label">成功率</div>
                            <div class="p-2 bg-gradient-purple rounded-lg">
                                <i data-lucide="target" class="w-5 h-5 text-white"></i>
                            </div>
                        </div>
                        <div class="metric-value text-info">87.3%</div>
                        <div class="metric-change positive">
                            <i data-lucide="arrow-up" class="w-4 h-4"></i>
                            <span>+2.1%</span>
                        </div>
                    </div>

                    <div class="data-card hover-lift fade-in" style="animation-delay: 0.3s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="metric-label">风险评级</div>
                            <div class="p-2 bg-gradient-red rounded-lg">
                                <i data-lucide="shield" class="w-5 h-5 text-white"></i>
                            </div>
                        </div>
                        <div class="metric-value text-warning">中等</div>
                        <div class="metric-change neutral">
                            <i data-lucide="minus" class="w-4 h-4"></i>
                            <span>稳定</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="grid grid-cols-3 gap-6 mb-8">
                    <!-- 收益趋势图 */
                    <div class="col-span-2">
                        <div class="chart-container slide-in-up">
                            <div class="chart-header">
                                <div class="chart-title">
                                    <i data-lucide="trending-up" class="w-5 h-5 text-success"></i>
                                    收益趋势分析
                                </div>
                                <div class="chart-controls">
                                    <button class="btn btn-sm btn-secondary">7天</button>
                                    <button class="btn btn-sm btn-primary">30天</button>
                                    <button class="btn btn-sm btn-secondary">90天</button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="profitChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 策略分布 -->
                    <div class="chart-container slide-in-up" style="animation-delay: 0.2s">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i data-lucide="pie-chart" class="w-5 h-5 text-info"></i>
                                策略分布
                            </div>
                        </div>
                        <div class="chart-body">
                            <canvas id="strategyChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="chart-container slide-in-up" style="animation-delay: 0.4s">
                    <div class="chart-header">
                        <div class="chart-title">
                            <i data-lucide="table" class="w-5 h-5 text-primary"></i>
                            最新交易记录
                        </div>
                        <div class="chart-controls">
                            <button class="btn btn-sm btn-secondary">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="chart-body">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>代币</th>
                                    <th>类型</th>
                                    <th>数量</th>
                                    <th>价格</th>
                                    <th>收益</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="font-mono text-sm">14:32:15</td>
                                    <td class="font-semibold">DOGE</td>
                                    <td>买入</td>
                                    <td class="table-cell-number">10,000</td>
                                    <td class="table-cell-number">$0.0847</td>
                                    <td class="table-cell-number text-success">+$127.50</td>
                                    <td><span class="table-cell-status status-active">已完成</span></td>
                                </tr>
                                <tr>
                                    <td class="font-mono text-sm">14:28:42</td>
                                    <td class="font-semibold">SHIB</td>
                                    <td>卖出</td>
                                    <td class="table-cell-number">50,000</td>
                                    <td class="table-cell-number">$0.0000234</td>
                                    <td class="table-cell-number text-danger">-$23.40</td>
                                    <td><span class="table-cell-status status-active">已完成</span></td>
                                </tr>
                                <tr>
                                    <td class="font-mono text-sm">14:25:18</td>
                                    <td class="font-semibold">PEPE</td>
                                    <td>买入</td>
                                    <td class="table-cell-number">1,000,000</td>
                                    <td class="table-cell-number">$0.00000156</td>
                                    <td class="table-cell-number text-success">+$89.20</td>
                                    <td><span class="table-cell-status status-warning">处理中</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 初始化图标
        lucide.createIcons();

        // 收益趋势图
        const profitCtx = document.getElementById('profitChart').getContext('2d');
        new Chart(profitCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [{
                    label: '收益率',
                    data: [12, 19, 15, 25, 22, 30],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#94A3B8' }
                    },
                    x: {
                        grid: { color: 'rgba(255, 255, 255, 0.1)' },
                        ticks: { color: '#94A3B8' }
                    }
                }
            }
        });

        // 策略分布图
        const strategyCtx = document.getElementById('strategyChart').getContext('2d');
        new Chart(strategyCtx, {
            type: 'doughnut',
            data: {
                labels: ['DeFi', 'NFT', 'GameFi', 'Meme'],
                datasets: [{
                    data: [35, 25, 20, 20],
                    backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: '#94A3B8' }
                    }
                }
            }
        });

        // 添加实时数据更新效果
        setInterval(() => {
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                metric.classList.add('data-update');
                setTimeout(() => metric.classList.remove('data-update'), 500);
            });
        }, 5000);
    </script>
</body>
</html>
