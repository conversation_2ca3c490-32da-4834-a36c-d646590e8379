# MemeMaster AI API 文档

## 概述

MemeMaster AI 提供了一套完整的 RESTful API，用于热点检测、策略生成和代币部署。

## 基础信息

- **Base URL**: `http://localhost:3000`
- **API Version**: `v1`
- **Content-Type**: `application/json`

## 认证

目前API不需要认证，但在生产环境中建议添加API密钥认证。

## 端点列表

### 1. 健康检查

检查系统健康状态。

**端点**: `GET /health`

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": **********,
  "circuit_breaker": "CLOSED",
  "system_metrics": {
    "cpu_percent": 45.2,
    "memory_percent": 67.8,
    "active_connections": 12
  },
  "blockchain": {
    "connected": true,
    "chain_id": 1,
    "latest_block": 18500000
  }
}
```

### 2. 分析热点

分析指定数据源的热点信息。

**端点**: `POST /api/v1/analyze-hotspot`

**请求参数**:
```json
{
  "source_url": "https://twitter.com/user/status/123456",
  "source_type": "twitter",
  "client_ip": "***********"
}
```

**参数说明**:
- `source_url` (必需): 数据源URL
- `source_type` (可选): 数据源类型 (`twitter`, `reddit`, `telegram`, `darkweb`, `video`, `news`)
- `client_ip` (可选): 客户端IP地址，用于地理围栏检查

**响应示例**:
```json
{
  "success": true,
  "data": {
    "source": "twitter",
    "sentiment_score": 0.75,
    "viral_score": 0.82,
    "timestamp": **********,
    "metadata": {
      "url": "https://twitter.com/user/status/123456",
      "likes": 1500,
      "retweets": 300,
      "sentiment_detail": {
        "sentiment": "POSITIVE",
        "score": 0.75,
        "confidence": 0.89
      }
    }
  }
}
```

### 3. 生成策略

基于热点数据生成代币策略。

**端点**: `POST /api/v1/generate-strategy`

**请求参数**:
```json
{
  "hotspot_data": {
    "content": "Amazing new crypto project launching soon!",
    "sentiment_score": 0.75,
    "viral_score": 0.82,
    "source": "twitter",
    "timestamp": **********
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "strategy": {
    "token_name": "AmazingCoin",
    "symbol": "AMZ",
    "total_supply": 1000000000,
    "burn_rate": 0.05,
    "estimated_roi": 2.5,
    "compliance_status": "PASSED",
    "launch_schedule": {
      "launch_times": {
        "US_EAST": "2024-01-15T14:00:00Z",
        "EUROPE": "2024-01-15T09:00:00Z",
        "ASIA": "2024-01-15T02:00:00Z"
      },
      "phases": [
        {
          "phase": "preparation",
          "duration_hours": 2,
          "actions": ["deploy_contract", "setup_liquidity"]
        }
      ]
    }
  }
}
```

### 4. 部署代币

部署代币合约到区块链。

**端点**: `POST /api/v1/deploy-token`

**请求参数**:
```json
{
  "strategy": {
    "token_name": "AmazingCoin",
    "symbol": "AMZ",
    "total_supply": 1000000000,
    "burn_rate": 0.05,
    "launch_schedule": {
      "immediate_trading": false
    }
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "Token deployment started",
  "status": "pending"
}
```

### 5. 获取系统指标

获取系统性能和应用指标。

**端点**: `GET /api/v1/metrics`

**响应示例**:
```json
{
  "system": {
    "cpu_percent": 45.2,
    "memory_percent": 67.8,
    "disk_percent": 23.1,
    "active_connections": 12
  },
  "application": {
    "circuit_breaker_state": "CLOSED",
    "hotspots_detected": 156,
    "tokens_deployed": 23
  }
}
```

## 错误处理

API使用标准HTTP状态码表示请求结果：

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `403 Forbidden`: 访问被拒绝（地理围栏）
- `500 Internal Server Error`: 服务器内部错误
- `503 Service Unavailable`: 服务不可用（熔断器开启）

**错误响应格式**:
```json
{
  "detail": "Error message description"
}
```

## 数据源类型

支持的数据源类型：

| 类型 | 描述 | 示例URL |
|------|------|---------|
| `twitter` | Twitter/X平台 | `https://twitter.com/user/status/123` |
| `reddit` | Reddit论坛 | `https://reddit.com/r/crypto/post/123` |
| `telegram` | Telegram频道 | `https://t.me/channel` |
| `darkweb` | 暗网论坛 | `https://example.onion/forum` |
| `video` | 视频平台 | `https://youtube.com/watch?v=123` |
| `news` | 新闻网站 | `https://news.example.com/article` |

## 地理围栏

系统支持地理围栏功能，以下地区可能被限制访问：

- 美国 (US)
- 中国 (CN)
- 韩国 (KR)
- 加拿大 (CA)
- 英国 (UK)

如果检测到来自受限地区的访问，API将返回403错误。

## 速率限制

为了保护系统稳定性，API实施了速率限制：

- 每个IP每分钟最多100个请求
- 热点分析API每分钟最多10个请求
- 代币部署API每小时最多5个请求

## 监控端点

### Prometheus指标

**端点**: `GET /metrics`

提供Prometheus格式的系统指标，用于监控和告警。

### 健康检查

**端点**: `GET /health`

提供详细的系统健康状态信息。

## SDK和客户端库

目前提供以下语言的SDK：

- Python: `pip install mememaster-ai-sdk`
- JavaScript: `npm install mememaster-ai-sdk`
- Go: `go get github.com/mememaster/go-sdk`

## 示例代码

### Python示例

```python
import requests

# 分析热点
response = requests.post('http://localhost:3000/api/v1/analyze-hotspot', json={
    'source_url': 'https://twitter.com/user/status/123456',
    'source_type': 'twitter'
})

if response.status_code == 200:
    hotspot_data = response.json()['data']
    print(f"Sentiment: {hotspot_data['sentiment_score']}")
    print(f"Viral Score: {hotspot_data['viral_score']}")
```

### JavaScript示例

```javascript
const axios = require('axios');

async function analyzeHotspot() {
    try {
        const response = await axios.post('http://localhost:3000/api/v1/analyze-hotspot', {
            source_url: 'https://twitter.com/user/status/123456',
            source_type: 'twitter'
        });
        
        const hotspotData = response.data.data;
        console.log(`Sentiment: ${hotspotData.sentiment_score}`);
        console.log(`Viral Score: ${hotspotData.viral_score}`);
    } catch (error) {
        console.error('Error:', error.response.data);
    }
}

analyzeHotspot();
```

## 支持

如有问题或建议，请联系：

- 邮箱: <EMAIL>
- GitHub: https://github.com/mememaster/mememaster-ai
- 文档: https://docs.mememaster.ai
