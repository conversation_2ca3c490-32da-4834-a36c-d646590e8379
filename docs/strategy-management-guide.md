# MemeMaster AI 策略管理模块使用指南

## 概述

策略管理模块是 MemeMaster AI 的核心功能之一，提供了从策略生成到部署监控的完整工作流。本模块按照用户使用逻辑分为两个主要部分：

1. **策略仓库** - 管理未部署的策略
2. **策略监控** - 监控已部署的策略

## 功能特性

### 🗄️ 策略仓库
- **集中管理**：所有AI生成但尚未部署的策略
- **智能筛选**：按状态、类别、评分、时间范围筛选
- **批量操作**：支持多选策略批量部署
- **详情查看**：完整的策略信息展示
- **一键部署**：简化的部署工作流

### 📊 策略监控
- **实时数据**：价格、市值、持币人数、流动性等关键指标
- **可视化图表**：24小时价格走势图
- **状态管理**：暂停、恢复、添加流动性等操作
- **预警系统**：价格波动、流动性变化等预警

## 使用流程

### 1. 策略仓库操作

#### 查看策略列表
```javascript
// 获取策略仓库数据
const response = await strategyAPI.getRepository()
const strategies = response.data.strategies
```

#### 筛选策略
- **按状态筛选**：草稿、待部署、部署中
- **按类别筛选**：政治、金融、科技、亚文化
- **按评分筛选**：高分(85+)、中分(70-84)、低分(<70)
- **搜索功能**：支持策略名称、符号、热点来源搜索

#### 批量部署
1. 选择多个策略（使用复选框）
2. 点击"批量部署"按钮
3. 确认部署配置
4. 等待部署完成

### 2. 策略监控操作

#### 查看已部署策略
```javascript
// 获取已部署策略数据
const response = await strategyAPI.getDeployedStrategies()
const deployedTokens = response.data.deployed_tokens
```

#### 监控关键指标
- **当前价格**：实时代币价格
- **市值**：总市值计算
- **持币人数**：代币持有者数量
- **流动性**：可用流动性金额
- **24h涨幅**：24小时价格变化
- **交易量**：24小时交易量

#### 策略操作
- **详细监控**：查看完整的策略监控数据
- **添加流动性**：为策略增加流动性
- **启动退出**：开始退出策略流程
- **暂停/恢复**：暂停或恢复策略运行

## API 接口

### 策略仓库相关
```javascript
// 获取策略仓库
GET /strategy/repository

// 部署单个策略
POST /strategy/deploy
{
  "strategy_id": "str_001",
  "chain": "solana",
  "platform": "pump.fun"
}

// 批量部署策略
POST /strategy/batch-deploy
{
  "strategy_ids": ["str_001", "str_002"],
  "deployment_config": {...}
}
```

### 策略监控相关
```javascript
// 获取已部署策略
GET /strategy/deployed

// 获取策略详情
GET /strategy/{strategy_id}

// 添加监控
POST /strategy/{strategy_id}/monitor
{
  "alerts": ["price_change", "liquidity_low"],
  "refresh_interval": 30
}

// 暂停策略
POST /strategy/{strategy_id}/pause

// 恢复策略
POST /strategy/{strategy_id}/resume
```

## 数据结构

### 策略对象
```javascript
{
  "id": "str_001",
  "name": "Trump 2024 Victory Token",
  "symbol": "TRUMP24",
  "status": "draft", // draft, pending, deploying, active, paused
  "category": "politics",
  "created": "2024-01-15T10:30:00Z",
  "hotspot_source": "Trump 2024 Campaign Launch",
  "score": 92.5,
  "estimated_roi": 245.8,
  "viral_potential": 0.92,
  "sentiment": 0.72,
  "tokenomics": {
    "total_supply": 1000000000,
    "allocation": {
      "airdrop": 50,
      "liquidity": 30,
      "marketing": 15,
      "team": 5
    },
    "burn_rate": 0.02
  },
  "deployment_config": {
    "platforms": ["pump.fun", "raydium"],
    "chains": ["solana"],
    "gas_optimized": true
  },
  "risk_level": "medium",
  "compliance_status": "passed"
}
```

### 已部署策略对象
```javascript
{
  "id": "str_006",
  "name": "Bitcoin ETF Surge",
  "symbol": "BTCETF",
  "status": "active",
  "contract_address": "******************************************",
  "current_price": 0.0023,
  "market_cap": 2300000,
  "holders": 3456,
  "liquidity": 145000,
  "volume_24h": 567000,
  "price_change_24h": 23.4
}
```

## 最佳实践

### 策略选择
1. **优先高评分策略**：选择评分85+的策略
2. **关注病毒潜力**：病毒潜力>0.85的策略更容易成功
3. **平衡风险等级**：混合低、中、高风险策略
4. **考虑时机**：关注热点事件的时效性

### 部署策略
1. **选择合适平台**：
   - Pump.fun：适合社区驱动的meme币
   - Raydium：适合需要专业流动性的代币
   - Uniswap：适合以太坊生态代币

2. **Gas优化**：
   - 启用Gas优化功能
   - 选择网络拥堵较低的时间部署
   - 批量部署可以节省Gas费用

### 监控管理
1. **设置合理预警**：
   - 价格波动：±15%
   - 流动性下降：30%
   - 大额交易：>5%总量

2. **定期检查**：
   - 每日查看关键指标
   - 关注持币人数变化
   - 监控社交媒体情绪

## 故障排除

### 常见问题

**Q: 策略部署失败怎么办？**
A: 检查网络状态、Gas费用设置、钱包余额是否充足

**Q: 监控数据不更新？**
A: 点击刷新按钮，检查网络连接，确认合约地址正确

**Q: 批量操作失败？**
A: 减少批量操作的策略数量，检查每个策略的状态

**Q: 策略详情无法加载？**
A: 确认策略ID正确，检查API服务状态

### 技术支持
如遇到技术问题，请联系开发团队或查看系统日志获取详细错误信息。

## 更新日志

### v2.0.0 (2024-01-15)
- 新增策略仓库功能
- 新增策略监控功能
- 支持批量操作
- 优化用户界面
- 增强API接口

### v1.0.0 (2024-01-01)
- 基础策略管理功能
- 单个策略部署
- 基本监控功能
