# 区块链节点配置
ETH_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
SOL_RPC_URL=https://api.mainnet-beta.solana.com
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_KEY
BSC_RPC_URL=https://bsc-dataseed.binance.org/

# 私钥配置 (请使用安全的密钥管理)
ETH_PRIVATE_KEY=your_ethereum_private_key
SOL_PRIVATE_KEY=your_solana_private_key

# API密钥配置
POLITICO_API_KEY=your_politico_api_key
TRADEMARKNOW_API_KEY=your_trademark_api_key
BRIGHTDATA_API_KEY=your_brightdata_api_key
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# 安全设置
HSM_MODULE_PATH=/opt/safenet/luna
ENCRYPTION_KEY=secure_encryption_key_32bytes_long
JWT_SECRET=your_jwt_secret_key

# 监管设置
RESTRICTED_REGIONS=US,CN,KR,CA,UK
COMPLIANCE_MODE=strict
GEOFENCE_ENABLED=true

# 数据库配置
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/mememaster
POSTGRES_URL=postgresql://user:password@localhost:5432/mememaster

# 代理配置
TOR_PROXY=socks5://127.0.0.1:9050
HTTP_PROXY_LIST=proxy1:port,proxy2:port,proxy3:port
PROXY_ROTATION_INTERVAL=300

# 交易配置
MAX_SLIPPAGE=0.05
GAS_PRICE_MULTIPLIER=1.2
MAX_GAS_PRICE=100
TRADE_AMOUNT_LIMIT=1000

# 监控配置
PROMETHEUS_PORT=8000
LOG_LEVEL=INFO
ALERT_WEBHOOK_URL=https://hooks.slack.com/your/webhook/url

# 开发环境配置
DEBUG=false
ENVIRONMENT=production
API_PORT=3000
WEB_PORT=8080
