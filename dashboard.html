<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 智能仪表盘</title>
    
    <!-- 字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f1f5f9;
            min-height: 100vh;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(71, 85, 105, 0.5);
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 10;
        }

        .logo {
            padding: 24px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.5);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo i {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .logo h1 {
            font-size: 20px;
            font-weight: 700;
            background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            margin: 4px 16px;
            border-radius: 8px;
            color: #cbd5e1;
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(71, 85, 105, 0.5);
            color: white;
        }

        .nav-item.active {
            background: rgba(99, 102, 241, 0.2);
            color: #a5b4fc;
            border: 1px solid rgba(99, 102, 241, 0.3);
        }

        .nav-item i {
            width: 20px;
            font-size: 16px;
        }

        .divider {
            height: 1px;
            background: rgba(71, 85, 105, 0.5);
            margin: 24px 16px;
        }

        .user-info {
            margin-top: auto;
            padding: 16px;
            border-top: 1px solid rgba(71, 85, 105, 0.5);
        }

        .plan {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: #000;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 700;
            display: inline-block;
            margin-bottom: 8px;
        }

        .info .name {
            font-weight: 600;
            color: white;
            font-size: 14px;
        }

        .info .email {
            color: #94a3b8;
            font-size: 12px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            margin-left: 260px;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(71, 85, 105, 0.5);
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 5;
        }

        .search-bar {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-bar i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
        }

        .search-bar input {
            width: 100%;
            padding: 8px 12px 8px 40px;
            background: rgba(71, 85, 105, 0.5);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 8px;
            color: white;
            font-size: 14px;
        }

        .search-bar input::placeholder {
            color: #94a3b8;
        }

        .search-bar input:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .notification {
            position: relative;
            padding: 8px;
            color: #94a3b8;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .notification:hover {
            color: white;
        }

        .notification::after {
            content: '';
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            background: #ef4444;
            border-radius: 50%;
        }

        .user-avatar {
            padding: 8px;
            background: rgba(71, 85, 105, 0.5);
            border-radius: 8px;
            color: #94a3b8;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .user-avatar:hover {
            color: white;
            background: rgba(71, 85, 105, 0.7);
        }

        /* 仪表盘内容 */
        .dashboard-content {
            padding: 24px;
            flex: 1;
            overflow-y: auto;
        }

        .welcome-banner {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(59, 130, 246, 0.2) 100%);
            border: 1px solid rgba(99, 102, 241, 0.3);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .welcome-text h2 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .welcome-text p {
            color: #cbd5e1;
            line-height: 1.5;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #6366f1 0%, #3b82f6 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #5b21b6 0%, #1d4ed8 100%);
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            border: 1px solid rgba(71, 85, 105, 0.5);
            color: #94a3b8;
        }

        .btn-outline:hover {
            background: rgba(71, 85, 105, 0.3);
            color: white;
        }

        /* 统计卡片 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-4px);
        }

        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .stat-title {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: white;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .icon-ai {
            background: rgba(139, 92, 246, 0.2);
            color: #a78bfa;
        }

        .icon-trade {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }

        .icon-sub {
            background: rgba(245, 158, 11, 0.2);
            color: #fbbf24;
        }

        .icon-wallet {
            background: rgba(16, 185, 129, 0.2);
            color: #34d399;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 14px;
        }

        .trend-up {
            color: #10b981;
        }

        .trend-down {
            color: #ef4444;
        }

        /* 图表区域 */
        .charts-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 24px;
        }

        .chart-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 24px;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .card-actions {
            display: flex;
            gap: 8px;
        }

        .chart-container {
            height: 320px;
            position: relative;
        }

        /* 热门代币 */
        .hot-coins {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .hot-coin {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: rgba(71, 85, 105, 0.3);
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .hot-coin:hover {
            background: rgba(71, 85, 105, 0.5);
        }

        .coin-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 12px;
            margin-right: 12px;
        }

        .coin-info {
            flex: 1;
        }

        .coin-name {
            font-weight: 600;
            color: white;
            font-size: 14px;
        }

        .coin-symbol {
            color: #94a3b8;
            font-size: 12px;
        }

        .coin-change {
            font-weight: 600;
            font-size: 14px;
        }

        .change-up {
            color: #10b981;
        }

        .change-down {
            color: #ef4444;
        }

        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .feature-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-4px);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        .feature-desc {
            color: #94a3b8;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .feature-card .btn {
            width: 100%;
            justify-content: center;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-container {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .welcome-banner {
                flex-direction: column;
                text-align: center;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <i class="fas fa-robot"></i>
                <h1>MemeMaster AI</h1>
            </div>

            <a href="#" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>仪表盘</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-chart-line"></i>
                <span>市场分析</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>交易策略</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-bolt"></i>
                <span>自动化交易</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-wallet"></i>
                <span>支付与订阅</span>
            </a>

            <div class="divider"></div>

            <a href="#" class="nav-item">
                <i class="fas fa-user-cog"></i>
                <span>用户管理</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>安全设置</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>系统设置</span>
            </a>

            <div class="user-info">
                <div class="plan">PRO 会员</div>
                <div class="info">
                    <div class="name">John Trader</div>
                    <div class="email"><EMAIL></div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <div class="header">
                <div class="search-bar">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索代币、策略或功能...">
                </div>

                <div class="header-actions">
                    <div class="notification">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>

            <div class="dashboard-content">
                <!-- 欢迎横幅 -->
                <div class="welcome-banner">
                    <div class="welcome-text">
                        <h2>👋 欢迎回来，John!</h2>
                        <p>系统检测到3个新的交易机会，你的策略正在执行中。今日市场情绪指数为<span style="color: #10b981; font-weight: 600;">+78.2</span>，适合积极交易。</p>
                    </div>
                    <button class="btn btn-primary">
                        <i class="fas fa-bolt"></i> 查看交易机会
                    </button>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">AI分析使用</div>
                                <div class="stat-value">42/∞</div>
                            </div>
                            <div class="stat-icon icon-ai">
                                <i class="fas fa-brain"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% 本周使用率</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">活跃策略</div>
                                <div class="stat-value">7</div>
                            </div>
                            <div class="stat-icon icon-trade">
                                <i class="fas fa-robot"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>今日收益: +3.2 SOL</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">订阅状态</div>
                                <div class="stat-value">PRO 会员</div>
                            </div>
                            <div class="stat-icon icon-sub">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="stat-trend">
                            <i class="fas fa-calendar"></i>
                            <span>到期日: 2023-12-15</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-header">
                            <div>
                                <div class="stat-title">钱包余额</div>
                                <div class="stat-value">48.75 SOL</div>
                            </div>
                            <div class="stat-icon icon-wallet">
                                <i class="fas fa-wallet"></i>
                            </div>
                        </div>
                        <div class="stat-trend trend-down">
                            <i class="fas fa-sync-alt"></i>
                            <span>≈ $1,250 USD</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区 -->
                <div class="charts-container">
                    <div class="chart-card">
                        <div class="card-header">
                            <div class="card-title">市场情绪与交易趋势</div>
                            <div class="card-actions">
                                <button class="btn btn-outline">7天</button>
                                <button class="btn btn-primary">30天</button>
                                <button class="btn btn-outline">90天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="marketChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="card-header">
                            <div class="card-title">热门Meme币</div>
                            <button class="btn btn-outline">查看更多</button>
                        </div>
                        <div class="hot-coins">
                            <div class="hot-coin">
                                <div class="coin-icon">DOGE</div>
                                <div class="coin-info">
                                    <div class="coin-name">Dogecoin</div>
                                    <div class="coin-symbol">DOGE/USD</div>
                                </div>
                                <div class="coin-change change-up">+12.4%</div>
                            </div>

                            <div class="hot-coin">
                                <div class="coin-icon">SHIB</div>
                                <div class="coin-info">
                                    <div class="coin-name">Shiba Inu</div>
                                    <div class="coin-symbol">SHIB/USD</div>
                                </div>
                                <div class="coin-change change-up">+8.2%</div>
                            </div>

                            <div class="hot-coin">
                                <div class="coin-icon">PEPE</div>
                                <div class="coin-info">
                                    <div class="coin-name">Pepe Coin</div>
                                    <div class="coin-symbol">PEPE/USD</div>
                                </div>
                                <div class="coin-change change-down">-3.1%</div>
                            </div>

                            <div class="hot-coin">
                                <div class="coin-icon">FLOKI</div>
                                <div class="coin-info">
                                    <div class="coin-name">Floki Inu</div>
                                    <div class="coin-symbol">FLOKI/USD</div>
                                </div>
                                <div class="coin-change change-up">+5.7%</div>
                            </div>

                            <div class="hot-coin">
                                <div class="coin-icon">BONK</div>
                                <div class="coin-info">
                                    <div class="coin-name">Bonk</div>
                                    <div class="coin-symbol">BONK/USD</div>
                                </div>
                                <div class="coin-change change-up">+21.8%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能卡片 -->
                <div class="card-header">
                    <div class="card-title">核心功能</div>
                    <button class="btn btn-outline">所有功能</button>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: rgba(239, 68, 68, 0.2); color: #f87171;">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="feature-title">热点检测</div>
                        <div class="feature-desc">实时监测社交媒体趋势，识别新兴Meme币机会</div>
                        <button class="btn btn-outline">立即扫描</button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon" style="background: rgba(59, 130, 246, 0.2); color: #60a5fa;">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="feature-title">策略生成</div>
                        <div class="feature-desc">AI驱动的交易策略，自动优化风险回报比</div>
                        <button class="btn btn-outline">创建策略</button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon" style="background: rgba(139, 92, 246, 0.2); color: #a78bfa;">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="feature-title">一键部署</div>
                        <div class="feature-desc">自动化部署代币到多个区块链网络</div>
                        <button class="btn btn-primary">立即部署</button>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon" style="background: rgba(16, 185, 129, 0.2); color: #34d399;">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="feature-title">流动性管理</div>
                        <div class="feature-desc">智能管理资金池，优化交易执行</div>
                        <button class="btn btn-outline">管理流动性</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化市场图表
        const ctx = document.getElementById('marketChart').getContext('2d');
        const marketChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                datasets: [
                    {
                        label: '市场情绪指数',
                        data: [65, 59, 70, 71, 76, 75, 80, 82, 78, 85, 90, 92],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 2
                    },
                    {
                        label: '交易量 (百万)',
                        data: [12, 19, 15, 25, 22, 30, 28, 35, 40, 48, 45, 60],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 2
                    },
                    {
                        label: '策略成功率 (%)',
                        data: [72, 75, 78, 76, 80, 82, 85, 84, 87, 88, 90, 92],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true,
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#e2e8f0',
                            font: {
                                family: 'Inter'
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        },
                        ticks: {
                            color: '#94a3b8',
                            font: {
                                family: 'Inter'
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)'
                        },
                        ticks: {
                            color: '#94a3b8',
                            font: {
                                family: 'Inter'
                            }
                        }
                    }
                }
            }
        });

        // 添加交互效果
        document.querySelectorAll('.stat-card, .feature-card, .hot-coin').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // 时间筛选按钮交互
        document.querySelectorAll('.card-actions .btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // 移除其他按钮的active状态
                this.parentElement.querySelectorAll('.btn').forEach(b => {
                    b.classList.remove('btn-primary');
                    b.classList.add('btn-outline');
                });

                // 添加当前按钮的active状态
                this.classList.remove('btn-outline');
                this.classList.add('btn-primary');

                // 这里可以添加图表数据更新逻辑
                console.log('切换时间范围:', this.textContent);
            });
        });

        // 搜索功能
        document.querySelector('.search-bar input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            console.log('搜索:', searchTerm);
            // 这里可以添加搜索逻辑
        });

        // 通知点击
        document.querySelector('.notification').addEventListener('click', function() {
            alert('您有3条新通知');
        });

        // 功能卡片点击事件
        document.querySelectorAll('.feature-card .btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const featureTitle = this.closest('.feature-card').querySelector('.feature-title').textContent;
                console.log('点击功能:', featureTitle);

                // 添加点击动画
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 实时数据更新模拟
        setInterval(() => {
            // 模拟实时数据更新
            const sentimentElement = document.querySelector('.welcome-text span');
            if (sentimentElement) {
                const currentValue = parseFloat(sentimentElement.textContent.replace('+', ''));
                const newValue = (currentValue + (Math.random() - 0.5) * 2).toFixed(1);
                sentimentElement.textContent = `+${newValue}`;

                // 根据变化调整颜色
                if (newValue > currentValue) {
                    sentimentElement.style.color = '#10b981';
                } else {
                    sentimentElement.style.color = '#ef4444';
                }
            }
        }, 5000);

        // 页面加载动画
        window.addEventListener('load', function() {
            document.querySelectorAll('.stat-card, .feature-card').forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
