"""
MemeMaster AI Web应用
提供完整的Web界面和API服务
"""

import os
import sys
import time
import psutil
import asyncio
import random
from datetime import datetime, timedelta
from typing import Optional
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入用户管理相关模块
try:
    from src.models.user import (
        User, UserCreate, UserLogin, UserRegister, UserResponse,
        PasswordReset, PasswordResetConfirm, UserStats
    )
    from src.models.subscription import (
        Subscription, SubscriptionTier, SubscriptionCreate, SubscriptionResponse,
        SubscriptionPlan, SubscriptionUpgrade, SUBSCRIPTION_PLANS
    )
    from src.models.payment import (
        Payment, PaymentCreate, PaymentResponse, PaymentRequest,
        PaymentConfirmation, PaymentStats, PAY_PER_USE_PRICES
    )
    from src.auth.auth_manager import auth_manager, get_current_user, get_current_active_user
    from src.auth.middleware import (
        require_subscription, require_feature_access, require_paid_feature,
        check_hotspot_quota, check_deployment_access
    )
    from src.subscription.feature_checker import FeatureChecker
    from src.database.connection import init_database

    USER_MANAGEMENT_ENABLED = True
    print("✅ 用户管理系统加载成功")
except ImportError as e:
    print(f"⚠️ 用户管理系统加载失败: {e}")
    USER_MANAGEMENT_ENABLED = False

    # 创建模拟的User类
    class User(BaseModel):
        id: int = 1
        username: str = "demo"
        email: str = "<EMAIL>"

    def get_current_user():
        return User()

    def get_current_active_user():
        return User()

# 尝试导入优化引擎
try:
    from config.optimization_config import OptimizationConfig
    from engines.optimization_engine import OptimizationEngine, TokenPerformance
    optimization_engine = OptimizationEngine()
    print("✅ 参数优化引擎加载成功")
except ImportError as e:
    print(f"⚠️ 参数优化引擎加载失败: {e}")
    # 创建模拟类
    class OptimizationConfig:
        @staticmethod
        def customize_exit_strategy(token_type, initial_performance):
            return {"signal_threshold": 0.75, "optimized": True}
        @staticmethod
        def calculate_initial_liquidity(strategy):
            return 0.5
        @staticmethod
        def get_market_sensitivity_config(level):
            return {"optimized": True}

    class OptimizationEngine:
        def __init__(self):
            pass
        async def analyze_hotspot_parameters(self, hotspot_data):
            return {"optimized": True, "source_weights": {"twitter": 0.4}}
        async def optimize_strategy_parameters(self, hotspot_data):
            return {"optimized": True, "allocation": {"airdrop": 0.5}}

    optimization_engine = OptimizationEngine()

# 创建FastAPI应用
app = FastAPI(
    title="MemeMaster AI",
    description="智能Meme币交易系统",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3001",
        "http://127.0.0.1:3001",
        "http://localhost:8001",
        "http://127.0.0.1:8001"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件服务 - 服务构建后的React应用
app.mount("/assets", StaticFiles(directory="dist/assets"), name="assets")
app.mount("/simple_assets", StaticFiles(directory="dist_simple/assets"), name="simple_assets")
app.mount("/static", StaticFiles(directory="frontend"), name="static")

# 添加前端文件路由处理
@app.get("/frontend/{file_path:path}")
async def serve_frontend_files(file_path: str):
    """服务前端文件"""
    file_location = f"frontend/{file_path}"
    if os.path.exists(file_location):
        return FileResponse(file_location)
    return {"error": "File not found"}

# 处理vite.svg图标
@app.get("/vite.svg")
async def get_vite_icon():
    """返回Vite图标"""
    # 简单的SVG图标
    svg_content = '''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M12 2L2 7l10 5 10-5-10-5z"/>
        <path d="M2 17l10 5 10-5"/>
        <path d="M2 12l10 5 10-5"/>
    </svg>'''
    return HTMLResponse(content=svg_content, media_type="image/svg+xml")

# Socket.IO 功能暂时禁用，专注于基本 API 功能

# 启动时间
start_time = time.time()

# 主页HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 智能Meme币交易系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .status-bar {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .status-item {
            text-align: center;
            margin: 10px;
        }
        
        .status-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #48bb78;
            font-weight: bold;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .api-endpoint {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: rgba(255,255,255,0.8);
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 10px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .alert.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .status-bar {
                flex-direction: column;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MemeMaster AI</h1>
            <p>智能驱动，收益无限 - 下一代Meme币交易系统</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-label">系统状态</div>
                <div class="status-value" id="system-status">🟢 运行中</div>
            </div>
            <div class="status-item">
                <div class="status-label">当前时间</div>
                <div class="status-value" id="current-time">--</div>
            </div>
            <div class="status-item">
                <div class="status-label">服务器</div>
                <div class="status-value">*************:8000</div>
            </div>
        </div>
        
        <div class="cards-grid">
            <div class="card">
                <h3>🤖 AI智能分析</h3>
                <p>基于深度学习的Meme币市场分析系统，实时监控热点趋势，智能识别投资机会。</p>
                
                <ul class="feature-list">
                    <li>实时市场数据分析</li>
                    <li>社交媒体情绪监控</li>
                    <li>智能风险评估</li>
                    <li>趋势预测算法</li>
                </ul>
                
                <div class="alert">
                    <strong>AI引擎状态:</strong> <span id="ai-status">正在初始化...</span>
                </div>
            </div>
            
            <div class="card">
                <h3>⚡ 自动化交易</h3>
                <p>高频交易算法，毫秒级响应，自动执行买卖策略，最大化收益潜力。</p>
                
                <ul class="feature-list">
                    <li>自动化交易策略</li>
                    <li>风险管理系统</li>
                    <li>止损止盈设置</li>
                    <li>收益优化算法</li>
                </ul>
                
                <div class="alert warning">
                    <strong>交易引擎:</strong> <span id="trading-status">待配置</span>
                </div>
            </div>
            
            <div class="card">
                <h3>📡 API接口</h3>
                <p>以下是可用的API端点：</p>
                
                <div class="api-endpoint">
                    <strong>GET /</strong> - 主页面板
                </div>
                
                <div class="api-endpoint">
                    <strong>GET /health</strong> - 健康检查
                </div>
                
                <div class="api-endpoint">
                    <strong>GET /api/status</strong> - 系统状态
                </div>
                
                <div class="api-endpoint">
                    <strong>GET /api/system-info</strong> - 系统信息
                </div>
                
                <div style="margin-top: 20px;">
                    <a href="/health" class="btn">健康检查</a>
                    <a href="/api/status" class="btn">系统状态</a>
                    <a href="/api/system-info" class="btn">系统信息</a>
                </div>
            </div>
            
            <div class="card">
                <h3>📈 系统统计</h3>
                <div class="stats">
                    <div class="stat-item">
                        <div class="stat-number" id="uptime">--</div>
                        <div class="stat-label">运行时间</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="requests">--</div>
                        <div class="stat-label">请求次数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="cpu-usage">--</div>
                        <div class="stat-label">CPU使用率</div>
                    </div>
                </div>
                
                <div class="alert">
                    <strong>性能监控:</strong> 系统运行正常，所有服务可用
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 MemeMaster AI. 智能驱动，收益无限。</p>
            <p>服务器时间: <span id="server-time">--</span></p>
        </div>
    </div>
    
    <script>
        // 全局变量
        let requestCount = Math.floor(Math.random() * 1000) + 500;
        
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString('zh-CN');
            document.getElementById('server-time').textContent = now.toLocaleString('zh-CN');
        }
        
        // 获取系统信息
        async function updateSystemInfo() {
            try {
                const response = await fetch('/api/system-info');
                const data = await response.json();
                
                if (data.uptime) {
                    const hours = Math.floor(data.uptime / 3600);
                    document.getElementById('uptime').textContent = hours + 'h';
                }
                if (data.cpu_percent !== undefined) {
                    document.getElementById('cpu-usage').textContent = data.cpu_percent.toFixed(1) + '%';
                }
                
                // 更新AI状态
                document.getElementById('ai-status').textContent = '运行正常 ✓';
                document.getElementById('trading-status').textContent = '监控中 📊';
                
            } catch (error) {
                console.log('获取系统信息失败:', error);
                document.getElementById('ai-status').textContent = '连接中...';
            }
        }
        
        // 更新请求计数
        function updateRequestCount() {
            requestCount += Math.floor(Math.random() * 3) + 1;
            document.getElementById('requests').textContent = requestCount.toLocaleString();
        }
        
        // 初始化
        updateTime();
        updateSystemInfo();
        updateRequestCount();
        
        // 定时更新
        setInterval(updateTime, 1000);
        setInterval(updateSystemInfo, 5000);
        setInterval(updateRequestCount, 3000);
        
        // 页面加载完成提示
        window.addEventListener('load', function() {
            console.log('🚀 MemeMaster AI Web界面加载完成');
            console.log('📊 系统监控已启动');
        });
    </script>
</body>
</html>
"""

@app.get("/")
async def home():
    """主页 - 返回构建后的React应用"""
    return FileResponse('dist/index.html')

# Catch-all route for React Router (must be after all API routes)
@app.get("/{path:path}")
async def catch_all(path: str):
    """处理React Router的客户端路由"""
    # 如果是API路径，返回404
    if path.startswith('api/'):
        raise HTTPException(status_code=404, detail="API endpoint not found")

    # 如果是静态资源路径，让FastAPI处理
    if path.startswith('assets/') or path.endswith(('.js', '.css', '.png', '.jpg', '.ico', '.svg')):
        raise HTTPException(status_code=404, detail="Static file not found")

    # 对于所有其他路径，返回React应用的index.html
    return FileResponse('dist/index.html')

@app.get("/legacy", response_class=HTMLResponse)
async def legacy_home():
    """旧版主页"""
    return HTML_TEMPLATE

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "MemeMaster AI",
        "version": "2.0.0"
    }

@app.get("/test-css")
async def test_css():
    """CSS测试页面"""
    return FileResponse('test_page.html')

@app.get("/test-react")
async def test_react():
    """React应用诊断页面"""
    return FileResponse('test_react.html')

@app.get("/test-minimal")
async def test_minimal():
    """最小化React测试页面"""
    return FileResponse('test_minimal_react.html')

@app.get("/simple")
async def simple_app():
    """简化版React应用"""
    return FileResponse('dist_simple/index_simple.html')

@app.get("/system/info")
async def system_info():
    """获取系统信息"""
    import psutil
    import time

    return {
        "uptime": int(time.time() - psutil.boot_time()),
        "cpu_usage": psutil.cpu_percent(),
        "memory_usage": psutil.virtual_memory().percent,
        "request_count": 1234,  # 这里应该从实际计数器获取
        "timestamp": datetime.now().isoformat()
    }

# 增强版热点监控 API
@app.get("/hotspot/current")
async def get_current_hotspots(current_user: User = Depends(get_current_active_user) if USER_MANAGEMENT_ENABLED else None):
    """获取当前热点 - 增强版多领域监测"""
    import random
    from datetime import datetime, timedelta

    # 用户管理功能启用时的权限检查
    if USER_MANAGEMENT_ENABLED and current_user:
        # 检查试用状态
        trial_status = FeatureChecker.check_trial_status(current_user)
        if trial_status["is_trial"] and trial_status["trial_expired"]:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="免费试用已过期，请升级订阅以继续使用热点监测功能"
            )

        # 检查每日热点限制
        limits = FeatureChecker.get_user_limits(current_user)
        daily_limit = limits.get("daily_hotspots", 3)

        if daily_limit != -1 and current_user.daily_hotspots_used >= daily_limit:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"今日热点检测次数已用完（{daily_limit}次），请明天再试或升级订阅"
            )

        # 获取用户可访问的内容类型
        allowed_content_types = FeatureChecker.get_content_type_access(current_user)

        # 增加使用次数
        from src.database.user_db import UserDatabase
        user_db = UserDatabase()
        await user_db.increment_daily_hotspots(current_user.id)

    # 模拟增强版热点数据 - 覆盖所有需求领域
    hotspots = [
        {
            "id": "hot_001",
            "title": "Trump 2024 Campaign Launch",
            "keyword": "Trump 2024",
            "description": "特朗普正式宣布2024年总统竞选计划，社交媒体热度爆表",
            "score": 92.5,
            "category": "politics",
            "source": "Truth Social, Twitter",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
            "virality": 0.95,
            "resonance": 0.88,
            "celebrity": 0.92,
            "cultural": 0.85,
            "sentiment": 0.72,
            "volume": 125000,
            "growth_rate": 0.45,
            "trending_duration": "2小时",
            "related_keywords": ["Election", "Republican", "MAGA", "Politics"],
            "influence_score": 0.89,
            "viral_potential": 0.92,
            "region": "US",
            "media_coverage": "EXTENSIVE",
            "celebrity_mentions": ["Elon Musk", "Tucker Carlson", "Ron DeSantis"],
            "price_impact": "+15.2%",
            "market_cap_change": "+$2.1B"
        },
        {
            "id": "hot_002",
            "title": "XRP ETF Approval Speculation",
            "keyword": "XRP ETF",
            "description": "市场传言XRP ETF即将获得SEC批准，加密社区沸腾",
            "score": 89.3,
            "category": "finance",
            "source": "Bloomberg, Reddit",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=12)).isoformat(),
            "virality": 0.87,
            "resonance": 0.91,
            "celebrity": 0.75,
            "cultural": 0.82,
            "sentiment": 0.84,
            "volume": 78000,
            "growth_rate": 0.58,
            "trending_duration": "1.5小时",
            "related_keywords": ["XRP", "ETF", "SEC", "Ripple"],
            "influence_score": 0.81,
            "viral_potential": 0.88,
            "region": "GLOBAL",
            "media_coverage": "HIGH",
            "celebrity_mentions": ["Gary Gensler", "Brad Garlinghouse"],
            "price_impact": "+28.7%",
            "market_cap_change": "+$4.2B"
        },
        {
            "id": "hot_003",
            "title": "Musk AI Commentary Storm",
            "keyword": "Musk AI",
            "description": "马斯克最新AI评论引发全球热议，xAI股价暴涨",
            "score": 86.7,
            "category": "technology",
            "source": "Twitter, YouTube",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=8)).isoformat(),
            "virality": 0.93,
            "resonance": 0.79,
            "celebrity": 0.98,
            "cultural": 0.76,
            "sentiment": 0.76,
            "volume": 95000,
            "growth_rate": 0.67,
            "trending_duration": "45分钟",
            "related_keywords": ["AI", "xAI", "Technology", "Innovation"],
            "influence_score": 0.94,
            "viral_potential": 0.91,
            "region": "GLOBAL",
            "media_coverage": "EXTENSIVE",
            "celebrity_mentions": ["Elon Musk", "Sam Altman", "Jensen Huang"],
            "price_impact": "+12.4%",
            "market_cap_change": "+$1.8B"
        },
        {
            "id": "hot_004",
            "title": "Pepe Community Revival",
            "keyword": "Pepe Comeback",
            "description": "Pepe原创作者Matt Furie宣布官方复活计划，社区狂欢",
            "score": 78.9,
            "category": "subculture",
            "source": "Twitter, Discord",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat(),
            "virality": 0.88,
            "resonance": 0.95,
            "celebrity": 0.71,
            "cultural": 0.94,
            "sentiment": 0.91,
            "volume": 52000,
            "growth_rate": 0.34,
            "trending_duration": "3小时",
            "related_keywords": ["Pepe", "Meme", "NFT", "Community"],
            "influence_score": 0.73,
            "viral_potential": 0.85,
            "region": "GLOBAL",
            "media_coverage": "MODERATE",
            "celebrity_mentions": ["Matt Furie", "Vitalik Buterin"],
            "price_impact": "+45.6%",
            "market_cap_change": "+$890M"
        },
        {
            "id": "hot_005",
            "title": "Sandbox Virtual Concert",
            "keyword": "Metaverse Concert",
            "description": "The Sandbox举办史上最大虚拟演唱会，元宇宙概念再次火爆",
            "score": 75.2,
            "category": "metaverse",
            "source": "Discord, YouTube",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=20)).isoformat(),
            "virality": 0.72,
            "resonance": 0.84,
            "celebrity": 0.65,
            "cultural": 0.89,
            "sentiment": 0.87,
            "volume": 34000,
            "growth_rate": 0.29,
            "trending_duration": "2.5小时",
            "related_keywords": ["Metaverse", "VR", "Gaming", "NFT"],
            "influence_score": 0.68,
            "viral_potential": 0.79,
            "region": "GLOBAL",
            "media_coverage": "MODERATE",
            "celebrity_mentions": ["Snoop Dogg", "Deadmau5"],
            "price_impact": "+18.3%",
            "market_cap_change": "+$320M"
        },
        {
            "id": "hot_006",
            "title": "K-pop NFT Collection Drop",
            "keyword": "K-pop NFT",
            "description": "韩国顶级娱乐公司推出限量版NFT收藏品，粉丝疯抢",
            "score": 82.1,
            "category": "subculture",
            "source": "TikTok, Instagram",
            "language": "ko",
            "timestamp": (datetime.now() - timedelta(minutes=25)).isoformat(),
            "virality": 0.88,
            "resonance": 0.95,
            "celebrity": 0.71,
            "cultural": 0.94,
            "sentiment": 0.93,
            "volume": 67000,
            "growth_rate": 0.52,
            "trending_duration": "4小时",
            "related_keywords": ["K-pop", "NFT", "Entertainment", "Korea"],
            "influence_score": 0.79,
            "viral_potential": 0.87,
            "region": "ASIA",
            "media_coverage": "HIGH",
            "celebrity_mentions": ["BTS", "BLACKPINK", "NewJeans"],
            "price_impact": "+32.1%",
            "market_cap_change": "+$1.2B"
        },
        {
            "id": "hot_007",
            "title": "AI生成二次元角色",
            "keyword": "AI Anime",
            "description": "AI生成的二次元角色在日本爆红，虚拟偶像产业迎来变革",
            "score": 71.8,
            "category": "subculture",
            "source": "YouTube, Pixiv",
            "language": "ja",
            "timestamp": (datetime.now() - timedelta(minutes=30)).isoformat(),
            "virality": 0.81,
            "resonance": 0.89,
            "celebrity": 0.62,
            "cultural": 0.96,
            "sentiment": 0.85,
            "volume": 28000,
            "growth_rate": 0.38,
            "trending_duration": "5小时",
            "related_keywords": ["AI", "Anime", "Virtual", "Japan"],
            "influence_score": 0.71,
            "viral_potential": 0.82,
            "region": "JAPAN",
            "media_coverage": "MODERATE",
            "celebrity_mentions": ["Hatsune Miku", "Kizuna AI"],
            "price_impact": "+24.7%",
            "market_cap_change": "+$450M"
        },
        {
            "id": "hot_008",
            "title": "Bitcoin ETF Inflow Record",
            "keyword": "Bitcoin ETF",
            "description": "比特币ETF创下单日资金流入新纪录，机构FOMO情绪高涨",
            "score": 88.4,
            "category": "cryptocurrency",
            "source": "CNBC, CoinDesk",
            "language": "en",
            "timestamp": (datetime.now() - timedelta(minutes=35)).isoformat(),
            "virality": 0.85,
            "resonance": 0.92,
            "celebrity": 0.78,
            "cultural": 0.73,
            "sentiment": 0.89,
            "volume": 89000,
            "growth_rate": 0.61,
            "trending_duration": "1小时",
            "related_keywords": ["Bitcoin", "ETF", "Institutional", "FOMO"],
            "influence_score": 0.87,
            "viral_potential": 0.90,
            "region": "GLOBAL",
            "media_coverage": "EXTENSIVE",
            "celebrity_mentions": ["Larry Fink", "Michael Saylor"],
            "price_impact": "+8.9%",
            "market_cap_change": "+$12.3B"
        }
    ]

    # 根据用户订阅级别过滤热点
    if USER_MANAGEMENT_ENABLED and current_user:
        allowed_content_types = FeatureChecker.get_content_type_access(current_user)

        # 映射内容类型
        category_mapping = {
            "politics": "politics",
            "finance": "technology",  # 金融归类为科技
            "technology": "technology",
            "subculture": "subculture",
            "metaverse": "entertainment",
            "cryptocurrency": "technology"
        }

        # 过滤热点
        filtered_hotspots = []
        for hotspot in hotspots:
            mapped_category = category_mapping.get(hotspot["category"], hotspot["category"])
            if mapped_category in allowed_content_types:
                filtered_hotspots.append(hotspot)

        hotspots = filtered_hotspots

        # 限制热点数量（免费用户最多3个）
        limits = FeatureChecker.get_user_limits(current_user)
        daily_limit = limits.get("daily_hotspots", 3)
        if daily_limit != -1:
            hotspots = hotspots[:daily_limit]

    return {
        "status": "success",
        "hotspots": hotspots,
        "total_count": len(hotspots),
        "last_updated": datetime.now().isoformat(),
        "trending_categories": ["politics", "finance", "technology", "cryptocurrency"],
        "total_volume": sum(h["volume"] for h in hotspots),
        "average_sentiment": round(sum(h["sentiment"] for h in hotspots) / len(hotspots), 2),
        "distribution": {
            "politics": 2,
            "finance": 2,
            "technology": 1,
            "subculture": 3,
            "metaverse": 1,
            "cryptocurrency": 1
        },
        "summary": {
            "high_score_count": len([h for h in hotspots if h["score"] > 85]),
            "medium_score_count": len([h for h in hotspots if 70 <= h["score"] <= 85]),
            "low_score_count": len([h for h in hotspots if h["score"] < 70]),
            "positive_sentiment_ratio": len([h for h in hotspots if h["sentiment"] > 0.8]) / len(hotspots),
            "total_market_impact": sum(float(h["market_cap_change"].replace("+$", "").replace("B", "000000000").replace("M", "000000")) for h in hotspots)
        }
    }

@app.post("/hotspot/settings")
async def update_hotspot_settings(settings: dict):
    """更新热点监控设置"""
    # 这里应该更新实际的设置
    return {"status": "success", "settings": settings}

@app.get("/hotspot/analytics")
async def get_advanced_analytics():
    """获取高级AI分析数据"""
    import random

    return {
        "mlMetrics": {
            "viralityModel": round(random.uniform(90, 98), 1),
            "resonanceModel": round(random.uniform(85, 95), 1),
            "celebrityModel": round(random.uniform(70, 85), 1),
            "culturalModel": round(random.uniform(88, 96), 1)
        },
        "sentimentIndex": {
            "overall": round(random.uniform(0.75, 0.90), 2),
            "politics": round(random.uniform(0.80, 0.90), 2),
            "subculture": round(random.uniform(0.85, 0.95), 2),
            "technology": round(random.uniform(0.70, 0.85), 2),
            "finance": round(random.uniform(0.75, 0.85), 2),
            "cryptocurrency": round(random.uniform(0.70, 0.80), 2),
            "metaverse": round(random.uniform(0.65, 0.75), 2)
        },
        "languageDistribution": {
            "en": random.randint(40, 50),
            "zh": random.randint(20, 30),
            "ko": random.randint(10, 15),
            "ja": random.randint(5, 12),
            "es": random.randint(3, 8),
            "ru": random.randint(2, 6)
        },
        "videoAnalysis": {
            "processedFrames": random.randint(1200, 1500),
            "recognitionAccuracy": round(random.uniform(92, 97), 1),
            "keywordsExtracted": random.randint(800, 1000),
            "visualMemes": random.randint(20, 30)
        },
        "predictionAccuracy": round(random.uniform(65, 75), 1),
        "darkwebSignals": [
            {
                "title": "Underground Meme Emergence",
                "confidence": 0.78,
                "timestamp": datetime.now().isoformat(),
                "source": "tor_forum",
                "category": "subculture",
                "risk_level": "medium"
            },
            {
                "title": "Crypto Regulation Discussion",
                "confidence": 0.65,
                "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                "source": "dark_forum",
                "category": "finance",
                "risk_level": "high"
            },
            {
                "title": "AI Token Insider Info",
                "confidence": 0.82,
                "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
                "source": "private_channel",
                "category": "technology",
                "risk_level": "low"
            }
        ],
        "regulatoryAlerts": [
            {
                "region": "US",
                "type": "SEC_GUIDANCE",
                "content": "New crypto token classification guidelines",
                "severity": "medium",
                "timestamp": datetime.now().isoformat(),
                "impact": "moderate",
                "action_required": False
            },
            {
                "region": "EU",
                "type": "MiCA_REGULATION",
                "content": "Markets in Crypto-Assets regulation updates",
                "severity": "high",
                "timestamp": (datetime.now() - timedelta(hours=3)).isoformat(),
                "impact": "significant",
                "action_required": True
            },
            {
                "region": "ASIA",
                "type": "LICENSING",
                "content": "New crypto exchange licensing requirements",
                "severity": "low",
                "timestamp": (datetime.now() - timedelta(days=1)).isoformat(),
                "impact": "minimal",
                "action_required": False
            }
        ],
        "volumeAnalysis": {
            "total_mentions": random.randint(2500000, 3000000),
            "hourly_growth": round(random.uniform(0.15, 0.35), 2),
            "daily_growth": round(random.uniform(1.2, 1.8), 2),
            "weekly_growth": round(random.uniform(0.8, 1.2), 2),
            "platform_distribution": {
                "twitter": round(random.uniform(0.40, 0.50), 2),
                "reddit": round(random.uniform(0.20, 0.30), 2),
                "telegram": round(random.uniform(0.10, 0.20), 2),
                "discord": round(random.uniform(0.08, 0.15), 2),
                "tiktok": round(random.uniform(0.05, 0.12), 2),
                "youtube": round(random.uniform(0.03, 0.08), 2)
            },
            "peak_hours": [14, 15, 16, 20, 21, 22],
            "trending_keywords": [
                {"keyword": "Trump 2024", "volume": 125000, "growth": 0.45},
                {"keyword": "AI Revolution", "volume": 89000, "growth": 0.67},
                {"keyword": "XRP ETF", "volume": 78000, "growth": 0.58},
                {"keyword": "Pepe Comeback", "volume": 52000, "growth": 0.34},
                {"keyword": "Bitcoin ETF", "volume": 89000, "growth": 0.61}
            ]
        },
        "influencerAnalysis": {
            "top_influencers": [
                {
                    "name": "Elon Musk",
                    "platform": "Twitter",
                    "followers": 150000000,
                    "engagement_rate": 0.089,
                    "sentiment_impact": 0.92,
                    "recent_mentions": 15,
                    "influence_score": 98.5
                },
                {
                    "name": "Vitalik Buterin",
                    "platform": "Twitter",
                    "followers": 5200000,
                    "engagement_rate": 0.076,
                    "sentiment_impact": 0.85,
                    "recent_mentions": 8,
                    "influence_score": 87.2
                },
                {
                    "name": "CZ Binance",
                    "platform": "Twitter",
                    "followers": 8900000,
                    "engagement_rate": 0.068,
                    "sentiment_impact": 0.79,
                    "recent_mentions": 12,
                    "influence_score": 82.7
                }
            ],
            "total_reach": random.randint(40000000, 50000000),
            "active_influencers": random.randint(1200, 1500),
            "average_engagement": round(random.uniform(0.65, 0.80), 2)
        },
        "geographicAnalysis": {
            "top_regions": [
                {"region": "North America", "volume": 1250000, "sentiment": 0.82, "growth": 0.23},
                {"region": "Europe", "volume": 890000, "sentiment": 0.75, "growth": 0.18},
                {"region": "Asia", "volume": 567000, "sentiment": 0.88, "growth": 0.31},
                {"region": "South America", "volume": 140000, "sentiment": 0.71, "growth": 0.15}
            ],
            "country_trends": {
                "US": {"volume": 1100000, "top_topic": "Trump 2024", "sentiment": 0.81},
                "UK": {"volume": 320000, "top_topic": "AI Tokens", "sentiment": 0.74},
                "Germany": {"volume": 280000, "top_topic": "XRP ETF", "sentiment": 0.76},
                "Japan": {"volume": 245000, "top_topic": "AI Anime", "sentiment": 0.89},
                "South Korea": {"volume": 189000, "top_topic": "K-pop NFT", "sentiment": 0.91}
            }
        }
    }

@app.get("/hotspot/global")
async def get_global_monitoring():
    """获取全球监控数据"""
    import random

    return {
        "global_trends": [
            {
                "region": "North America",
                "trending_topics": [
                    {"topic": "Trump 2024", "volume": 1250000, "sentiment": 0.82, "growth": 0.45},
                    {"topic": "AI Revolution", "volume": 890000, "sentiment": 0.85, "growth": 0.67},
                    {"topic": "Bitcoin ETF", "volume": 567000, "sentiment": 0.79, "growth": 0.52}
                ],
                "market_sentiment": 0.78,
                "total_volume": 2707000,
                "active_users": 15600000,
                "peak_hours": [14, 15, 16, 20, 21]
            },
            {
                "region": "Europe",
                "trending_topics": [
                    {"topic": "XRP ETF", "volume": 780000, "sentiment": 0.84, "growth": 0.58},
                    {"topic": "EU Regulation", "volume": 450000, "sentiment": 0.65, "growth": 0.23},
                    {"topic": "Green Crypto", "volume": 320000, "sentiment": 0.88, "growth": 0.41}
                ],
                "market_sentiment": 0.72,
                "total_volume": 1550000,
                "active_users": 8900000,
                "peak_hours": [9, 10, 11, 17, 18]
            },
            {
                "region": "Asia",
                "trending_topics": [
                    {"topic": "K-pop NFT", "volume": 670000, "sentiment": 0.91, "growth": 0.52},
                    {"topic": "AI Anime", "volume": 450000, "sentiment": 0.85, "growth": 0.38},
                    {"topic": "Gaming Tokens", "volume": 380000, "sentiment": 0.79, "growth": 0.29}
                ],
                "market_sentiment": 0.85,
                "total_volume": 1500000,
                "active_users": 12300000,
                "peak_hours": [2, 3, 4, 12, 13]
            }
        ],
        "cross_platform_analysis": {
            "twitter": {"volume": 45.2, "sentiment": 0.78, "engagement": 0.82},
            "reddit": {"volume": 25.8, "sentiment": 0.74, "engagement": 0.76},
            "telegram": {"volume": 15.3, "sentiment": 0.85, "engagement": 0.89},
            "discord": {"volume": 8.7, "sentiment": 0.81, "engagement": 0.84},
            "tiktok": {"volume": 5.0, "sentiment": 0.88, "engagement": 0.92}
        },
        "language_insights": {
            "english": {"volume": 48.5, "sentiment": 0.79, "top_topic": "Trump 2024"},
            "chinese": {"volume": 22.3, "sentiment": 0.82, "top_topic": "AI Revolution"},
            "korean": {"volume": 12.1, "sentiment": 0.91, "top_topic": "K-pop NFT"},
            "japanese": {"volume": 8.9, "sentiment": 0.85, "top_topic": "AI Anime"},
            "spanish": {"volume": 5.2, "sentiment": 0.76, "top_topic": "Bitcoin ETF"},
            "russian": {"volume": 3.0, "sentiment": 0.68, "top_topic": "Crypto Mining"}
        },
        "time_zone_activity": {
            "UTC-8": {"peak_volume": 1250000, "peak_time": "20:00", "sentiment": 0.81},
            "UTC+0": {"peak_volume": 890000, "peak_time": "17:00", "sentiment": 0.75},
            "UTC+8": {"peak_volume": 670000, "peak_time": "13:00", "sentiment": 0.88},
            "UTC+9": {"peak_volume": 450000, "peak_time": "12:00", "sentiment": 0.85}
        }
    }

@app.get("/hotspot/darkweb")
async def get_darkweb_signals():
    """获取暗网信号数据"""
    import random

    return {
        "monitoring_status": "active",
        "total_sources": 127,
        "active_sources": 89,
        "signal_strength": round(random.uniform(0.65, 0.85), 2),
        "last_scan": datetime.now().isoformat(),
        "signals": [
            {
                "id": "dw_001",
                "title": "Underground Meme Emergence",
                "description": "New meme format gaining traction in underground forums",
                "confidence": 0.78,
                "risk_level": "medium",
                "category": "subculture",
                "source": "tor_forum_alpha",
                "timestamp": datetime.now().isoformat(),
                "keywords": ["pepe", "underground", "viral", "meme"],
                "estimated_surface_time": "6-12 hours",
                "potential_impact": "high"
            },
            {
                "id": "dw_002",
                "title": "Crypto Regulation Insider Info",
                "description": "Leaked information about upcoming SEC decisions",
                "confidence": 0.65,
                "risk_level": "high",
                "category": "finance",
                "source": "private_channel_beta",
                "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                "keywords": ["SEC", "regulation", "ETF", "approval"],
                "estimated_surface_time": "24-48 hours",
                "potential_impact": "very_high"
            },
            {
                "id": "dw_003",
                "title": "AI Token Insider Trading",
                "description": "Coordinated buying activity detected for AI tokens",
                "confidence": 0.82,
                "risk_level": "low",
                "category": "technology",
                "source": "encrypted_chat_gamma",
                "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
                "keywords": ["AI", "insider", "pump", "coordination"],
                "estimated_surface_time": "2-4 hours",
                "potential_impact": "medium"
            },
            {
                "id": "dw_004",
                "title": "Political Token Manipulation",
                "description": "Evidence of coordinated political token manipulation",
                "confidence": 0.71,
                "risk_level": "high",
                "category": "politics",
                "source": "dark_forum_delta",
                "timestamp": (datetime.now() - timedelta(hours=4)).isoformat(),
                "keywords": ["trump", "election", "manipulation", "bots"],
                "estimated_surface_time": "12-24 hours",
                "potential_impact": "high"
            }
        ],
        "threat_analysis": {
            "market_manipulation": {"level": "medium", "sources": 15, "confidence": 0.73},
            "regulatory_risks": {"level": "high", "sources": 8, "confidence": 0.81},
            "insider_trading": {"level": "low", "sources": 23, "confidence": 0.67},
            "coordinated_attacks": {"level": "medium", "sources": 12, "confidence": 0.75}
        },
        "source_reliability": {
            "verified_sources": 34,
            "unverified_sources": 55,
            "blacklisted_sources": 12,
            "average_accuracy": 0.74
        }
    }

@app.post("/hotspot/ml-settings")
async def update_ml_settings(settings: dict):
    """更新ML模型设置"""
    return {"status": "success", "settings": settings}

@app.post("/hotspot/darkweb/toggle")
async def toggle_darkweb_monitoring():
    """切换暗网监控状态"""
    import random
    enabled = random.choice([True, False])
    return {"status": "success", "enabled": enabled}

@app.post("/hotspot/languages")
async def update_language_settings(languageSettings: dict):
    """更新语言设置"""
    return {"status": "success", "languages": languageSettings}

# 策略管理 API
@app.get("/strategy/repository")
async def get_strategy_repository():
    """获取策略仓库 - 未部署策略列表"""
    import random
    from datetime import datetime, timedelta

    strategies = [
        {
            "id": "str_001",
            "name": "Trump 2024 Victory Token",
            "symbol": "TRUMP24",
            "status": "draft",
            "category": "politics",
            "created": (datetime.now() - timedelta(hours=2)).isoformat(),
            "hotspot_source": "Trump 2024 Campaign Launch",
            "score": 92.5,
            "estimated_roi": 245.8,
            "viral_potential": 0.92,
            "sentiment": 0.72,
            "tokenomics": {
                "total_supply": 1000000000,
                "allocation": {"airdrop": 50, "liquidity": 30, "marketing": 15, "team": 5},
                "burn_rate": 0.02
            },
            "deployment_config": {
                "platforms": ["pump.fun", "raydium"],
                "chains": ["solana"],
                "gas_optimized": True
            },
            "risk_level": "medium",
            "compliance_status": "passed"
        },
        {
            "id": "str_002",
            "name": "XRP ETF Celebration",
            "symbol": "XRPETF",
            "status": "pending",
            "category": "finance",
            "created": (datetime.now() - timedelta(hours=3)).isoformat(),
            "hotspot_source": "XRP ETF Approval Speculation",
            "score": 89.3,
            "estimated_roi": 189.4,
            "viral_potential": 0.88,
            "sentiment": 0.84,
            "tokenomics": {
                "total_supply": 500000000,
                "allocation": {"airdrop": 40, "liquidity": 35, "marketing": 20, "team": 5},
                "burn_rate": 0.015
            },
            "deployment_config": {
                "platforms": ["uniswap", "raydium"],
                "chains": ["ethereum", "solana"],
                "gas_optimized": True
            },
            "risk_level": "low",
            "compliance_status": "passed"
        },
        {
            "id": "str_003",
            "name": "AI Musk Revolution",
            "symbol": "AIMUSK",
            "status": "deploying",
            "category": "technology",
            "created": (datetime.now() - timedelta(hours=4)).isoformat(),
            "hotspot_source": "Musk AI Commentary Storm",
            "score": 86.7,
            "estimated_roi": 167.3,
            "viral_potential": 0.91,
            "sentiment": 0.76,
            "tokenomics": {
                "total_supply": **********,
                "allocation": {"airdrop": 45, "liquidity": 30, "marketing": 20, "team": 5},
                "burn_rate": 0.025
            },
            "deployment_config": {
                "platforms": ["pump.fun"],
                "chains": ["solana"],
                "gas_optimized": True
            },
            "risk_level": "medium",
            "compliance_status": "passed",
            "deployment_progress": random.randint(60, 90)
        },
        {
            "id": "str_004",
            "name": "Pepe Renaissance",
            "symbol": "PEPE2",
            "status": "draft",
            "category": "subculture",
            "created": (datetime.now() - timedelta(hours=6)).isoformat(),
            "hotspot_source": "Pepe Community Revival",
            "score": 78.9,
            "estimated_roi": 134.7,
            "viral_potential": 0.85,
            "sentiment": 0.91,
            "tokenomics": {
                "total_supply": ************,
                "allocation": {"airdrop": 60, "liquidity": 25, "marketing": 10, "team": 5},
                "burn_rate": 0.01
            },
            "deployment_config": {
                "platforms": ["pump.fun", "raydium"],
                "chains": ["solana"],
                "gas_optimized": True
            },
            "risk_level": "low",
            "compliance_status": "passed"
        },
        {
            "id": "str_005",
            "name": "K-Pop NFT Mania",
            "symbol": "KPOP",
            "status": "pending",
            "category": "subculture",
            "created": (datetime.now() - timedelta(hours=8)).isoformat(),
            "hotspot_source": "K-pop NFT Collection Drop",
            "score": 82.1,
            "estimated_roi": 156.2,
            "viral_potential": 0.87,
            "sentiment": 0.93,
            "tokenomics": {
                "total_supply": 777777777,
                "allocation": {"airdrop": 55, "liquidity": 25, "marketing": 15, "team": 5},
                "burn_rate": 0.02
            },
            "deployment_config": {
                "platforms": ["uniswap"],
                "chains": ["ethereum"],
                "gas_optimized": True
            },
            "risk_level": "medium",
            "compliance_status": "passed"
        }
    ]

    return {
        "status": "success",
        "strategies": strategies,
        "total_count": len(strategies),
        "summary": {
            "draft_count": len([s for s in strategies if s["status"] == "draft"]),
            "pending_count": len([s for s in strategies if s["status"] == "pending"]),
            "deploying_count": len([s for s in strategies if s["status"] == "deploying"]),
            "avg_score": round(sum(s["score"] for s in strategies) / len(strategies), 1),
            "high_potential_count": len([s for s in strategies if s["viral_potential"] > 0.85])
        }
    }

@app.get("/strategy/deployed")
async def get_deployed_strategies():
    """获取已部署策略监控数据"""
    import random
    from datetime import datetime, timedelta

    deployed_tokens = [
        {
            "id": "str_006",
            "name": "Bitcoin ETF Surge",
            "symbol": "BTCETF",
            "status": "active",
            "category": "cryptocurrency",
            "created": (datetime.now() - timedelta(days=2)).isoformat(),
            "deployed": (datetime.now() - timedelta(hours=18)).isoformat(),
            "hotspot_source": "Bitcoin ETF Inflow Record",
            "score": 88.4,
            "estimated_roi": 178.9,
            "viral_potential": 0.90,
            "sentiment": 0.89,
            "contract_address": "******************************************",
            "current_price": 0.0023,
            "market_cap": 2300000,
            "holders": 3456,
            "liquidity": 145000,
            "volume_24h": 567000,
            "price_change_24h": 23.4
        },
        {
            "id": "str_007",
            "name": "Metaverse Concert",
            "symbol": "METACON",
            "status": "active",
            "category": "metaverse",
            "created": (datetime.now() - timedelta(days=3)).isoformat(),
            "deployed": (datetime.now() - timedelta(hours=36)).isoformat(),
            "hotspot_source": "Sandbox Virtual Concert",
            "score": 75.2,
            "estimated_roi": 145.3,
            "viral_potential": 0.79,
            "sentiment": 0.87,
            "contract_address": "So11111111111111111111111111111111111111112",
            "current_price": 0.00089,
            "market_cap": 890000,
            "holders": 1892,
            "liquidity": 67000,
            "volume_24h": 234000,
            "price_change_24h": 18.7
        }
    ]

    return {
        "status": "success",
        "deployed_tokens": deployed_tokens,
        "total_count": len(deployed_tokens),
        "summary": {
            "total_market_cap": sum(token["market_cap"] for token in deployed_tokens),
            "total_holders": sum(token["holders"] for token in deployed_tokens),
            "total_liquidity": sum(token["liquidity"] for token in deployed_tokens),
            "avg_performance": round(sum(token["price_change_24h"] for token in deployed_tokens) / len(deployed_tokens), 1)
        }
    }

@app.get("/strategy/list")
async def get_strategies():
    """获取策略列表 - 兼容旧接口"""
    return [
        {
            "id": 1,
            "name": "TrumpCoin",
            "adaptation": 0.92,
            "sentiment": 0.85,
            "status": "pending",
            "category": "politics"
        }
    ]

@app.post("/strategy/create")
async def create_strategy(
    strategy_data: dict,
    current_user: User = Depends(get_current_active_user) if USER_MANAGEMENT_ENABLED else None
):
    """创建新策略"""
    if USER_MANAGEMENT_ENABLED and current_user:
        try:
            # 检查策略生成权限和限制
            from src.subscription.usage_tracker import UsageTracker
            from src.models.usage import UsageType

            usage_tracker = UsageTracker()

            # 跟踪策略生成使用
            usage_record = await usage_tracker.track_strategy_generation(
                user=current_user,
                strategy_data=strategy_data
            )

            strategy_id = f"strategy_{current_user.id}_{int(time.time())}"

            return {
                "status": "success",
                "strategy_id": strategy_id,
                "data": strategy_data,
                "usage_record_id": usage_record.id,
                "user_tier": current_user.role
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建策略失败: {str(e)}"
            )
    else:
        # 未启用用户管理时的原始逻辑
        return {"status": "success", "strategy_id": 123, "data": strategy_data}

@app.post("/strategy/deploy")
async def deploy_strategy(deployment_data: dict):
    """部署单个策略"""
    import random
    import asyncio

    strategy_id = deployment_data.get("strategy_id")

    # 模拟部署过程
    await asyncio.sleep(1)  # 模拟部署延迟

    # 生成模拟合约地址
    if deployment_data.get("chain") == "solana":
        contract_address = f"{''.join(random.choices('**********************************************************', k=44))}"
    else:
        contract_address = f"0x{''.join(random.choices('0123456789abcdef', k=40))}"

    return {
        "status": "success",
        "strategy_id": strategy_id,
        "contract_address": contract_address,
        "deployment_time": random.randint(8, 15),
        "gas_used": random.randint(2000000, 3500000),
        "transaction_hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
        "estimated_completion": "2-5 minutes"
    }

@app.post("/strategy/batch-deploy")
async def batch_deploy_strategies(deployment_data: dict):
    """批量部署策略"""
    import random
    import asyncio

    strategy_ids = deployment_data.get("strategy_ids", [])

    deployed_strategies = []

    for strategy_id in strategy_ids:
        # 模拟每个策略的部署
        await asyncio.sleep(0.5)  # 模拟部署延迟

        # 生成模拟合约地址
        contract_address = f"0x{''.join(random.choices('0123456789abcdef', k=40))}"

        deployed_strategies.append({
            "strategy_id": strategy_id,
            "contract_address": contract_address,
            "deployment_time": random.randint(8, 15),
            "gas_used": random.randint(2000000, 3500000),
            "status": "success"
        })

    return {
        "status": "success",
        "deployed_count": len(deployed_strategies),
        "deployed_strategies": deployed_strategies,
        "total_deployment_time": sum(s["deployment_time"] for s in deployed_strategies),
        "total_gas_used": sum(s["gas_used"] for s in deployed_strategies)
    }

@app.get("/strategy/{strategy_id}")
async def get_strategy_details(strategy_id: str):
    """获取策略详情"""
    import random
    from datetime import datetime, timedelta

    # 模拟策略详情数据
    strategy_detail = {
        "id": strategy_id,
        "name": "Trump 2024 Victory Token",
        "symbol": "TRUMP24",
        "status": "draft",
        "category": "politics",
        "created": (datetime.now() - timedelta(hours=2)).isoformat(),
        "hotspot_source": "Trump 2024 Campaign Launch",
        "score": 92.5,
        "estimated_roi": 245.8,
        "viral_potential": 0.92,
        "sentiment": 0.72,
        "tokenomics": {
            "total_supply": 1000000000,
            "allocation": {"airdrop": 50, "liquidity": 30, "marketing": 15, "team": 5},
            "burn_rate": 0.02,
            "vesting_schedule": {
                "team": "12 months linear",
                "marketing": "6 months linear",
                "liquidity": "immediate"
            }
        },
        "deployment_config": {
            "platforms": ["pump.fun", "raydium"],
            "chains": ["solana"],
            "gas_optimized": True,
            "security_features": ["reentrancy_guard", "pausable", "access_control"]
        },
        "risk_level": "medium",
        "compliance_status": "passed",
        "audit_results": {
            "slither_score": 96,
            "mythril_score": 94,
            "certik_score": 97,
            "overall_score": 95.7
        },
        "market_analysis": {
            "target_audience": "Political meme enthusiasts",
            "competition_level": "medium",
            "timing_score": 0.89,
            "social_momentum": 0.92
        }
    }

    return {
        "status": "success",
        "strategy": strategy_detail
    }

@app.post("/strategy/{strategy_id}/monitor")
async def add_strategy_monitoring(strategy_id: str, monitoring_config: dict):
    """为策略添加监控"""
    return {
        "status": "success",
        "strategy_id": strategy_id,
        "monitoring_enabled": True,
        "alerts_configured": monitoring_config.get("alerts", []),
        "refresh_interval": monitoring_config.get("refresh_interval", 30)
    }

@app.post("/strategy/{strategy_id}/pause")
async def pause_strategy(strategy_id: str):
    """暂停策略"""
    return {
        "status": "success",
        "strategy_id": strategy_id,
        "new_status": "paused",
        "paused_at": datetime.now().isoformat()
    }

@app.post("/strategy/{strategy_id}/resume")
async def resume_strategy(strategy_id: str):
    """恢复策略"""
    return {
        "status": "success",
        "strategy_id": strategy_id,
        "new_status": "active",
        "resumed_at": datetime.now().isoformat()
    }

@app.delete("/strategy/{strategy_id}")
async def delete_strategy(strategy_id: str):
    """删除策略"""
    return {
        "status": "success",
        "strategy_id": strategy_id,
        "deleted_at": datetime.now().isoformat()
    }

@app.post("/strategy/deploy-token")
async def deploy_token(
    token_data: dict,
    current_user: User = Depends(get_current_active_user) if USER_MANAGEMENT_ENABLED else None
):
    """安全发币部署"""
    import random
    import asyncio

    if USER_MANAGEMENT_ENABLED and current_user:
        try:
            # 检查一键部署权限
            from src.subscription.usage_tracker import UsageTracker
            from src.models.payment import PaymentType

            usage_tracker = UsageTracker()

            # 检查是否需要付费
            payment_check = await usage_tracker.track_paid_feature(
                user=current_user,
                feature_type=PaymentType.ONE_TIME_DEPLOYMENT,
                description="代币部署",
                metadata={"token_data": token_data}
            )

            if payment_check.get("requires_payment", False):
                return {
                    "status": "payment_required",
                    "message": "一键部署功能需要付费",
                    "cost_sol": payment_check["cost_sol"],
                    "description": payment_check["description"],
                    "payment_type": "one_time_deployment"
                }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"部署权限检查失败: {str(e)}"
            )

    # 模拟部署过程
    chains = token_data.get('chains', ['ethereum'])

    deployed_contracts = []
    for chain in chains:
        # 模拟合约地址生成
        if chain == 'ethereum':
            address = f"0x{random.randint(10**39, 10**40-1):040x}"
        elif chain == 'solana':
            address = f"{random.randint(10**43, 10**44-1):044x}"
        else:
            address = f"0x{random.randint(10**39, 10**40-1):040x}"

        deployed_contracts.append({
            "chain": chain,
            "address": address,
            "deployTime": random.randint(8, 15),
            "gasUsed": random.randint(2000000, 3500000),
            "status": "success"
        })

    return {
        "status": "success",
        "deployedContracts": deployed_contracts,
        "totalTime": sum(c["deployTime"] for c in deployed_contracts),
        "totalGasSaved": 42.1,
        "auditPassed": True
    }

@app.post("/strategy/security-audit")
async def run_security_audit(token_data: dict):
    """运行安全审计"""
    import random

    return {
        "status": "completed",
        "certikScore": round(random.uniform(95, 99), 1),
        "vulnerabilities": 0,
        "codeCoverage": round(random.uniform(92, 96), 1),
        "auditScore": round(random.uniform(96, 99), 1),
        "tools": {
            "slither": {"status": "completed", "issues": 0},
            "mythril": {"status": "completed", "issues": 0},
            "echidna": {"status": "completed", "issues": 0}
        },
        "recommendations": [
            "合约符合ERC-20标准",
            "无已知安全漏洞",
            "建议部署到主网"
        ]
    }

@app.post("/strategy/optimize-gas")
async def optimize_gas(token_data: dict):
    """Gas优化"""
    import random

    current_gas = random.randint(40, 60)
    optimized_gas = int(current_gas * 0.58)  # 42% savings

    return {
        "status": "optimized",
        "currentGas": current_gas,
        "optimizedGas": optimized_gas,
        "savings": round((current_gas - optimized_gas) / current_gas * 100, 1),
        "bestDeployTime": "02:00-06:00 UTC",
        "estimatedCost": {
            "before": f"${current_gas * 0.02:.2f}",
            "after": f"${optimized_gas * 0.02:.2f}"
        }
    }

@app.post("/strategy/generate-contract")
async def generate_contract(token_data: dict):
    """生成智能合约"""
    template = token_data.get('template', 'ERC-20')
    features = token_data.get('features', {})

    # 模拟合约代码生成
    contract_code = f"""
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

contract {token_data.get('name', 'MyToken')} is ERC20, Pausable, AccessControl {{
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");

    constructor() ERC20("{token_data.get('name', 'MyToken')}", "{token_data.get('symbol', 'MTK')}") {{
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(PAUSER_ROLE, msg.sender);
        _mint(msg.sender, {token_data.get('totalSupply', 1000000)} * 10 ** decimals());
    }}

    function pause() public onlyRole(PAUSER_ROLE) {{
        _pause();
    }}

    function unpause() public onlyRole(PAUSER_ROLE) {{
        _unpause();
    }}
}}
"""

    return {
        "status": "generated",
        "template": template,
        "features": features,
        "code": contract_code.strip(),
        "size": len(contract_code),
        "gasEstimate": random.randint(2000000, 3000000)
    }

# 钱包管理 API
@app.get("/wallet/list")
async def get_wallets():
    """获取钱包列表"""
    # 模拟钱包数据
    wallets = []
    for i in range(1, 21):  # 返回20个示例钱包
        wallet = {
            "id": i,
            "address": f"0x{''.join(random.choices('0123456789abcdef', k=8))}...{''.join(random.choices('0123456789abcdef', k=8))}",
            "balance": {
                "eth": round(random.uniform(0.05, 2.0), 3),
                "sol": round(random.uniform(0.5, 5.0), 2),
                "usdc": round(random.uniform(50, 500), 2)
            },
            "status": random.choice(["active", "low_balance", "inactive"]),
            "network": random.choice(["ethereum", "solana", "bsc", "polygon"]),
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 30))).isoformat(),
            "private_key": f"0x{''.join(random.choices('0123456789abcdef', k=64))}"
        }
        wallets.append(wallet)

    return wallets

@app.post("/wallet/create")
async def create_wallets(data: dict):
    """创建钱包"""
    count = data.get("count", 1)
    return {"status": "success", "created": count}

@app.post("/wallet/generate-batch")
async def generate_wallets_batch(config: dict):
    """批量生成钱包"""
    import asyncio

    count = config.get("count", 50)
    chains = config.get("chains", ["ethereum", "solana"])
    funding_config = config.get("fundingConfig", {"eth": 0.1, "sol": 1.0, "usdc": 100})
    random_variation = config.get("randomVariation", 10)

    # 模拟批量生成过程
    generated_wallets = []

    for i in range(count):
        # 为每个链生成钱包
        for chain in chains:
            # 计算随机资金分配
            variation_factor = 1 + (random.uniform(-random_variation, random_variation) / 100)

            wallet = {
                "id": len(generated_wallets) + 1,
                "address": generate_wallet_address(chain),
                "balance": {
                    "eth": round(funding_config.get("eth", 0.1) * variation_factor, 4) if chain == "ethereum" else 0,
                    "sol": round(funding_config.get("sol", 1.0) * variation_factor, 3) if chain == "solana" else 0,
                    "usdc": round(funding_config.get("usdc", 100) * variation_factor, 2)
                },
                "status": "active",
                "network": chain,
                "created_at": datetime.now().isoformat(),
                "private_key": generate_private_key(),
                "funding_variation": f"{(variation_factor - 1) * 100:+.1f}%"
            }
            generated_wallets.append(wallet)

            # 模拟生成延迟
            await asyncio.sleep(0.01)

    return generated_wallets

def generate_wallet_address(chain: str) -> str:
    """生成钱包地址"""
    if chain == "solana":
        # Solana地址格式
        return ''.join(random.choices('**********************************************************', k=44))
    else:
        # EVM地址格式
        return f"0x{''.join(random.choices('0123456789abcdef', k=40))}"

def generate_private_key() -> str:
    """生成私钥"""
    return f"0x{''.join(random.choices('0123456789abcdef', k=64))}"

@app.post("/wallet/distribute-funds")
async def distribute_random_funds(config: dict):
    """随机资金分配"""
    wallets = config.get("wallets", [])
    funding_config = config.get("fundingConfig", {"eth": 0.1, "sol": 1.0, "usdc": 100})
    random_variation = config.get("randomVariation", 10)

    # 为每个钱包重新分配随机资金
    for wallet in wallets:
        variation_factor = 1 + (random.uniform(-random_variation, random_variation) / 100)

        if wallet["network"] == "ethereum":
            wallet["balance"]["eth"] = round(funding_config.get("eth", 0.1) * variation_factor, 4)
        elif wallet["network"] == "solana":
            wallet["balance"]["sol"] = round(funding_config.get("sol", 1.0) * variation_factor, 3)

        wallet["balance"]["usdc"] = round(funding_config.get("usdc", 100) * variation_factor, 2)
        wallet["funding_variation"] = f"{(variation_factor - 1) * 100:+.1f}%"
        wallet["last_funding"] = datetime.now().isoformat()

    return wallets

@app.post("/wallet/rent-recovery/enable")
async def enable_rent_recovery():
    """启用Solana租金回收"""
    return {
        "status": "success",
        "message": "Solana rent recovery enabled",
        "threshold": 0.001,
        "auto_close_enabled": True,
        "recovered_accounts": random.randint(5, 15),
        "total_recovered": round(random.uniform(0.05, 0.2), 4)
    }

@app.post("/wallet/proxy/rotate")
async def rotate_proxy():
    """轮换代理IP"""
    new_ip = f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}"

    return {
        "status": "success",
        "old_ip": "*************",
        "new_ip": new_ip,
        "proxy_type": random.choice(["residential", "datacenter"]),
        "location": random.choice(["US", "UK", "DE", "JP", "SG"]),
        "rotation_time": datetime.now().isoformat()
    }

@app.get("/wallet/proxy/status")
async def get_proxy_status():
    """获取代理状态"""
    return {
        "residential": random.choice(["active", "rotating", "error"]),
        "datacenter": random.choice(["active", "rotating", "error"]),
        "currentIP": f"{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}.{random.randint(1, 255)}",
        "location": random.choice(["US-East", "EU-West", "Asia-Pacific"]),
        "uptime": f"{random.randint(1, 24)}h {random.randint(1, 59)}m",
        "last_rotation": (datetime.now() - timedelta(minutes=random.randint(5, 120))).isoformat()
    }

@app.get("/wallet/anti-ban/metrics")
async def get_anti_ban_metrics():
    """获取防封禁指标"""
    return {
        "evasionRate": round(random.uniform(88, 95), 1),
        "behaviorDisguise": True,
        "currentRate": round(random.uniform(2.0, 3.5), 1),
        "platformResponse": random.choice(["正常", "轻微限制", "监控中"]),
        "adaptiveStatus": random.choice(["监控中", "调整中", "优化中"]),
        "banned_wallets": random.randint(0, 3),
        "success_rate": round(random.uniform(92, 98), 1),
        "last_detection": (datetime.now() - timedelta(hours=random.randint(1, 48))).isoformat()
    }

@app.post("/wallet/anti-ban/settings")
async def update_anti_ban_settings(settings: dict):
    """更新防封禁设置"""
    return {
        "status": "success",
        "settings": settings,
        "applied_at": datetime.now().isoformat(),
        "estimated_effect": "Settings will take effect in 5-10 minutes"
    }

# 业务流程 API
@app.post("/business-flow/hotspot-alert")
async def trigger_hotspot_alert(hotspot_data: dict):
    """触发热点警报，启动业务流程"""
    flow_id = f"flow_{int(datetime.now().timestamp())}"

    return {
        "status": "success",
        "flow_id": flow_id,
        "hotspot_data": hotspot_data,
        "triggered_at": datetime.now().isoformat(),
        "next_step": "ai_strategy_generation"
    }

@app.post("/business-flow/generate-strategy")
async def generate_ai_strategy(hotspot_data: dict):
    """基于热点数据生成AI策略 - 集成参数优化"""
    import asyncio

    keyword = hotspot_data.get("keyword", "AI")
    category = hotspot_data.get("category", "technology")
    score = hotspot_data.get("score", 0.8)

    # 使用优化引擎分析热点参数
    hotspot_optimization = await optimization_engine.analyze_hotspot_parameters(hotspot_data)

    # 使用优化引擎生成策略参数
    strategy_optimization = await optimization_engine.optimize_strategy_parameters(hotspot_data)

    # 模拟AI策略生成过程
    await asyncio.sleep(1)  # 模拟AI处理时间

    # 根据热点数据和优化参数生成策略
    optimized_allocation = strategy_optimization.get("allocation", {
        "airdrop": 0.50, "liquidity": 0.30, "marketing": 0.10, "team": 0.10
    })

    optimized_supply = strategy_optimization.get("total_supply", random.randint(100000000, 1000000000))

    strategy = {
        "token_name": f"{keyword.upper()} Token",
        "token_symbol": f"{keyword[:4].upper()}",
        "total_supply": optimized_supply,
        "description": f"基于{keyword}热点的AI驱动Meme代币",
        "recommended_platform": "pump.fun" if score >= 0.8 else "raydium",
        "hotspot_type": category,
        "token_allocation": optimized_allocation,
        "marketing_strategy": {
            "initial_budget": strategy_optimization.get("hedging_params", {}).get("budget_allocation", round(random.uniform(3, 10), 1)),
            "target_audience": category,
            "viral_potential": score,
            "social_channels": ["twitter", "telegram", "discord"],
            "optimal_timing": strategy_optimization.get("optimal_timing", {"US": "20:00 ET"})
        },
        "liquidity_strategy": {
            "initial_liquidity": round(random.uniform(5, 20), 1),
            "liquidity_lock_period": "365 days",
            "auto_liquidity_management": True
        },
        "exit_strategy": {
            "profit_targets": [50, 100, 200, 500],  # 百分比
            "stop_loss": -20,
            "time_based_exit": "7 days",
            "volume_based_exit": True
        },
        "risk_assessment": {
            "regulatory_risk": "low" if category != "politics" else "medium",
            "market_risk": "medium",
            "technical_risk": "low",
            "overall_risk": "medium"
        }
    }

    return {
        "status": "success",
        "strategy": strategy,
        "confidence": round(random.uniform(0.7, 0.95), 2),
        "generated_at": datetime.now().isoformat(),
        "estimated_roi": f"{random.randint(50, 300)}%",
        "recommended_timeline": "24-48 hours"
    }

@app.post("/business-flow/deploy-meme")
async def deploy_meme_token(config: dict):
    """部署Meme代币"""
    import asyncio

    strategy = config.get("strategy", {})
    platform = config.get("platform", "pump.fun")

    # 模拟部署过程
    await asyncio.sleep(2)

    # 生成部署结果
    contract_address = f"{''.join(random.choices('**********************************************************', k=44))}"

    deployment_result = {
        "status": "success",
        "platform": platform,
        "contract_address": contract_address,
        "token_name": strategy.get("token_name", "AI Token"),
        "token_symbol": strategy.get("token_symbol", "AI"),
        "total_supply": strategy.get("total_supply", 1000000000),
        "deployment_time": datetime.now().isoformat(),
        "platform_url": f"https://{platform}.com/token/{contract_address}",
        "initial_price": round(random.uniform(0.0001, 0.001), 6),
        "initial_market_cap": round(random.uniform(10000, 100000), 2),
        "liquidity_pool_address": f"{''.join(random.choices('**********************************************************', k=44))}",
        "transaction_hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}"
    }

    return deployment_result

@app.post("/business-flow/init-liquidity")
async def initialize_liquidity_control(token_data: dict):
    """初始化流动性控制"""
    import asyncio

    await asyncio.sleep(1)

    return {
        "status": "success",
        "token_address": token_data.get("contract_address"),
        "liquidity_control": {
            "auto_management": True,
            "mev_protection": True,
            "slippage_control": "0.5%",
            "liquidity_depth": round(random.uniform(50000, 200000), 2),
            "daily_volume": round(random.uniform(10000, 50000), 2)
        },
        "monitoring": {
            "whale_detection": True,
            "price_impact_alerts": True,
            "volume_spike_detection": True
        },
        "initialized_at": datetime.now().isoformat()
    }

@app.post("/business-flow/create-exit-strategy")
async def create_exit_strategy(token_data: dict):
    """为特定代币创建退出策略"""
    import asyncio

    await asyncio.sleep(1)

    token_symbol = token_data.get("token_symbol", "MEME")

    exit_strategy = {
        "status": "success",
        "token_symbol": token_symbol,
        "strategy_id": f"exit_{int(datetime.now().timestamp())}",
        "xgboost_model": {
            "prediction_confidence": round(random.uniform(0.75, 0.95), 2),
            "feature_importance": {
                "rsi": 0.18,
                "whale_activity": 0.22,
                "sentiment": 0.15,
                "volume": 0.12,
                "macd": 0.10,
                "polymarket": 0.23
            },
            "model_accuracy": 0.847
        },
        "multi_indicator_analysis": {
            "technical_score": round(random.uniform(0.6, 0.9), 2),
            "onchain_score": round(random.uniform(0.7, 0.9), 2),
            "sentiment_score": round(random.uniform(0.5, 0.8), 2),
            "overall_score": round(random.uniform(0.6, 0.85), 2)
        },
        "exit_parameters": {
            "profit_targets": [25, 50, 100, 200],
            "stop_loss": -15,
            "trailing_stop": True,
            "volume_based_exit": True,
            "time_based_exit": "7 days"
        },
        "batch_selling": {
            "enabled": True,
            "batch_size": "10-15%",
            "interval": "dynamic",
            "volatility_adjustment": True
        },
        "hedging": {
            "options_enabled": True,
            "hedge_ratio": 0.3,
            "protection_level": 0.85
        },
        "created_at": datetime.now().isoformat()
    }

    return exit_strategy

@app.get("/business-flow/deployed-tokens")
async def get_deployed_tokens():
    """获取已部署代币列表"""
    # 这里应该从数据库获取，现在返回模拟数据
    tokens = [
        {
            "id": "token_1",
            "token_name": "AI Meme Token",
            "token_symbol": "AIMEME",
            "contract_address": "7xKs9YQMvLTBjAqK8tx4ePAqtVwqQxMvLTBjAqK8tx4e",
            "platform": "pump.fun",
            "deployed_at": "2024-01-15T10:30:00Z",
            "status": "active",
            "initial_price": 0.0001,
            "current_price": 0.00015,
            "market_cap": 150000,
            "liquidity_control_enabled": True,
            "exit_strategy_enabled": True
        },
        {
            "id": "token_2",
            "token_name": "Trump Victory Token",
            "token_symbol": "TRUMP24",
            "contract_address": "9zMs8YQMvLTBjAqK8tx4ePAqtVwqQxMvLTBjAqK8tx4e",
            "platform": "raydium",
            "deployed_at": "2024-01-14T15:45:00Z",
            "status": "active",
            "initial_price": 0.0005,
            "current_price": 0.0012,
            "market_cap": 1200000,
            "liquidity_control_enabled": True,
            "exit_strategy_enabled": False
        }
    ]

    return {
        "status": "success",
        "tokens": tokens,
        "total_count": len(tokens),
        "active_count": len([t for t in tokens if t["status"] == "active"])
    }

# 参数优化 API
@app.post("/optimization/analyze-hotspot")
async def analyze_hotspot_optimization(hotspot_data: dict):
    """分析热点参数优化"""
    optimization_result = await optimization_engine.analyze_hotspot_parameters(hotspot_data)

    return {
        "status": "success",
        "hotspot_data": hotspot_data,
        "optimization": optimization_result,
        "recommendations": {
            "virality_threshold": optimization_result.get("virality_threshold", 0.75),
            "response_urgency": optimization_result.get("response_urgency", "normal"),
            "cultural_adaptation": optimization_result.get("cultural_adaptation", {}),
            "source_weights": optimization_result.get("source_weights", {})
        },
        "analyzed_at": datetime.now().isoformat()
    }

@app.post("/optimization/strategy-parameters")
async def optimize_strategy_parameters(hotspot_data: dict):
    """优化策略参数"""
    strategy_optimization = await optimization_engine.optimize_strategy_parameters(hotspot_data)

    return {
        "status": "success",
        "optimized_parameters": strategy_optimization,
        "allocation_optimization": strategy_optimization.get("allocation", {}),
        "burn_rate": strategy_optimization.get("burn_rate", 0.05),
        "optimal_timing": strategy_optimization.get("optimal_timing", {}),
        "hedging_params": strategy_optimization.get("hedging_params", {}),
        "optimized_at": datetime.now().isoformat()
    }

@app.post("/optimization/exit-strategy")
async def create_optimized_exit_strategy(token_data: dict):
    """创建优化的退出策略"""
    token_type = token_data.get("token_type", "general")
    initial_performance = token_data.get("initial_performance", 1.0)

    # 使用优化配置创建个性化退出策略
    optimized_strategy = OptimizationConfig.customize_exit_strategy(token_type, initial_performance)

    # 计算初始流动性
    initial_liquidity = OptimizationConfig.calculate_initial_liquidity(token_data)

    return {
        "status": "success",
        "token_data": token_data,
        "optimized_exit_strategy": optimized_strategy,
        "initial_liquidity": initial_liquidity,
        "customizations": {
            "token_type_specific": True,
            "performance_adjusted": initial_performance != 1.0,
            "volatility_adaptive": True
        },
        "created_at": datetime.now().isoformat()
    }

@app.get("/optimization/market-sensitivity/{level}")
async def get_market_sensitivity_config(level: str):
    """获取市场灵敏度配置"""
    if level not in ['conservative', 'balanced', 'aggressive']:
        level = 'balanced'

    config = OptimizationConfig.get_market_sensitivity_config(level)

    return {
        "status": "success",
        "sensitivity_level": level,
        "configuration": config,
        "description": {
            "conservative": "保守策略 - 低风险，稳定收益",
            "balanced": "平衡策略 - 中等风险，均衡收益",
            "aggressive": "激进策略 - 高风险，高收益潜力"
        }.get(level, "平衡策略"),
        "retrieved_at": datetime.now().isoformat()
    }

@app.post("/optimization/validate-parameters")
async def validate_optimization_parameters(params: dict):
    """验证和修正参数"""
    validated_params = OptimizationConfig.validate_parameters(params)

    return {
        "status": "success",
        "original_params": params,
        "validated_params": validated_params,
        "adjustments_made": params != validated_params,
        "validated_at": datetime.now().isoformat()
    }

@app.post("/optimization/custom-parameters/save")
async def save_custom_parameters(params: dict):
    """保存自定义参数配置"""
    # 验证参数
    validated_params = OptimizationConfig.validate_parameters(params)

    # 这里应该保存到数据库，现在模拟保存
    config_id = f"config_{int(datetime.now().timestamp())}"

    return {
        "status": "success",
        "config_id": config_id,
        "validated_params": validated_params,
        "saved_at": datetime.now().isoformat(),
        "message": "自定义参数配置已保存"
    }

@app.get("/optimization/custom-parameters/load/{config_id}")
async def load_custom_parameters(config_id: str):
    """加载自定义参数配置"""
    # 这里应该从数据库加载，现在返回默认配置
    default_params = {
        "hotspot": {
            "min_virality": 0.85,
            "cultural_fit_threshold": 0.75,
            "response_timeout": 300,
            "min_volume_twitter": 1000,
            "min_volume_reddit": 500,
            "min_volume_tiktok": 100000
        },
        "strategy": {
            "supply_min": 1000000000,
            "supply_max": **********0,
            "airdrop_ratio": 0.50,
            "liquidity_ratio": 0.30,
            "marketing_ratio": 0.10,
            "team_ratio": 0.10,
            "burn_rate_base": 0.05
        },
        "liquidity": {
            "min_wallets": 20,
            "max_wallets": 100,
            "initial_funding": 0.5,
            "slippage_tolerance": 0.005,
            "whale_alert_threshold": 0.05,
            "tvl_ratio_min": 0.5
        },
        "exit": {
            "signal_threshold": 0.75,
            "rsi_weight": 0.25,
            "sentiment_weight": 0.20,
            "volume_weight": 0.15,
            "whale_weight": 0.10,
            "volatility_weight": 0.30,
            "stop_loss_default": 0.15,
            "batch_size_high_vol": 0.08,
            "batch_size_medium_vol": 0.10,
            "batch_size_low_vol": 0.12
        }
    }

    return {
        "status": "success",
        "config_id": config_id,
        "parameters": default_params,
        "loaded_at": datetime.now().isoformat()
    }

@app.get("/optimization/custom-parameters/presets")
async def get_parameter_presets():
    """获取参数预设配置"""
    presets = {
        "conservative": {
            "name": "保守配置",
            "description": "低风险，稳定收益的参数配置",
            "params": {
                "hotspot": {
                    "min_virality": 0.90,
                    "cultural_fit_threshold": 0.80,
                    "response_timeout": 600
                },
                "strategy": {
                    "airdrop_ratio": 0.40,
                    "liquidity_ratio": 0.40,
                    "marketing_ratio": 0.15,
                    "team_ratio": 0.05
                },
                "exit": {
                    "signal_threshold": 0.65,
                    "stop_loss_default": 0.10
                }
            }
        },
        "balanced": {
            "name": "平衡配置",
            "description": "风险收益平衡的参数配置",
            "params": {
                "hotspot": {
                    "min_virality": 0.75,
                    "cultural_fit_threshold": 0.70,
                    "response_timeout": 300
                },
                "strategy": {
                    "airdrop_ratio": 0.50,
                    "liquidity_ratio": 0.30,
                    "marketing_ratio": 0.10,
                    "team_ratio": 0.10
                },
                "exit": {
                    "signal_threshold": 0.75,
                    "stop_loss_default": 0.15
                }
            }
        },
        "aggressive": {
            "name": "激进配置",
            "description": "高风险高收益的参数配置",
            "params": {
                "hotspot": {
                    "min_virality": 0.60,
                    "cultural_fit_threshold": 0.60,
                    "response_timeout": 180
                },
                "strategy": {
                    "airdrop_ratio": 0.60,
                    "liquidity_ratio": 0.20,
                    "marketing_ratio": 0.15,
                    "team_ratio": 0.05
                },
                "exit": {
                    "signal_threshold": 0.85,
                    "stop_loss_default": 0.20
                }
            }
        }
    }

    return {
        "status": "success",
        "presets": presets,
        "retrieved_at": datetime.now().isoformat()
    }

# ==================== AI发币策略生成 ====================

@app.post("/ai/generate-token-strategy")
async def generate_token_strategy(hotspot_data: dict):
    """基于热点数据生成AI发币策略"""

    # 模拟AI分析过程
    category = hotspot_data.get('category', 'technology')
    keyword = hotspot_data.get('keyword', 'AI')
    score = hotspot_data.get('score', 0.8)
    sentiment = hotspot_data.get('sentiment', 0.75)

    # 基于分类和关键词生成策略
    strategies = {
        'politics': {
            'name_patterns': ['MAGA', 'TRUMP', 'VOTE', 'FREEDOM', 'LIBERTY'],
            'symbol_patterns': ['MAGA', 'TRUMP', 'VOTE', 'FREE', 'USA'],
            'themes': ['政治改革', '自由民主', '爱国主义', '选举热潮', '政治觉醒']
        },
        'technology': {
            'name_patterns': ['AI', 'ROBOT', 'TECH', 'FUTURE', 'QUANTUM'],
            'symbol_patterns': ['AI', 'BOT', 'TECH', 'QTM', 'ROBO'],
            'themes': ['人工智能革命', '科技创新', '未来科技', '智能时代', '数字化转型']
        },
        'subculture': {
            'name_patterns': ['MEME', 'PEPE', 'DOGE', 'SHIB', 'FLOKI'],
            'symbol_patterns': ['MEME', 'PEPE', 'DOGE', 'SHIB', 'MOON'],
            'themes': ['网络文化', '社区力量', '草根运动', '文化符号', '集体记忆']
        },
        'metaverse': {
            'name_patterns': ['META', 'VR', 'VIRTUAL', 'WORLD', 'SPACE'],
            'symbol_patterns': ['META', 'VR', 'VIRT', 'WRLD', 'SPACE'],
            'themes': ['虚拟世界', '元宇宙生态', '数字身份', '虚拟经济', '沉浸体验']
        },
        'gamefi': {
            'name_patterns': ['GAME', 'PLAY', 'WIN', 'EARN', 'QUEST'],
            'symbol_patterns': ['GAME', 'PLAY', 'WIN', 'EARN', 'QUEST'],
            'themes': ['游戏赚钱', 'P2E生态', '游戏经济', '虚拟资产', '娱乐金融']
        },
        'nft': {
            'name_patterns': ['ART', 'PIXEL', 'RARE', 'UNIQUE', 'COLLECT'],
            'symbol_patterns': ['ART', 'PIX', 'RARE', 'UNQ', 'COLL'],
            'themes': ['数字艺术', '收藏价值', '创作者经济', '稀缺性', '文化价值']
        },
        'sports': {
            'name_patterns': ['SPORT', 'WIN', 'CHAMPION', 'TEAM', 'VICTORY'],
            'symbol_patterns': ['SPORT', 'WIN', 'CHAMP', 'TEAM', 'VIC'],
            'themes': ['体育精神', '竞技文化', '团队合作', '胜利荣耀', '运动激情']
        }
    }

    # 获取对应分类的策略模板
    strategy_template = strategies.get(category, strategies['technology'])

    # 生成代币名称
    import random
    base_name = random.choice(strategy_template['name_patterns'])
    if keyword.upper() not in base_name:
        token_name = f"{keyword.upper()}{base_name}"
    else:
        token_name = base_name

    # 生成代币符号
    token_symbol = random.choice(strategy_template['symbol_patterns'])
    if len(token_symbol) < 4:
        token_symbol += str(random.randint(10, 99))

    # 生成描述
    theme = random.choice(strategy_template['themes'])
    description = f"基于'{keyword}'热点的{theme}主题代币，捕捉当前市场热度，具备强大的社区驱动力和文化价值。"

    # 计算推荐参数
    supply_base = 1000000000  # 10亿基础供应量
    if score > 0.9:
        supply_multiplier = 5.0  # 超高热度增加供应量
        initial_price = 0.001
    elif score > 0.8:
        supply_multiplier = 3.0
        initial_price = 0.0005
    elif score > 0.7:
        supply_multiplier = 2.0
        initial_price = 0.0001
    else:
        supply_multiplier = 1.0
        initial_price = 0.00005

    total_supply = int(supply_base * supply_multiplier)

    # 生成分配策略
    if sentiment > 0.8:  # 高情绪热度，增加营销预算
        allocation = {
            "airdrop": 0.45,
            "liquidity": 0.25,
            "marketing": 0.20,
            "team": 0.10
        }
    elif sentiment > 0.6:
        allocation = {
            "airdrop": 0.50,
            "liquidity": 0.30,
            "marketing": 0.15,
            "team": 0.05
        }
    else:
        allocation = {
            "airdrop": 0.40,
            "liquidity": 0.35,
            "marketing": 0.15,
            "team": 0.10
        }

    # 推荐平台
    if category in ['politics', 'subculture']:
        recommended_platform = 'pump.fun'
        platform_reason = '适合病毒式传播和社区驱动'
    elif category in ['technology', 'metaverse']:
        recommended_platform = 'raydium'
        platform_reason = '技术社区活跃，流动性充足'
    else:
        recommended_platform = 'pump.fun'
        platform_reason = '快速启动，社区参与度高'

    strategy = {
        "token_info": {
            "name": token_name,
            "symbol": token_symbol,
            "description": description,
            "total_supply": total_supply,
            "initial_price": initial_price,
            "decimals": 9
        },
        "allocation": allocation,
        "deployment": {
            "recommended_platform": recommended_platform,
            "platform_reason": platform_reason,
            "estimated_cost": {
                "solana": "0.5 SOL",
                "ethereum": "0.1 ETH"
            },
            "estimated_time": "2-5分钟"
        },
        "marketing_strategy": {
            "target_audience": f"{category}社区",
            "key_messaging": [
                f"捕捉{keyword}热点趋势",
                f"社区驱动的{theme}",
                "早期参与者福利",
                "透明的代币经济学"
            ],
            "launch_channels": [
                "Twitter/X 官方发布",
                "Telegram 社区群",
                "Discord 技术讨论",
                "Reddit 相关板块"
            ]
        },
        "risk_assessment": {
            "market_risk": "中等" if score > 0.8 else "较高",
            "technical_risk": "低",
            "regulatory_risk": "中等" if category == 'politics' else "低",
            "liquidity_risk": "低" if recommended_platform == 'raydium' else "中等"
        },
        "success_metrics": {
            "target_market_cap": f"${int(total_supply * initial_price * 1000):,}",
            "target_holders": f"{int(score * 10000):,}",
            "target_volume_24h": f"${int(score * 100000):,}",
            "success_probability": f"{int(score * 100)}%"
        },
        "ai_confidence": score,
        "generated_at": datetime.now().isoformat()
    }

    return {
        "status": "success",
        "hotspot_keyword": keyword,
        "hotspot_category": category,
        "hotspot_score": score,
        "strategy": strategy,
        "generation_time": "1.2s"
    }

@app.post("/ai/deploy-token-strategy")
async def deploy_token_strategy(strategy_data: dict):
    """一键部署AI生成的代币策略"""

    token_info = strategy_data.get('token_info', {})
    allocation = strategy_data.get('allocation', {})
    deployment = strategy_data.get('deployment', {})

    # 模拟部署过程
    deployment_steps = [
        {"step": "验证代币参数", "status": "completed", "time": "0.1s"},
        {"step": "生成智能合约", "status": "completed", "time": "0.3s"},
        {"step": "安全审计检查", "status": "completed", "time": "0.5s"},
        {"step": "部署到区块链", "status": "processing", "time": "1.2s"},
        {"step": "初始化流动性", "status": "pending", "time": "0.8s"},
        {"step": "启动营销活动", "status": "pending", "time": "0.2s"}
    ]

    # 生成部署结果
    import random

    # 模拟合约地址
    if deployment.get('recommended_platform') == 'pump.fun':
        contract_address = f"pump_{random.randint(100000, 999999)}"
        explorer_url = f"https://pump.fun/coin/{contract_address}"
    else:
        contract_address = f"0x{''.join([random.choice('0123456789abcdef') for _ in range(40)])}"
        explorer_url = f"https://solscan.io/token/{contract_address}"

    # 计算初始流动性
    total_supply = token_info.get('total_supply', 1000000000)
    initial_price = token_info.get('initial_price', 0.0001)
    liquidity_ratio = allocation.get('liquidity', 0.3)
    initial_liquidity_tokens = int(total_supply * liquidity_ratio)
    initial_liquidity_value = initial_liquidity_tokens * initial_price

    deployment_result = {
        "deployment_id": f"deploy_{int(datetime.now().timestamp())}",
        "contract_address": contract_address,
        "explorer_url": explorer_url,
        "token_info": {
            **token_info,
            "contract_verified": True,
            "audit_score": random.randint(85, 98),
            "security_features": [
                "Anti-MEV保护",
                "流动性锁定",
                "代币燃烧机制",
                "多签钱包控制"
            ]
        },
        "liquidity_info": {
            "initial_tokens": initial_liquidity_tokens,
            "initial_value_usd": f"${initial_liquidity_value:,.2f}",
            "pool_address": f"pool_{random.randint(100000, 999999)}",
            "lock_period": "30天",
            "lock_percentage": "80%"
        },
        "marketing_launch": {
            "twitter_post": f"🚀 ${token_info.get('symbol', 'TOKEN')} 正式发布！\n\n{token_info.get('description', '')}\n\n合约地址: {contract_address}\n\n#DeFi #Meme #Crypto",
            "telegram_message": f"🎉 {token_info.get('name', 'Token')} 发布成功！\n\n立即交易: {explorer_url}",
            "scheduled_posts": [
                {"platform": "Twitter", "time": "+5分钟", "type": "技术介绍"},
                {"platform": "Telegram", "time": "+10分钟", "type": "社区互动"},
                {"platform": "Discord", "time": "+15分钟", "type": "AMA预告"}
            ]
        },
        "performance_tracking": {
            "price_alerts": [
                {"type": "价格上涨", "threshold": "+50%", "enabled": True},
                {"type": "价格下跌", "threshold": "-30%", "enabled": True},
                {"type": "交易量激增", "threshold": "10x", "enabled": True}
            ],
            "analytics_dashboard": f"https://dextools.io/app/solana/pair-explorer/{contract_address}",
            "holder_tracking": True,
            "whale_monitoring": True
        },
        "next_steps": [
            {
                "action": "监控初始交易",
                "description": "观察前30分钟的交易活动和价格走势",
                "priority": "高",
                "estimated_time": "30分钟"
            },
            {
                "action": "社区建设",
                "description": "在Telegram和Discord建立官方群组",
                "priority": "中",
                "estimated_time": "1小时"
            },
            {
                "action": "KOL合作",
                "description": "联系相关领域的意见领袖进行推广",
                "priority": "中",
                "estimated_time": "2-4小时"
            },
            {
                "action": "流动性管理",
                "description": "根据交易情况调整流动性策略",
                "priority": "高",
                "estimated_time": "持续监控"
            }
        ],
        "deployment_steps": deployment_steps,
        "deployment_time": "2.1s",
        "gas_used": f"{random.randint(150000, 300000):,} gas",
        "deployment_cost": deployment.get('estimated_cost', {}).get('solana', '0.5 SOL'),
        "deployed_at": datetime.now().isoformat(),
        "status": "success"
    }

    return {
        "status": "success",
        "message": "代币部署成功！",
        "deployment": deployment_result,
        "estimated_roi": f"{random.randint(200, 1000)}%",
        "risk_level": "中等",
        "success_probability": f"{random.randint(70, 95)}%"
    }

# ==================== 热点预测与策略自动化系统 ====================

@app.get("/prediction/hotspots")
async def get_hotspot_predictions():
    """获取热点预测数据"""

    # 模拟预测数据 - 更丰富的数据集
    predictions = [
        {
            "id": "pred_001",
            "event_id": "event_001",
            "hotspot_prob": 0.94,
            "confidence": "HIGH",
            "predicted_time": (datetime.now() + timedelta(hours=2)).isoformat(),
            "factors": {
                "temporal_score": 0.89,
                "influence_score": 0.95,
                "sentiment_score": 0.87,
                "social_momentum": 0.92,
                "media_coverage": 0.88,
                "viral_potential": 0.91
            },
            "risk_assessment": {
                "market_volatility": "高",
                "regulatory_risk": "中等",
                "competition_risk": "低",
                "technical_risk": "低",
                "liquidity_risk": "中等"
            },
            "strategy_ready": True,
            "analysis_details": {
                "social_momentum": "急速上升",
                "media_attention": "极高",
                "influencer_activity": "活跃",
                "keyword_trending": ["Trump", "2024", "Election", "Crypto", "MAGA"],
                "sentiment_shift": "+15% (过去1小时)",
                "volume_spike": "+340% (过去2小时)",
                "engagement_rate": 0.23,
                "reach_estimate": 2500000
            },
            "ml_metrics": {
                "lstm_confidence": 0.91,
                "transformer_score": 0.89,
                "ensemble_weight": 0.93,
                "feature_importance": {
                    "temporal": 0.35,
                    "sentiment": 0.28,
                    "social": 0.22,
                    "technical": 0.15
                }
            }
        },
        {
            "id": "pred_002",
            "event_id": "event_002",
            "hotspot_prob": 0.78,
            "confidence": "MEDIUM",
            "predicted_time": (datetime.now() + timedelta(hours=6)).isoformat(),
            "factors": {
                "temporal_score": 0.72,
                "influence_score": 0.81,
                "sentiment_score": 0.75,
                "social_momentum": 0.68,
                "media_coverage": 0.82,
                "viral_potential": 0.74
            },
            "risk_assessment": {
                "market_volatility": "中等",
                "regulatory_risk": "低",
                "competition_risk": "中等",
                "technical_risk": "低",
                "liquidity_risk": "低"
            },
            "strategy_ready": False,
            "analysis_details": {
                "social_momentum": "稳步上升",
                "media_attention": "高",
                "influencer_activity": "中等",
                "keyword_trending": ["AI", "GPT5", "OpenAI", "Technology", "Innovation"],
                "sentiment_shift": "+8% (过去1小时)",
                "volume_spike": "+120% (过去2小时)",
                "engagement_rate": 0.18,
                "reach_estimate": 1800000
            },
            "ml_metrics": {
                "lstm_confidence": 0.76,
                "transformer_score": 0.81,
                "ensemble_weight": 0.78,
                "feature_importance": {
                    "temporal": 0.30,
                    "sentiment": 0.32,
                    "social": 0.25,
                    "technical": 0.13
                }
            }
        },
        {
            "id": "pred_003",
            "event_id": "event_003",
            "hotspot_prob": 0.65,
            "confidence": "MEDIUM",
            "predicted_time": (datetime.now() + timedelta(hours=12)).isoformat(),
            "factors": {
                "temporal_score": 0.68,
                "influence_score": 0.62,
                "sentiment_score": 0.71,
                "social_momentum": 0.58,
                "media_coverage": 0.65,
                "viral_potential": 0.69
            },
            "risk_assessment": {
                "market_volatility": "低",
                "regulatory_risk": "低",
                "competition_risk": "高",
                "technical_risk": "中等",
                "liquidity_risk": "中等"
            },
            "strategy_ready": True,
            "analysis_details": {
                "social_momentum": "缓慢上升",
                "media_attention": "中等",
                "influencer_activity": "低",
                "keyword_trending": ["Pepe", "Meme", "Community", "Revival", "NFT"],
                "sentiment_shift": "+3% (过去1小时)",
                "volume_spike": "+45% (过去2小时)",
                "engagement_rate": 0.12,
                "reach_estimate": 850000
            },
            "ml_metrics": {
                "lstm_confidence": 0.63,
                "transformer_score": 0.67,
                "ensemble_weight": 0.65,
                "feature_importance": {
                    "temporal": 0.25,
                    "sentiment": 0.35,
                    "social": 0.30,
                    "technical": 0.10
                }
            }
        },
        {
            "id": "pred_004",
            "event_id": "event_004",
            "hotspot_prob": 0.52,
            "confidence": "LOW",
            "predicted_time": (datetime.now() + timedelta(days=1)).isoformat(),
            "factors": {
                "temporal_score": 0.45,
                "influence_score": 0.58,
                "sentiment_score": 0.53,
                "social_momentum": 0.48,
                "media_coverage": 0.61,
                "viral_potential": 0.49
            },
            "risk_assessment": {
                "market_volatility": "中等",
                "regulatory_risk": "高",
                "competition_risk": "中等",
                "technical_risk": "高",
                "liquidity_risk": "高"
            },
            "strategy_ready": False,
            "analysis_details": {
                "social_momentum": "平稳",
                "media_attention": "中等",
                "influencer_activity": "低",
                "keyword_trending": ["FIFA", "WorldCup", "Sports", "Messi", "Football"],
                "sentiment_shift": "-2% (过去1小时)",
                "volume_spike": "+15% (过去2小时)",
                "engagement_rate": 0.08,
                "reach_estimate": 3200000
            },
            "ml_metrics": {
                "lstm_confidence": 0.51,
                "transformer_score": 0.54,
                "ensemble_weight": 0.52,
                "feature_importance": {
                    "temporal": 0.20,
                    "sentiment": 0.25,
                    "social": 0.35,
                    "technical": 0.20
                }
            }
        },
        {
            "id": "pred_005",
            "event_id": "event_005",
            "hotspot_prob": 0.87,
            "confidence": "HIGH",
            "predicted_time": (datetime.now() + timedelta(hours=4)).isoformat(),
            "factors": {
                "temporal_score": 0.84,
                "influence_score": 0.89,
                "sentiment_score": 0.82,
                "social_momentum": 0.91,
                "media_coverage": 0.78,
                "viral_potential": 0.86
            },
            "risk_assessment": {
                "market_volatility": "中等",
                "regulatory_risk": "低",
                "competition_risk": "低",
                "technical_risk": "低",
                "liquidity_risk": "低"
            },
            "strategy_ready": True,
            "analysis_details": {
                "social_momentum": "爆发式增长",
                "media_attention": "高",
                "influencer_activity": "极高",
                "keyword_trending": ["Elon", "Tesla", "Dogecoin", "X", "SpaceX"],
                "sentiment_shift": "+22% (过去1小时)",
                "volume_spike": "+580% (过去2小时)",
                "engagement_rate": 0.31,
                "reach_estimate": 4100000
            },
            "ml_metrics": {
                "lstm_confidence": 0.85,
                "transformer_score": 0.88,
                "ensemble_weight": 0.87,
                "feature_importance": {
                    "temporal": 0.32,
                    "sentiment": 0.30,
                    "social": 0.28,
                    "technical": 0.10
                }
            }
        }
    ]

    return {
        "status": "success",
        "predictions": predictions,
        "total_count": len(predictions),
        "high_confidence_count": len([p for p in predictions if p["confidence"] == "HIGH"]),
        "medium_confidence_count": len([p for p in predictions if p["confidence"] == "MEDIUM"]),
        "low_confidence_count": len([p for p in predictions if p["confidence"] == "LOW"]),
        "analysis_time": datetime.now().isoformat(),
        "model_version": "v2.1.0",
        "model_accuracy": 0.847,
        "last_training": "2024-12-15T10:30:00Z",
        "data_sources": ["twitter", "reddit", "tiktok", "telegram", "discord", "darkweb"],
        "processing_stats": {
            "events_analyzed": 1247,
            "signals_processed": 8934,
            "ml_predictions": 156,
            "accuracy_score": 84.7
        }
    }

# ==================== 热点监控相关API ====================

@app.get("/hotspot/global")
async def get_global_hotspot_data():
    """获取全球热点监控数据"""

    return {
        "status": "success",
        "global_stats": {
            "total_hotspots": 1247,
            "active_regions": 6,
            "trending_topics": 89,
            "prediction_accuracy": 84.7,
            "last_update": datetime.now().isoformat()
        },
        "regional_data": [
            {
                "region": "北美",
                "country": "US",
                "hotspot_count": 456,
                "trending_score": 0.92,
                "top_keywords": ["Trump", "Election", "Crypto", "AI", "Tesla"],
                "sentiment": 0.78,
                "volume_24h": 2340000,
                "growth_rate": 0.15
            },
            {
                "region": "欧洲",
                "country": "EU",
                "hotspot_count": 234,
                "trending_score": 0.76,
                "top_keywords": ["Brexit", "ECB", "Digital Euro", "Green Deal"],
                "sentiment": 0.65,
                "volume_24h": 1120000,
                "growth_rate": 0.08
            },
            {
                "region": "亚太",
                "country": "APAC",
                "hotspot_count": 387,
                "trending_score": 0.84,
                "top_keywords": ["K-pop", "Anime", "Gaming", "NFT", "Metaverse"],
                "sentiment": 0.82,
                "volume_24h": 1890000,
                "growth_rate": 0.22
            },
            {
                "region": "中东",
                "country": "ME",
                "hotspot_count": 89,
                "trending_score": 0.58,
                "top_keywords": ["Oil", "Blockchain", "Dubai", "Innovation"],
                "sentiment": 0.71,
                "volume_24h": 450000,
                "growth_rate": 0.12
            },
            {
                "region": "南美",
                "country": "SA",
                "hotspot_count": 67,
                "trending_score": 0.49,
                "top_keywords": ["Football", "Carnival", "Bitcoin", "Politics"],
                "sentiment": 0.68,
                "volume_24h": 320000,
                "growth_rate": 0.05
            },
            {
                "region": "非洲",
                "country": "AF",
                "hotspot_count": 34,
                "trending_score": 0.41,
                "top_keywords": ["Mobile Money", "Agriculture", "Solar"],
                "sentiment": 0.73,
                "volume_24h": 180000,
                "growth_rate": 0.18
            }
        ],
        "trending_categories": {
            "politics": {"score": 0.89, "growth": "+15%", "volume": 890000},
            "technology": {"score": 0.82, "growth": "+12%", "volume": 750000},
            "entertainment": {"score": 0.76, "growth": "+8%", "volume": 620000},
            "sports": {"score": 0.71, "growth": "+5%", "volume": 540000},
            "finance": {"score": 0.68, "growth": "+18%", "volume": 480000}
        },
        "real_time_metrics": {
            "signals_per_minute": 234,
            "processing_latency": "1.2s",
            "accuracy_rate": 0.847,
            "false_positive_rate": 0.023
        }
    }

@app.get("/hotspot/darkweb")
async def get_darkweb_signals():
    """获取暗网信号数据"""

    return {
        "status": "success",
        "darkweb_enabled": True,
        "security_level": "HIGH",
        "signals": [
            {
                "id": "dw_001",
                "title": "Underground Meme Emergence",
                "description": "新兴meme在暗网论坛获得关注",
                "confidence": 0.78,
                "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat(),
                "source": "tor_forum",
                "risk_level": "medium",
                "keywords": ["pepe", "underground", "viral"],
                "threat_score": 0.45,
                "authenticity": 0.82,
                "network_depth": 3
            },
            {
                "id": "dw_002",
                "title": "Early Token Discussion",
                "description": "加密社区讨论即将发布的代币",
                "confidence": 0.65,
                "timestamp": (datetime.now() - timedelta(minutes=45)).isoformat(),
                "source": "encrypted_chat",
                "risk_level": "low",
                "keywords": ["token", "launch", "presale"],
                "threat_score": 0.23,
                "authenticity": 0.71,
                "network_depth": 2
            },
            {
                "id": "dw_003",
                "title": "Regulatory Evasion Strategy",
                "description": "讨论规避监管的策略",
                "confidence": 0.89,
                "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                "source": "dark_market",
                "risk_level": "high",
                "keywords": ["regulation", "evasion", "privacy"],
                "threat_score": 0.78,
                "authenticity": 0.94,
                "network_depth": 5
            },
            {
                "id": "dw_004",
                "title": "Whale Movement Coordination",
                "description": "大户协调市场操作",
                "confidence": 0.72,
                "timestamp": (datetime.now() - timedelta(hours=3)).isoformat(),
                "source": "private_channel",
                "risk_level": "high",
                "keywords": ["whale", "coordination", "pump"],
                "threat_score": 0.85,
                "authenticity": 0.88,
                "network_depth": 4
            }
        ],
        "monitoring_stats": {
            "active_nodes": 23,
            "monitored_forums": 156,
            "daily_signals": 89,
            "accuracy_rate": 0.73,
            "coverage_depth": 7
        },
        "security_metrics": {
            "anonymity_level": "MAXIMUM",
            "detection_risk": "MINIMAL",
            "data_encryption": "AES-256",
            "network_hops": 12
        }
    }

@app.get("/hotspot/analytics")
async def get_hotspot_analytics():
    """获取热点分析数据"""

    return {
        "status": "success",
        "ml_metrics": {
            "viralityModel": 95.2,
            "resonanceModel": 88.7,
            "celebrityModel": 76.3,
            "culturalModel": 92.1,
            "sentimentModel": 89.4,
            "temporalModel": 84.6
        },
        "sentiment_index": {
            "overall": 0.82,
            "politics": 0.85,
            "subculture": 0.90,
            "technology": 0.78,
            "finance": 0.73,
            "entertainment": 0.88
        },
        "prediction_performance": {
            "accuracy_7d": 0.847,
            "accuracy_24h": 0.923,
            "accuracy_1h": 0.956,
            "false_positive_rate": 0.023,
            "false_negative_rate": 0.031,
            "precision": 0.891,
            "recall": 0.876,
            "f1_score": 0.883
        },
        "model_insights": {
            "top_features": [
                {"name": "social_momentum", "importance": 0.34},
                {"name": "sentiment_velocity", "importance": 0.28},
                {"name": "influencer_activity", "importance": 0.22},
                {"name": "temporal_patterns", "importance": 0.16}
            ],
            "model_confidence": 0.89,
            "training_samples": 125000,
            "last_retrain": "2024-12-15T08:00:00Z"
        },
        "real_time_stats": {
            "processing_speed": "1.2s avg",
            "queue_length": 23,
            "active_monitors": 156,
            "data_throughput": "2.3MB/s"
        }
    }

@app.get("/prediction/events")
async def get_prediction_events():
    """获取预测事件数据"""

    # 模拟事件数据 - 更丰富的数据集
    events = [
        {
            "id": "event_001",
            "title": "Trump 2024 竞选集会",
            "description": "特朗普在关键摇摆州举行大型竞选集会，预计将发表重要政策声明",
            "event_time": (datetime.now() + timedelta(hours=2)).isoformat(),
            "event_type": "POLITICAL",
            "key_person": "Donald Trump",
            "region": "US",
            "category": "politics",
            "importance": "HIGH",
            "expected_audience": 50000,
            "media_coverage": "EXTENSIVE",
            "social_buzz": 0.92,
            "created_at": datetime.now().isoformat(),
            "reminder_set": True,
            "notes": "关注政策声明对加密货币的影响",
            "tags": ["election", "politics", "crypto", "policy"],
            "location": "Pennsylvania",
            "duration": 120,
            "live_streams": ["Truth Social", "X", "YouTube"],
            "market_impact": "HIGH",
            "volatility_expected": 0.85,
            "related_tokens": ["TRUMP", "MAGA", "FREEDOM"],
            "historical_impact": 0.78
        },
        {
            "id": "event_002",
            "title": "OpenAI GPT-5 发布会",
            "description": "OpenAI 宣布将发布下一代AI模型GPT-5，预计将带来重大技术突破",
            "event_time": (datetime.now() + timedelta(hours=6)).isoformat(),
            "event_type": "TECHNOLOGY",
            "key_person": "Sam Altman",
            "region": "GLOBAL",
            "category": "technology",
            "importance": "HIGH",
            "expected_audience": 100000,
            "media_coverage": "GLOBAL",
            "social_buzz": 0.88,
            "created_at": datetime.now().isoformat(),
            "reminder_set": True,
            "notes": "AI概念代币可能受到影响",
            "tags": ["ai", "technology", "innovation", "openai"],
            "location": "San Francisco",
            "duration": 90,
            "live_streams": ["YouTube", "Twitch", "LinkedIn"],
            "market_impact": "HIGH",
            "volatility_expected": 0.72,
            "related_tokens": ["AI", "GPT", "TECH", "FUTURE"],
            "historical_impact": 0.84
        },
        {
            "id": "event_003",
            "title": "Pepe 社区复活节",
            "description": "Pepe meme社区举办大型线上活动，多个KOL参与",
            "event_time": (datetime.now() + timedelta(hours=12)).isoformat(),
            "event_type": "ENTERTAINMENT",
            "key_person": "Matt Furie",
            "region": "GLOBAL",
            "category": "subculture",
            "importance": "MEDIUM",
            "expected_audience": 25000,
            "media_coverage": "MODERATE",
            "social_buzz": 0.75,
            "created_at": datetime.now().isoformat(),
            "reminder_set": False,
            "notes": "Meme币热度可能上升",
            "tags": ["meme", "pepe", "community", "nft"],
            "location": "Virtual",
            "duration": 180,
            "live_streams": ["Discord", "Telegram", "Twitter Spaces"],
            "market_impact": "MEDIUM",
            "volatility_expected": 0.58,
            "related_tokens": ["PEPE", "MEME", "FROG", "RARE"],
            "historical_impact": 0.65
        },
        {
            "id": "event_004",
            "title": "FIFA 世界杯决赛",
            "description": "2026年世界杯预选赛关键比赛，多个国家队争夺出线名额",
            "event_time": (datetime.now() + timedelta(days=1)).isoformat(),
            "event_type": "SPORTS",
            "key_person": "Lionel Messi",
            "region": "GLOBAL",
            "category": "sports",
            "importance": "HIGH",
            "expected_audience": 500000000,
            "media_coverage": "GLOBAL",
            "social_buzz": 0.95,
            "created_at": datetime.now().isoformat(),
            "reminder_set": True,
            "notes": "体育相关代币关注度可能提升",
            "tags": ["fifa", "worldcup", "sports", "football"],
            "location": "Qatar",
            "duration": 120,
            "live_streams": ["FIFA+", "ESPN", "BBC"],
            "market_impact": "MEDIUM",
            "volatility_expected": 0.45,
            "related_tokens": ["FIFA", "GOAL", "SOCCER", "WORLD"],
            "historical_impact": 0.52
        },
        {
            "id": "event_005",
            "title": "Elon Musk X 平台重大更新",
            "description": "马斯克宣布X平台将集成加密货币支付功能",
            "event_time": (datetime.now() + timedelta(hours=4)).isoformat(),
            "event_type": "TECHNOLOGY",
            "key_person": "Elon Musk",
            "region": "GLOBAL",
            "category": "technology",
            "importance": "HIGH",
            "expected_audience": 200000,
            "media_coverage": "EXTENSIVE",
            "social_buzz": 0.96,
            "created_at": datetime.now().isoformat(),
            "reminder_set": True,
            "notes": "Dogecoin和X相关代币可能暴涨",
            "tags": ["elon", "x", "crypto", "payment"],
            "location": "Austin",
            "duration": 60,
            "live_streams": ["X", "YouTube", "Tesla"],
            "market_impact": "EXTREME",
            "volatility_expected": 0.94,
            "related_tokens": ["DOGE", "X", "ELON", "TESLA"],
            "historical_impact": 0.91
        },
        {
            "id": "event_006",
            "title": "韩国K-pop NFT发布",
            "description": "BTS等顶级K-pop团体发布限量NFT收藏品",
            "event_time": (datetime.now() + timedelta(hours=8)).isoformat(),
            "event_type": "ENTERTAINMENT",
            "key_person": "BTS",
            "region": "ASIA",
            "category": "subculture",
            "importance": "MEDIUM",
            "expected_audience": 150000,
            "media_coverage": "MODERATE",
            "social_buzz": 0.83,
            "created_at": datetime.now().isoformat(),
            "reminder_set": False,
            "notes": "K-pop和NFT相关代币关注",
            "tags": ["kpop", "nft", "bts", "korea"],
            "location": "Seoul",
            "duration": 45,
            "live_streams": ["Weverse", "YouTube", "TikTok"],
            "market_impact": "MEDIUM",
            "volatility_expected": 0.67,
            "related_tokens": ["KPOP", "BTS", "KOREA", "MUSIC"],
            "historical_impact": 0.71
        }
    ]

    return {
        "status": "success",
        "events": events,
        "total_count": len(events),
        "upcoming_count": len([e for e in events if datetime.fromisoformat(e["event_time"]) > datetime.now()]),
        "high_importance_count": len([e for e in events if e["importance"] == "HIGH"]),
        "medium_importance_count": len([e for e in events if e["importance"] == "MEDIUM"]),
        "low_importance_count": len([e for e in events if e["importance"] == "LOW"]),
        "categories": {
            "politics": len([e for e in events if e["category"] == "politics"]),
            "technology": len([e for e in events if e["category"] == "technology"]),
            "subculture": len([e for e in events if e["category"] == "subculture"]),
            "sports": len([e for e in events if e["category"] == "sports"])
        },
        "regions": {
            "US": len([e for e in events if e["region"] == "US"]),
            "GLOBAL": len([e for e in events if e["region"] == "GLOBAL"]),
            "ASIA": len([e for e in events if e["region"] == "ASIA"])
        },
        "market_impact_distribution": {
            "EXTREME": len([e for e in events if e.get("market_impact") == "EXTREME"]),
            "HIGH": len([e for e in events if e.get("market_impact") == "HIGH"]),
            "MEDIUM": len([e for e in events if e.get("market_impact") == "MEDIUM"]),
            "LOW": len([e for e in events if e.get("market_impact") == "LOW"])
        },
        "last_updated": datetime.now().isoformat()
    }

@app.post("/prediction/analyze")
async def run_prediction_analysis():
    """运行AI预测分析"""

    # 模拟AI分析过程
    import time
    await asyncio.sleep(2)  # 模拟分析时间

    # 生成新的预测结果
    predictions = [
        {
            "id": "pred_new_001",
            "event_id": "event_001",
            "hotspot_prob": 0.96,  # 提升概率
            "confidence": "HIGH",
            "predicted_time": (datetime.now() + timedelta(hours=1, minutes=30)).isoformat(),
            "factors": {
                "temporal_score": 0.92,
                "influence_score": 0.98,
                "sentiment_score": 0.91
            },
            "analysis_details": {
                "social_momentum": "急速上升",
                "media_attention": "极高",
                "influencer_activity": "活跃",
                "keyword_trending": ["Trump", "2024", "Election", "Crypto"],
                "sentiment_shift": "+15% (过去1小时)"
            },
            "strategy_recommendation": {
                "action": "立即准备",
                "timing": "事件前1小时发币",
                "risk_level": "中等",
                "expected_roi": "300-800%"
            }
        },
        {
            "id": "pred_new_002",
            "event_id": "event_002",
            "hotspot_prob": 0.85,
            "confidence": "HIGH",
            "predicted_time": (datetime.now() + timedelta(hours=5, minutes=45)).isoformat(),
            "factors": {
                "temporal_score": 0.78,
                "influence_score": 0.89,
                "sentiment_score": 0.82
            },
            "analysis_details": {
                "social_momentum": "稳步上升",
                "media_attention": "高",
                "influencer_activity": "中等",
                "keyword_trending": ["AI", "GPT5", "OpenAI", "Technology"],
                "sentiment_shift": "+8% (过去1小时)"
            },
            "strategy_recommendation": {
                "action": "准备策略",
                "timing": "事件前2小时发币",
                "risk_level": "低",
                "expected_roi": "200-500%"
            }
        },
        {
            "id": "pred_new_003",
            "event_id": "event_003",
            "hotspot_prob": 0.72,
            "confidence": "MEDIUM",
            "predicted_time": (datetime.now() + timedelta(hours=11, minutes=20)).isoformat(),
            "factors": {
                "temporal_score": 0.75,
                "influence_score": 0.68,
                "sentiment_score": 0.79
            },
            "analysis_details": {
                "social_momentum": "缓慢上升",
                "media_attention": "中等",
                "influencer_activity": "低",
                "keyword_trending": ["Pepe", "Meme", "Community", "Revival"],
                "sentiment_shift": "+3% (过去1小时)"
            },
            "strategy_recommendation": {
                "action": "观察等待",
                "timing": "事件前4小时发币",
                "risk_level": "中等",
                "expected_roi": "150-400%"
            }
        }
    ]

    return {
        "status": "success",
        "message": "AI预测分析完成",
        "predictions": predictions,
        "analysis_summary": {
            "total_events_analyzed": 4,
            "high_confidence_predictions": 2,
            "medium_confidence_predictions": 1,
            "low_confidence_predictions": 0,
            "recommended_actions": 2,
            "analysis_duration": "2.1秒",
            "model_accuracy": "94.2%",
            "last_training": "2024-06-01"
        },
        "market_insights": {
            "overall_sentiment": "积极",
            "trending_categories": ["politics", "technology"],
            "risk_factors": ["市场波动", "监管不确定性"],
            "opportunities": ["政治热点", "AI技术突破"],
            "recommended_focus": "政治类和科技类热点"
        },
        "next_analysis": (datetime.now() + timedelta(hours=1)).isoformat()
    }

# ==================== 预测事件管理API ====================

@app.post("/prediction/events")
async def create_prediction_event(event_data: dict):
    """创建新的预测事件"""

    # 验证事件数据
    required_fields = ["title", "description", "event_time", "event_type"]
    for field in required_fields:
        if field not in event_data:
            return {"status": "error", "message": f"缺少必需字段: {field}"}

    # 生成事件ID
    event_id = f"event_{int(datetime.now().timestamp())}"

    # 创建事件 - 增强版数据结构
    new_event = {
        "id": event_id,
        "title": event_data["title"],
        "description": event_data["description"],
        "event_time": event_data["event_time"],
        "event_type": event_data["event_type"],
        "key_person": event_data.get("key_person", ""),
        "region": event_data.get("region", "GLOBAL"),
        "category": event_data.get("category", "general"),
        "importance": event_data.get("importance", "MEDIUM"),
        "expected_audience": event_data.get("expected_audience", 10000),
        "media_coverage": event_data.get("media_coverage", "MODERATE"),
        "social_buzz": event_data.get("social_buzz", 0.5),
        "created_at": datetime.now().isoformat(),
        "reminder_set": event_data.get("reminder_set", False),
        "notes": event_data.get("notes", ""),
        # 新增字段
        "tags": event_data.get("tags", []),
        "location": event_data.get("location", "Unknown"),
        "duration": event_data.get("duration", 60),
        "live_streams": event_data.get("live_streams", []),
        "market_impact": event_data.get("market_impact", "MEDIUM"),
        "volatility_expected": event_data.get("volatility_expected", 0.5),
        "related_tokens": event_data.get("related_tokens", []),
        "historical_impact": event_data.get("historical_impact", 0.5)
    }

    # 自动运行预测分析 - 增强版
    prediction_prob = random.uniform(0.3, 0.9)
    confidence = "HIGH" if prediction_prob > 0.8 else "MEDIUM" if prediction_prob > 0.6 else "LOW"

    prediction = {
        "id": f"pred_{event_id}",
        "event_id": event_id,
        "hotspot_prob": prediction_prob,
        "confidence": confidence,
        "predicted_time": event_data["event_time"],
        "factors": {
            "temporal_score": random.uniform(0.4, 0.9),
            "influence_score": random.uniform(0.4, 0.9),
            "sentiment_score": random.uniform(0.4, 0.9),
            "social_momentum": random.uniform(0.3, 0.9),
            "media_coverage": random.uniform(0.4, 0.8),
            "viral_potential": random.uniform(0.3, 0.9)
        },
        "risk_assessment": {
            "market_volatility": random.choice(["低", "中等", "高"]),
            "regulatory_risk": random.choice(["低", "中等", "高"]),
            "competition_risk": random.choice(["低", "中等", "高"]),
            "technical_risk": random.choice(["低", "中等", "高"]),
            "liquidity_risk": random.choice(["低", "中等", "高"])
        },
        "strategy_ready": random.choice([True, False]),
        "auto_generated": True,
        "analysis_details": {
            "social_momentum": random.choice(["平稳", "缓慢上升", "稳步上升", "急速上升"]),
            "media_attention": random.choice(["低", "中等", "高", "极高"]),
            "influencer_activity": random.choice(["低", "中等", "活跃", "极高"]),
            "sentiment_shift": f"{random.choice(['+', '-'])}{random.randint(1, 25)}% (过去1小时)",
            "volume_spike": f"+{random.randint(10, 500)}% (过去2小时)",
            "engagement_rate": round(random.uniform(0.05, 0.35), 2),
            "reach_estimate": random.randint(100000, 5000000)
        }
    }

    return {
        "status": "success",
        "message": "事件创建成功",
        "event": new_event,
        "prediction": prediction,
        "created_at": datetime.now().isoformat(),
        "auto_analysis": True,
        "confidence_score": prediction_prob
    }

@app.get("/prediction/calendar")
async def get_prediction_calendar():
    """获取预测日历数据"""

    # 生成未来7天的日历数据
    calendar_data = []
    for i in range(7):
        date = datetime.now() + timedelta(days=i)
        daily_events = random.randint(2, 8)

        calendar_data.append({
            "date": date.strftime("%Y-%m-%d"),
            "day_name": date.strftime("%A"),
            "events_count": daily_events,
            "high_impact_events": random.randint(0, 3),
            "prediction_accuracy": round(random.uniform(0.75, 0.95), 2),
            "market_volatility": random.choice(["低", "中等", "高"]),
            "recommended_actions": random.randint(0, 5),
            "events": [
                {
                    "time": f"{random.randint(8, 22):02d}:{random.randint(0, 59):02d}",
                    "title": random.choice([
                        "政治集会", "科技发布会", "体育赛事", "娱乐活动",
                        "经济数据发布", "监管公告", "名人动态", "社区活动"
                    ]),
                    "impact": random.choice(["LOW", "MEDIUM", "HIGH"]),
                    "probability": round(random.uniform(0.3, 0.9), 2)
                } for _ in range(min(daily_events, 3))
            ]
        })

    return {
        "status": "success",
        "calendar": calendar_data,
        "summary": {
            "total_events": sum(day["events_count"] for day in calendar_data),
            "high_impact_total": sum(day["high_impact_events"] for day in calendar_data),
            "avg_accuracy": round(sum(day["prediction_accuracy"] for day in calendar_data) / len(calendar_data), 2),
            "peak_day": max(calendar_data, key=lambda x: x["events_count"])["date"],
            "recommended_focus": random.choice(["政治事件", "科技发布", "体育赛事", "娱乐活动"])
        },
        "generated_at": datetime.now().isoformat()
    }

@app.get("/prediction/timeline")
async def get_prediction_timeline():
    """获取预测时间轴数据"""

    # 生成时间轴数据
    timeline_events = []
    base_time = datetime.now()

    # 过去事件
    for i in range(5):
        past_time = base_time - timedelta(hours=i*2)
        timeline_events.append({
            "id": f"past_{i}",
            "timestamp": past_time.isoformat(),
            "type": "COMPLETED",
            "title": random.choice([
                "马斯克发推", "政策公告", "市场数据", "技术突破", "社区活动"
            ]),
            "impact_score": round(random.uniform(0.3, 0.9), 2),
            "actual_result": random.choice(["成功", "部分成功", "失败"]),
            "prediction_accuracy": round(random.uniform(0.6, 0.95), 2)
        })

    # 未来事件
    for i in range(10):
        future_time = base_time + timedelta(hours=i*3)
        timeline_events.append({
            "id": f"future_{i}",
            "timestamp": future_time.isoformat(),
            "type": "PREDICTED",
            "title": random.choice([
                "重要发布会", "政治集会", "体育赛事", "经济数据", "监管动态"
            ]),
            "prediction_score": round(random.uniform(0.4, 0.95), 2),
            "confidence": random.choice(["HIGH", "MEDIUM", "LOW"]),
            "estimated_impact": random.choice(["EXTREME", "HIGH", "MEDIUM", "LOW"])
        })

    return {
        "status": "success",
        "timeline": sorted(timeline_events, key=lambda x: x["timestamp"]),
        "stats": {
            "total_events": len(timeline_events),
            "completed_events": len([e for e in timeline_events if e["type"] == "COMPLETED"]),
            "predicted_events": len([e for e in timeline_events if e["type"] == "PREDICTED"]),
            "high_confidence": len([e for e in timeline_events if e.get("confidence") == "HIGH"]),
            "avg_prediction_score": round(
                sum(e.get("prediction_score", 0) for e in timeline_events if e["type"] == "PREDICTED") /
                len([e for e in timeline_events if e["type"] == "PREDICTED"]), 2
            )
        },
        "generated_at": datetime.now().isoformat()
    }

@app.get("/prediction/strategies/{event_id}")
async def get_event_strategies(event_id: str):
    """获取特定事件的策略建议"""

    # 模拟策略生成
    strategies = [
        {
            "id": "strategy_001",
            "event_id": event_id,
            "token_name": "TRUMPWIN",
            "token_symbol": "TWIN",
            "strategy_type": "政治热点策略",
            "confidence_score": 0.92,
            "expected_roi": "400-1000%",
            "risk_level": "中等",
            "launch_timing": "事件前1小时",
            "target_audience": "政治关注者",
            "marketing_channels": ["Twitter", "Telegram", "Truth Social"],
            "tokenomics": {
                "total_supply": 1000000000,
                "initial_price": 0.001,
                "allocation": {
                    "airdrop": 0.45,
                    "liquidity": 0.30,
                    "marketing": 0.20,
                    "team": 0.05
                }
            },
            "success_factors": [
                "事件关注度高",
                "政治热点传播快",
                "目标群体明确",
                "时机把握准确"
            ]
        },
        {
            "id": "strategy_002",
            "event_id": event_id,
            "token_name": "ELECTION2024",
            "token_symbol": "ELECT",
            "strategy_type": "选举主题策略",
            "confidence_score": 0.87,
            "expected_roi": "300-700%",
            "risk_level": "低",
            "launch_timing": "事件前2小时",
            "target_audience": "加密投资者",
            "marketing_channels": ["Reddit", "Discord", "YouTube"],
            "tokenomics": {
                "total_supply": 2024000000,
                "initial_price": 0.0005,
                "allocation": {
                    "airdrop": 0.50,
                    "liquidity": 0.35,
                    "marketing": 0.10,
                    "team": 0.05
                }
            },
            "success_factors": [
                "选举年热度",
                "数字寓意好",
                "社区参与度高",
                "长期价值"
            ]
        }
    ]

    return {
        "status": "success",
        "event_id": event_id,
        "strategies": strategies,
        "total_strategies": len(strategies),
        "recommended_strategy": strategies[0]["id"],
        "generated_at": datetime.now().isoformat()
    }

@app.post("/prediction/generate-hotspot-strategy")
async def generate_hotspot_strategy(hotspot_data: dict):
    """基于热点数据生成AI策略"""

    hotspot_id = hotspot_data.get("id", "unknown")
    category = hotspot_data.get("category", "general")
    score = hotspot_data.get("score", 70)
    sentiment = hotspot_data.get("sentiment", 0.75)
    keyword = hotspot_data.get("keyword", hotspot_data.get("title", "Unknown"))
    volume = hotspot_data.get("volume", 10000)

    # 基于热点类型生成策略
    strategy_templates = {
        "politics": {
            "name_patterns": ["TRUMP", "MAGA", "VOTE", "FREEDOM", "LIBERTY", "ELECT"],
            "symbol_patterns": ["TRUMP", "MAGA", "VOTE", "FREE", "USA", "ELECT"],
            "themes": ["政治改革", "自由民主", "爱国主义", "选举热潮", "政治觉醒"],
            "base_supply": 1000000000,
            "initial_price": 0.001,
            "allocation": {"airdrop": 0.45, "liquidity": 0.30, "marketing": 0.20, "team": 0.05}
        },
        "technology": {
            "name_patterns": ["AI", "TECH", "FUTURE", "QUANTUM", "ROBOT", "CYBER"],
            "symbol_patterns": ["AI", "TECH", "QTM", "ROBO", "CYBER", "FUT"],
            "themes": ["人工智能革命", "科技创新", "未来科技", "智能时代", "数字化转型"],
            "base_supply": **********,
            "initial_price": 0.0005,
            "allocation": {"airdrop": 0.50, "liquidity": 0.25, "marketing": 0.20, "team": 0.05}
        },
        "finance": {
            "name_patterns": ["ETF", "BULL", "MOON", "PUMP", "GOLD", "BANK"],
            "symbol_patterns": ["ETF", "BULL", "MOON", "PUMP", "GOLD", "BANK"],
            "themes": ["金融革命", "投资机会", "财富增长", "市场突破", "资本流入"],
            "base_supply": **********,
            "initial_price": 0.0008,
            "allocation": {"airdrop": 0.40, "liquidity": 0.35, "marketing": 0.20, "team": 0.05}
        },
        "subculture": {
            "name_patterns": ["PEPE", "MEME", "DOGE", "SHIB", "FLOKI", "WOJAK"],
            "symbol_patterns": ["PEPE", "MEME", "DOGE", "SHIB", "FLOKI", "WOJ"],
            "themes": ["网络文化", "社区力量", "草根运动", "文化符号", "集体记忆"],
            "base_supply": ***********,
            "initial_price": 0.0001,
            "allocation": {"airdrop": 0.60, "liquidity": 0.25, "marketing": 0.10, "team": 0.05}
        },
        "cryptocurrency": {
            "name_patterns": ["CRYPTO", "COIN", "TOKEN", "CHAIN", "DEFI", "WEB3"],
            "symbol_patterns": ["CRYPTO", "COIN", "TKN", "CHAIN", "DEFI", "WEB3"],
            "themes": ["加密革命", "去中心化", "区块链创新", "数字资产", "Web3时代"],
            "base_supply": 1000000000,
            "initial_price": 0.001,
            "allocation": {"airdrop": 0.45, "liquidity": 0.30, "marketing": 0.20, "team": 0.05}
        },
        "metaverse": {
            "name_patterns": ["META", "VR", "VIRTUAL", "AVATAR", "WORLD", "VERSE"],
            "symbol_patterns": ["META", "VR", "VIRT", "AVT", "WORLD", "VERSE"],
            "themes": ["虚拟世界", "元宇宙体验", "数字身份", "虚拟经济", "沉浸体验"],
            "base_supply": 3000000000,
            "initial_price": 0.0003,
            "allocation": {"airdrop": 0.55, "liquidity": 0.25, "marketing": 0.15, "team": 0.05}
        }
    }

    # 获取策略模板
    template = strategy_templates.get(category, strategy_templates["subculture"])

    # 基于热点评分调整参数
    score_multiplier = score / 100.0
    sentiment_multiplier = sentiment
    volume_multiplier = min(volume / 50000, 3.0)  # 最大3倍

    # 生成代币名称和符号
    import random
    token_name = random.choice(template["name_patterns"])
    if "trump" in keyword.lower():
        token_name = "TRUMPWIN"
        token_symbol = "TWIN"
    elif "ai" in keyword.lower():
        token_name = "AIREVOLUTION"
        token_symbol = "AIREV"
    elif "pepe" in keyword.lower():
        token_name = "PEPEBACK"
        token_symbol = "PEPEB"
    elif "etf" in keyword.lower():
        token_name = "ETFBULL"
        token_symbol = "ETFB"
    elif "bitcoin" in keyword.lower():
        token_name = "BTCMOON"
        token_symbol = "BTCM"
    else:
        token_symbol = random.choice(template["symbol_patterns"])

    # 计算动态参数
    total_supply = int(template["base_supply"] * score_multiplier * volume_multiplier)
    initial_price = template["initial_price"] * (1 + sentiment_multiplier * 0.5)

    # 调整分配比例
    allocation = template["allocation"].copy()
    if score > 90:
        allocation["marketing"] += 0.05
        allocation["airdrop"] -= 0.05

    # 计算预期收益
    if score > 90:
        expected_roi = "500-1200%"
        risk_level = "中等"
        success_probability = random.randint(85, 95)
    elif score > 80:
        expected_roi = "300-800%"
        risk_level = "中等"
        success_probability = random.randint(75, 90)
    else:
        expected_roi = "200-500%"
        risk_level = "低"
        success_probability = random.randint(65, 85)

    # 生成策略
    strategy = {
        "id": f"strategy_{hotspot_id}_{int(datetime.now().timestamp())}",
        "hotspot_id": hotspot_id,
        "token_info": {
            "name": token_name,
            "symbol": token_symbol,
            "description": f"基于'{keyword}'热点的{random.choice(template['themes'])}主题代币",
            "total_supply": total_supply,
            "initial_price": round(initial_price, 6),
            "decimals": 18
        },
        "allocation": allocation,
        "deployment": {
            "recommended_platform": "pump.fun" if category in ["politics", "subculture"] else "raydium",
            "platform_reason": "适合病毒式传播和社区驱动" if category in ["politics", "subculture"] else "技术社区活跃，流动性充足",
            "estimated_cost": {"solana": f"{random.uniform(0.3, 0.8):.1f} SOL"},
            "estimated_time": f"{random.randint(2, 5)}分钟",
            "gas_optimization": f"{random.randint(35, 45)}% 节省"
        },
        "success_metrics": {
            "target_market_cap": f"${random.randint(500, 2000)}K",
            "expected_roi": expected_roi,
            "success_probability": f"{success_probability}%",
            "risk_level": risk_level
        },
        "marketing_strategy": {
            "primary_channels": ["Twitter", "Telegram", "Discord"],
            "target_audience": get_target_audience(category),
            "launch_timing": "立即部署" if score > 85 else "热点峰值前1小时",
            "viral_potential": f"{int(sentiment * score)}%"
        },
        "technical_features": {
            "anti_rug": True,
            "liquidity_lock": "30天",
            "ownership_renounced": True,
            "audit_score": random.randint(92, 98)
        },
        "generated_at": datetime.now().isoformat(),
        "expires_at": (datetime.now() + timedelta(hours=2)).isoformat()
    }

    return {
        "status": "success",
        "message": "AI策略生成成功",
        "strategy": strategy,
        "hotspot_analysis": {
            "score_impact": f"评分{score}分，策略优化{int((score_multiplier - 0.7) * 100)}%",
            "sentiment_impact": f"情绪{sentiment:.2f}，价格调整{int(sentiment_multiplier * 50)}%",
            "volume_impact": f"讨论量{volume}，供应量调整{int((volume_multiplier - 1) * 100)}%"
        },
        "recommendations": [
            "建议在热点峰值前部署以获得最大曝光",
            "利用社交媒体热度进行病毒式营销",
            "设置流动性锁定以增强投资者信心",
            "考虑与KOL合作扩大影响力"
        ]
    }

def get_target_audience(category):
    """获取目标受众"""
    audiences = {
        "politics": "政治关注者、投资者、保守派社区",
        "technology": "科技爱好者、AI从业者、创新投资者",
        "finance": "加密投资者、机构投资者、金融从业者",
        "subculture": "Meme爱好者、年轻投资者、网络文化参与者",
        "cryptocurrency": "加密货币社区、DeFi用户、区块链开发者",
        "metaverse": "游戏玩家、VR爱好者、数字艺术收藏者"
    }
    return audiences.get(category, "加密货币投资者")

@app.get("/ai/token-strategies/trending")
async def get_trending_token_strategies():
    """获取基于热点的推荐发币策略"""

    # 模拟当前热门策略
    trending_strategies = [
        {
            "id": "strategy_001",
            "hotspot_keyword": "Trump 2024",
            "category": "politics",
            "score": 0.94,
            "token_name": "TRUMPMAGA",
            "token_symbol": "MAGA24",
            "estimated_roi": "500-1200%",
            "risk_level": "中等",
            "time_sensitive": True,
            "expires_in": "2小时",
            "reason": "选举热度持续上升，政治类代币关注度极高"
        },
        {
            "id": "strategy_002",
            "hotspot_keyword": "AI Revolution",
            "category": "technology",
            "score": 0.89,
            "token_name": "AIREVOLUTION",
            "token_symbol": "AIREV",
            "estimated_roi": "300-800%",
            "risk_level": "低",
            "time_sensitive": False,
            "expires_in": "24小时",
            "reason": "AI技术持续发展，长期趋势明确"
        },
        {
            "id": "strategy_003",
            "hotspot_keyword": "Pepe Comeback",
            "category": "subculture",
            "score": 0.87,
            "token_name": "PEPECOMEBACK",
            "token_symbol": "PEPEBACK",
            "estimated_roi": "400-1000%",
            "risk_level": "高",
            "time_sensitive": True,
            "expires_in": "6小时",
            "reason": "Meme文化复苏，社区情绪高涨"
        },
        {
            "id": "strategy_004",
            "hotspot_keyword": "Metaverse Gaming",
            "category": "metaverse",
            "score": 0.82,
            "token_name": "METAGAMING",
            "token_symbol": "MGAME",
            "estimated_roi": "250-600%",
            "risk_level": "中等",
            "time_sensitive": False,
            "expires_in": "12小时",
            "reason": "元宇宙游戏概念持续升温"
        },
        {
            "id": "strategy_005",
            "hotspot_keyword": "DeFi Summer 2.0",
            "category": "technology",
            "score": 0.79,
            "token_name": "DEFISUMMER",
            "token_symbol": "DEFI2",
            "estimated_roi": "200-500%",
            "risk_level": "中等",
            "time_sensitive": False,
            "expires_in": "18小时",
            "reason": "DeFi协议创新加速，市场预期乐观"
        }
    ]

    return {
        "status": "success",
        "trending_strategies": trending_strategies,
        "total_count": len(trending_strategies),
        "last_updated": datetime.now().isoformat(),
        "market_sentiment": "积极",
        "recommended_action": "建议优先关注时效性强的策略"
    }

# Pump.fun 部署 API
@app.post("/deploy/pumpfun")
async def deploy_to_pumpfun(config: dict):
    """部署到Pump.fun"""
    import asyncio

    token_name = config.get("tokenName", "MemeMaster Token")
    token_symbol = config.get("tokenSymbol", "MMT")
    total_supply = config.get("totalSupply", 1000000000)
    description = config.get("description", "AI驱动的下一代Meme代币")
    icon_url = config.get("iconUrl", "")
    initial_price = config.get("initialPrice", 0.0001)
    marketing_budget = config.get("marketingBudget", 5)

    # 模拟部署过程
    deployment_steps = [
        {"step": 1, "message": "连接Pump.fun API...", "progress": 15},
        {"step": 2, "message": "创建代币元数据...", "progress": 30},
        {"step": 3, "message": "部署智能合约...", "progress": 50},
        {"step": 4, "message": "设置营销参数...", "progress": 70},
        {"step": 5, "message": "启动社区功能...", "progress": 85},
        {"step": 6, "message": "Pump.fun部署完成!", "progress": 100}
    ]

    # 模拟异步部署
    for step in deployment_steps:
        await asyncio.sleep(0.5)  # 模拟处理时间

    # 生成部署结果
    contract_address = f"{''.join(random.choices('**********************************************************', k=44))}"

    return {
        "status": "success",
        "platform": "pump.fun",
        "contract_address": contract_address,
        "token_name": token_name,
        "token_symbol": token_symbol,
        "total_supply": total_supply,
        "initial_price": initial_price,
        "marketing_budget": marketing_budget,
        "deployment_time": datetime.now().isoformat(),
        "pumpfun_url": f"https://pump.fun/coin/{contract_address}",
        "social_links": {
            "twitter": f"https://twitter.com/pumpfun_{token_symbol.lower()}",
            "telegram": f"https://t.me/{token_symbol.lower()}_community"
        },
        "community_stats": {
            "initial_holders": random.randint(50, 200),
            "social_engagement": round(random.uniform(0.6, 0.9), 2),
            "viral_coefficient": round(random.uniform(1.8, 3.2), 1)
        },
        "liquidity_strategy": {
            "type": "community_driven",
            "auto_marketing": True,
            "social_integration": True,
            "influencer_network": "expanding",
            "dynamic_pricing": True
        }
    }

@app.post("/deploy/raydium")
async def deploy_to_raydium(config: dict):
    """部署到RAYDIUM"""
    import asyncio

    token_name = config.get("tokenName", "MemeMaster Token")
    token_symbol = config.get("tokenSymbol", "MMT")
    total_supply = config.get("totalSupply", 1000000000)
    initial_liquidity = config.get("initialLiquidity", 10)
    token_allocation = config.get("tokenAllocation", 50)
    liquidity_lock = config.get("liquidityLock", True)

    # 模拟部署过程
    deployment_steps = [
        {"step": 1, "message": "连接RAYDIUM协议...", "progress": 20},
        {"step": 2, "message": "创建流动性池...", "progress": 40},
        {"step": 3, "message": "注入初始流动性...", "progress": 60},
        {"step": 4, "message": "配置交易参数...", "progress": 80},
        {"step": 5, "message": "RAYDIUM部署完成!", "progress": 100}
    ]

    # 模拟异步部署
    for step in deployment_steps:
        await asyncio.sleep(0.5)  # 模拟处理时间

    # 生成部署结果
    contract_address = f"0x{''.join(random.choices('0123456789abcdef', k=40))}"
    pool_address = f"{''.join(random.choices('**********************************************************', k=44))}"

    return {
        "status": "success",
        "platform": "raydium",
        "contract_address": contract_address,
        "pool_address": pool_address,
        "token_name": token_name,
        "token_symbol": token_symbol,
        "total_supply": total_supply,
        "initial_liquidity": initial_liquidity,
        "token_allocation": token_allocation,
        "deployment_time": datetime.now().isoformat(),
        "raydium_url": f"https://raydium.io/swap/?inputCurrency=sol&outputCurrency={contract_address}",
        "dexscreener_url": f"https://dexscreener.com/solana/{pool_address}",
        "pool_stats": {
            "liquidity_depth": initial_liquidity * 2,
            "estimated_24h_volume": round(initial_liquidity * random.uniform(0.5, 2.0), 2),
            "price_impact": round(random.uniform(0.1, 0.5), 3),
            "fee_tier": "0.25%"
        },
        "liquidity_strategy": {
            "type": "professional_trading",
            "amm_enabled": True,
            "arbitrage_protection": True,
            "liquidity_locked": liquidity_lock,
            "lock_duration": "365 days" if liquidity_lock else "none",
            "impermanent_loss_protection": 96.8,
            "yield_farming_apy": round(random.uniform(150, 300), 1)
        }
    }

@app.get("/deploy/pumpfun/strategy")
async def get_pumpfun_strategy():
    """获取Pump.fun流动性策略"""
    return {
        "platform": "pump.fun",
        "strategy_type": "community_driven",
        "metrics": {
            "community_activity": round(random.uniform(80, 95), 1),
            "viral_coefficient": round(random.uniform(2.0, 3.5), 1),
            "social_engagement": round(random.uniform(0.7, 0.9), 2),
            "influencer_reach": random.randint(50000, 200000)
        },
        "features": {
            "auto_marketing": True,
            "social_integration": True,
            "influencer_network": "expanding",
            "dynamic_pricing": True,
            "community_governance": True
        },
        "liquidity_management": {
            "dynamic_pricing_algorithm": 78,
            "community_incentive_pool": f"{round(random.uniform(10, 25), 1)}K SOL",
            "marketing_automation": "active",
            "viral_mechanics": "optimized"
        },
        "recommendations": [
            "启用自动营销推广",
            "连接主要社交媒体平台",
            "建立影响者合作网络",
            "优化病毒传播机制"
        ]
    }

@app.get("/deploy/raydium/strategy")
async def get_raydium_strategy():
    """获取RAYDIUM流动性策略"""
    return {
        "platform": "raydium",
        "strategy_type": "professional_trading",
        "metrics": {
            "liquidity_depth": f"${random.randint(1000000, 5000000):,}",
            "daily_volume": f"${random.randint(500000, 2000000):,}",
            "price_stability": round(random.uniform(85, 95), 1),
            "institutional_adoption": round(random.uniform(60, 80), 1)
        },
        "features": {
            "automated_market_making": True,
            "arbitrage_monitoring": True,
            "liquidity_locking": True,
            "yield_farming": True,
            "professional_tools": True
        },
        "liquidity_management": {
            "impermanent_loss_protection": 96.8,
            "yield_farming_apy": round(random.uniform(200, 350), 1),
            "amm_efficiency": 94.2,
            "arbitrage_protection": "active"
        },
        "recommendations": [
            "启用无常损失保护",
            "配置自动做市商",
            "设置套利监控",
            "优化收益农场参数"
        ]
    }

# 流动性控制 API
@app.get("/liquidity/data")
async def get_liquidity_data():
    """获取流动性数据"""
    return {
        "tvl": random.randint(1000000, 2500000),
        "change24h": round(random.uniform(-15, 25), 1),
        "slippage": round(random.uniform(0.2, 0.5), 3)
    }

@app.post("/liquidity/settings")
async def update_liquidity_settings(settings: dict):
    """更新流动性设置"""
    return {"status": "success", "settings": settings}

@app.post("/liquidity/mev/enable")
async def enable_mev_protection():
    """启用MEV防护"""
    return {
        "status": "success",
        "message": "MEV protection enhanced",
        "lossReduction": round(random.uniform(82, 88), 1),
        "flashbotsUsage": round(random.uniform(70, 85), 0),
        "privateMempool": True,
        "randomization": True,
        "frontrunDetection": random.randint(8, 20),
        "enabled_at": datetime.now().isoformat()
    }

@app.get("/liquidity/mev/stats")
async def get_mev_stats():
    """获取MEV统计"""
    return {
        "lossReduction": round(random.uniform(82, 88), 1),
        "flashbotsUsage": round(random.uniform(70, 85), 0),
        "privateMempool": True,
        "randomization": True,
        "frontrunDetection": random.randint(8, 20),
        "total_saved": round(random.uniform(50, 200), 2),
        "mev_attacks_blocked": random.randint(15, 45),
        "last_update": datetime.now().isoformat()
    }

@app.post("/liquidity/mev/flashbots")
async def configure_flashbots(config: dict):
    """配置Flashbots"""
    return {
        "status": "success",
        "config": config,
        "endpoint": "https://relay.flashbots.net",
        "bundle_success_rate": round(random.uniform(85, 95), 1),
        "average_priority_fee": round(random.uniform(2, 8), 2)
    }

@app.get("/liquidity/whale/activity")
async def get_whale_activity():
    """获取鲸鱼活动"""
    activities = []
    for i in range(5):
        activity = {
            "address": f"0x{''.join(random.choices('0123456789abcdef', k=8))}...{''.join(random.choices('0123456789abcdef', k=8))}",
            "action": random.choice(["大额买入", "大额卖出", "流动性撤出", "流动性添加"]),
            "amount": f"${random.randint(500000, 5000000):,}",
            "impact": f"{random.uniform(-8, 8):+.1f}%",
            "time": f"{random.randint(1, 60)}分钟前",
            "confidence": round(random.uniform(0.7, 0.95), 2)
        }
        activities.append(activity)

    return activities

@app.post("/liquidity/whale/enable")
async def enable_whale_monitoring():
    """启用鲸鱼监控"""
    return {
        "status": "success",
        "message": "Whale monitoring enabled",
        "nansen_api": "connected",
        "custom_detector": "active",
        "threshold": "$500,000",
        "alerts_enabled": True,
        "monitored_addresses": random.randint(150, 300)
    }

@app.get("/liquidity/whale/nansen")
async def get_nansen_data():
    """获取Nansen数据"""
    return {
        "smart_money_flow": round(random.uniform(0.6, 0.9), 2),
        "whale_transactions": random.randint(5, 25),
        "fund_flows": {
            "inflow": round(random.uniform(1000000, 5000000), 0),
            "outflow": round(random.uniform(800000, 4000000), 0)
        },
        "top_holders": [
            {"rank": 1, "address": "0x1234...5678", "percentage": 12.5},
            {"rank": 2, "address": "0x9876...4321", "percentage": 8.3},
            {"rank": 3, "address": "0xabcd...efgh", "percentage": 6.7}
        ],
        "last_update": datetime.now().isoformat()
    }

@app.post("/liquidity/tvl/auto-refill")
async def trigger_auto_refill():
    """触发TVL自动补充"""
    refill_amount = random.randint(200000, 800000)

    return {
        "status": "success",
        "message": "Auto refill triggered",
        "amount": refill_amount,
        "newTVL": random.randint(1500000, 2500000),
        "source": random.choice(["reserve_fund", "emergency_pool", "partner_liquidity"]),
        "estimated_time": f"{random.randint(5, 15)} minutes",
        "transaction_hash": f"0x{''.join(random.choices('0123456789abcdef', k=64))}",
        "triggered_at": datetime.now().isoformat()
    }

@app.get("/liquidity/tvl/history")
async def get_tvl_history():
    """获取TVL历史"""
    history = []
    base_tvl = 1500000

    for i in range(24):  # 24小时历史
        timestamp = datetime.now() - timedelta(hours=23-i)
        tvl = base_tvl + random.randint(-200000, 300000)

        history.append({
            "timestamp": timestamp.isoformat(),
            "tvl": tvl,
            "change": round(random.uniform(-5, 8), 2)
        })

    return history

@app.get("/liquidity/slippage/optimization")
async def get_slippage_optimization():
    """获取滑点优化数据"""
    return {
        "aggregatorSavings": round(random.uniform(20, 30), 1),
        "oneinchActive": True,
        "paraswapActive": True,
        "orderSplitting": random.randint(2, 8),
        "current_slippage": round(random.uniform(0.2, 0.4), 3),
        "optimized_routes": random.randint(15, 35),
        "gas_savings": round(random.uniform(15, 25), 1),
        "last_optimization": datetime.now().isoformat()
    }

@app.post("/liquidity/aggregator/1inch")
async def configure_1inch(config: dict):
    """配置1inch聚合器"""
    return {
        "status": "success",
        "config": config,
        "api_key": "configured",
        "supported_chains": ["ethereum", "bsc", "polygon", "arbitrum"],
        "success_rate": round(random.uniform(92, 98), 1),
        "average_savings": round(random.uniform(15, 25), 1)
    }

@app.post("/liquidity/aggregator/paraswap")
async def configure_paraswap(config: dict):
    """配置Paraswap聚合器"""
    return {
        "status": "success",
        "config": config,
        "api_key": "configured",
        "supported_chains": ["ethereum", "bsc", "polygon", "avalanche"],
        "success_rate": round(random.uniform(90, 96), 1),
        "average_savings": round(random.uniform(12, 22), 1)
    }

@app.post("/liquidity/order/split")
async def split_large_order(order_data: dict):
    """拆分大额订单"""
    total_amount = order_data.get("amount", 1000000)
    split_count = random.randint(3, 8)

    splits = []
    remaining = total_amount

    for i in range(split_count):
        if i == split_count - 1:
            amount = remaining
        else:
            amount = random.randint(int(remaining * 0.1), int(remaining * 0.4))
            remaining -= amount

        splits.append({
            "order_id": f"split_{i+1}",
            "amount": amount,
            "estimated_slippage": round(random.uniform(0.1, 0.3), 3),
            "delay": random.randint(30, 300),  # seconds
            "exchange": random.choice(["uniswap", "sushiswap", "1inch", "paraswap"])
        })

    return {
        "status": "success",
        "original_amount": total_amount,
        "split_count": split_count,
        "splits": splits,
        "total_estimated_slippage": round(sum(s["estimated_slippage"] for s in splits), 3),
        "execution_time": f"{sum(s['delay'] for s in splits)} seconds"
    }

# 退出系统 API
@app.get("/exit/signals")
async def get_exit_signals():
    """获取退出信号"""
    return {
        "strength": 0.78,
        "action": "partial_sell",
        "roi": 215.3
    }

@app.post("/exit/settings")
async def update_exit_settings(settings: dict):
    """更新退出设置"""
    return {"status": "success", "settings": settings}

@app.post("/exit/emergency")
async def emergency_exit():
    """紧急退出"""
    return {"status": "success", "message": "Emergency exit executed"}

@app.get("/exit/multi-indicators")
async def get_multi_indicators():
    """获取多指标融合数据"""
    import random

    return {
        "technical": {
            "rsi": round(random.uniform(30, 80), 1),
            "macd": round(random.uniform(-0.5, 0.5), 3),
            "bollinger": random.choice(["upper", "middle", "lower"]),
            "volume_spike": round(random.uniform(1.0, 3.0), 1)
        },
        "onchain": {
            "whale_activity": round(random.uniform(0.5, 0.9), 2),
            "transaction_volume": round(random.uniform(1.0, 2.0), 2),
            "holder_distribution": round(random.uniform(0.4, 0.8), 2),
            "smart_money_flow": round(random.uniform(0.6, 0.9), 2)
        },
        "sentiment": {
            "social_score": round(random.uniform(0.4, 0.9), 2),
            "news_sentiment": round(random.uniform(0.3, 0.8), 2),
            "fear_greed": round(random.uniform(0.2, 0.8), 2),
            "polymarket_prob": round(random.uniform(0.5, 0.8), 2)
        },
        "events": {
            "regulatory_risk": round(random.uniform(0.1, 0.4), 2),
            "market_correlation": round(random.uniform(0.7, 0.95), 2),
            "volatility_forecast": round(random.uniform(0.5, 0.9), 2),
            "liquidity_depth": round(random.uniform(0.8, 0.95), 2)
        }
    }

@app.post("/exit/xgboost/retrain")
async def retrain_xgboost():
    """重新训练XGBoost模型"""
    import random

    new_accuracy = round(random.uniform(0.82, 0.90), 3)

    return {
        "status": "success",
        "model_accuracy": new_accuracy,
        "training_samples": random.randint(800, 1200),
        "retrain_time": datetime.now().isoformat(),
        "feature_importance": {
            "RSI": round(random.uniform(0.15, 0.25), 2),
            "Whale Activity": round(random.uniform(0.18, 0.28), 2),
            "Sentiment": round(random.uniform(0.12, 0.20), 2),
            "Volume": round(random.uniform(0.08, 0.15), 2),
            "MACD": round(random.uniform(0.08, 0.15), 2),
            "Polymarket": round(random.uniform(0.18, 0.28), 2)
        }
    }

@app.post("/exit/hedging/activate")
async def activate_hedging(params: dict):
    """激活期权对冲"""
    import random

    token = params.get('token', 'MEME')
    volatility = params.get('volatility', 'medium')
    hedge_ratio = params.get('hedgeRatio', 30)

    positions = [
        {
            "type": "PUT",
            "strike": 0.0208,
            "expiry": "7d",
            "amount": 30,
            "premium": 0.0012,
            "status": "active"
        },
        {
            "type": "PUT",
            "strike": 0.0195,
            "expiry": "14d",
            "amount": 20,
            "premium": 0.0018,
            "status": "active"
        }
    ]

    return {
        "status": "success",
        "message": f"Hedging activated for {token}",
        "positions": positions,
        "total_premium": sum(p["premium"] for p in positions),
        "hedge_ratio": hedge_ratio,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    uptime = time.time() - start_time
    return {
        "status": "running",
        "uptime": uptime,
        "uptime_formatted": f"{int(uptime // 3600)}h {int((uptime % 3600) // 60)}m",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "web": "running",
            "ai_engine": "ready",
            "trading_engine": "monitoring",
            "database": "connected"
        }
    }

@app.get("/api/system-info")
async def get_system_info():
    """获取系统信息"""
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        return {
            "uptime": time.time() - start_time,
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "disk_percent": disk.percent,
            "timestamp": datetime.now().isoformat(),
            "system": {
                "platform": os.name,
                "python_version": "3.12.3"
            }
        }
    except Exception as e:
        return {
            "error": str(e),
            "uptime": time.time() - start_time,
            "cpu_percent": 0,
            "timestamp": datetime.now().isoformat()
        }

@app.post("/api/strategy/check-duplicates")
async def check_token_duplicates(
    request: dict,
    current_user: User = Depends(get_current_active_user) if USER_MANAGEMENT_ENABLED else None
):
    """检查代币重名"""
    try:
        if USER_MANAGEMENT_ENABLED and current_user:
            # 检查查重功能权限
            from src.subscription.usage_tracker import UsageTracker
            from src.models.payment import PaymentType

            usage_tracker = UsageTracker()

            # 检查是否需要付费
            payment_check = await usage_tracker.track_paid_feature(
                user=current_user,
                feature_type=PaymentType.DUPLICATE_CHECK,
                description="代币查重检查",
                metadata={"token_name": request.get("token_name", "")}
            )

            if payment_check.get("requires_payment", False):
                return {
                    "status": "payment_required",
                    "message": "查重功能需要付费或升级订阅",
                    "cost_sol": payment_check["cost_sol"],
                    "description": payment_check["description"],
                    "payment_type": "duplicate_check"
                }

        token_name = request.get("token_name", "")
        platforms = request.get("platforms", ["pump.fun", "raydium"])

        if not token_name:
            return {
                "status": "error",
                "message": "代币名称不能为空"
            }

        # 模拟查重结果
        platform_results = []
        total_risk_score = 0
        warnings = []
        suggestions = []

        for platform in platforms:
            # 模拟平台查询
            exists = random.random() > 0.4  # 60% 概率存在同名代币

            if not exists:
                platform_results.append({
                    "platform": platform,
                    "exists": False,
                    "data": None
                })
                continue

            # 生成模拟数据
            is_dead = random.random() > 0.6  # 40% 概率是死亡代币
            created_days_ago = random.randint(1, 365)
            liquidity = random.uniform(0.01, 100) if not is_dead else random.uniform(0.001, 0.1)

            platform_data = {
                "address": f"0x{''.join(random.choices('0123456789abcdef', k=40))}",
                "created_at": (datetime.now() - timedelta(days=created_days_ago)).isoformat(),
                "is_dead": is_dead,
                "liquidity": round(liquidity, 3),
                "max_liquidity": round(liquidity * random.uniform(1.2, 5.0), 3),
                "last_trade": (datetime.now() - timedelta(hours=random.randint(1, 720))).isoformat(),
                "trade_count_24h": 0 if is_dead else random.randint(10, 1000),
                "holder_count": random.randint(10, 2000),
                "price_change_24h": round((random.random() - 0.5) * 100, 2)
            }

            platform_results.append({
                "platform": platform,
                "exists": True,
                "data": platform_data
            })

            # 计算风险评分
            risk_score = 30  # 基础风险

            if not is_dead:
                risk_score += 50  # 活跃代币高风险
                warnings.append(f"{platform}上存在活跃同名代币")
                suggestions.append({
                    "action": "rename",
                    "label": "修改代币名称",
                    "priority": "high"
                })
            else:
                risk_score += 10  # 死亡代币低风险
                warnings.append(f"{platform}上存在已死亡同名代币")

            if liquidity > 50:
                risk_score += 40  # 高流动性风险
                warnings.append(f"同名代币流动性较高 ({liquidity:.2f} SOL)")

            if created_days_ago < 7:
                risk_score += 30  # 近期创建风险
                warnings.append(f"同名代币在{created_days_ago}天前创建")

            total_risk_score = max(total_risk_score, risk_score)

        # 确定风险级别
        if total_risk_score >= 70:
            risk_level = "HIGH"
            suggestions.append({
                "action": "cancel",
                "label": "取消部署",
                "priority": "high"
            })
        elif total_risk_score >= 40:
            risk_level = "MEDIUM"
            suggestions.append({
                "action": "modify",
                "label": "添加版本后缀",
                "priority": "medium"
            })
        else:
            risk_level = "LOW"

        if not suggestions or risk_level == "LOW":
            suggestions.append({
                "action": "proceed",
                "label": "继续部署",
                "priority": "low"
            })

        result = {
            "token_name": token_name,
            "risk_level": risk_level,
            "risk_score": total_risk_score,
            "platform_results": platform_results,
            "warnings": warnings,
            "suggestions": suggestions,
            "checked_at": datetime.now().isoformat()
        }

        return {
            "status": "success",
            "data": result,
            "message": "查重检查完成"
        }

    except Exception as e:
        print(f"查重检查失败: {e}")
        return {
            "status": "error",
            "message": f"查重检查失败: {str(e)}"
        }

# ==================== 用户管理 API 端点 ====================

if USER_MANAGEMENT_ENABLED:

    # 注意：@app.on_event("startup") 已过时，建议使用 lifespan 事件处理器
    # 但为了保持兼容性，暂时保留此实现
    @app.on_event("startup")
    async def startup_event():
        """应用启动时初始化数据库"""
        try:
            await init_database()
            print("✅ 数据库初始化成功")
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")

    # 用户认证端点
    @app.post("/api/auth/register", response_model=UserResponse)
    async def register_user(user_data: UserRegister):
        """用户注册"""
        try:
            user = await auth_manager.register_user(
                email=user_data.email,
                username=user_data.username,
                password=user_data.password,
                full_name=user_data.full_name,
                wallet_address=user_data.wallet_address
            )

            return UserResponse(
                id=user.id,
                email=user.email,
                username=user.username,
                full_name=user.full_name,
                role=user.role,
                is_active=user.is_active,
                created_at=user.created_at,
                status=user.status,
                current_subscription="free",
                daily_hotspots_used=user.daily_hotspots_used,
                daily_hotspots_limit=3,
                total_strategies_created=user.total_strategies_created,
                total_deployments=user.total_deployments
            )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"注册失败: {str(e)}"
            )

    @app.post("/api/auth/login")
    async def login_user(user_data: UserLogin):
        """用户登录"""
        try:
            user = await auth_manager.authenticate_user(user_data.email, user_data.password)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="邮箱或密码错误"
                )

            # 更新最后登录时间
            from src.database.user_db import UserDatabase
            user_db = UserDatabase()
            await user_db.update_last_login(user.id)

            # 创建访问令牌
            access_token = auth_manager.create_access_token(data={"sub": str(user.id)})
            refresh_token = auth_manager.create_refresh_token(data={"sub": str(user.id)})

            # 获取用户功能权限
            features = FeatureChecker.get_user_features(user)
            limits = FeatureChecker.get_user_limits(user)
            trial_status = FeatureChecker.check_trial_status(user)

            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": UserResponse(
                    id=user.id,
                    email=user.email,
                    username=user.username,
                    full_name=user.full_name,
                    role=user.role,
                    is_active=user.is_active,
                    created_at=user.created_at,
                    last_login=user.last_login,
                    status=user.status,
                    current_subscription=user.role.value,
                    subscription_expires=user.free_trial_ends,
                    daily_hotspots_used=user.daily_hotspots_used,
                    daily_hotspots_limit=limits.get("daily_hotspots", 3),
                    total_strategies_created=user.total_strategies_created,
                    total_deployments=user.total_deployments
                ),
                "features": features,
                "limits": limits,
                "trial_status": trial_status
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"登录失败: {str(e)}"
            )

    @app.get("/api/auth/me", response_model=UserResponse)
    async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
        """获取当前用户信息"""
        try:
            features = FeatureChecker.get_user_features(current_user)
            limits = FeatureChecker.get_user_limits(current_user)
            trial_status = FeatureChecker.check_trial_status(current_user)

            return {
                "user": UserResponse(
                    id=current_user.id,
                    email=current_user.email,
                    username=current_user.username,
                    full_name=current_user.full_name,
                    role=current_user.role,
                    is_active=current_user.is_active,
                    created_at=current_user.created_at,
                    last_login=current_user.last_login,
                    status=current_user.status,
                    current_subscription=current_user.role.value,
                    subscription_expires=current_user.free_trial_ends,
                    daily_hotspots_used=current_user.daily_hotspots_used,
                    daily_hotspots_limit=limits.get("daily_hotspots", 3),
                    total_strategies_created=current_user.total_strategies_created,
                    total_deployments=current_user.total_deployments
                ),
                "features": features,
                "limits": limits,
                "trial_status": trial_status
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取用户信息失败: {str(e)}"
            )

    # 订阅管理端点
    @app.get("/api/subscription/plans")
    async def get_subscription_plans():
        """获取订阅计划"""
        return {
            "plans": [plan.model_dump() for plan in SUBSCRIPTION_PLANS.values()],
            "pay_per_use": PAY_PER_USE_PRICES
        }

    @app.post("/api/subscription/upgrade")
    async def upgrade_subscription(
        upgrade_data: SubscriptionUpgrade,
        current_user: User = Depends(get_current_active_user)
    ):
        """升级订阅"""
        try:
            plan = SUBSCRIPTION_PLANS.get(upgrade_data.target_tier)
            if not plan:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的订阅计划"
                )

            # 创建支付请求
            payment_request = PaymentRequest(
                payment_type="subscription",
                amount_sol=plan.price_sol,
                description=f"升级到 {plan.name}",
                wallet_address=upgrade_data.wallet_address or current_user.wallet_address or "",
                subscription_tier=upgrade_data.target_tier.value
            )

            return {
                "payment_required": True,
                "payment_details": {
                    "amount_sol": plan.price_sol,
                    "amount_usd": plan.price_usd,
                    "description": f"升级到 {plan.name}",
                    "recipient_address": "MemeMasterAI_Payment_Address",  # 实际的收款地址
                    "memo": f"subscription_{current_user.id}_{upgrade_data.target_tier.value}"
                },
                "plan_details": plan.model_dump()
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"升级订阅失败: {str(e)}"
            )

    # 付费功能端点
    @app.post("/api/payment/pay-per-use")
    async def create_pay_per_use_payment(
        payment_request: PaymentRequest,
        current_user: User = Depends(get_current_active_user)
    ):
        """创建按次付费支付"""
        try:
            # 检查付费类型是否有效
            if payment_request.payment_type not in PAY_PER_USE_PRICES:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的付费类型"
                )

            price_info = PAY_PER_USE_PRICES[payment_request.payment_type]
            expected_amount = price_info["price_sol"]

            # 验证支付金额
            if abs(payment_request.amount_sol - expected_amount) > 0.001:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"支付金额不正确，应为 {expected_amount} SOL"
                )

            return {
                "payment_required": True,
                "payment_details": {
                    "amount_sol": expected_amount,
                    "description": price_info["description"],
                    "recipient_address": "MemeMasterAI_Payment_Address",
                    "memo": f"{payment_request.payment_type}_{current_user.id}_{int(time.time())}"
                },
                "feature_info": price_info
            }

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建支付失败: {str(e)}"
            )

    @app.post("/api/payment/confirm")
    async def confirm_payment(
        confirmation: PaymentConfirmation,
        current_user: User = Depends(get_current_active_user)
    ):
        """确认支付"""
        try:
            # 这里应该验证区块链交易
            # 暂时返回成功状态
            return {
                "status": "confirmed",
                "transaction_hash": confirmation.transaction_hash,
                "message": "支付确认成功，功能已激活"
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"确认支付失败: {str(e)}"
            )

    # 功能访问控制端点
    @app.get("/api/features/check/{feature_name}")
    async def check_feature_access_endpoint(
        feature_name: str,
        current_user: User = Depends(get_current_active_user)
    ):
        """检查功能访问权限"""
        try:
            has_access = FeatureChecker.check_feature_access(current_user, feature_name)
            features = FeatureChecker.get_user_features(current_user)
            limits = FeatureChecker.get_user_limits(current_user)

            return {
                "feature_name": feature_name,
                "has_access": has_access,
                "subscription_tier": current_user.role.value,
                "all_features": features,
                "usage_limits": limits
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"检查功能权限失败: {str(e)}"
            )

    @app.get("/api/usage/summary")
    async def get_usage_summary(current_user: User = Depends(get_current_active_user)):
        """获取用户使用摘要"""
        try:
            features = FeatureChecker.get_user_features(current_user)
            limits = FeatureChecker.get_user_limits(current_user)
            trial_status = FeatureChecker.check_trial_status(current_user)
            content_types = FeatureChecker.get_content_type_access(current_user)
            data_sources = FeatureChecker.get_data_source_access(current_user)

            return {
                "user_id": current_user.id,
                "subscription_tier": current_user.role.value,
                "trial_status": trial_status,
                "features": features,
                "usage_limits": limits,
                "current_usage": {
                    "daily_hotspots_used": current_user.daily_hotspots_used,
                    "strategies_created": current_user.total_strategies_created,
                    "deployments": current_user.total_deployments,
                    "paid_features_used": current_user.total_paid_features_used
                },
                "access_permissions": {
                    "content_types": content_types,
                    "data_sources": data_sources
                }
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取使用摘要失败: {str(e)}"
            )

    # 管理员功能端点
    @app.get("/api/admin/stats")
    async def get_admin_stats(current_user: User = Depends(get_current_active_user)):
        """获取管理员统计数据"""
        if current_user.role != "enterprise":  # 简化的管理员检查
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

        try:
            from src.subscription.subscription_manager import SubscriptionManager
            from src.subscription.usage_tracker import UsageTracker
            from src.database.payment_db import PaymentDatabase

            subscription_manager = SubscriptionManager()
            usage_tracker = UsageTracker()
            payment_db = PaymentDatabase()

            # 获取各种统计数据
            subscription_stats = await subscription_manager.get_subscription_analytics()
            usage_stats = await usage_tracker.get_usage_analytics()
            payment_stats = await payment_db.get_payment_stats()

            # 用户统计
            from src.database.user_db import UserDatabase
            user_db = UserDatabase()
            total_users = await user_db.count_users()
            free_users = await user_db.count_users(role="free")
            pro_users = await user_db.count_users(role="pro")
            enterprise_users = await user_db.count_users(role="enterprise")

            return {
                "overview": {
                    "total_users": total_users,
                    "free_users": free_users,
                    "pro_users": pro_users,
                    "enterprise_users": enterprise_users,
                    "conversion_rate": (pro_users + enterprise_users) / max(total_users, 1) * 100
                },
                "subscriptions": subscription_stats,
                "usage": usage_stats,
                "payments": payment_stats,
                "generated_at": datetime.now()
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取管理员统计失败: {str(e)}"
            )

    @app.get("/api/admin/users")
    async def get_admin_users(
        skip: int = 0,
        limit: int = 50,
        role: Optional[str] = None,
        current_user: User = Depends(get_current_active_user)
    ):
        """获取用户列表（管理员）"""
        if current_user.role != "enterprise":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要管理员权限"
            )

        try:
            from src.database.user_db import UserDatabase
            user_db = UserDatabase()

            users = await user_db.list_users(skip=skip, limit=limit, role=role)
            total_count = await user_db.count_users(role=role)

            return {
                "users": [
                    {
                        "id": user.id,
                        "email": user.email,
                        "username": user.username,
                        "role": user.role,
                        "status": user.status,
                        "created_at": user.created_at,
                        "last_login": user.last_login,
                        "total_strategies": user.total_strategies_created,
                        "total_deployments": user.total_deployments
                    }
                    for user in users
                ],
                "total": total_count,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取用户列表失败: {str(e)}"
            )

    # 高级分析端点
    @app.get("/api/analytics/revenue")
    async def get_revenue_analytics(
        period: str = "daily",
        days: int = 30,
        current_user: User = Depends(get_current_active_user)
    ):
        """获取收入分析"""
        if current_user.role not in ["pro", "enterprise"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="需要Pro或更高级别订阅"
            )

        try:
            from src.database.payment_db import PaymentDatabase
            payment_db = PaymentDatabase()

            revenue_data = await payment_db.get_revenue_by_period(period, days)

            return {
                "period": period,
                "days": days,
                "revenue_data": revenue_data,
                "total_revenue_sol": sum(item["revenue_sol"] for item in revenue_data),
                "total_payments": sum(item["payment_count"] for item in revenue_data)
            }

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取收入分析失败: {str(e)}"
            )

    # 折扣码功能
    @app.post("/api/subscription/apply-discount")
    async def apply_discount_code(
        discount_code: str,
        current_user: User = Depends(get_current_active_user)
    ):
        """应用折扣码"""
        try:
            from src.subscription.subscription_manager import SubscriptionManager
            subscription_manager = SubscriptionManager()

            discount_info = await subscription_manager.apply_discount(current_user.id, discount_code)

            return discount_info

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"应用折扣码失败: {str(e)}"
            )

# 新增：钱包管理API（抗MEV版）
@app.post("/api/wallet/batch-generate")
async def batch_generate_wallets(
    count: int = 50,
    chains: list = ["ethereum", "solana"],
    current_user: User = Depends(get_current_active_user)
):
    """批量生成钱包（抗MEV版）"""
    try:
        import secrets
        import uuid

        funding_config = {'eth': 0.1, 'sol': 1.0, 'usdc': 100}

        wallets = []
        for i in range(count):
            for chain in chains:
                if chain == 'ethereum':
                    private_key = secrets.token_hex(32)
                    # 添加随机变化 ±10%
                    eth_amount = funding_config.get('eth', 0.1) * (0.9 + random.random() * 0.2)
                    usdc_amount = funding_config.get('usdc', 100) * (0.9 + random.random() * 0.2)

                    wallet = {
                        'id': str(uuid.uuid4()),
                        'address': f"0x{secrets.token_hex(20)}",
                        'private_key': private_key,
                        'network': chain,
                        'balance': {'eth': round(eth_amount, 4), 'usdc': round(usdc_amount, 2)},
                        'status': 'active',
                        'antiBan': {
                            'proxyAssigned': f"proxy_{random.randint(1, 100)}",
                            'behaviorProfile': f"profile_{random.randint(1, 20)}",
                            'lastActivity': datetime.now().isoformat()
                        }
                    }
                else:  # solana
                    sol_amount = funding_config.get('sol', 1.0) * (0.9 + random.random() * 0.2)
                    usdc_amount = funding_config.get('usdc', 100) * (0.9 + random.random() * 0.2)

                    wallet = {
                        'id': str(uuid.uuid4()),
                        'address': f"Sol{secrets.token_hex(16)}",
                        'private_key': secrets.token_hex(32),
                        'network': chain,
                        'balance': {'sol': round(sol_amount, 4), 'usdc': round(usdc_amount, 2)},
                        'status': 'active',
                        'antiBan': {
                            'proxyAssigned': f"proxy_{random.randint(1, 100)}",
                            'behaviorProfile': f"profile_{random.randint(1, 20)}",
                            'lastActivity': datetime.now().isoformat()
                        }
                    }
                wallets.append(wallet)

        return {
            'success': True,
            'wallets': wallets,
            'count': len(wallets),
            'antiBanMetrics': {
                'evasionRate': round(90 + random.random() * 8, 1),
                'behaviorDisguise': True,
                'currentRate': round(2 + random.random() * 2, 1),
                'platformResponse': '正常',
                'adaptiveStatus': '监控中'
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量生成钱包失败: {str(e)}")

# 新增：MEV防护状态API
@app.get("/api/liquidity/mev-protection")
async def get_mev_protection_status(current_user: User = Depends(get_current_active_user)):
    """获取MEV防护状态"""
    try:
        return {
            'success': True,
            'mevProtection': {
                'flashbotsIntegration': {
                    'status': 'connected',
                    'bundleSuccessRate': round(90 + random.random() * 8, 1),
                    'averagePriorityFee': round(2 + random.random() * 3, 1),
                    'lossReduction': round(80 + random.random() * 10, 1)
                },
                'privateMempool': {
                    'enabled': True,
                    'transactionRandomization': True,
                    'delayRange': '2-8秒',
                    'frontrunInterceptions': random.randint(10, 20)
                },
                'statistics': {
                    'totalSavings': random.randint(100000, 200000),
                    'attacksBlocked': random.randint(80, 120),
                    'protectionEfficiency': round(95 + random.random() * 4, 1)
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MEV防护状态失败: {str(e)}")

# 新增：鲸鱼监控API
@app.get("/api/liquidity/whale-activity")
async def get_whale_activity(current_user: User = Depends(get_current_active_user)):
    """获取鲸鱼活动监控数据"""
    try:
        import secrets

        activities = []
        for i in range(5):
            activity = {
                'address': f"0x{secrets.token_hex(4)}...{secrets.token_hex(4)}",
                'action': random.choice(['大额买入', '流动性撤出', '大额卖出', '流动性添加']),
                'amount': f"${random.randint(1, 5)}.{random.randint(1, 9)}M",
                'impact': f"{'+' if random.random() > 0.5 else '-'}{random.randint(1, 8)}.{random.randint(1, 9)}%",
                'time': f"{random.randint(1, 30)}分钟前",
                'risk': random.choice(['low', 'medium', 'high'])
            }
            activities.append(activity)

        return {
            'success': True,
            'whaleActivity': activities,
            'nansenData': {
                'smartMoneyInflow': random.randint(5, 15),
                'whaleTransactions': random.randint(15, 35),
                'monitoredAddresses': random.randint(200, 300),
                'alertsTriggered': random.randint(3, 8)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取鲸鱼活动失败: {str(e)}")

# 新增：滑点优化API
@app.get("/api/liquidity/slippage-optimization")
async def get_slippage_optimization(current_user: User = Depends(get_current_active_user)):
    """获取滑点优化数据"""
    try:
        return {
            'success': True,
            'slippageOptimization': {
                'oneinch': {
                    'status': 'connected',
                    'supportedChains': 4,
                    'successRate': round(95 + random.random() * 4, 1),
                    'averageSavings': round(15 + random.random() * 8, 1),
                    'todaySavings': random.randint(10000, 20000)
                },
                'paraswap': {
                    'status': 'connected',
                    'supportedChains': 5,
                    'successRate': round(92 + random.random() * 6, 1),
                    'averageSavings': round(12 + random.random() * 8, 1),
                    'todaySavings': random.randint(8000, 15000)
                },
                'orderSplitting': {
                    'activeSplitOrders': random.randint(5, 10),
                    'averageSplits': round(3 + random.random() * 2, 1),
                    'slippageReduction': round(60 + random.random() * 15, 0)
                }
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取滑点优化数据失败: {str(e)}")

# WebSocket 功能暂时禁用，专注于基本 API 功能

if __name__ == "__main__":
    print("🚀 启动MemeMaster AI Web服务...")
    print("📊 Web界面: http://0.0.0.0:8001")
    print("🔗 外部访问: http://*************:8001")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info"
    )
