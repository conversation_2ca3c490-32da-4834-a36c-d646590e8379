#!/usr/bin/env python3
"""
MemeMaster AI 仪表盘启动脚本
同时启动前端和后端服务
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class DashboardLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查 Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            print(f"   ✓ Node.js: {result.stdout.strip()}")
        except FileNotFoundError:
            print("   ✗ Node.js 未安装")
            return False
            
        # 检查 npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            print(f"   ✓ npm: {result.stdout.strip()}")
        except FileNotFoundError:
            print("   ✗ npm 未安装")
            return False
            
        # 检查 Python 包
        required_packages = ['fastapi', 'uvicorn', 'psutil']
        for package in required_packages:
            try:
                __import__(package)
                print(f"   ✓ {package}")
            except ImportError:
                print(f"   ✗ {package} 未安装")
                return False
                
        return True
    
    def install_frontend_deps(self):
        """安装前端依赖"""
        print("📦 安装前端依赖...")
        
        if not Path('node_modules').exists():
            try:
                subprocess.run(['npm', 'install'], check=True)
                print("   ✓ 前端依赖安装完成")
            except subprocess.CalledProcessError:
                print("   ✗ 前端依赖安装失败")
                return False
        else:
            print("   ✓ 前端依赖已存在")
            
        return True
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, '-m', 'uvicorn', 
                'web_app:app', 
                '--host', '0.0.0.0', 
                '--port', '8000',
                '--reload'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待后端启动
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("   ✓ 后端服务启动成功 (http://localhost:8000)")
                return True
            else:
                print("   ✗ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"   ✗ 后端启动错误: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端服务...")
        
        try:
            self.frontend_process = subprocess.Popen([
                'npm', 'run', 'frontend:dev'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待前端启动
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("   ✓ 前端服务启动成功 (http://localhost:3000)")
                return True
            else:
                print("   ✗ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"   ✗ 前端启动错误: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            
            # 检查后端进程
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️  后端进程意外退出")
                self.running = False
                break
                
            # 检查前端进程
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️  前端进程意外退出")
                self.running = False
                break
    
    def cleanup(self):
        """清理进程"""
        print("\n🛑 正在停止服务...")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("   ✓ 前端服务已停止")
            except:
                self.frontend_process.kill()
                print("   ✓ 前端服务已强制停止")
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("   ✓ 后端服务已停止")
            except:
                self.backend_process.kill()
                print("   ✓ 后端服务已强制停止")
    
    def signal_handler(self, signum, frame):
        """信号处理"""
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """运行仪表盘"""
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        print("🚀 MemeMaster AI 仪表盘启动器")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败，请安装缺失的依赖")
            return False
        
        # 安装前端依赖
        if not self.install_frontend_deps():
            print("❌ 前端依赖安装失败")
            return False
        
        # 启动后端
        if not self.start_backend():
            print("❌ 后端启动失败")
            return False
        
        # 启动前端
        if not self.start_frontend():
            print("❌ 前端启动失败")
            self.cleanup()
            return False
        
        print("\n🎉 MemeMaster AI 仪表盘启动成功！")
        print("=" * 50)
        print("📊 前端界面: http://localhost:3000")
        print("🔧 后端API:  http://localhost:8000")
        print("📚 API文档:  http://localhost:8000/docs")
        print("\n按 Ctrl+C 停止服务")
        print("-" * 50)
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_processes)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 等待用户中断
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        self.cleanup()
        return True

def main():
    launcher = DashboardLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
