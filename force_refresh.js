// 强制刷新浏览器缓存的脚本
import puppeteer from 'puppeteer';

async function forceRefresh() {
    console.log('🔄 强制刷新浏览器缓存...');
    
    const browser = await puppeteer.launch({
        headless: false,
        defaultViewport: { width: 1920, height: 1080 }
    });
    
    try {
        const page = await browser.newPage();
        
        // 清除缓存
        await page.setCacheEnabled(false);
        
        // 访问策略管理页面并强制刷新
        console.log('📱 访问策略管理页面并强制刷新...');
        await page.goto('http://localhost:8001/strategy', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 强制刷新页面
        await page.reload({ waitUntil: 'networkidle2' });
        
        // 再等待一下
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('✅ 页面已强制刷新，请检查标题显示');
        
        // 保持浏览器打开以便观察
        console.log('🔍 浏览器将保持打开状态，请手动检查页面标题是否正确显示');
        console.log('📝 如果标题仍然垂直显示，请按 Ctrl+C 停止脚本');
        
        // 等待用户手动检查
        await new Promise(resolve => {
            process.on('SIGINT', () => {
                console.log('\n👋 用户停止检查，关闭浏览器...');
                resolve();
            });
        });
        
    } catch (error) {
        console.error('❌ 强制刷新过程中出现错误:', error);
    } finally {
        await browser.close();
    }
}

forceRefresh().catch(console.error);
