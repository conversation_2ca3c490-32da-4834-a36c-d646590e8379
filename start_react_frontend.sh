#!/bin/bash

echo "🚀 MemeMaster AI React前端启动脚本"
echo "=================================="

# 进入项目目录
cd /Users/<USER>/Dapp-env/MemeMasterAI

# 清理旧进程
echo "🔧 清理旧进程..."
pkill -f "npm run frontend:dev" 2>/dev/null
pkill -f "python3 working_server.py" 2>/dev/null
sleep 2

# 启动后端
echo "📡 启动后端服务..."
python3 working_server.py &
BACKEND_PID=$!
echo "   后端 PID: $BACKEND_PID"

# 等待后端启动
echo "⏳ 等待后端启动..."
sleep 5

# 检查后端是否启动成功
if curl -s http://localhost:8001/health > /dev/null 2>&1; then
    echo "✅ 后端启动成功"
else
    echo "❌ 后端启动失败"
    exit 1
fi

# 启动前端
echo "🎨 启动React前端..."
npm run frontend:dev &
FRONTEND_PID=$!
echo "   前端 PID: $FRONTEND_PID"

# 等待前端启动
echo "⏳ 等待前端启动..."
sleep 10

# 检查前端是否启动成功
if curl -s http://localhost:3000/ > /dev/null 2>&1; then
    echo "✅ React前端启动成功"
else
    echo "⚠️ 前端可能还在启动中..."
fi

echo ""
echo "🎉 MemeMaster AI 启动完成！"
echo "================================"
echo "🌐 React前端: http://localhost:3000/"
echo "📡 后端API: http://localhost:8001/"
echo "📚 API文档: http://localhost:8001/docs"
echo ""
echo "💡 提示:"
echo "   • React前端是主要界面"
echo "   • 后端提供API服务"
echo "   • 按 Ctrl+C 停止所有服务"
echo ""

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "✅ 所有服务已停止"; exit 0' INT

# 保持脚本运行
echo "系统运行中... 按 Ctrl+C 停止"
while true; do
    sleep 1
done
