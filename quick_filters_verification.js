/**
 * 快速验证高级筛选功能
 * 简化版验证脚本，用于快速检查核心功能
 */

const puppeteer = require('puppeteer');

async function quickFiltersVerification() {
  console.log('🚀 开始快速验证高级筛选功能...');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 导航到页面
    console.log('📍 导航到热点监控页面...');
    await page.goto('http://localhost:3002/hotspot', { waitUntil: 'networkidle2' });
    
    // 2. 验证页面加载
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.$eval('h1', el => el.textContent);
    console.log(`✅ 页面标题: ${title}`);
    
    // 3. 确保在实时热点标签页
    console.log('🔄 确保在实时热点标签页...');
    const hotspotTab = await page.$('button:has-text("实时热点")');
    if (hotspotTab) {
      await hotspotTab.click();
      await page.waitForTimeout(1000);
    }
    
    // 4. 验证高级筛选按钮
    console.log('🔍 验证高级筛选按钮...');
    const filtersButton = await page.$('button:has-text("高级筛选")');
    if (filtersButton) {
      console.log('✅ 高级筛选按钮存在');
      
      // 点击高级筛选按钮
      await filtersButton.click();
      await page.waitForTimeout(2000);
      
      // 验证按钮激活状态
      const isActive = await page.$eval('button:has-text("高级筛选")', 
        el => el.classList.contains('bg-blue-500/20')
      );
      
      if (isActive) {
        console.log('✅ 高级筛选按钮激活状态正确');
      } else {
        console.log('⚠️ 高级筛选按钮激活状态异常');
      }
    } else {
      console.log('❌ 高级筛选按钮未找到');
    }
    
    // 5. 验证筛选面板
    console.log('📊 验证筛选面板...');
    const panelTitle = await page.$('h3:has-text("高级筛选器")');
    if (panelTitle) {
      console.log('✅ 高级筛选器面板显示正确');
    } else {
      console.log('❌ 高级筛选器面板未找到');
    }
    
    // 6. 验证操作按钮
    const resetButton = await page.$('button:has-text("重置")');
    const applyButton = await page.$('button:has-text("应用筛选")');
    const closeButton = await page.$('button svg'); // X按钮
    
    if (resetButton && applyButton && closeButton) {
      console.log('✅ 筛选操作按钮完整');
    } else {
      console.log('❌ 筛选操作按钮缺失');
    }
    
    // 7. 验证筛选模块
    const modules = [
      '分类筛选',
      '数据源',
      '风险等级',
      '语言',
      '地区'
    ];
    
    for (const module of modules) {
      const moduleElement = await page.$(`label:has-text("${module}")`);
      if (moduleElement) {
        console.log(`✅ ${module} 模块存在`);
      } else {
        console.log(`❌ ${module} 模块缺失`);
      }
    }
    
    // 8. 验证分类选项
    console.log('🏷️ 验证分类选项...');
    const categories = ['政治', '科技', '亚文化', '元宇宙'];
    let categoryCount = 0;
    
    for (const category of categories) {
      const categoryElement = await page.$(`span:has-text("${category}")`);
      if (categoryElement) categoryCount++;
    }
    console.log(`✅ 找到 ${categoryCount}/${categories.length} 个分类选项`);
    
    // 9. 验证数据源选项
    console.log('🌐 验证数据源选项...');
    const sources = ['Twitter', 'Reddit', 'TikTok', 'YouTube'];
    let sourceCount = 0;
    
    for (const source of sources) {
      const sourceElement = await page.$(`span:has-text("${source}")`);
      if (sourceElement) sourceCount++;
    }
    console.log(`✅ 找到 ${sourceCount}/${sources.length} 个数据源选项`);
    
    // 10. 验证控件数量
    const checkboxes = await page.$$('input[type="checkbox"]');
    const sliders = await page.$$('input[type="range"]');
    
    console.log(`✅ 控件统计: ${checkboxes.length} 个复选框, ${sliders.length} 个滑块`);
    
    // 11. 测试交互功能
    console.log('🎯 测试交互功能...');
    
    // 测试复选框
    if (checkboxes.length > 0) {
      await checkboxes[0].click();
      await page.waitForTimeout(300);
      console.log('✅ 复选框交互正常');
    }
    
    // 测试滑块
    if (sliders.length > 0) {
      await sliders[0].click();
      await page.waitForTimeout(300);
      console.log('✅ 滑块交互正常');
    }
    
    // 测试应用筛选按钮
    if (applyButton) {
      await applyButton.click();
      await page.waitForTimeout(500);
      console.log('✅ 应用筛选按钮交互正常');
    }
    
    // 测试重置按钮
    if (resetButton) {
      await resetButton.click();
      await page.waitForTimeout(500);
      console.log('✅ 重置按钮交互正常');
    }
    
    // 12. 验证风险等级选项
    console.log('⚠️ 验证风险等级选项...');
    const riskLevels = ['低风险', '中风险', '高风险'];
    let riskCount = 0;
    
    for (const risk of riskLevels) {
      const riskElement = await page.$(`span:has-text("${risk}")`);
      if (riskElement) riskCount++;
    }
    console.log(`✅ 找到 ${riskCount}/${riskLevels.length} 个风险等级选项`);
    
    // 13. 验证语言选项
    console.log('🌍 验证语言选项...');
    const languages = ['英语', '中文', '韩语', '日语'];
    let languageCount = 0;
    
    for (const lang of languages) {
      const langElement = await page.$(`span:has-text("${lang}")`);
      if (langElement) languageCount++;
    }
    console.log(`✅ 找到 ${languageCount}/${languages.length} 个语言选项`);
    
    // 14. 验证地区选项
    console.log('🌏 验证地区选项...');
    const regions = ['北美', '欧洲', '亚太', '中国'];
    let regionCount = 0;
    
    for (const region of regions) {
      const regionElement = await page.$(`span:has-text("${region}")`);
      if (regionElement) regionCount++;
    }
    console.log(`✅ 找到 ${regionCount}/${regions.length} 个地区选项`);
    
    // 15. 验证置信度滑块
    console.log('📊 验证置信度滑块...');
    const confidenceLabel = await page.$('label:has-text("最小置信度")');
    if (confidenceLabel) {
      console.log('✅ 置信度滑块标签存在');
    }
    
    // 16. 截图保存
    await page.screenshot({ 
      path: 'advanced_filters_verification.png',
      fullPage: true 
    });
    console.log('📸 验证截图已保存: advanced_filters_verification.png');
    
    console.log('\n🎉 快速验证完成! 高级筛选功能基本正常');
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行快速验证
quickFiltersVerification().catch(console.error);
