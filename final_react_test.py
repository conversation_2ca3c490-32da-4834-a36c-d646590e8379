#!/usr/bin/env python3
"""
React前端最终测试脚本
"""

import requests
import webbrowser
import time

def test_react_frontend():
    """测试React前端"""
    print("🎯 MemeMaster AI React前端最终测试")
    print("=" * 50)
    
    # 测试React主页
    try:
        print("🔍 测试React主页...")
        response = requests.get("http://localhost:3002/", timeout=10)
        
        if response.status_code == 200:
            print("✅ React服务器响应正常 (HTTP 200)")
            
            content = response.text
            if "<!DOCTYPE html>" in content:
                print("✅ 返回HTML内容")
                
                if "root" in content:
                    print("✅ 包含React根元素")
                    
                    if "MemeMaster AI" in content:
                        print("✅ 包含应用标题")
                    else:
                        print("⚠️ 未找到应用标题")
                else:
                    print("❌ 缺少React根元素")
            else:
                print("❌ 响应不是HTML格式")
                print(f"响应内容: {content[:200]}...")
                
        else:
            print(f"❌ React服务器响应异常: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到React服务器")
        print("请确保React开发服务器正在运行: npm run frontend:dev")
        return False
    except Exception as e:
        print(f"❌ 测试React服务器时出错: {e}")
        return False
    
    # 测试路由
    print("\n🔍 测试React路由...")
    routes = ["/", "/test", "/simple"]
    
    for route in routes:
        try:
            response = requests.get(f"http://localhost:3002{route}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {route} - 正常")
            else:
                print(f"❌ {route} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {route} - 错误: {e}")
    
    # 测试后端API
    print("\n🔍 测试后端API...")
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API正常")
        else:
            print(f"⚠️ 后端API异常: HTTP {response.status_code}")
    except:
        print("⚠️ 后端API无法连接")
    
    # 测试HTML备用方案
    print("\n🔍 测试HTML备用方案...")
    try:
        response = requests.get("http://localhost:8090/simple_dashboard.html", timeout=5)
        if response.status_code == 200:
            print("✅ HTML仪表盘可用")
        else:
            print("⚠️ HTML仪表盘不可用")
    except:
        print("⚠️ HTML服务器无法连接")
    
    return True

def main():
    success = test_react_frontend()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    
    if success:
        print("🎉 React前端测试完成！")
        print("\n📱 可用的访问地址:")
        print("   • React主页: http://localhost:3002/")
        print("   • React测试: http://localhost:3002/test")
        print("   • React仪表盘: http://localhost:3002/simple")
        print("   • HTML仪表盘: http://localhost:8090/simple_dashboard.html")
        
        print("\n🌐 自动打开React主页...")
        try:
            webbrowser.open("http://localhost:3002/")
            print("✅ 已在浏览器中打开React主页")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问")
            
        print("\n💡 如果页面仍然显示空白:")
        print("   1. 按F12打开开发者工具查看错误")
        print("   2. 按Ctrl+Shift+R强制刷新页面")
        print("   3. 检查Console标签页的错误信息")
        print("   4. 尝试访问: http://localhost:3002/test")
        
    else:
        print("❌ React前端测试失败")
        print("\n🔧 建议的解决步骤:")
        print("   1. 重启React服务: npm run frontend:dev")
        print("   2. 检查端口3002是否被占用")
        print("   3. 查看终端错误信息")
        print("   4. 使用HTML备用方案")

if __name__ == "__main__":
    main()
