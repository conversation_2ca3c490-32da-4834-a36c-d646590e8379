#!/usr/bin/env python3
"""
最简单的测试服务器
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
import uvicorn
from datetime import datetime

app = FastAPI(title="MemeMaster AI - 简单测试服务器")

@app.get("/")
async def root():
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>MemeMaster AI - 测试服务器</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
            .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }}
            h1 {{ color: #333; text-align: center; }}
            .status {{ padding: 15px; margin: 20px 0; background: #d4edda; color: #155724; border-radius: 5px; }}
            .links a {{ display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 MemeMaster AI 测试服务器</h1>
            <div class="status">
                ✅ 服务器运行正常 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
            </div>
            <div class="links">
                <a href="/docs">📚 API 文档</a>
                <a href="/health">🏥 健康检查</a>
                <a href="/test">🧪 测试端点</a>
            </div>
        </div>
    </body>
    </html>
    """)

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "服务器运行正常"
    }

@app.get("/test")
async def test():
    return {
        "message": "测试端点工作正常",
        "timestamp": datetime.now().isoformat(),
        "server": "MemeMaster AI"
    }

if __name__ == "__main__":
    print("🚀 启动简单测试服务器...")
    print("🌐 访问地址: http://localhost:8001/")
    print("📚 API文档: http://localhost:8001/docs")
    print("按 Ctrl+C 停止服务器")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
