#!/usr/bin/env python3
"""
MemeMaster AI 系统状态检查工具
"""

import requests
import sys
import time
from datetime import datetime

def print_banner():
    print("""
╔══════════════════════════════════════════════════════════════╗
║                MemeMaster AI 状态检查工具                    ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_backend():
    """检查后端服务器状态"""
    print("🔍 检查后端服务器...")
    
    try:
        # 检查健康状态
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端服务器运行正常")
            print(f"   📡 地址: http://localhost:8001/")
            print(f"   ⏱️ 运行时间: {data.get('uptime', 0):.1f} 秒")
            print(f"   🔧 用户管理: {'启用' if data.get('user_management_available') else '禁用'}")
            print(f"   📅 时间戳: {data.get('timestamp', 'N/A')}")
            return True
        else:
            print(f"❌ 后端服务器响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 后端服务器连接失败 (端口8001)")
        print("   💡 请确保后端服务器已启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 后端服务器响应超时")
        return False
    except Exception as e:
        print(f"❌ 后端服务器检查失败: {e}")
        return False

def check_frontend():
    """检查前端应用状态"""
    print("\n🔍 检查前端应用...")
    
    try:
        # 检查前端页面
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ 前端应用运行正常")
            print(f"   🌐 地址: http://localhost:3000/")
            print(f"   🎨 框架: React + Vite")
            return True
        else:
            print(f"❌ 前端应用响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 前端应用连接失败 (端口3000)")
        print("   💡 请确保前端应用已启动")
        return False
    except requests.exceptions.Timeout:
        print("❌ 前端应用响应超时")
        return False
    except Exception as e:
        print(f"❌ 前端应用检查失败: {e}")
        return False

def check_api_endpoints():
    """检查API端点"""
    print("\n🔍 检查API端点...")
    
    endpoints = [
        ("/api/system/info", "系统信息"),
        ("/api/hotspot/current", "热点数据"),
        ("/health", "健康检查"),
    ]
    
    working_endpoints = 0
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8001{endpoint}", timeout=3)
            if response.status_code == 200:
                print(f"   ✅ {description}: {endpoint}")
                working_endpoints += 1
            else:
                print(f"   ❌ {description}: {endpoint} (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {description}: {endpoint} (错误: {str(e)[:50]})")
    
    print(f"\n📊 API端点状态: {working_endpoints}/{len(endpoints)} 正常")
    return working_endpoints == len(endpoints)

def check_user_management():
    """检查用户管理系统"""
    print("\n🔍 检查用户管理系统...")
    
    try:
        # 检查订阅计划端点
        response = requests.get("http://localhost:8001/api/subscription/plans", timeout=5)
        if response.status_code == 200:
            data = response.json()
            plans = data.get('plans', [])
            print("✅ 用户管理系统可用")
            print(f"   📋 订阅计划: {len(plans)} 个")
            for plan in plans:
                tier = plan.get('tier', 'unknown')
                price = plan.get('price_sol', 0)
                print(f"      • {tier}: {price} SOL")
            return True
        else:
            print(f"❌ 用户管理系统响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️ 用户管理系统不可用: {str(e)[:50]}")
        print("   💡 可能需要安装依赖: pip install pyjwt bcrypt aiosqlite")
        return False

def check_proxy_configuration():
    """检查代理配置"""
    print("\n🔍 检查前端代理配置...")
    
    try:
        # 通过前端代理访问后端API
        response = requests.get("http://localhost:3000/api/system/info", timeout=5)
        if response.status_code == 200:
            print("✅ 前端代理配置正常")
            print("   🔗 前端可以正常访问后端API")
            return True
        else:
            print(f"❌ 前端代理配置异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"⚠️ 前端代理检查失败: {str(e)[:50]}")
        print("   💡 请检查vite.config.js中的代理配置")
        return False

def generate_report(results):
    """生成状态报告"""
    print("\n" + "="*60)
    print("📋 MemeMaster AI 系统状态报告")
    print("="*60)
    
    total_checks = len(results)
    passed_checks = sum(1 for result in results.values() if result)
    
    print(f"\n📊 总体状态: {passed_checks}/{total_checks} 项检查通过")
    
    if passed_checks == total_checks:
        print("🎉 系统运行完全正常！")
        status = "优秀"
    elif passed_checks >= total_checks * 0.8:
        print("✅ 系统基本正常，有少量问题")
        status = "良好"
    elif passed_checks >= total_checks * 0.5:
        print("⚠️ 系统部分功能异常")
        status = "一般"
    else:
        print("❌ 系统存在严重问题")
        status = "差"
    
    print(f"\n🏆 系统评级: {status}")
    
    print("\n📋 详细结果:")
    for check_name, result in results.items():
        status_icon = "✅" if result else "❌"
        print(f"   {status_icon} {check_name}")
    
    print(f"\n⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if passed_checks < total_checks:
        print("\n💡 建议:")
        if not results.get("后端服务器"):
            print("   • 启动后端服务器: python3 working_server.py")
        if not results.get("前端应用"):
            print("   • 启动前端应用: npm run frontend:dev")
        if not results.get("用户管理系统"):
            print("   • 安装用户管理依赖: pip install pyjwt bcrypt aiosqlite")
        print("   • 使用完整启动脚本: ./start_system.sh")
    
    print("\n" + "="*60)

def main():
    """主函数"""
    print_banner()
    
    # 执行各项检查
    results = {}
    
    results["后端服务器"] = check_backend()
    results["前端应用"] = check_frontend()
    results["API端点"] = check_api_endpoints()
    results["用户管理系统"] = check_user_management()
    results["代理配置"] = check_proxy_configuration()
    
    # 生成报告
    generate_report(results)
    
    # 返回退出码
    all_passed = all(results.values())
    sys.exit(0 if all_passed else 1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 检查过程出错: {e}")
        sys.exit(1)
