#!/usr/bin/env python3
"""
MemeMaster AI 自动启动系统
确保每次都能正确启动React前端和后端服务
"""

import os
import sys
import time
import signal
import subprocess
import threading
import json
import psutil
from datetime import datetime

class SystemManager:
    """系统管理器 - 确保服务持续运行"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        self.pid_file = os.path.join(self.project_root, '.system_pids.json')
        self.processes = {}
        self.running = True
        
    def check_port_in_use(self, port):
        """检查端口是否被占用"""
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                return True
        return False
    
    def kill_process_on_port(self, port):
        """杀死占用指定端口的进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                            print(f"🔄 终止占用端口{port}的进程 PID:{proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=3)
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"清理端口{port}时出错: {e}")
        return False
    
    def save_pids(self):
        """保存进程PID到文件"""
        try:
            pids = {}
            for name, process in self.processes.items():
                if process and process.poll() is None:
                    pids[name] = process.pid
            
            with open(self.pid_file, 'w') as f:
                json.dump(pids, f)
        except Exception as e:
            print(f"保存PID文件失败: {e}")
    
    def load_and_cleanup_old_processes(self):
        """加载并清理旧进程"""
        try:
            if os.path.exists(self.pid_file):
                with open(self.pid_file, 'r') as f:
                    old_pids = json.load(f)
                
                for name, pid in old_pids.items():
                    try:
                        proc = psutil.Process(pid)
                        print(f"🔄 清理旧的{name}进程 PID:{pid}")
                        proc.terminate()
                        proc.wait(timeout=3)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
                os.remove(self.pid_file)
        except Exception as e:
            print(f"清理旧进程失败: {e}")
    
    def ensure_dependencies(self):
        """确保依赖已安装"""
        print("🔍 检查依赖...")
        
        # 检查Python依赖
        python_deps = ['fastapi', 'uvicorn']
        missing_deps = []
        
        for dep in python_deps:
            try:
                __import__(dep)
            except ImportError:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"📦 安装缺失的Python依赖: {missing_deps}")
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + missing_deps, check=True)
        
        # 检查Node.js依赖
        if not os.path.exists(os.path.join(self.project_root, 'node_modules')):
            print("📦 安装Node.js依赖...")
            subprocess.run(['npm', 'install'], cwd=self.project_root, check=True)
        
        print("✅ 依赖检查完成")
    
    def start_backend(self):
        """启动后端服务器"""
        print("🚀 启动后端服务器...")
        
        # 清理端口
        if self.check_port_in_use(8001):
            self.kill_process_on_port(8001)
            time.sleep(2)
        
        try:
            # 确保使用working_server.py
            backend_script = os.path.join(self.project_root, 'working_server.py')
            if not os.path.exists(backend_script):
                print("❌ working_server.py 不存在")
                return False
            
            process = subprocess.Popen([
                sys.executable, backend_script
            ], cwd=self.project_root, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes['backend'] = process
            
            # 等待启动
            for i in range(10):
                time.sleep(1)
                if process.poll() is not None:
                    stdout, stderr = process.communicate()
                    print(f"❌ 后端启动失败: {stderr.decode()}")
                    return False
                
                # 检查端口是否可用
                if self.check_port_in_use(8001):
                    print(f"✅ 后端服务器启动成功 (PID: {process.pid})")
                    print("   📡 地址: http://localhost:8001/")
                    return True
            
            print("❌ 后端服务器启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 后端启动异常: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务器"""
        print("🎨 启动React前端...")
        
        # 清理端口
        if self.check_port_in_use(3000):
            self.kill_process_on_port(3000)
            time.sleep(2)
        
        try:
            process = subprocess.Popen([
                'npm', 'run', 'frontend:dev'
            ], cwd=self.project_root, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes['frontend'] = process
            
            # 等待启动
            for i in range(15):
                time.sleep(1)
                if process.poll() is not None:
                    stdout, stderr = process.communicate()
                    print(f"❌ 前端启动失败: {stderr.decode()}")
                    return False
                
                # 检查端口是否可用
                if self.check_port_in_use(3000):
                    print(f"✅ React前端启动成功 (PID: {process.pid})")
                    print("   🌐 地址: http://localhost:3000/")
                    return True
            
            print("❌ 前端服务器启动超时")
            return False
            
        except Exception as e:
            print(f"❌ 前端启动异常: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态并自动重启"""
        while self.running:
            try:
                # 检查后端
                if 'backend' in self.processes:
                    if self.processes['backend'].poll() is not None:
                        print("⚠️ 后端进程已退出，正在重启...")
                        self.start_backend()
                
                # 检查前端
                if 'frontend' in self.processes:
                    if self.processes['frontend'].poll() is not None:
                        print("⚠️ 前端进程已退出，正在重启...")
                        self.start_frontend()
                
                # 保存PID
                self.save_pids()
                
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                print(f"监控进程时出错: {e}")
                time.sleep(5)
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        for name, process in self.processes.items():
            try:
                if process and process.poll() is None:
                    print(f"停止 {name} 服务...")
                    process.terminate()
                    
                    try:
                        process.wait(timeout=5)
                        print(f"✅ {name} 服务已停止")
                    except subprocess.TimeoutExpired:
                        print(f"⚠️ 强制终止 {name} 服务...")
                        process.kill()
                        process.wait()
                        print(f"✅ {name} 服务已强制停止")
            except Exception as e:
                print(f"停止 {name} 服务时出错: {e}")
        
        # 清理PID文件
        if os.path.exists(self.pid_file):
            os.remove(self.pid_file)
    
    def start_system(self):
        """启动完整系统"""
        print("""
╔══════════════════════════════════════════════════════════════╗
║                MemeMaster AI 自动启动系统                    ║
║                     永久解决方案 v1.0                       ║
╚══════════════════════════════════════════════════════════════╝
        """)
        
        try:
            # 清理旧进程
            self.load_and_cleanup_old_processes()
            
            # 确保依赖
            self.ensure_dependencies()
            
            # 启动后端
            if not self.start_backend():
                print("❌ 后端启动失败，退出")
                return False
            
            # 启动前端
            if not self.start_frontend():
                print("❌ 前端启动失败，退出")
                self.stop_all()
                return False
            
            # 保存PID
            self.save_pids()
            
            print("\n🎉 系统启动完成！")
            print("="*60)
            print("🌐 React前端: http://localhost:3000/")
            print("📡 后端API: http://localhost:8001/")
            print("📚 API文档: http://localhost:8001/docs")
            print("="*60)
            print("💡 系统将自动监控并重启异常进程")
            print("⚠️ 按 Ctrl+C 停止所有服务")
            print()
            
            # 启动监控
            monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
            monitor_thread.start()
            
            return True
            
        except Exception as e:
            print(f"❌ 系统启动失败: {e}")
            self.stop_all()
            return False

def setup_signal_handlers(manager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭系统...")
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """主函数"""
    # 切换到项目目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    os.chdir(project_root)
    
    manager = SystemManager()
    setup_signal_handlers(manager)
    
    try:
        if manager.start_system():
            # 保持运行
            while manager.running:
                time.sleep(1)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭系统...")
        manager.stop_all()
    except Exception as e:
        print(f"\n系统运行错误: {e}")
        manager.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main()
