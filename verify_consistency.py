#!/usr/bin/env python3
"""
MemeMaster AI 文件一致性验证脚本
确保本地文件和服务器文件保持一致
"""

import os
import sys
import hashlib
import json
from pathlib import Path
import requests

def calculate_file_hash(file_path):
    """计算文件的 MD5 哈希值"""
    if not Path(file_path).exists():
        return None
    
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except:
        return None

def check_service_status():
    """检查服务状态"""
    print("🌐 检查服务状态...")
    
    services = {
        "简化版仪表盘 (8000)": "http://localhost:8000/health",
        "React 前端 (5173)": "http://localhost:5173/",
        "备用前端 (3000)": "http://localhost:3000/"
    }
    
    status = {}
    for name, url in services.items():
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"   ✅ {name} - 运行正常")
                status[name] = "running"
            else:
                print(f"   ⚠️  {name} - 响应异常 ({response.status_code})")
                status[name] = "error"
        except:
            print(f"   ❌ {name} - 未运行")
            status[name] = "stopped"
    
    return status

def check_file_structure():
    """检查文件结构完整性"""
    print("📁 检查文件结构...")
    
    critical_files = [
        "package.json",
        "vite.config.js",
        "tailwind.config.js",
        "postcss.config.js",
        "index.html",
        "web_app.py",
        "simple_dashboard.py",
        "frontend/main.jsx",
        "frontend/App.jsx",
        "frontend/index.css",
        "frontend/store/index.js",
        "frontend/services/api.js",
        "frontend/layouts/MainLayout.jsx",
        "frontend/components/Sidebar.jsx",
        "frontend/components/TopBar.jsx",
        "frontend/pages/Dashboard.jsx",
        "frontend/pages/HotspotMonitor.jsx",
        "frontend/pages/StrategyManager.jsx",
        "frontend/pages/WalletManager.jsx",
        "frontend/pages/LiquidityControl.jsx",
        "frontend/pages/ExitSystem.jsx",
        "frontend/pages/Settings.jsx"
    ]
    
    missing_files = []
    existing_files = []
    
    for file_path in critical_files:
        if Path(file_path).exists():
            existing_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"   ❌ {file_path} - 缺失")
    
    return {
        "existing": existing_files,
        "missing": missing_files,
        "total": len(critical_files),
        "completion": len(existing_files) / len(critical_files) * 100
    }

def check_dependencies():
    """检查依赖状态"""
    print("📦 检查依赖状态...")
    
    # 检查 Node.js 依赖
    if Path("node_modules").exists():
        print("   ✅ Node.js 依赖已安装")
        node_deps = True
    else:
        print("   ❌ Node.js 依赖未安装")
        node_deps = False
    
    # 检查 Python 依赖
    python_deps = []
    required_modules = ["fastapi", "uvicorn", "psutil", "requests"]
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ Python: {module}")
            python_deps.append(module)
        except ImportError:
            print(f"   ❌ Python: {module} - 未安装")
    
    return {
        "node_deps": node_deps,
        "python_deps": len(python_deps) == len(required_modules),
        "python_missing": len(required_modules) - len(python_deps)
    }

def generate_file_manifest():
    """生成文件清单"""
    print("📋 生成文件清单...")
    
    manifest = {
        "timestamp": __import__("datetime").datetime.now().isoformat(),
        "files": {}
    }
    
    # 扫描关键文件
    for root, dirs, files in os.walk("."):
        # 跳过 node_modules 和其他大目录
        dirs[:] = [d for d in dirs if d not in [
            "node_modules", ".git", "dist", "__pycache__", ".vscode"
        ]]
        
        for file in files:
            if file.endswith(('.js', '.jsx', '.py', '.json', '.html', '.css', '.md')):
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, ".")
                
                manifest["files"][rel_path] = {
                    "size": os.path.getsize(file_path),
                    "hash": calculate_file_hash(file_path),
                    "modified": os.path.getmtime(file_path)
                }
    
    # 保存清单
    with open("file_manifest.json", "w") as f:
        json.dump(manifest, f, indent=2)
    
    print(f"   ✅ 已生成文件清单 ({len(manifest['files'])} 个文件)")
    return manifest

def create_deployment_summary():
    """创建部署总结"""
    print("📊 创建部署总结...")
    
    summary = {
        "project": "MemeMaster AI Dashboard",
        "version": "2.0.0",
        "status": "deployed",
        "services": check_service_status(),
        "files": check_file_structure(),
        "dependencies": check_dependencies(),
        "urls": {
            "react_frontend": "http://localhost:5173/",
            "simple_dashboard": "http://localhost:8000/",
            "api_docs": "http://localhost:8000/docs"
        },
        "features": [
            "React 18 + Redux Toolkit 前端",
            "FastAPI 后端 API",
            "Tailwind CSS 样式系统",
            "Recharts 数据可视化",
            "WebSocket 实时数据",
            "响应式设计",
            "模块化组件架构",
            "多种启动方式"
        ]
    }
    
    # 保存总结
    with open("deployment_summary.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    return summary

def main():
    """主验证流程"""
    print("🔍 MemeMaster AI 文件一致性验证")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("package.json").exists():
        print("❌ 未在正确的项目目录中运行")
        return False
    
    # 执行各项检查
    services = check_service_status()
    files = check_file_structure()
    deps = check_dependencies()
    manifest = generate_file_manifest()
    summary = create_deployment_summary()
    
    print("\n" + "=" * 50)
    print("📊 验证结果总结")
    print("=" * 50)
    
    # 服务状态
    running_services = sum(1 for status in services.values() if status == "running")
    print(f"🌐 服务状态: {running_services}/{len(services)} 个服务运行中")
    
    # 文件完整性
    print(f"📁 文件完整性: {files['completion']:.1f}% ({len(files['existing'])}/{files['total']})")
    
    # 依赖状态
    deps_ok = deps['node_deps'] and deps['python_deps']
    print(f"📦 依赖状态: {'✅ 完整' if deps_ok else '❌ 不完整'}")
    
    # 文件清单
    print(f"📋 文件清单: {len(manifest['files'])} 个文件已记录")
    
    print("\n🚀 访问地址:")
    print("   • React 前端: http://localhost:5173/")
    print("   • 简化版: http://localhost:8000/")
    print("   • API 文档: http://localhost:8000/docs")
    
    # 总体状态
    overall_ok = (
        running_services >= 1 and
        files['completion'] >= 80 and
        deps_ok
    )
    
    if overall_ok:
        print("\n🎉 验证通过！MemeMaster AI 仪表盘已成功部署并运行。")
        return True
    else:
        print("\n⚠️ 验证发现问题，请检查上述错误并修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
