#!/usr/bin/env python3
"""
MemeMaster AI Web应用 - 修复版
提供完整的Web界面和API服务
"""

import os
import time
import psutil
import asyncio
import random
import sys
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
import uvicorn

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 检查用户管理系统是否可用
USER_MANAGEMENT_ENABLED = False

try:
    # 检查必要的依赖
    import jwt
    import bcrypt
    import aiosqlite
    
    # 尝试导入用户管理模块
    from src.models.user import (
        User, User<PERSON>reate, User<PERSON>ogin, UserRegister, UserResponse
    )
    from src.models.subscription import (
        SubscriptionTier, SUBSCRIPTION_PLANS
    )
    from src.models.payment import (
        PaymentRequest, PaymentConfirmation, PAY_PER_USE_PRICES
    )
    from src.auth.auth_manager import auth_manager, get_current_user, get_current_active_user
    from src.subscription.feature_checker import FeatureChecker
    from src.database.connection import init_database
    
    USER_MANAGEMENT_ENABLED = True
    print("✅ 用户管理系统加载成功")
    
except ImportError as e:
    print(f"⚠️ 用户管理系统加载失败: {e}")
    print("将以基础模式运行")
    USER_MANAGEMENT_ENABLED = False

# 创建FastAPI应用
app = FastAPI(
    title="MemeMaster AI",
    description="智能Meme币交易系统",
    version="2.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 启动时间
start_time = time.time()

# 主页路由
@app.get("/")
async def root():
    """主页"""
    return HTMLResponse(content=f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>MemeMaster AI - 智能Meme币交易系统</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }}
            .container {{ max-width: 1200px; margin: 0 auto; }}
            .header {{ text-align: center; margin-bottom: 40px; }}
            .header h1 {{ font-size: 3rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }}
            .status {{ background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 15px; padding: 20px; margin: 20px 0; }}
            .cards {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }}
            .card {{ background: rgba(255,255,255,0.95); color: #333; border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }}
            .card h3 {{ color: #4a5568; margin-bottom: 15px; }}
            .btn {{ display: inline-block; background: #667eea; color: white; padding: 10px 20px; border-radius: 25px; text-decoration: none; margin: 5px; transition: all 0.3s ease; }}
            .btn:hover {{ background: #5a67d8; transform: translateY(-2px); }}
            .feature-list {{ list-style: none; padding: 0; }}
            .feature-list li {{ padding: 8px 0; position: relative; padding-left: 25px; }}
            .feature-list li:before {{ content: "✓"; position: absolute; left: 0; color: #48bb78; font-weight: bold; }}
            .alert {{ padding: 15px; margin: 15px 0; border-radius: 10px; }}
            .alert.success {{ background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }}
            .alert.warning {{ background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 MemeMaster AI</h1>
                <p>智能驱动，收益无限 - 下一代Meme币交易系统</p>
            </div>
            
            <div class="status">
                <h3>📊 系统状态</h3>
                <p>🟢 服务器运行正常 | ⏰ {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                <p>🔧 用户管理系统: {'✅ 已启用' if USER_MANAGEMENT_ENABLED else '❌ 未启用'}</p>
            </div>
            
            <div class="cards">
                <div class="card">
                    <h3>🤖 AI智能分析</h3>
                    <ul class="feature-list">
                        <li>实时市场数据分析</li>
                        <li>社交媒体情绪监控</li>
                        <li>智能风险评估</li>
                        <li>趋势预测算法</li>
                    </ul>
                    <a href="/hotspot/current" class="btn">查看热点</a>
                </div>
                
                <div class="card">
                    <h3>⚡ 自动化交易</h3>
                    <ul class="feature-list">
                        <li>自动化交易策略</li>
                        <li>风险管理系统</li>
                        <li>止损止盈设置</li>
                        <li>收益优化算法</li>
                    </ul>
                    <a href="/strategy/create" class="btn">创建策略</a>
                </div>
                
                {'<div class="card"><h3>👤 用户管理</h3><ul class="feature-list"><li>分层收费模式</li><li>Solana支付系统</li><li>功能权限控制</li><li>使用统计分析</li></ul><a href="/login" class="btn">用户登录</a><a href="/user-dashboard" class="btn">用户中心</a><a href="/subscription" class="btn">订阅管理</a></div>' if USER_MANAGEMENT_ENABLED else ''}
                
                <div class="card">
                    <h3>📡 API接口</h3>
                    <ul class="feature-list">
                        <li>RESTful API设计</li>
                        <li>实时数据推送</li>
                        <li>完整的文档支持</li>
                        <li>开发者友好</li>
                    </ul>
                    <a href="/docs" class="btn">API文档</a>
                    <a href="/health" class="btn">健康检查</a>
                </div>
            </div>
            
            {'<div class="alert success"><strong>🎉 用户管理系统已启用！</strong><br>您可以注册账户、管理订阅、使用付费功能。<br><a href="/login" style="color: #155724; text-decoration: underline;">立即登录</a> 或 <a href="/subscription" style="color: #155724; text-decoration: underline;">查看订阅计划</a></div>' if USER_MANAGEMENT_ENABLED else '<div class="alert warning"><strong>⚠️ 用户管理系统未启用</strong><br>当前以基础模式运行，部分功能可能受限。</div>'}
        </div>
    </body>
    </html>
    """)

# 基础API端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "uptime": time.time() - start_time,
        "user_management": USER_MANAGEMENT_ENABLED,
        "version": "2.0.0"
    }

@app.get("/api/status")
async def get_status():
    """获取系统状态"""
    return {
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "uptime": time.time() - start_time,
        "features": {
            "user_management": USER_MANAGEMENT_ENABLED,
            "ai_analysis": True,
            "auto_trading": True
        }
    }

@app.get("/api/system-info")
async def get_system_info():
    """获取系统信息"""
    try:
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
    except:
        cpu_percent = 0
        memory = None
    
    return {
        "uptime": time.time() - start_time,
        "cpu_percent": cpu_percent,
        "memory_percent": memory.percent if memory else 0,
        "timestamp": datetime.now().isoformat(),
        "user_management_enabled": USER_MANAGEMENT_ENABLED
    }

# 热点监测API
@app.get("/hotspot/current")
async def get_current_hotspots():
    """获取当前热点"""
    # 模拟热点数据
    hotspots = [
        {
            "id": f"hotspot_{i}",
            "title": f"测试热点 {i}",
            "category": random.choice(["technology", "politics", "entertainment"]),
            "score": random.randint(60, 95),
            "sentiment": round(random.uniform(0.3, 0.9), 2),
            "volume": random.randint(5000, 50000),
            "trend": random.choice(["rising", "stable", "falling"])
        }
        for i in range(1, 6)
    ]
    
    return {
        "status": "success",
        "hotspots": hotspots,
        "timestamp": datetime.now().isoformat(),
        "total": len(hotspots)
    }

# 策略管理API
@app.post("/strategy/create")
async def create_strategy(strategy_data: dict):
    """创建策略"""
    strategy_id = f"strategy_{int(time.time())}"
    
    return {
        "status": "success",
        "strategy_id": strategy_id,
        "data": strategy_data,
        "created_at": datetime.now().isoformat()
    }

# 如果用户管理系统启用，添加相关端点
if USER_MANAGEMENT_ENABLED:
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动时初始化数据库"""
        try:
            await init_database()
            print("✅ 数据库初始化成功")
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
    
    # 用户认证端点
    @app.post("/api/auth/register")
    async def register_user(user_data: UserRegister):
        """用户注册"""
        try:
            user = await auth_manager.register_user(
                email=user_data.email,
                username=user_data.username,
                password=user_data.password,
                full_name=user_data.full_name,
                wallet_address=user_data.wallet_address
            )
            
            return {
                "status": "success",
                "message": "用户注册成功",
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "role": user.role
                }
            }
            
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    @app.post("/api/auth/login")
    async def login_user(user_data: UserLogin):
        """用户登录"""
        try:
            user = await auth_manager.authenticate_user(user_data.email, user_data.password)
            if not user:
                raise HTTPException(status_code=401, detail="邮箱或密码错误")
            
            access_token = auth_manager.create_access_token(data={"sub": str(user.id)})
            
            return {
                "status": "success",
                "access_token": access_token,
                "token_type": "bearer",
                "user": {
                    "id": user.id,
                    "email": user.email,
                    "username": user.username,
                    "role": user.role
                }
            }
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.get("/api/auth/me")
    async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
        """获取当前用户信息"""
        return {
            "user": {
                "id": current_user.id,
                "email": current_user.email,
                "username": current_user.username,
                "role": current_user.role,
                "created_at": current_user.created_at,
                "last_login": current_user.last_login
            }
        }
    
    # 订阅管理端点
    @app.get("/api/subscription/plans")
    async def get_subscription_plans():
        """获取订阅计划"""
        return {
            "plans": [plan.dict() for plan in SUBSCRIPTION_PLANS.values()],
            "pay_per_use": PAY_PER_USE_PRICES
        }

# 前端路由处理
@app.get("/login")
async def login_page():
    """登录页面"""
    if not USER_MANAGEMENT_ENABLED:
        return HTMLResponse("用户管理系统未启用")
    
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head><title>用户登录 - MemeMaster AI</title></head>
    <body style="font-family: Arial; padding: 40px; background: #f5f5f5;">
        <div style="max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
            <h2>用户登录</h2>
            <p>用户管理系统已启用，请使用API端点进行登录。</p>
            <p><strong>API端点:</strong> POST /api/auth/login</p>
            <p><a href="/docs">查看API文档</a></p>
            <p><a href="/">返回首页</a></p>
        </div>
    </body>
    </html>
    """)

@app.get("/user-dashboard")
async def user_dashboard():
    """用户仪表盘"""
    if not USER_MANAGEMENT_ENABLED:
        return HTMLResponse("用户管理系统未启用")
    
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head><title>用户中心 - MemeMaster AI</title></head>
    <body style="font-family: Arial; padding: 40px; background: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
            <h2>用户中心</h2>
            <p>用户管理系统已启用，请先登录后访问。</p>
            <p><strong>相关API端点:</strong></p>
            <ul>
                <li>GET /api/auth/me - 获取用户信息</li>
                <li>GET /api/usage/summary - 获取使用摘要</li>
            </ul>
            <p><a href="/docs">查看API文档</a></p>
            <p><a href="/">返回首页</a></p>
        </div>
    </body>
    </html>
    """)

@app.get("/subscription")
async def subscription_page():
    """订阅管理页面"""
    if not USER_MANAGEMENT_ENABLED:
        return HTMLResponse("用户管理系统未启用")
    
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head><title>订阅管理 - MemeMaster AI</title></head>
    <body style="font-family: Arial; padding: 40px; background: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px;">
            <h2>订阅管理</h2>
            <p>用户管理系统已启用，支持分层收费模式。</p>
            <p><strong>订阅计划:</strong></p>
            <ul>
                <li>免费版: 14天试用，基础功能</li>
                <li>Pro版: 15 SOL/月，全功能</li>
                <li>机构版: 50+ SOL/月，企业级</li>
            </ul>
            <p><strong>API端点:</strong> GET /api/subscription/plans</p>
            <p><a href="/docs">查看API文档</a></p>
            <p><a href="/">返回首页</a></p>
        </div>
    </body>
    </html>
    """)

if __name__ == "__main__":
    print("🚀 启动 MemeMaster AI Web应用...")
    print(f"📊 用户管理系统: {'✅ 启用' if USER_MANAGEMENT_ENABLED else '❌ 禁用'}")
    print("🌐 访问地址:")
    print("   • 主页: http://localhost:8001/")
    print("   • API文档: http://localhost:8001/docs")
    print("   • 健康检查: http://localhost:8001/health")
    
    if USER_MANAGEMENT_ENABLED:
        print("   • 用户登录: http://localhost:8001/login")
        print("   • 用户中心: http://localhost:8001/user-dashboard")
        print("   • 订阅管理: http://localhost:8001/subscription")
    
    print("\n按 Ctrl+C 停止服务器")
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
