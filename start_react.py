#!/usr/bin/env python3
"""
MemeMaster AI React前端一键启动脚本
解决每次新对话线程时React前端无法访问的问题
"""

import os
import sys
import time
import subprocess
import psutil
import requests
from pathlib import Path

def log(message):
    """输出日志"""
    print(f"🔧 {message}")

def check_port(port):
    """检查端口是否被占用"""
    try:
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                return True
    except:
        pass
    return False

def kill_port(port):
    """终止占用端口的进程"""
    try:
        for proc in psutil.process_iter(['pid', 'connections']):
            try:
                for conn in proc.info['connections'] or []:
                    if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                        log(f"终止端口{port}上的进程 PID:{proc.info['pid']}")
                        proc.terminate()
                        proc.wait(timeout=3)
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except:
        pass
    return False

def check_health(url):
    """检查服务健康状态"""
    try:
        response = requests.get(url, timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """启动后端服务"""
    if check_port(8001) and check_health("http://localhost:8001/health"):
        log("后端服务已运行")
        return True
    
    log("启动后端服务...")
    kill_port(8001)
    time.sleep(1)
    
    try:
        subprocess.Popen([
            sys.executable, "working_server.py"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 等待启动
        for i in range(10):
            time.sleep(1)
            if check_health("http://localhost:8001/health"):
                log("✅ 后端服务启动成功")
                return True
        
        log("❌ 后端服务启动失败")
        return False
    except:
        log("❌ 后端服务启动异常")
        return False

def start_frontend():
    """启动React前端"""
    if check_port(3000) and check_health("http://localhost:3000/"):
        log("React前端已运行")
        return True
    
    log("启动React前端...")
    kill_port(3000)
    time.sleep(2)
    
    try:
        # 检查依赖
        if not Path("node_modules").exists():
            log("安装Node.js依赖...")
            subprocess.run(["npm", "install"], check=True, 
                         stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 启动前端
        subprocess.Popen([
            "npm", "run", "frontend:dev"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        # 等待启动
        for i in range(20):
            time.sleep(1)
            if check_port(3000):
                time.sleep(2)  # 额外等待确保完全启动
                if check_health("http://localhost:3000/"):
                    log("✅ React前端启动成功")
                    return True
        
        log("❌ React前端启动失败")
        return False
    except Exception as e:
        log(f"❌ React前端启动异常: {e}")
        return False

def main():
    """主函数"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║              MemeMaster AI React前端一键启动                 ║
║                    解决新对话线程问题                        ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    # 切换到项目目录
    project_root = Path(__file__).parent.absolute()
    os.chdir(project_root)
    log(f"项目目录: {project_root}")
    
    try:
        # 启动后端
        backend_ok = start_backend()
        
        # 启动前端
        frontend_ok = start_frontend()
        
        if backend_ok and frontend_ok:
            print("\n🎉 启动完成！")
            print("="*50)
            print("🌐 React前端: http://localhost:3000/")
            print("📡 后端API: http://localhost:8001/")
            print("📚 API文档: http://localhost:8001/docs")
            print("="*50)
            print("💡 现在可以正常访问React前端页面了！")
            
            # 自动打开浏览器
            try:
                import webbrowser
                webbrowser.open('http://localhost:3000/')
                print("🌐 已自动打开React前端页面")
            except:
                pass
            
            return True
        else:
            print("\n❌ 启动失败")
            if not backend_ok:
                print("   • 后端服务启动失败")
            if not frontend_ok:
                print("   • React前端启动失败")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 启动被用户中断")
        return False
    except Exception as e:
        print(f"\n❌ 启动过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
