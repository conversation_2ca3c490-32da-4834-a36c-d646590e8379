#!/usr/bin/env python3
"""
检查左侧菜单每个页面的状态
"""

import os
import requests
import webbrowser
from pathlib import Path

def check_file_exists(file_path):
    """检查文件是否存在"""
    return os.path.exists(file_path)

def check_react_pages():
    """检查React页面文件"""
    print("🔍 检查React页面文件...")
    
    pages = [
        ("仪表盘", "frontend/pages/Dashboard.jsx", "/"),
        ("舆情监控", "frontend/pages/HotspotMonitor.jsx", "/hotspot"),
        ("业务流程", "frontend/components/BusinessFlowCenter.jsx", "/business-flow"),
        ("策略管理", "frontend/pages/StrategyManager.jsx", "/strategy"),
        ("钱包管理", "frontend/pages/WalletManager.jsx", "/wallet"),
        ("流动性控制", "frontend/pages/LiquidityControl.jsx", "/liquidity"),
        ("动态策略", "frontend/pages/ExitSystem.jsx", "/exit"),
        ("系统设置", "frontend/pages/Settings.jsx", "/settings"),
    ]
    
    results = []
    for name, file_path, route in pages:
        exists = check_file_exists(file_path)
        status = "✅" if exists else "❌"
        print(f"   {status} {name}: {file_path}")
        results.append((name, file_path, route, exists))
    
    return results

def check_html_version():
    """检查HTML版本"""
    print("\n🔍 检查HTML版本...")
    
    try:
        response = requests.get("http://localhost:8090/simple_dashboard.html", timeout=5)
        if response.status_code == 200:
            print("✅ HTML版本响应正常")
            return True
        else:
            print(f"❌ HTML版本响应异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ HTML版本连接失败: {e}")
        return False

def check_react_server():
    """检查React服务器"""
    print("\n🔍 检查React服务器...")
    
    try:
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ React服务器响应正常")
            return True
        else:
            print(f"❌ React服务器响应异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ React服务器连接失败: {e}")
        return False

def main():
    print("🔍 MemeMaster AI 左侧菜单检查")
    print("=" * 60)
    
    # 切换到项目目录
    project_root = Path(__file__).parent.absolute()
    os.chdir(project_root)
    
    # 检查React页面文件
    react_pages = check_react_pages()
    
    # 检查服务器状态
    html_ok = check_html_version()
    react_ok = check_react_server()
    
    print("\n" + "=" * 60)
    print("📋 菜单检查结果")
    print("=" * 60)
    
    print("\n🎯 左侧菜单项状态:")
    for name, file_path, route, exists in react_pages:
        status = "✅ 正常" if exists else "❌ 缺失"
        print(f"   • {name:<12} {status:<8} 路由: {route}")
    
    print("\n🌐 服务器状态:")
    print(f"   • HTML版本: {'✅ 正常' if html_ok else '❌ 异常'}")
    print(f"   • React版本: {'✅ 正常' if react_ok else '❌ 异常'}")
    
    print("\n📁 页面文件详情:")
    missing_files = []
    for name, file_path, route, exists in react_pages:
        if exists:
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")
            missing_files.append((name, file_path))
    
    if missing_files:
        print(f"\n⚠️ 发现 {len(missing_files)} 个缺失的页面文件:")
        for name, file_path in missing_files:
            print(f"   • {name}: {file_path}")
    
    print("\n🎯 可用的访问方式:")
    if html_ok:
        print("   ✅ HTML版本 (推荐): http://localhost:8090/simple_dashboard.html")
    if react_ok:
        print("   ⚠️ React版本 (有问题): http://localhost:3000/")
    
    print("\n💡 HTML版本菜单项:")
    html_menus = [
        "仪表盘 (当前页面)",
        "市场分析 (静态)",
        "交易策略 (静态)",
        "自动化交易 (静态)",
        "支付与订阅 (静态)"
    ]
    for menu in html_menus:
        print(f"   • {menu}")
    
    print("\n💡 React版本菜单项:")
    for name, file_path, route, exists in react_pages:
        status = "可用" if exists else "不可用"
        print(f"   • {name} ({status})")
    
    print("\n🎊 检查完成！")
    
    if html_ok:
        print("\n🌐 自动打开HTML版本...")
        try:
            webbrowser.open("http://localhost:8090/simple_dashboard.html")
            print("✅ 已在浏览器中打开HTML版本")
        except:
            print("⚠️ 无法自动打开浏览器")

if __name__ == "__main__":
    main()
