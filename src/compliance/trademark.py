"""
商标检查模块
检查代币名称是否存在商标冲突
"""

import requests
import time
import hashlib
from typing import Dict, List, Optional, Any
from ..utils.logger import LoggerManager

logger = LoggerManager.get_logger('compliance')


class TrademarkAPI:
    """商标检查API接口"""
    
    def __init__(self):
        self.api_key = self._get_api_key()
        self.cache = {}  # 简单的内存缓存
        self.cache_ttl = 3600  # 缓存1小时
        
        # API端点配置
        self.endpoints = {
            'uspto': 'https://developer.uspto.gov/api/v1/trademark/search',
            'euipo': 'https://euipo.europa.eu/api/trademark/search',
            'wipo': 'https://www.wipo.int/api/trademark/search'
        }
        
        logger.info("TrademarkAPI initialized")
    
    def _get_api_key(self) -> str:
        """获取API密钥"""
        import os
        return os.getenv('TRADEMARKNOW_API_KEY', '')
    
    def check_trademark(self, name: str) -> bool:
        """
        检查商标冲突
        
        Args:
            name: 要检查的名称
            
        Returns:
            是否存在冲突
        """
        try:
            # 检查缓存
            cache_key = self._get_cache_key(name)
            if self._is_cached(cache_key):
                result = self.cache[cache_key]['result']
                logger.info(f"Trademark check (cached): {name} -> {'CONFLICT' if result else 'CLEAR'}")
                return result
            
            # 执行检查
            conflict = self._perform_trademark_search(name)
            
            # 缓存结果
            self._cache_result(cache_key, conflict)
            
            logger.info(f"Trademark check: {name} -> {'CONFLICT' if conflict else 'CLEAR'}")
            return conflict
            
        except Exception as e:
            logger.error(f"Trademark check failed for {name}: {str(e)}")
            # 出错时保守处理，假设存在冲突
            return True
    
    def _perform_trademark_search(self, name: str) -> bool:
        """
        执行商标搜索
        
        Args:
            name: 商标名称
            
        Returns:
            是否存在冲突
        """
        # 多个数据源检查
        sources = [
            self._check_uspto,
            self._check_common_trademarks,
            self._check_crypto_names
        ]
        
        for check_func in sources:
            try:
                if check_func(name):
                    return True
            except Exception as e:
                logger.warning(f"Trademark source check failed: {str(e)}")
                continue
        
        return False
    
    def _check_uspto(self, name: str) -> bool:
        """
        检查美国专利商标局数据库
        
        Args:
            name: 商标名称
            
        Returns:
            是否存在冲突
        """
        try:
            # 模拟USPTO API调用
            # 实际实现需要真实的API密钥和端点
            
            # 这里使用模拟数据进行演示
            known_trademarks = [
                'bitcoin', 'ethereum', 'dogecoin', 'shiba', 'safemoon',
                'apple', 'google', 'microsoft', 'amazon', 'tesla',
                'coca-cola', 'pepsi', 'nike', 'adidas', 'mcdonalds'
            ]
            
            name_lower = name.lower()
            
            # 精确匹配
            if name_lower in known_trademarks:
                return True
            
            # 相似性检查
            for trademark in known_trademarks:
                if self._calculate_similarity(name_lower, trademark) > 0.8:
                    logger.warning(f"Similar trademark found: {name} -> {trademark}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"USPTO check failed: {str(e)}")
            return False
    
    def _check_common_trademarks(self, name: str) -> bool:
        """
        检查常见商标数据库
        
        Args:
            name: 商标名称
            
        Returns:
            是否存在冲突
        """
        # 常见的受保护名称
        protected_names = {
            # 科技公司
            'apple', 'google', 'microsoft', 'amazon', 'facebook', 'meta',
            'twitter', 'instagram', 'youtube', 'netflix', 'spotify',
            
            # 金融机构
            'visa', 'mastercard', 'paypal', 'stripe', 'square',
            'jpmorgan', 'goldman', 'morgan', 'wells', 'bank',
            
            # 加密货币
            'bitcoin', 'ethereum', 'binance', 'coinbase', 'kraken',
            'bitfinex', 'huobi', 'okex', 'kucoin', 'gemini',
            
            # 品牌名称
            'nike', 'adidas', 'coca', 'pepsi', 'mcdonalds',
            'starbucks', 'walmart', 'target', 'disney', 'marvel'
        }
        
        name_lower = name.lower()
        
        # 检查是否包含受保护的名称
        for protected in protected_names:
            if protected in name_lower or name_lower in protected:
                logger.warning(f"Protected name detected: {name} contains {protected}")
                return True
        
        return False
    
    def _check_crypto_names(self, name: str) -> bool:
        """
        检查现有加密货币名称
        
        Args:
            name: 代币名称
            
        Returns:
            是否存在冲突
        """
        # 主要加密货币列表
        crypto_names = {
            'bitcoin', 'btc', 'ethereum', 'eth', 'binance', 'bnb',
            'cardano', 'ada', 'solana', 'sol', 'polkadot', 'dot',
            'dogecoin', 'doge', 'shiba', 'shib', 'avalanche', 'avax',
            'polygon', 'matic', 'chainlink', 'link', 'uniswap', 'uni',
            'litecoin', 'ltc', 'bitcoin cash', 'bch', 'stellar', 'xlm',
            'vechain', 'vet', 'filecoin', 'fil', 'tron', 'trx',
            'monero', 'xmr', 'eos', 'iota', 'miota', 'neo',
            'cosmos', 'atom', 'tezos', 'xtz', 'algorand', 'algo'
        }
        
        name_lower = name.lower()
        
        # 精确匹配和包含检查
        for crypto in crypto_names:
            if crypto == name_lower or crypto in name_lower:
                logger.warning(f"Crypto name conflict: {name} conflicts with {crypto}")
                return True
        
        return False
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        计算字符串相似度
        
        Args:
            str1: 字符串1
            str2: 字符串2
            
        Returns:
            相似度 (0-1)
        """
        # 使用简单的编辑距离算法
        def levenshtein_distance(s1, s2):
            if len(s1) < len(s2):
                return levenshtein_distance(s2, s1)
            
            if len(s2) == 0:
                return len(s1)
            
            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row
            
            return previous_row[-1]
        
        distance = levenshtein_distance(str1, str2)
        max_len = max(len(str1), len(str2))
        
        if max_len == 0:
            return 1.0
        
        similarity = 1 - (distance / max_len)
        return similarity
    
    def _get_cache_key(self, name: str) -> str:
        """生成缓存键"""
        return hashlib.md5(name.lower().encode()).hexdigest()
    
    def _is_cached(self, cache_key: str) -> bool:
        """检查是否已缓存"""
        if cache_key not in self.cache:
            return False
        
        cached_time = self.cache[cache_key]['timestamp']
        return time.time() - cached_time < self.cache_ttl
    
    def _cache_result(self, cache_key: str, result: bool):
        """缓存结果"""
        self.cache[cache_key] = {
            'result': result,
            'timestamp': time.time()
        }
    
    def batch_check(self, names: List[str]) -> Dict[str, bool]:
        """
        批量检查商标
        
        Args:
            names: 名称列表
            
        Returns:
            检查结果字典
        """
        results = {}
        
        for name in names:
            try:
                results[name] = self.check_trademark(name)
                # 添加延迟避免API限制
                time.sleep(0.1)
            except Exception as e:
                logger.error(f"Batch check failed for {name}: {str(e)}")
                results[name] = True  # 出错时保守处理
        
        logger.info(f"Batch trademark check completed: {len(results)} names")
        return results
    
    def suggest_alternatives(self, name: str, count: int = 5) -> List[str]:
        """
        建议替代名称
        
        Args:
            name: 原始名称
            count: 建议数量
            
        Returns:
            替代名称列表
        """
        alternatives = []
        
        # 添加后缀
        suffixes = ['Coin', 'Token', 'Inu', 'Moon', 'Safe', 'X', 'Pro', 'Plus']
        for suffix in suffixes:
            alt_name = f"{name}{suffix}"
            if not self.check_trademark(alt_name):
                alternatives.append(alt_name)
                if len(alternatives) >= count:
                    break
        
        # 添加前缀
        if len(alternatives) < count:
            prefixes = ['Super', 'Meta', 'Ultra', 'Mega', 'Hyper', 'Neo', 'Crypto']
            for prefix in prefixes:
                alt_name = f"{prefix}{name}"
                if not self.check_trademark(alt_name):
                    alternatives.append(alt_name)
                    if len(alternatives) >= count:
                        break
        
        # 数字变体
        if len(alternatives) < count:
            for i in range(2, 10):
                alt_name = f"{name}{i}"
                if not self.check_trademark(alt_name):
                    alternatives.append(alt_name)
                    if len(alternatives) >= count:
                        break
        
        logger.info(f"Generated {len(alternatives)} alternatives for {name}")
        return alternatives[:count]
    
    def clear_cache(self):
        """清空缓存"""
        self.cache.clear()
        logger.info("Trademark cache cleared")


# 全局商标检查实例
global_trademark_api = TrademarkAPI()
