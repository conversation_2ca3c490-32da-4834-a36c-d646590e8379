"""
地理围栏模块
检查用户地理位置并实施地区限制
"""

import requests
import os
from typing import Dict, List, Optional, Tuple, Any
from ..utils.logger import LoggerManager

logger = LoggerManager.get_logger('compliance')


class GeoFence:
    """地理围栏检查器"""
    
    # 受限地区列表
    RESTRICTED_REGIONS = {
        'US': 'United States',
        'CN': 'China',
        'KR': 'South Korea',
        'CA': 'Canada',
        'UK': 'United Kingdom',
        'JP': 'Japan',
        'SG': 'Singapore'
    }
    
    # IP地理位置API
    GEO_APIS = [
        'http://ip-api.com/json/',
        'https://ipapi.co/json/',
        'https://freegeoip.app/json/'
    ]
    
    def __init__(self):
        # 从环境变量读取受限地区配置
        env_restricted = os.getenv('RESTRICTED_REGIONS', 'US,CN,KR,CA,UK')
        self.restricted_countries = set(env_restricted.split(','))
        
        # 合规模式
        self.compliance_mode = os.getenv('COMPLIANCE_MODE', 'strict')
        self.geofence_enabled = os.getenv('GEOFENCE_ENABLED', 'true').lower() == 'true'
        
        logger.info(f"GeoFence initialized with restricted regions: {self.restricted_countries}")
    
    def get_client_location(self, ip_address: str = None) -> Optional[Dict[str, str]]:
        """
        获取客户端地理位置
        
        Args:
            ip_address: IP地址，如果为None则获取当前IP
            
        Returns:
            地理位置信息字典或None
        """
        if not self.geofence_enabled:
            logger.info("Geofence is disabled, skipping location check")
            return None
        
        for api_url in self.GEO_APIS:
            try:
                url = f"{api_url}{ip_address}" if ip_address else api_url
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # 标准化响应格式
                    location = self._normalize_location_data(data, api_url)
                    if location:
                        logger.info(f"Location detected: {location['country']} ({location['country_code']})")
                        return location
                        
            except Exception as e:
                logger.warning(f"Failed to get location from {api_url}: {str(e)}")
                continue
        
        logger.error("Failed to determine client location from all APIs")
        return None
    
    def _normalize_location_data(self, data: Dict, api_url: str) -> Optional[Dict[str, str]]:
        """
        标准化不同API的响应格式
        
        Args:
            data: API响应数据
            api_url: API URL
            
        Returns:
            标准化的位置数据
        """
        try:
            if 'ip-api.com' in api_url:
                return {
                    'country': data.get('country', ''),
                    'country_code': data.get('countryCode', ''),
                    'region': data.get('regionName', ''),
                    'city': data.get('city', ''),
                    'ip': data.get('query', ''),
                    'timezone': data.get('timezone', '')
                }
            elif 'ipapi.co' in api_url:
                return {
                    'country': data.get('country_name', ''),
                    'country_code': data.get('country_code', ''),
                    'region': data.get('region', ''),
                    'city': data.get('city', ''),
                    'ip': data.get('ip', ''),
                    'timezone': data.get('timezone', '')
                }
            elif 'freegeoip.app' in api_url:
                return {
                    'country': data.get('country_name', ''),
                    'country_code': data.get('country_code', ''),
                    'region': data.get('region_name', ''),
                    'city': data.get('city', ''),
                    'ip': data.get('ip', ''),
                    'timezone': data.get('time_zone', '')
                }
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error normalizing location data: {str(e)}")
            return None
    
    def is_restricted(self, ip_address: str = None) -> bool:
        """
        检查是否为受限地区
        
        Args:
            ip_address: IP地址
            
        Returns:
            是否受限
        """
        if not self.geofence_enabled:
            return False
        
        location = self.get_client_location(ip_address)
        if not location:
            # 如果无法确定位置，根据合规模式决定
            if self.compliance_mode == 'strict':
                logger.warning("Cannot determine location, blocking access in strict mode")
                return True
            else:
                logger.warning("Cannot determine location, allowing access in permissive mode")
                return False
        
        country_code = location.get('country_code', '').upper()
        is_restricted = country_code in self.restricted_countries
        
        if is_restricted:
            logger.warning(f"Access blocked for restricted region: {location['country']} ({country_code})")
        else:
            logger.info(f"Access allowed for region: {location['country']} ({country_code})")
        
        return is_restricted
    
    def get_allowed_regions(self) -> List[str]:
        """
        获取允许的地区列表
        
        Returns:
            允许的地区代码列表
        """
        all_regions = set(['US', 'CN', 'KR', 'CA', 'UK', 'JP', 'SG', 'DE', 'FR', 'IT', 'ES', 'AU', 'BR', 'IN', 'RU'])
        allowed = all_regions - self.restricted_countries
        return list(allowed)
    
    def add_restricted_region(self, country_code: str):
        """
        添加受限地区
        
        Args:
            country_code: 国家代码
        """
        self.restricted_countries.add(country_code.upper())
        logger.info(f"Added restricted region: {country_code}")
    
    def remove_restricted_region(self, country_code: str):
        """
        移除受限地区
        
        Args:
            country_code: 国家代码
        """
        self.restricted_countries.discard(country_code.upper())
        logger.info(f"Removed restricted region: {country_code}")
    
    def check_vpn_proxy(self, ip_address: str) -> Tuple[bool, Dict[str, Any]]:
        """
        检查是否使用VPN/代理
        
        Args:
            ip_address: IP地址
            
        Returns:
            (是否为代理, 检测详情)
        """
        try:
            # 使用多个代理检测API
            apis = [
                f"http://ip-api.com/json/{ip_address}?fields=proxy",
                f"https://ipapi.co/{ip_address}/json/"
            ]
            
            for api_url in apis:
                response = requests.get(api_url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    
                    # 检查代理指标
                    is_proxy = (
                        data.get('proxy', False) or
                        data.get('threat', {}).get('is_proxy', False) or
                        data.get('threat', {}).get('is_anonymous', False)
                    )
                    
                    if is_proxy:
                        logger.warning(f"Proxy/VPN detected for IP: {ip_address}")
                        return True, data
            
            return False, {}
            
        except Exception as e:
            logger.error(f"Error checking proxy status: {str(e)}")
            return False, {}
    
    def validate_access(self, ip_address: str = None) -> Dict[str, Any]:
        """
        综合访问验证
        
        Args:
            ip_address: IP地址
            
        Returns:
            验证结果
        """
        result = {
            'allowed': True,
            'reason': '',
            'location': None,
            'is_proxy': False,
            'compliance_mode': self.compliance_mode
        }
        
        try:
            # 地理位置检查
            if self.is_restricted(ip_address):
                result['allowed'] = False
                result['reason'] = 'Restricted geographic region'
                result['location'] = self.get_client_location(ip_address)
                return result
            
            # 代理检查
            if ip_address:
                is_proxy, proxy_data = self.check_vpn_proxy(ip_address)
                result['is_proxy'] = is_proxy
                
                if is_proxy and self.compliance_mode == 'strict':
                    result['allowed'] = False
                    result['reason'] = 'VPN/Proxy usage detected'
                    return result
            
            result['location'] = self.get_client_location(ip_address)
            logger.info(f"Access validation passed for IP: {ip_address}")
            
        except Exception as e:
            logger.error(f"Error in access validation: {str(e)}")
            if self.compliance_mode == 'strict':
                result['allowed'] = False
                result['reason'] = 'Validation error'
        
        return result


# 全局地理围栏实例
global_geofence = GeoFence()
