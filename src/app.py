"""
MemeMaster AI 主应用入口
启动Web服务器和后台任务
"""

import os
import asyncio
import signal
import sys
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
import uvicorn
from prometheus_client import start_http_server, Counter, Histogram, Gauge
import structlog

# 导入项目模块
from .utils.logger import LoggerManager, setup_structured_logger
from .utils.circuit_breaker import global_circuit_breaker
from .hotspot.detector import HotspotDetector
from .strategy.generator import StrategyGenerator
from .compliance.geofence import global_geofence
from .blockchain.ethereum import get_ethereum_client

# 设置结构化日志
setup_structured_logger()
logger = LoggerManager.get_logger('app')

# Prometheus指标
REQUEST_COUNT = Counter('mememaster_requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('mememaster_request_duration_seconds', 'Request duration')
ACTIVE_CONNECTIONS = Gauge('mememaster_active_connections', 'Active connections')
HOTSPOTS_DETECTED = Counter('mememaster_hotspots_detected_total', 'Total hotspots detected')
TOKENS_DEPLOYED = Counter('mememaster_tokens_deployed_total', 'Total tokens deployed')

# 创建FastAPI应用
app = FastAPI(
    title="MemeMaster AI",
    description="AI-powered meme token creation and trading system",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
hotspot_detector = None
strategy_generator = None
shutdown_event = asyncio.Event()


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global hotspot_detector, strategy_generator
    
    logger.info("Starting MemeMaster AI application...")
    
    try:
        # 初始化核心组件
        hotspot_detector = HotspotDetector()
        strategy_generator = StrategyGenerator()
        
        # 启动Prometheus监控服务器
        prometheus_port = int(os.getenv('PROMETHEUS_PORT', '8000'))
        start_http_server(prometheus_port)
        logger.info(f"Prometheus metrics server started on port {prometheus_port}")
        
        # 启动后台任务
        asyncio.create_task(background_hotspot_monitoring())
        asyncio.create_task(system_health_monitor())
        
        logger.info("MemeMaster AI application started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        raise


@app.on_event("shutdown")
async def shutdown_event_handler():
    """应用关闭事件"""
    logger.info("Shutting down MemeMaster AI application...")
    
    # 设置关闭事件
    shutdown_event.set()
    
    # 停止熔断器监控
    global_circuit_breaker.stop_monitoring()
    
    logger.info("Application shutdown completed")


@app.middleware("http")
async def metrics_middleware(request, call_next):
    """请求指标中间件"""
    start_time = time.time()
    
    # 增加活跃连接数
    ACTIVE_CONNECTIONS.inc()
    
    try:
        response = await call_next(request)
        
        # 记录请求指标
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.url.path
        ).inc()
        
        REQUEST_DURATION.observe(time.time() - start_time)
        
        return response
        
    finally:
        # 减少活跃连接数
        ACTIVE_CONNECTIONS.dec()


@app.get("/")
async def root():
    """根路径"""
    return {"message": "MemeMaster AI is running", "version": "2.0.0"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查各组件状态
        circuit_state = global_circuit_breaker.get_state()
        system_metrics = global_circuit_breaker.get_metrics()
        
        # 检查区块链连接
        ethereum_client = get_ethereum_client()
        network_info = ethereum_client.get_network_info()
        
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "circuit_breaker": circuit_state.value,
            "system_metrics": {
                "cpu_percent": system_metrics.cpu_percent,
                "memory_percent": system_metrics.memory_percent,
                "active_connections": system_metrics.active_connections
            },
            "blockchain": {
                "connected": bool(network_info),
                "chain_id": network_info.get('chain_id'),
                "latest_block": network_info.get('latest_block')
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/api/v1/analyze-hotspot")
async def analyze_hotspot(request: Dict[str, Any]):
    """分析热点端点"""
    try:
        source_url = request.get('source_url')
        source_type = request.get('source_type')
        
        if not source_url:
            raise HTTPException(status_code=400, detail="source_url is required")
        
        # 地理围栏检查
        client_ip = request.get('client_ip')
        if global_geofence.is_restricted(client_ip):
            raise HTTPException(status_code=403, detail="Access denied from your region")
        
        # 分析热点
        hotspot_data = await hotspot_detector.analyze_source(source_url, source_type)
        
        if hotspot_data:
            HOTSPOTS_DETECTED.inc()
            return {
                "success": True,
                "data": {
                    "source": hotspot_data.source,
                    "sentiment_score": hotspot_data.sentiment_score,
                    "viral_score": hotspot_data.viral_score,
                    "timestamp": hotspot_data.timestamp,
                    "metadata": hotspot_data.metadata
                }
            }
        else:
            return {"success": False, "message": "Failed to analyze hotspot"}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Hotspot analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/api/v1/generate-strategy")
async def generate_strategy(request: Dict[str, Any]):
    """生成策略端点"""
    try:
        hotspot_data = request.get('hotspot_data')
        
        if not hotspot_data:
            raise HTTPException(status_code=400, detail="hotspot_data is required")
        
        # 生成策略
        strategy = strategy_generator.generate_token_strategy(hotspot_data)
        
        if strategy:
            return {
                "success": True,
                "strategy": {
                    "token_name": strategy.token_name,
                    "symbol": strategy.symbol,
                    "total_supply": strategy.total_supply,
                    "burn_rate": strategy.burn_rate,
                    "estimated_roi": strategy.estimated_roi,
                    "compliance_status": strategy.compliance_status,
                    "launch_schedule": strategy.launch_schedule
                }
            }
        else:
            return {"success": False, "message": "Failed to generate strategy"}
            
    except Exception as e:
        logger.error(f"Strategy generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.post("/api/v1/deploy-token")
async def deploy_token(request: Dict[str, Any], background_tasks: BackgroundTasks):
    """部署代币端点"""
    try:
        strategy_data = request.get('strategy')
        
        if not strategy_data:
            raise HTTPException(status_code=400, detail="strategy is required")
        
        # 添加后台部署任务
        background_tasks.add_task(deploy_token_background, strategy_data)
        
        return {
            "success": True,
            "message": "Token deployment started",
            "status": "pending"
        }
        
    except Exception as e:
        logger.error(f"Token deployment request failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/api/v1/metrics")
async def get_metrics():
    """获取系统指标"""
    try:
        circuit_metrics = global_circuit_breaker.get_metrics()
        
        return {
            "system": {
                "cpu_percent": circuit_metrics.cpu_percent,
                "memory_percent": circuit_metrics.memory_percent,
                "disk_percent": circuit_metrics.disk_percent,
                "active_connections": circuit_metrics.active_connections
            },
            "application": {
                "circuit_breaker_state": global_circuit_breaker.get_state().value,
                "hotspots_detected": HOTSPOTS_DETECTED._value.get(),
                "tokens_deployed": TOKENS_DEPLOYED._value.get()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def deploy_token_background(strategy_data: Dict[str, Any]):
    """后台代币部署任务"""
    try:
        logger.info("Starting background token deployment...")
        
        # 这里应该调用实际的部署逻辑
        # from .scripts.deploy import ContractDeployer
        # deployer = ContractDeployer()
        # result = deployer.deploy_token(strategy)
        
        # 模拟部署过程
        await asyncio.sleep(30)  # 模拟部署时间
        
        TOKENS_DEPLOYED.inc()
        logger.info("Token deployment completed successfully")
        
    except Exception as e:
        logger.error(f"Background token deployment failed: {str(e)}")


async def background_hotspot_monitoring():
    """后台热点监控任务"""
    logger.info("Starting background hotspot monitoring...")
    
    while not shutdown_event.is_set():
        try:
            # 这里可以添加自动热点扫描逻辑
            # 例如：定期扫描社交媒体、新闻等
            
            await asyncio.sleep(300)  # 每5分钟检查一次
            
        except Exception as e:
            logger.error(f"Background hotspot monitoring error: {str(e)}")
            await asyncio.sleep(60)  # 出错时等待1分钟


async def system_health_monitor():
    """系统健康监控任务"""
    logger.info("Starting system health monitor...")
    
    while not shutdown_event.is_set():
        try:
            # 检查系统健康状态
            metrics = global_circuit_breaker.get_metrics()
            
            # 记录关键指标
            logger.debug(f"System metrics - CPU: {metrics.cpu_percent}%, Memory: {metrics.memory_percent}%")
            
            await asyncio.sleep(30)  # 每30秒检查一次
            
        except Exception as e:
            logger.error(f"System health monitor error: {str(e)}")
            await asyncio.sleep(60)


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"Received signal {signum}, shutting down...")
    shutdown_event.set()


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 获取配置
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', '3000'))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting MemeMaster AI server on {host}:{port}")
    
    # 启动服务器
    uvicorn.run(
        "src.app:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if not debug else "debug",
        access_log=True
    )


if __name__ == "__main__":
    main()
