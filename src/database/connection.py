"""
数据库连接管理
"""

import sqlite3
import os
from typing import Optional
from contextlib import asynccontextmanager
import aiosqlite


class DatabaseConnection:
    """数据库连接管理器"""
    
    def __init__(self, db_path: str = "mememaster.db"):
        self.db_path = db_path
        self._ensure_db_directory()
    
    def _ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    @asynccontextmanager
    async def get_connection(self):
        """获取异步数据库连接"""
        async with aiosqlite.connect(self.db_path) as conn:
            conn.row_factory = aiosqlite.Row
            yield conn
    
    def get_sync_connection(self):
        """获取同步数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    async def initialize_database(self):
        """初始化数据库表"""
        async with self.get_connection() as conn:
            # 用户表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE NOT NULL,
                    username TEXT UNIQUE NOT NULL,
                    hashed_password TEXT NOT NULL,
                    full_name TEXT,
                    wallet_address TEXT,
                    role TEXT DEFAULT 'free',
                    is_active BOOLEAN DEFAULT 1,
                    status TEXT DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    free_trial_ends TIMESTAMP,
                    daily_hotspots_used INTEGER DEFAULT 0,
                    daily_hotspots_reset TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_strategies_created INTEGER DEFAULT 0,
                    total_deployments INTEGER DEFAULT 0,
                    total_paid_features_used INTEGER DEFAULT 0
                )
            """)
            
            # 订阅表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS subscriptions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    tier TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    price_sol REAL NOT NULL,
                    price_usd REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    starts_at TIMESTAMP NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    auto_renew BOOLEAN DEFAULT 1,
                    payment_transaction_hash TEXT,
                    features TEXT,  -- JSON字符串
                    usage_limits TEXT,  -- JSON字符串
                    current_usage TEXT,  -- JSON字符串
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # 支付表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    subscription_id INTEGER,
                    amount_sol REAL NOT NULL,
                    amount_usd REAL,
                    payment_method TEXT DEFAULT 'solana',
                    payment_type TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    description TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    transaction_hash TEXT,
                    block_number INTEGER,
                    gas_used INTEGER,
                    wallet_address TEXT,
                    sol_usd_rate REAL,
                    metadata TEXT,  -- JSON字符串
                    error_message TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id)
                )
            """)
            
            # 使用记录表
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS usage_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    usage_type TEXT NOT NULL,
                    status TEXT DEFAULT 'pending',
                    cost_sol REAL,
                    description TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    result_data TEXT,  -- JSON字符串
                    error_message TEXT,
                    metadata TEXT,  -- JSON字符串
                    payment_id INTEGER,
                    subscription_id INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (payment_id) REFERENCES payments (id),
                    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id)
                )
            """)
            
            # 创建索引
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users (email)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions (user_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments (user_id)")
            await conn.execute("CREATE INDEX IF NOT EXISTS idx_usage_records_user_id ON usage_records (user_id)")
            
            await conn.commit()


# 全局数据库连接实例
db_connection = DatabaseConnection()


async def get_db_connection():
    """获取数据库连接的依赖项"""
    async with db_connection.get_connection() as conn:
        yield conn


async def init_database():
    """初始化数据库"""
    await db_connection.initialize_database()
