"""
使用记录数据库操作
"""

import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from ..models.usage import UsageRecord, UsageType, UsageStatus
from .connection import db_connection


class UsageDatabase:
    """使用记录数据库操作类"""
    
    def __init__(self):
        self.db = db_connection
    
    async def create_usage_record(self, usage_data: Dict[str, Any]) -> UsageRecord:
        """创建使用记录"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                INSERT INTO usage_records (
                    user_id, usage_type, status, cost_sol, description,
                    created_at, updated_at, metadata, payment_id, subscription_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                usage_data["user_id"],
                usage_data["usage_type"],
                usage_data.get("status", UsageStatus.PENDING),
                usage_data.get("cost_sol"),
                usage_data["description"],
                usage_data["created_at"],
                usage_data["updated_at"],
                json.dumps(usage_data.get("metadata", {})),
                usage_data.get("payment_id"),
                usage_data.get("subscription_id")
            ))
            
            usage_id = cursor.lastrowid
            await conn.commit()
            
            return await self.get_usage_record_by_id(usage_id)
    
    async def get_usage_record_by_id(self, usage_id: int) -> Optional[UsageRecord]:
        """根据ID获取使用记录"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM usage_records WHERE id = ?", (usage_id,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_usage_record(row)
            return None
    
    async def get_user_usage_records(self, user_id: int, limit: int = 100,
                                   usage_type: Optional[UsageType] = None,
                                   start_date: Optional[datetime] = None,
                                   end_date: Optional[datetime] = None) -> List[UsageRecord]:
        """获取用户使用记录"""
        query = "SELECT * FROM usage_records WHERE user_id = ?"
        params = [user_id]
        
        if usage_type:
            query += " AND usage_type = ?"
            params.append(usage_type)
        
        if start_date:
            query += " AND created_at >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND created_at <= ?"
            params.append(end_date)
        
        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(query, params)
            rows = await cursor.fetchall()
            
            return [self._row_to_usage_record(row) for row in rows]
    
    async def update_usage_record(self, usage_id: int, update_data: Dict[str, Any]) -> Optional[UsageRecord]:
        """更新使用记录"""
        update_data["updated_at"] = datetime.utcnow()
        
        # 构建更新语句
        set_clauses = []
        values = []
        
        for key, value in update_data.items():
            if key in ["metadata", "result_data"] and isinstance(value, dict):
                set_clauses.append(f"{key} = ?")
                values.append(json.dumps(value))
            else:
                set_clauses.append(f"{key} = ?")
                values.append(value)
        
        values.append(usage_id)
        
        async with self.db.get_connection() as conn:
            await conn.execute(
                f"UPDATE usage_records SET {', '.join(set_clauses)} WHERE id = ?",
                values
            )
            await conn.commit()
            
            return await self.get_usage_record_by_id(usage_id)
    
    async def complete_usage_record(self, usage_id: int, result_data: Optional[Dict[str, Any]] = None) -> Optional[UsageRecord]:
        """完成使用记录"""
        update_data = {"status": UsageStatus.SUCCESS}
        if result_data:
            update_data["result_data"] = result_data
        
        return await self.update_usage_record(usage_id, update_data)
    
    async def fail_usage_record(self, usage_id: int, error_message: str) -> Optional[UsageRecord]:
        """使用记录失败"""
        return await self.update_usage_record(usage_id, {
            "status": UsageStatus.FAILED,
            "error_message": error_message
        })
    
    async def get_user_daily_usage_count(self, user_id: int, usage_type: UsageType, 
                                       date: Optional[datetime] = None) -> int:
        """获取用户每日使用次数"""
        if not date:
            date = datetime.utcnow()
        
        start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = start_of_day + timedelta(days=1)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM usage_records 
                WHERE user_id = ? AND usage_type = ? 
                AND created_at >= ? AND created_at < ?
                AND status = 'success'
            """, (user_id, usage_type, start_of_day, end_of_day))
            
            result = await cursor.fetchone()
            return result[0] if result else 0
    
    async def get_user_monthly_usage_count(self, user_id: int, usage_type: UsageType,
                                         month: Optional[datetime] = None) -> int:
        """获取用户月度使用次数"""
        if not month:
            month = datetime.utcnow()
        
        start_of_month = month.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if month.month == 12:
            end_of_month = start_of_month.replace(year=month.year + 1, month=1)
        else:
            end_of_month = start_of_month.replace(month=month.month + 1)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM usage_records 
                WHERE user_id = ? AND usage_type = ? 
                AND created_at >= ? AND created_at < ?
                AND status = 'success'
            """, (user_id, usage_type, start_of_month, end_of_month))
            
            result = await cursor.fetchone()
            return result[0] if result else 0
    
    async def get_user_usage_summary(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """获取用户使用摘要"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        async with self.db.get_connection() as conn:
            # 按类型统计使用次数
            cursor = await conn.execute("""
                SELECT usage_type, COUNT(*), SUM(COALESCE(cost_sol, 0)) 
                FROM usage_records 
                WHERE user_id = ? AND created_at >= ? AND status = 'success'
                GROUP BY usage_type
            """, (user_id, start_date))
            
            usage_by_type = {}
            total_cost = 0
            
            for row in await cursor.fetchall():
                usage_type = row[0]
                count = row[1]
                cost = row[2] or 0
                
                usage_by_type[usage_type] = {
                    "count": count,
                    "cost_sol": cost
                }
                total_cost += cost
            
            # 今日使用统计
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(COALESCE(cost_sol, 0)) 
                FROM usage_records 
                WHERE user_id = ? AND created_at >= ? AND status = 'success'
            """, (user_id, today_start))
            
            today_result = await cursor.fetchone()
            usage_today = today_result[0] if today_result else 0
            cost_today = today_result[1] if today_result else 0
            
            # 本周使用统计
            week_start = today_start - timedelta(days=today_start.weekday())
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(COALESCE(cost_sol, 0)) 
                FROM usage_records 
                WHERE user_id = ? AND created_at >= ? AND status = 'success'
            """, (user_id, week_start))
            
            week_result = await cursor.fetchone()
            usage_this_week = week_result[0] if week_result else 0
            cost_this_week = week_result[1] if week_result else 0
            
            return {
                "period_days": days,
                "usage_by_type": usage_by_type,
                "total_usage": sum(item["count"] for item in usage_by_type.values()),
                "total_cost_sol": total_cost,
                "usage_today": usage_today,
                "usage_this_week": usage_this_week,
                "cost_today_sol": cost_today,
                "cost_this_week_sol": cost_this_week
            }
    
    async def get_usage_stats(self, start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取使用统计"""
        if not start_date:
            start_date = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not end_date:
            end_date = datetime.utcnow()
        
        async with self.db.get_connection() as conn:
            # 总使用次数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM usage_records 
                WHERE created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            total_usage = (await cursor.fetchone())[0]
            
            # 成功使用次数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM usage_records 
                WHERE status = 'success' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            successful_usage = (await cursor.fetchone())[0]
            
            # 失败使用次数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM usage_records 
                WHERE status = 'failed' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            failed_usage = (await cursor.fetchone())[0]
            
            # 按类型统计
            cursor = await conn.execute("""
                SELECT usage_type, COUNT(*) FROM usage_records 
                WHERE status = 'success' AND created_at BETWEEN ? AND ?
                GROUP BY usage_type
            """, (start_date, end_date))
            
            usage_by_type = dict(await cursor.fetchall())
            
            # 收入统计
            cursor = await conn.execute("""
                SELECT SUM(COALESCE(cost_sol, 0)) FROM usage_records 
                WHERE status = 'success' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            total_revenue_sol = (await cursor.fetchone())[0] or 0
            
            # 时间段统计
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            week_start = today_start - timedelta(days=today_start.weekday())
            
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(COALESCE(cost_sol, 0)) FROM usage_records 
                WHERE status = 'success' AND created_at >= ?
            """, (today_start,))
            today_result = await cursor.fetchone()
            
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(COALESCE(cost_sol, 0)) FROM usage_records 
                WHERE status = 'success' AND created_at >= ?
            """, (week_start,))
            week_result = await cursor.fetchone()
            
            return {
                "total_usage": total_usage,
                "successful_usage": successful_usage,
                "failed_usage": failed_usage,
                "hotspot_detections": usage_by_type.get("hotspot_detection", 0),
                "strategy_generations": usage_by_type.get("strategy_generation", 0),
                "token_deployments": usage_by_type.get("token_deployment", 0),
                "wallet_creations": usage_by_type.get("wallet_creation", 0),
                "liquidity_operations": usage_by_type.get("liquidity_operation", 0),
                "duplicate_checks": usage_by_type.get("duplicate_check", 0),
                "exit_executions": usage_by_type.get("exit_execution", 0),
                "api_calls": usage_by_type.get("api_call", 0),
                "usage_today": today_result[0] if today_result else 0,
                "usage_this_week": week_result[0] if week_result else 0,
                "usage_this_month": successful_usage,
                "total_revenue_sol": total_revenue_sol,
                "revenue_today_sol": today_result[1] if today_result else 0,
                "revenue_this_week_sol": week_result[1] if week_result else 0,
                "revenue_this_month_sol": total_revenue_sol
            }
    
    def _row_to_usage_record(self, row) -> UsageRecord:
        """将数据库行转换为使用记录对象"""
        return UsageRecord(
            id=row["id"],
            user_id=row["user_id"],
            usage_type=UsageType(row["usage_type"]),
            status=UsageStatus(row["status"]),
            cost_sol=row["cost_sol"],
            description=row["description"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            result_data=json.loads(row["result_data"]) if row["result_data"] else None,
            error_message=row["error_message"],
            metadata=json.loads(row["metadata"]) if row["metadata"] else {},
            payment_id=row["payment_id"],
            subscription_id=row["subscription_id"]
        )
