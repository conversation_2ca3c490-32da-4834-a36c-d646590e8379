"""
支付数据库操作
"""

import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from ..models.payment import Payment, PaymentStatus, PaymentMethod, PaymentType
from .connection import db_connection


class PaymentDatabase:
    """支付数据库操作类"""
    
    def __init__(self):
        self.db = db_connection
    
    async def create_payment(self, payment_data: Dict[str, Any]) -> Payment:
        """创建支付记录"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                INSERT INTO payments (
                    user_id, subscription_id, amount_sol, amount_usd,
                    payment_method, payment_type, status, description,
                    created_at, updated_at, wallet_address, sol_usd_rate,
                    metadata
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                payment_data["user_id"],
                payment_data.get("subscription_id"),
                payment_data["amount_sol"],
                payment_data.get("amount_usd"),
                payment_data.get("payment_method", PaymentMethod.SOLANA),
                payment_data["payment_type"],
                payment_data.get("status", PaymentStatus.PENDING),
                payment_data["description"],
                payment_data["created_at"],
                payment_data["updated_at"],
                payment_data.get("wallet_address"),
                payment_data.get("sol_usd_rate"),
                json.dumps(payment_data.get("metadata", {}))
            ))
            
            payment_id = cursor.lastrowid
            await conn.commit()
            
            return await self.get_payment_by_id(payment_id)
    
    async def get_payment_by_id(self, payment_id: int) -> Optional[Payment]:
        """根据ID获取支付记录"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM payments WHERE id = ?", (payment_id,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_payment(row)
            return None
    
    async def get_payment_by_transaction_hash(self, transaction_hash: str) -> Optional[Payment]:
        """根据交易哈希获取支付记录"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM payments WHERE transaction_hash = ?", (transaction_hash,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_payment(row)
            return None
    
    async def get_user_payments(self, user_id: int, limit: int = 50, 
                               payment_type: Optional[PaymentType] = None) -> List[Payment]:
        """获取用户支付记录"""
        query = "SELECT * FROM payments WHERE user_id = ?"
        params = [user_id]
        
        if payment_type:
            query += " AND payment_type = ?"
            params.append(payment_type)
        
        query += " ORDER BY created_at DESC LIMIT ?"
        params.append(limit)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(query, params)
            rows = await cursor.fetchall()
            
            return [self._row_to_payment(row) for row in rows]
    
    async def update_payment(self, payment_id: int, update_data: Dict[str, Any]) -> Optional[Payment]:
        """更新支付记录"""
        update_data["updated_at"] = datetime.utcnow()
        
        # 构建更新语句
        set_clauses = []
        values = []
        
        for key, value in update_data.items():
            if key == "metadata" and isinstance(value, dict):
                set_clauses.append(f"{key} = ?")
                values.append(json.dumps(value))
            else:
                set_clauses.append(f"{key} = ?")
                values.append(value)
        
        values.append(payment_id)
        
        async with self.db.get_connection() as conn:
            await conn.execute(
                f"UPDATE payments SET {', '.join(set_clauses)} WHERE id = ?",
                values
            )
            await conn.commit()
            
            return await self.get_payment_by_id(payment_id)
    
    async def confirm_payment(self, payment_id: int, transaction_hash: str, 
                            block_number: Optional[int] = None, 
                            gas_used: Optional[int] = None) -> Optional[Payment]:
        """确认支付"""
        return await self.update_payment(payment_id, {
            "status": PaymentStatus.COMPLETED,
            "transaction_hash": transaction_hash,
            "block_number": block_number,
            "gas_used": gas_used
        })
    
    async def fail_payment(self, payment_id: int, error_message: str) -> Optional[Payment]:
        """支付失败"""
        return await self.update_payment(payment_id, {
            "status": PaymentStatus.FAILED,
            "error_message": error_message
        })
    
    async def get_pending_payments(self, older_than_minutes: int = 30) -> List[Payment]:
        """获取待处理的支付"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=older_than_minutes)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT * FROM payments 
                WHERE status = 'pending' AND created_at < ?
                ORDER BY created_at ASC
            """, (cutoff_time,))
            rows = await cursor.fetchall()
            
            return [self._row_to_payment(row) for row in rows]
    
    async def get_payment_stats(self, start_date: Optional[datetime] = None,
                              end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取支付统计"""
        if not start_date:
            start_date = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        if not end_date:
            end_date = datetime.utcnow()
        
        async with self.db.get_connection() as conn:
            # 总支付数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM payments 
                WHERE created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            total_payments = (await cursor.fetchone())[0]
            
            # 成功支付数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM payments 
                WHERE status = 'completed' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            successful_payments = (await cursor.fetchone())[0]
            
            # 失败支付数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM payments 
                WHERE status = 'failed' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            failed_payments = (await cursor.fetchone())[0]
            
            # 待处理支付数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM payments 
                WHERE status = 'pending' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            pending_payments = (await cursor.fetchone())[0]
            
            # 总收入
            cursor = await conn.execute("""
                SELECT SUM(amount_sol), SUM(amount_usd) FROM payments 
                WHERE status = 'completed' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            revenue_result = await cursor.fetchone()
            total_revenue_sol = revenue_result[0] or 0
            total_revenue_usd = revenue_result[1] or 0
            
            # 平均支付金额
            cursor = await conn.execute("""
                SELECT AVG(amount_sol) FROM payments 
                WHERE status = 'completed' AND created_at BETWEEN ? AND ?
            """, (start_date, end_date))
            avg_payment_result = await cursor.fetchone()
            average_payment_sol = avg_payment_result[0] or 0
            
            # 按类型统计
            cursor = await conn.execute("""
                SELECT payment_type, COUNT(*) FROM payments 
                WHERE status = 'completed' AND created_at BETWEEN ? AND ?
                GROUP BY payment_type
            """, (start_date, end_date))
            payment_type_stats = dict(await cursor.fetchall())
            
            # 今日统计
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(amount_sol) FROM payments 
                WHERE status = 'completed' AND created_at >= ?
            """, (today_start,))
            today_result = await cursor.fetchone()
            payments_today = today_result[0] or 0
            revenue_today_sol = today_result[1] or 0
            
            # 本周统计
            week_start = today_start - timedelta(days=today_start.weekday())
            cursor = await conn.execute("""
                SELECT COUNT(*), SUM(amount_sol) FROM payments 
                WHERE status = 'completed' AND created_at >= ?
            """, (week_start,))
            week_result = await cursor.fetchone()
            payments_this_week = week_result[0] or 0
            revenue_this_week_sol = week_result[1] or 0
            
            return {
                "total_payments": total_payments,
                "successful_payments": successful_payments,
                "failed_payments": failed_payments,
                "pending_payments": pending_payments,
                "total_revenue_sol": total_revenue_sol,
                "total_revenue_usd": total_revenue_usd,
                "average_payment_sol": average_payment_sol,
                "payment_success_rate": (successful_payments / max(total_payments, 1)) * 100,
                "subscription_payments": payment_type_stats.get("subscription", 0),
                "one_time_payments": sum(v for k, v in payment_type_stats.items() if k != "subscription"),
                "payments_today": payments_today,
                "payments_this_week": payments_this_week,
                "payments_this_month": successful_payments,
                "revenue_today_sol": revenue_today_sol,
                "revenue_this_week_sol": revenue_this_week_sol,
                "revenue_this_month_sol": total_revenue_sol
            }
    
    async def get_revenue_by_period(self, period: str = "daily", days: int = 30) -> List[Dict[str, Any]]:
        """获取按时间段的收入统计"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        if period == "daily":
            date_format = "%Y-%m-%d"
        elif period == "weekly":
            date_format = "%Y-%W"
        else:  # monthly
            date_format = "%Y-%m"
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(f"""
                SELECT 
                    strftime('{date_format}', created_at) as period,
                    COUNT(*) as payment_count,
                    SUM(amount_sol) as revenue_sol,
                    SUM(amount_usd) as revenue_usd
                FROM payments 
                WHERE status = 'completed' AND created_at BETWEEN ? AND ?
                GROUP BY strftime('{date_format}', created_at)
                ORDER BY period
            """, (start_date, end_date))
            
            rows = await cursor.fetchall()
            return [
                {
                    "period": row["period"],
                    "payment_count": row["payment_count"],
                    "revenue_sol": row["revenue_sol"] or 0,
                    "revenue_usd": row["revenue_usd"] or 0
                }
                for row in rows
            ]
    
    def _row_to_payment(self, row) -> Payment:
        """将数据库行转换为支付对象"""
        return Payment(
            id=row["id"],
            user_id=row["user_id"],
            subscription_id=row["subscription_id"],
            amount_sol=row["amount_sol"],
            amount_usd=row["amount_usd"],
            payment_method=PaymentMethod(row["payment_method"]),
            payment_type=PaymentType(row["payment_type"]),
            status=PaymentStatus(row["status"]),
            description=row["description"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            transaction_hash=row["transaction_hash"],
            block_number=row["block_number"],
            gas_used=row["gas_used"],
            wallet_address=row["wallet_address"],
            sol_usd_rate=row["sol_usd_rate"],
            metadata=json.loads(row["metadata"]) if row["metadata"] else {},
            error_message=row["error_message"]
        )
