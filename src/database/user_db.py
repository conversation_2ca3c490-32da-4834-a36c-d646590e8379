"""
用户数据库操作
"""

import json
from datetime import datetime
from typing import Optional, List, Dict, Any
from ..models.user import User, UserRole, UserStatus
from .connection import db_connection


class UserDatabase:
    """用户数据库操作类"""
    
    def __init__(self):
        self.db = db_connection
    
    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """创建用户"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                INSERT INTO users (
                    email, username, hashed_password, full_name, wallet_address,
                    role, is_active, status, created_at, updated_at,
                    free_trial_ends, daily_hotspots_reset
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                user_data["email"],
                user_data["username"], 
                user_data["hashed_password"],
                user_data.get("full_name"),
                user_data.get("wallet_address"),
                user_data.get("role", UserRole.FREE),
                user_data.get("is_active", True),
                user_data.get("status", UserStatus.ACTIVE),
                user_data["created_at"],
                user_data["updated_at"],
                user_data.get("free_trial_ends"),
                user_data["daily_hotspots_reset"]
            ))
            
            user_id = cursor.lastrowid
            await conn.commit()
            
            return await self.get_user_by_id(user_id)
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM users WHERE id = ?", (user_id,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_user(row)
            return None
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM users WHERE email = ?", (email,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_user(row)
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM users WHERE username = ?", (username,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_user(row)
            return None
    
    async def update_user(self, user_id: int, update_data: Dict[str, Any]) -> Optional[User]:
        """更新用户信息"""
        update_data["updated_at"] = datetime.utcnow()
        
        # 构建更新语句
        set_clauses = []
        values = []
        
        for key, value in update_data.items():
            set_clauses.append(f"{key} = ?")
            values.append(value)
        
        values.append(user_id)
        
        async with self.db.get_connection() as conn:
            await conn.execute(
                f"UPDATE users SET {', '.join(set_clauses)} WHERE id = ?",
                values
            )
            await conn.commit()
            
            return await self.get_user_by_id(user_id)
    
    async def delete_user(self, user_id: int) -> bool:
        """删除用户"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "DELETE FROM users WHERE id = ?", (user_id,)
            )
            await conn.commit()
            
            return cursor.rowcount > 0
    
    async def list_users(self, skip: int = 0, limit: int = 100, 
                        role: Optional[UserRole] = None,
                        status: Optional[UserStatus] = None) -> List[User]:
        """获取用户列表"""
        query = "SELECT * FROM users WHERE 1=1"
        params = []
        
        if role:
            query += " AND role = ?"
            params.append(role)
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.extend([limit, skip])
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(query, params)
            rows = await cursor.fetchall()
            
            return [self._row_to_user(row) for row in rows]
    
    async def count_users(self, role: Optional[UserRole] = None,
                         status: Optional[UserStatus] = None) -> int:
        """统计用户数量"""
        query = "SELECT COUNT(*) FROM users WHERE 1=1"
        params = []
        
        if role:
            query += " AND role = ?"
            params.append(role)
        
        if status:
            query += " AND status = ?"
            params.append(status)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(query, params)
            result = await cursor.fetchone()
            
            return result[0] if result else 0
    
    async def update_last_login(self, user_id: int) -> None:
        """更新最后登录时间"""
        await self.update_user(user_id, {"last_login": datetime.utcnow()})
    
    async def reset_daily_hotspots(self, user_id: int) -> None:
        """重置每日热点使用次数"""
        await self.update_user(user_id, {
            "daily_hotspots_used": 0,
            "daily_hotspots_reset": datetime.utcnow()
        })
    
    async def increment_daily_hotspots(self, user_id: int) -> None:
        """增加每日热点使用次数"""
        user = await self.get_user_by_id(user_id)
        if user:
            # 检查是否需要重置
            now = datetime.utcnow()
            if (now - user.daily_hotspots_reset).days >= 1:
                await self.reset_daily_hotspots(user_id)
            else:
                await self.update_user(user_id, {
                    "daily_hotspots_used": user.daily_hotspots_used + 1
                })
    
    def _row_to_user(self, row) -> User:
        """将数据库行转换为用户对象"""
        return User(
            id=row["id"],
            email=row["email"],
            username=row["username"],
            hashed_password=row["hashed_password"],
            full_name=row["full_name"],
            wallet_address=row["wallet_address"],
            role=UserRole(row["role"]),
            is_active=bool(row["is_active"]),
            status=UserStatus(row["status"]),
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            last_login=datetime.fromisoformat(row["last_login"]) if row["last_login"] else None,
            free_trial_ends=datetime.fromisoformat(row["free_trial_ends"]) if row["free_trial_ends"] else None,
            daily_hotspots_used=row["daily_hotspots_used"],
            daily_hotspots_reset=datetime.fromisoformat(row["daily_hotspots_reset"]),
            total_strategies_created=row["total_strategies_created"],
            total_deployments=row["total_deployments"],
            total_paid_features_used=row["total_paid_features_used"]
        )
