"""
订阅数据库操作
"""

import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from ..models.subscription import Subscription, SubscriptionTier, SubscriptionStatus
from .connection import db_connection


class SubscriptionDatabase:
    """订阅数据库操作类"""
    
    def __init__(self):
        self.db = db_connection
    
    async def create_subscription(self, subscription_data: Dict[str, Any]) -> Subscription:
        """创建订阅"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                INSERT INTO subscriptions (
                    user_id, tier, status, price_sol, price_usd,
                    created_at, updated_at, starts_at, expires_at,
                    auto_renew, payment_transaction_hash, features,
                    usage_limits, current_usage
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                subscription_data["user_id"],
                subscription_data["tier"],
                subscription_data.get("status", SubscriptionStatus.PENDING),
                subscription_data["price_sol"],
                subscription_data.get("price_usd"),
                subscription_data["created_at"],
                subscription_data["updated_at"],
                subscription_data["starts_at"],
                subscription_data["expires_at"],
                subscription_data.get("auto_renew", True),
                subscription_data.get("payment_transaction_hash"),
                json.dumps(subscription_data.get("features", {})),
                json.dumps(subscription_data.get("usage_limits", {})),
                json.dumps(subscription_data.get("current_usage", {}))
            ))
            
            subscription_id = cursor.lastrowid
            await conn.commit()
            
            return await self.get_subscription_by_id(subscription_id)
    
    async def get_subscription_by_id(self, subscription_id: int) -> Optional[Subscription]:
        """根据ID获取订阅"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute(
                "SELECT * FROM subscriptions WHERE id = ?", (subscription_id,)
            )
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_subscription(row)
            return None
    
    async def get_user_active_subscription(self, user_id: int) -> Optional[Subscription]:
        """获取用户当前活跃订阅"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT * FROM subscriptions 
                WHERE user_id = ? AND status = 'active' AND expires_at > ?
                ORDER BY created_at DESC LIMIT 1
            """, (user_id, datetime.utcnow()))
            row = await cursor.fetchone()
            
            if row:
                return self._row_to_subscription(row)
            return None
    
    async def get_user_subscriptions(self, user_id: int, limit: int = 10) -> List[Subscription]:
        """获取用户订阅历史"""
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT * FROM subscriptions 
                WHERE user_id = ? 
                ORDER BY created_at DESC LIMIT ?
            """, (user_id, limit))
            rows = await cursor.fetchall()
            
            return [self._row_to_subscription(row) for row in rows]
    
    async def update_subscription(self, subscription_id: int, update_data: Dict[str, Any]) -> Optional[Subscription]:
        """更新订阅信息"""
        update_data["updated_at"] = datetime.utcnow()
        
        # 构建更新语句
        set_clauses = []
        values = []
        
        for key, value in update_data.items():
            if key in ["features", "usage_limits", "current_usage"] and isinstance(value, dict):
                set_clauses.append(f"{key} = ?")
                values.append(json.dumps(value))
            else:
                set_clauses.append(f"{key} = ?")
                values.append(value)
        
        values.append(subscription_id)
        
        async with self.db.get_connection() as conn:
            await conn.execute(
                f"UPDATE subscriptions SET {', '.join(set_clauses)} WHERE id = ?",
                values
            )
            await conn.commit()
            
            return await self.get_subscription_by_id(subscription_id)
    
    async def expire_subscription(self, subscription_id: int) -> bool:
        """使订阅过期"""
        return await self.update_subscription(subscription_id, {
            "status": SubscriptionStatus.EXPIRED,
            "expires_at": datetime.utcnow()
        }) is not None
    
    async def renew_subscription(self, subscription_id: int, duration_days: int = 30) -> Optional[Subscription]:
        """续费订阅"""
        subscription = await self.get_subscription_by_id(subscription_id)
        if not subscription:
            return None
        
        new_expires_at = max(subscription.expires_at, datetime.utcnow()) + timedelta(days=duration_days)
        
        return await self.update_subscription(subscription_id, {
            "status": SubscriptionStatus.ACTIVE,
            "expires_at": new_expires_at
        })
    
    async def get_expiring_subscriptions(self, days_ahead: int = 3) -> List[Subscription]:
        """获取即将过期的订阅"""
        expiry_date = datetime.utcnow() + timedelta(days=days_ahead)
        
        async with self.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT * FROM subscriptions 
                WHERE status = 'active' AND expires_at <= ? AND expires_at > ?
                ORDER BY expires_at ASC
            """, (expiry_date, datetime.utcnow()))
            rows = await cursor.fetchall()
            
            return [self._row_to_subscription(row) for row in rows]
    
    async def get_subscription_stats(self) -> Dict[str, Any]:
        """获取订阅统计"""
        async with self.db.get_connection() as conn:
            # 总订阅数
            cursor = await conn.execute("SELECT COUNT(*) FROM subscriptions")
            total_subscriptions = (await cursor.fetchone())[0]
            
            # 活跃订阅数
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM subscriptions 
                WHERE status = 'active' AND expires_at > ?
            """, (datetime.utcnow(),))
            active_subscriptions = (await cursor.fetchone())[0]
            
            # 按层级统计
            cursor = await conn.execute("""
                SELECT tier, COUNT(*) FROM subscriptions 
                WHERE status = 'active' AND expires_at > ?
                GROUP BY tier
            """, (datetime.utcnow(),))
            tier_stats = dict(await cursor.fetchall())
            
            # 月度收入
            first_day_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            cursor = await conn.execute("""
                SELECT SUM(price_sol), SUM(price_usd) FROM subscriptions 
                WHERE created_at >= ? AND status = 'active'
            """, (first_day_of_month,))
            revenue_result = await cursor.fetchone()
            monthly_revenue_sol = revenue_result[0] or 0
            monthly_revenue_usd = revenue_result[1] or 0
            
            return {
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "free_tier_users": tier_stats.get("free", 0),
                "pro_tier_users": tier_stats.get("pro", 0),
                "enterprise_tier_users": tier_stats.get("enterprise", 0),
                "monthly_revenue_sol": monthly_revenue_sol,
                "monthly_revenue_usd": monthly_revenue_usd,
                "conversion_rate": (active_subscriptions / max(total_subscriptions, 1)) * 100,
                "churn_rate": 0  # 需要更复杂的计算
            }
    
    async def update_usage(self, subscription_id: int, usage_type: str, increment: int = 1) -> bool:
        """更新使用量"""
        subscription = await self.get_subscription_by_id(subscription_id)
        if not subscription:
            return False
        
        current_usage = subscription.current_usage.copy()
        current_usage[usage_type] = current_usage.get(usage_type, 0) + increment
        
        await self.update_subscription(subscription_id, {
            "current_usage": current_usage
        })
        
        return True
    
    async def reset_monthly_usage(self, subscription_id: int) -> bool:
        """重置月度使用量"""
        return await self.update_subscription(subscription_id, {
            "current_usage": {}
        }) is not None
    
    def _row_to_subscription(self, row) -> Subscription:
        """将数据库行转换为订阅对象"""
        return Subscription(
            id=row["id"],
            user_id=row["user_id"],
            tier=SubscriptionTier(row["tier"]),
            status=SubscriptionStatus(row["status"]),
            price_sol=row["price_sol"],
            price_usd=row["price_usd"],
            created_at=datetime.fromisoformat(row["created_at"]),
            updated_at=datetime.fromisoformat(row["updated_at"]),
            starts_at=datetime.fromisoformat(row["starts_at"]),
            expires_at=datetime.fromisoformat(row["expires_at"]),
            auto_renew=bool(row["auto_renew"]),
            payment_transaction_hash=row["payment_transaction_hash"],
            features=json.loads(row["features"]) if row["features"] else {},
            usage_limits=json.loads(row["usage_limits"]) if row["usage_limits"] else {},
            current_usage=json.loads(row["current_usage"]) if row["current_usage"] else {}
        )
