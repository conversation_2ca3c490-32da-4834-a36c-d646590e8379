"""
以太坊区块链交互模块
处理以太坊网络上的智能合约部署和交易
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from web3 import Web3
from web3.middleware import geth_poa_middleware
from eth_account import Account
from ..utils.logger import LoggerManager
from ..utils.circuit_breaker import global_circuit_breaker

logger = LoggerManager.get_logger('blockchain')


class EthereumClient:
    """以太坊客户端"""
    
    def __init__(self, rpc_url: str = None, private_key: str = None):
        """
        初始化以太坊客户端
        
        Args:
            rpc_url: RPC节点URL
            private_key: 私钥
        """
        self.rpc_url = rpc_url or os.getenv('ETH_RPC_URL')
        self.private_key = private_key or os.getenv('ETH_PRIVATE_KEY')
        
        if not self.rpc_url:
            raise ValueError("ETH_RPC_URL not provided")
        if not self.private_key:
            raise ValueError("ETH_PRIVATE_KEY not provided")
        
        # 初始化Web3连接
        self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
        
        # 添加POA中间件（用于某些测试网络）
        if 'goerli' in self.rpc_url.lower() or 'sepolia' in self.rpc_url.lower():
            self.w3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # 验证连接
        if not self.w3.is_connected():
            raise ConnectionError(f"Failed to connect to Ethereum node: {self.rpc_url}")
        
        # 设置账户
        self.account = Account.from_key(self.private_key)
        self.address = self.account.address
        
        # 获取链ID
        self.chain_id = self.w3.eth.chain_id
        
        logger.info(f"Ethereum client initialized - Chain ID: {self.chain_id}, Address: {self.address}")
    
    def get_balance(self, address: str = None) -> float:
        """
        获取ETH余额
        
        Args:
            address: 地址，默认为当前账户
            
        Returns:
            ETH余额
        """
        try:
            address = address or self.address
            balance_wei = self.w3.eth.get_balance(address)
            balance_eth = self.w3.from_wei(balance_wei, 'ether')
            return float(balance_eth)
        except Exception as e:
            logger.error(f"Failed to get balance: {str(e)}")
            return 0.0
    
    def get_gas_price(self) -> int:
        """
        获取当前Gas价格
        
        Returns:
            Gas价格（wei）
        """
        try:
            gas_price = self.w3.eth.gas_price
            
            # 应用Gas价格倍数
            multiplier = float(os.getenv('GAS_PRICE_MULTIPLIER', '1.2'))
            adjusted_price = int(gas_price * multiplier)
            
            # 检查最大Gas价格限制
            max_gas_price = int(os.getenv('MAX_GAS_PRICE', '100')) * 10**9  # 转换为wei
            if adjusted_price > max_gas_price:
                adjusted_price = max_gas_price
                logger.warning(f"Gas price capped at {max_gas_price / 10**9} Gwei")
            
            logger.info(f"Gas price: {adjusted_price / 10**9:.2f} Gwei")
            return adjusted_price
            
        except Exception as e:
            logger.error(f"Failed to get gas price: {str(e)}")
            return 20 * 10**9  # 默认20 Gwei
    
    def estimate_gas(self, transaction: Dict[str, Any]) -> int:
        """
        估算Gas消耗
        
        Args:
            transaction: 交易参数
            
        Returns:
            估算的Gas量
        """
        try:
            gas_estimate = self.w3.eth.estimate_gas(transaction)
            # 添加20%的缓冲
            return int(gas_estimate * 1.2)
        except Exception as e:
            logger.error(f"Gas estimation failed: {str(e)}")
            return 300000  # 默认值
    
    def deploy_contract(self, 
                       abi: List[Dict], 
                       bytecode: str, 
                       constructor_args: List[Any] = None) -> Optional[Dict[str, Any]]:
        """
        部署智能合约
        
        Args:
            abi: 合约ABI
            bytecode: 合约字节码
            constructor_args: 构造函数参数
            
        Returns:
            部署结果字典
        """
        try:
            # 创建合约实例
            contract = self.w3.eth.contract(abi=abi, bytecode=bytecode)
            
            # 构建部署交易
            constructor = contract.constructor(*(constructor_args or []))
            
            # 估算Gas
            gas_estimate = constructor.estimate_gas({'from': self.address})
            gas_limit = int(gas_estimate * 1.2)
            
            # 构建交易
            transaction = constructor.build_transaction({
                'from': self.address,
                'gas': gas_limit,
                'gasPrice': self.get_gas_price(),
                'nonce': self.w3.eth.get_transaction_count(self.address),
                'chainId': self.chain_id
            })
            
            # 签名并发送交易
            result = self._send_transaction(transaction)
            
            if result and result['status'] == 1:
                contract_address = result['contractAddress']
                logger.info(f"Contract deployed successfully at: {contract_address}")
                
                return {
                    'contract_address': contract_address,
                    'transaction_hash': result['transactionHash'].hex(),
                    'gas_used': result['gasUsed'],
                    'block_number': result['blockNumber']
                }
            else:
                logger.error("Contract deployment failed")
                return None
                
        except Exception as e:
            logger.error(f"Contract deployment failed: {str(e)}")
            return None
    
    def call_contract_function(self, 
                              contract_address: str,
                              abi: List[Dict],
                              function_name: str,
                              args: List[Any] = None,
                              value: int = 0) -> Optional[Dict[str, Any]]:
        """
        调用合约函数
        
        Args:
            contract_address: 合约地址
            abi: 合约ABI
            function_name: 函数名
            args: 函数参数
            value: 发送的ETH数量（wei）
            
        Returns:
            交易结果
        """
        try:
            # 创建合约实例
            contract = self.w3.eth.contract(
                address=Web3.to_checksum_address(contract_address),
                abi=abi
            )
            
            # 获取函数
            function = getattr(contract.functions, function_name)
            
            # 构建函数调用
            func_call = function(*(args or []))
            
            # 构建交易
            transaction = func_call.build_transaction({
                'from': self.address,
                'gas': self.estimate_gas({
                    'to': contract_address,
                    'from': self.address,
                    'value': value,
                    'data': func_call._encode_transaction_data()
                }),
                'gasPrice': self.get_gas_price(),
                'nonce': self.w3.eth.get_transaction_count(self.address),
                'value': value,
                'chainId': self.chain_id
            })
            
            # 发送交易
            result = self._send_transaction(transaction)
            
            if result and result['status'] == 1:
                logger.info(f"Contract function {function_name} called successfully")
                return {
                    'transaction_hash': result['transactionHash'].hex(),
                    'gas_used': result['gasUsed'],
                    'block_number': result['blockNumber']
                }
            else:
                logger.error(f"Contract function {function_name} call failed")
                return None
                
        except Exception as e:
            logger.error(f"Contract function call failed: {str(e)}")
            return None
    
    def read_contract(self, 
                     contract_address: str,
                     abi: List[Dict],
                     function_name: str,
                     args: List[Any] = None) -> Any:
        """
        读取合约状态（不消耗Gas）
        
        Args:
            contract_address: 合约地址
            abi: 合约ABI
            function_name: 函数名
            args: 函数参数
            
        Returns:
            函数返回值
        """
        try:
            contract = self.w3.eth.contract(
                address=Web3.to_checksum_address(contract_address),
                abi=abi
            )
            
            function = getattr(contract.functions, function_name)
            result = function(*(args or [])).call()
            
            logger.debug(f"Contract read {function_name}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Contract read failed: {str(e)}")
            return None
    
    def send_transaction(self, 
                        to: str, 
                        value: int = 0, 
                        data: str = "0x") -> Optional[Dict[str, Any]]:
        """
        发送交易
        
        Args:
            to: 接收地址
            value: 发送金额（wei）
            data: 交易数据
            
        Returns:
            交易结果
        """
        try:
            transaction = {
                'to': Web3.to_checksum_address(to),
                'value': value,
                'data': data,
                'gas': 21000 if data == "0x" else self.estimate_gas({
                    'to': to,
                    'from': self.address,
                    'value': value,
                    'data': data
                }),
                'gasPrice': self.get_gas_price(),
                'nonce': self.w3.eth.get_transaction_count(self.address),
                'chainId': self.chain_id
            }
            
            result = self._send_transaction(transaction)
            
            if result and result['status'] == 1:
                logger.info(f"Transaction sent successfully to {to}")
                return {
                    'transaction_hash': result['transactionHash'].hex(),
                    'gas_used': result['gasUsed'],
                    'block_number': result['blockNumber']
                }
            else:
                logger.error(f"Transaction failed to {to}")
                return None
                
        except Exception as e:
            logger.error(f"Transaction failed: {str(e)}")
            return None
    
    def _send_transaction(self, transaction: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        内部方法：签名并发送交易
        
        Args:
            transaction: 交易参数
            
        Returns:
            交易收据
        """
        try:
            # 通过熔断器执行
            return global_circuit_breaker.call(self._execute_transaction, transaction)
        except Exception as e:
            logger.error(f"Transaction execution failed: {str(e)}")
            return None
    
    def _execute_transaction(self, transaction: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        执行交易
        
        Args:
            transaction: 交易参数
            
        Returns:
            交易收据
        """
        # 签名交易
        signed_txn = self.w3.eth.account.sign_transaction(transaction, self.private_key)
        
        # 发送交易
        tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        logger.info(f"Transaction sent: {tx_hash.hex()}")
        
        # 等待交易确认
        receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        logger.info(f"Transaction confirmed in block {receipt['blockNumber']}")
        return receipt
    
    def get_transaction_status(self, tx_hash: str) -> Optional[Dict[str, Any]]:
        """
        获取交易状态
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            交易状态信息
        """
        try:
            receipt = self.w3.eth.get_transaction_receipt(tx_hash)
            transaction = self.w3.eth.get_transaction(tx_hash)
            
            return {
                'status': receipt['status'],
                'block_number': receipt['blockNumber'],
                'gas_used': receipt['gasUsed'],
                'gas_price': transaction['gasPrice'],
                'from': transaction['from'],
                'to': transaction['to'],
                'value': transaction['value']
            }
            
        except Exception as e:
            logger.error(f"Failed to get transaction status: {str(e)}")
            return None
    
    def wait_for_confirmations(self, tx_hash: str, confirmations: int = 3) -> bool:
        """
        等待交易确认
        
        Args:
            tx_hash: 交易哈希
            confirmations: 需要的确认数
            
        Returns:
            是否成功确认
        """
        try:
            receipt = self.w3.eth.get_transaction_receipt(tx_hash)
            current_block = self.w3.eth.block_number
            
            confirmed_blocks = current_block - receipt['blockNumber']
            
            while confirmed_blocks < confirmations:
                time.sleep(15)  # 等待15秒
                current_block = self.w3.eth.block_number
                confirmed_blocks = current_block - receipt['blockNumber']
                
                logger.info(f"Waiting for confirmations: {confirmed_blocks}/{confirmations}")
            
            logger.info(f"Transaction {tx_hash} confirmed with {confirmations} confirmations")
            return True
            
        except Exception as e:
            logger.error(f"Error waiting for confirmations: {str(e)}")
            return False
    
    def get_network_info(self) -> Dict[str, Any]:
        """
        获取网络信息
        
        Returns:
            网络信息字典
        """
        try:
            latest_block = self.w3.eth.get_block('latest')
            
            return {
                'chain_id': self.chain_id,
                'latest_block': latest_block['number'],
                'gas_price': self.w3.eth.gas_price,
                'peer_count': self.w3.net.peer_count,
                'is_syncing': self.w3.eth.syncing,
                'account_balance': self.get_balance()
            }
            
        except Exception as e:
            logger.error(f"Failed to get network info: {str(e)}")
            return {}


# 全局以太坊客户端实例
ethereum_client = None

def get_ethereum_client() -> EthereumClient:
    """获取全局以太坊客户端实例"""
    global ethereum_client
    if ethereum_client is None:
        ethereum_client = EthereumClient()
    return ethereum_client
