"""
支付模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum


class PaymentStatus(str, Enum):
    """支付状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"


class PaymentMethod(str, Enum):
    """支付方式"""
    SOLANA = "solana"
    ETHEREUM = "ethereum"
    POLYGON = "polygon"
    BSC = "bsc"


class PaymentType(str, Enum):
    """支付类型"""
    SUBSCRIPTION = "subscription"
    ONE_TIME_DEPLOYMENT = "one_time_deployment"
    WALLET_CREATION = "wallet_creation"
    LIQUIDITY_OPERATION = "liquidity_operation"
    DUPLICATE_CHECK = "duplicate_check"
    EXIT_EXECUTION = "exit_execution"


class PaymentBase(BaseModel):
    """支付基础模型"""
    amount_sol: float
    amount_usd: Optional[float] = None
    payment_method: PaymentMethod = PaymentMethod.SOLANA
    payment_type: PaymentType
    description: str


class PaymentCreate(PaymentBase):
    """创建支付模型"""
    user_id: int
    subscription_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class PaymentUpdate(BaseModel):
    """更新支付模型"""
    status: Optional[PaymentStatus] = None
    transaction_hash: Optional[str] = None
    block_number: Optional[int] = None
    gas_used: Optional[int] = None
    error_message: Optional[str] = None


class PaymentInDB(PaymentBase):
    """数据库中的支付模型"""
    id: int
    user_id: int
    subscription_id: Optional[int] = None
    status: PaymentStatus = PaymentStatus.PENDING
    created_at: datetime
    updated_at: datetime
    
    # 区块链信息
    transaction_hash: Optional[str] = None
    block_number: Optional[int] = None
    gas_used: Optional[int] = None
    wallet_address: Optional[str] = None
    
    # 汇率信息
    sol_usd_rate: Optional[float] = None
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class Payment(PaymentInDB):
    """完整支付模型"""
    pass


class PaymentResponse(BaseModel):
    """支付响应模型"""
    id: int
    amount_sol: float
    amount_usd: Optional[float] = None
    payment_method: PaymentMethod
    payment_type: PaymentType
    status: PaymentStatus
    description: str
    created_at: datetime
    transaction_hash: Optional[str] = None
    
    class Config:
        from_attributes = True


class PaymentRequest(BaseModel):
    """支付请求模型"""
    payment_type: PaymentType
    amount_sol: float
    description: str
    wallet_address: str
    subscription_tier: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class PaymentConfirmation(BaseModel):
    """支付确认模型"""
    payment_id: int
    transaction_hash: str
    signature: Optional[str] = None


class SolanaPaymentDetails(BaseModel):
    """Solana支付详情"""
    recipient_address: str
    amount_lamports: int
    memo: Optional[str] = None
    reference: Optional[str] = None


class PaymentStats(BaseModel):
    """支付统计模型"""
    total_payments: int
    successful_payments: int
    failed_payments: int
    pending_payments: int
    total_revenue_sol: float
    total_revenue_usd: float
    average_payment_sol: float
    payment_success_rate: float
    
    # 按类型统计
    subscription_payments: int
    one_time_payments: int
    
    # 按时间统计
    payments_today: int
    payments_this_week: int
    payments_this_month: int
    revenue_today_sol: float
    revenue_this_week_sol: float
    revenue_this_month_sol: float


# 按次付费价格表
PAY_PER_USE_PRICES = {
    PaymentType.ONE_TIME_DEPLOYMENT: {
        "price_sol": 0.5,
        "description": "一键部署代币"
    },
    PaymentType.WALLET_CREATION: {
        "price_sol": 0.008,
        "description": "创建钱包（每个）"
    },
    PaymentType.LIQUIDITY_OPERATION: {
        "price_sol": 0.001,
        "description": "流动性操作（每笔）"
    },
    PaymentType.DUPLICATE_CHECK: {
        "price_sol": 0.1,
        "description": "查重检查"
    },
    PaymentType.EXIT_EXECUTION: {
        "price_sol": 0.3,
        "description": "退出执行（基础费）",
        "profit_share": 0.05  # 5%利润分成
    }
}
