"""
用户模型
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr
from enum import Enum


class UserRole(str, Enum):
    """用户角色"""
    FREE = "free"
    PRO = "pro"
    ENTERPRISE = "enterprise"
    ADMIN = "admin"


class UserStatus(str, Enum):
    """用户状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    BANNED = "banned"


class UserBase(BaseModel):
    """用户基础模型"""
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    is_active: bool = True
    role: UserRole = UserRole.FREE


class UserCreate(UserBase):
    """创建用户模型"""
    password: str
    wallet_address: Optional[str] = None


class UserUpdate(BaseModel):
    """更新用户模型"""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None
    wallet_address: Optional[str] = None


class UserInDB(UserBase):
    """数据库中的用户模型"""
    id: int
    hashed_password: str
    wallet_address: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    status: UserStatus = UserStatus.ACTIVE
    
    # 免费版限制
    free_trial_ends: Optional[datetime] = None
    daily_hotspots_used: int = 0
    daily_hotspots_reset: datetime
    
    # 使用统计
    total_strategies_created: int = 0
    total_deployments: int = 0
    total_paid_features_used: int = 0


class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    last_login: Optional[datetime] = None
    status: UserStatus
    
    # 订阅信息
    current_subscription: Optional[str] = None
    subscription_expires: Optional[datetime] = None
    
    # 使用统计
    daily_hotspots_used: int = 0
    daily_hotspots_limit: int = 3
    total_strategies_created: int = 0
    total_deployments: int = 0
    
    class Config:
        from_attributes = True


class User(UserInDB):
    """完整用户模型"""
    pass


class UserLogin(BaseModel):
    """用户登录模型"""
    email: EmailStr
    password: str


class UserRegister(BaseModel):
    """用户注册模型"""
    email: EmailStr
    username: str
    password: str
    full_name: Optional[str] = None
    wallet_address: Optional[str] = None


class PasswordReset(BaseModel):
    """密码重置模型"""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """密码重置确认模型"""
    token: str
    new_password: str


class UserStats(BaseModel):
    """用户统计模型"""
    total_users: int
    active_users: int
    free_users: int
    pro_users: int
    enterprise_users: int
    new_users_today: int
    new_users_this_week: int
    new_users_this_month: int
