"""
使用记录模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum


class UsageType(str, Enum):
    """使用类型"""
    HOTSPOT_DETECTION = "hotspot_detection"
    STRATEGY_GENERATION = "strategy_generation"
    TOKEN_DEPLOYMENT = "token_deployment"
    WALLET_CREATION = "wallet_creation"
    LIQUIDITY_OPERATION = "liquidity_operation"
    DUPLICATE_CHECK = "duplicate_check"
    EXIT_EXECUTION = "exit_execution"
    API_CALL = "api_call"


class UsageStatus(str, Enum):
    """使用状态"""
    SUCCESS = "success"
    FAILED = "failed"
    PENDING = "pending"
    CANCELLED = "cancelled"


class UsageBase(BaseModel):
    """使用记录基础模型"""
    usage_type: UsageType
    status: UsageStatus = UsageStatus.PENDING
    cost_sol: Optional[float] = None
    description: str


class UsageCreate(UsageBase):
    """创建使用记录模型"""
    user_id: int
    metadata: Optional[Dict[str, Any]] = None


class UsageUpdate(BaseModel):
    """更新使用记录模型"""
    status: Optional[UsageStatus] = None
    cost_sol: Optional[float] = None
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class UsageInDB(UsageBase):
    """数据库中的使用记录模型"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    
    # 结果数据
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    
    # 元数据
    metadata: Optional[Dict[str, Any]] = None
    
    # 关联信息
    payment_id: Optional[int] = None
    subscription_id: Optional[int] = None


class UsageRecord(UsageInDB):
    """完整使用记录模型"""
    pass


class UsageResponse(BaseModel):
    """使用记录响应模型"""
    id: int
    usage_type: UsageType
    status: UsageStatus
    cost_sol: Optional[float] = None
    description: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class UsageStats(BaseModel):
    """使用统计模型"""
    total_usage: int
    successful_usage: int
    failed_usage: int
    
    # 按类型统计
    hotspot_detections: int
    strategy_generations: int
    token_deployments: int
    wallet_creations: int
    liquidity_operations: int
    duplicate_checks: int
    exit_executions: int
    api_calls: int
    
    # 按时间统计
    usage_today: int
    usage_this_week: int
    usage_this_month: int
    
    # 收入统计
    total_revenue_sol: float
    revenue_today_sol: float
    revenue_this_week_sol: float
    revenue_this_month_sol: float


class UserUsageSummary(BaseModel):
    """用户使用摘要"""
    user_id: int
    subscription_tier: str
    
    # 当前周期使用情况
    current_period_start: datetime
    current_period_end: datetime
    
    # 使用限制和当前使用量
    daily_hotspots_limit: int
    daily_hotspots_used: int
    
    max_strategies_limit: int
    strategies_created: int
    
    # 付费功能使用
    paid_deployments: int
    paid_wallet_creations: int
    paid_liquidity_operations: int
    paid_duplicate_checks: int
    paid_exit_executions: int
    
    # 总花费
    total_spent_sol: float
    this_month_spent_sol: float
    
    # 权限状态
    can_use_hotspot_detection: bool
    can_use_strategy_generation: bool
    can_use_one_click_deployment: bool
    can_use_duplicate_checking: bool
    can_use_advanced_features: bool


class UsageLimits(BaseModel):
    """使用限制模型"""
    subscription_tier: str
    
    # 每日限制
    daily_hotspots: int  # -1 表示无限制
    daily_api_calls: int
    
    # 功能限制
    max_strategies: int
    max_wallets: int
    
    # 内容类型限制
    allowed_content_types: list[str]
    allowed_data_sources: list[str]
    
    # 功能权限
    features: Dict[str, bool]
    
    # 免费额度
    free_duplicate_checks: int
    free_deployments: int
