"""
订阅模型
"""

from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum


class SubscriptionTier(str, Enum):
    """订阅层级"""
    FREE = "free"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class SubscriptionStatus(str, Enum):
    """订阅状态"""
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    PENDING = "pending"
    SUSPENDED = "suspended"


class SubscriptionBase(BaseModel):
    """订阅基础模型"""
    tier: SubscriptionTier
    status: SubscriptionStatus = SubscriptionStatus.PENDING
    price_sol: float
    price_usd: Optional[float] = None


class SubscriptionCreate(SubscriptionBase):
    """创建订阅模型"""
    user_id: int
    payment_transaction_hash: Optional[str] = None


class SubscriptionUpdate(BaseModel):
    """更新订阅模型"""
    status: Optional[SubscriptionStatus] = None
    expires_at: Optional[datetime] = None
    auto_renew: Optional[bool] = None


class SubscriptionInDB(SubscriptionBase):
    """数据库中的订阅模型"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    starts_at: datetime
    expires_at: datetime
    auto_renew: bool = True
    payment_transaction_hash: Optional[str] = None
    
    # 使用限制
    features: Dict[str, Any] = {}
    usage_limits: Dict[str, int] = {}
    current_usage: Dict[str, int] = {}


class Subscription(SubscriptionInDB):
    """完整订阅模型"""
    pass


class SubscriptionResponse(BaseModel):
    """订阅响应模型"""
    id: int
    tier: SubscriptionTier
    status: SubscriptionStatus
    created_at: datetime
    starts_at: datetime
    expires_at: datetime
    auto_renew: bool
    price_sol: float
    price_usd: Optional[float] = None
    
    # 功能权限
    features: Dict[str, Any]
    usage_limits: Dict[str, int]
    current_usage: Dict[str, int]
    
    class Config:
        from_attributes = True


class SubscriptionPlan(BaseModel):
    """订阅计划模型"""
    tier: SubscriptionTier
    name: str
    description: str
    price_sol: float
    price_usd: float
    duration_days: int
    features: Dict[str, Any]
    usage_limits: Dict[str, int]
    
    # 特色功能
    highlights: list[str]
    restrictions: list[str]


class SubscriptionUpgrade(BaseModel):
    """订阅升级模型"""
    target_tier: SubscriptionTier
    payment_method: str = "solana"
    wallet_address: Optional[str] = None


class SubscriptionStats(BaseModel):
    """订阅统计模型"""
    total_subscriptions: int
    active_subscriptions: int
    free_tier_users: int
    pro_tier_users: int
    enterprise_tier_users: int
    monthly_revenue_sol: float
    monthly_revenue_usd: float
    conversion_rate: float
    churn_rate: float


# 预定义的订阅计划
SUBSCRIPTION_PLANS = {
    SubscriptionTier.FREE: SubscriptionPlan(
        tier=SubscriptionTier.FREE,
        name="免费版",
        description="零成本体验基础功能",
        price_sol=0.0,
        price_usd=0.0,
        duration_days=14,  # 14天试用
        features={
            "hotspot_monitoring": True,
            "strategy_management": True,
            "basic_analytics": True,
            "one_click_deployment": False,
            "duplicate_checking": False,
            "advanced_features": False,
            "darkweb_monitoring": False,
            "custom_ai_models": False
        },
        usage_limits={
            "daily_hotspots": 3,
            "max_strategies": 2,
            "content_types": 2,  # 仅政治、科技
            "data_sources": 2,   # Reddit、Discord
        },
        highlights=[
            "热点监测（政治、科技）",
            "基础策略管理",
            "智能日历+时间轴",
            "14天免费试用"
        ],
        restrictions=[
            "每日热点限制3个",
            "最多2个策略",
            "无一键部署",
            "无查重功能"
        ]
    ),
    
    SubscriptionTier.PRO: SubscriptionPlan(
        tier=SubscriptionTier.PRO,
        name="Pro版",
        description="全功能专业交易支持",
        price_sol=15.0,
        price_usd=450.0,  # 假设SOL价格$30
        duration_days=30,
        features={
            "hotspot_monitoring": True,
            "strategy_management": True,
            "basic_analytics": True,
            "one_click_deployment": True,
            "duplicate_checking": True,
            "advanced_features": True,
            "darkweb_monitoring": False,
            "custom_ai_models": False,
            "liquidity_management": True,
            "dynamic_exit": True,
            "basic_hedging": True
        },
        usage_limits={
            "daily_hotspots": -1,  # 无限制
            "max_strategies": -1,  # 无限制
            "content_types": -1,   # 所有类型
            "data_sources": -1,    # 除暗网外所有
            "free_duplicate_checks": 5
        },
        highlights=[
            "所有内容类型监测",
            "无限制策略生成",
            "一键部署功能",
            "智能查重系统",
            "动态退出策略",
            "Gas优化返还30%"
        ],
        restrictions=[]
    ),
    
    SubscriptionTier.ENTERPRISE: SubscriptionPlan(
        tier=SubscriptionTier.ENTERPRISE,
        name="机构版",
        description="企业级定制解决方案",
        price_sol=50.0,
        price_usd=1500.0,
        duration_days=30,
        features={
            "hotspot_monitoring": True,
            "strategy_management": True,
            "basic_analytics": True,
            "one_click_deployment": True,
            "duplicate_checking": True,
            "advanced_features": True,
            "darkweb_monitoring": True,
            "custom_ai_models": True,
            "liquidity_management": True,
            "dynamic_exit": True,
            "basic_hedging": True,
            "multi_account_management": True,
            "api_access": True,
            "private_deployment": True
        },
        usage_limits={
            "daily_hotspots": -1,
            "max_strategies": -1,
            "content_types": -1,
            "data_sources": -1,
            "free_duplicate_checks": -1
        },
        highlights=[
            "暗网信号监测",
            "自定义AI模型",
            "多账户协同管理",
            "24/7专属客户经理",
            "私有链部署选项",
            "API访问权限"
        ],
        restrictions=[]
    )
}
