"""
熔断机制模块
监控系统资源和性能指标，在异常情况下自动触发熔断保护
"""

import psutil
import time
import threading
from typing import Dict, Any, Callable
from dataclasses import dataclass
from enum import Enum
from .logger import LoggerManager

logger = LoggerManager.get_logger('circuit_breaker')


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "CLOSED"      # 正常状态
    OPEN = "OPEN"          # 熔断状态
    HALF_OPEN = "HALF_OPEN"  # 半开状态


@dataclass
class SystemMetrics:
    """系统指标数据类"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    avg_latency: float
    error_rate: float
    active_connections: int


class MetricsCollector:
    """系统指标收集器"""
    
    def __init__(self):
        self.latency_samples = []
        self.error_count = 0
        self.request_count = 0
        self.start_time = time.time()
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集当前系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        
        # 网络IO
        network_io = psutil.net_io_counters()._asdict()
        
        # 平均延迟
        avg_latency = sum(self.latency_samples) / len(self.latency_samples) if self.latency_samples else 0
        
        # 错误率
        error_rate = (self.error_count / self.request_count * 100) if self.request_count > 0 else 0
        
        # 活跃连接数
        active_connections = len(psutil.net_connections())
        
        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            disk_percent=disk_percent,
            network_io=network_io,
            avg_latency=avg_latency,
            error_rate=error_rate,
            active_connections=active_connections
        )
    
    def record_latency(self, latency: float):
        """记录请求延迟"""
        self.latency_samples.append(latency)
        # 只保留最近100个样本
        if len(self.latency_samples) > 100:
            self.latency_samples.pop(0)
    
    def record_request(self, is_error: bool = False):
        """记录请求"""
        self.request_count += 1
        if is_error:
            self.error_count += 1


class CircuitBreaker:
    """熔断器主类"""
    
    # 触发阈值配置
    TRIGGER_THRESHOLDS = {
        'cpu': 90,          # CPU使用率%
        'memory': 85,       # 内存使用率%
        'disk': 95,         # 磁盘使用率%
        'latency': 5.0,     # 平均响应延迟(秒)
        'error_rate': 50,   # 错误率%
        'connections': 1000  # 最大连接数
    }
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: int = 300,
                 half_open_max_calls: int = 3):
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间(秒)
            half_open_max_calls: 半开状态最大调用次数
        """
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        self.half_open_calls = 0
        self.last_failure_time = None
        
        self.metrics_collector = MetricsCollector()
        self.callbacks = {
            'on_open': [],
            'on_close': [],
            'on_half_open': []
        }
        
        # 启动监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_system, daemon=True)
        self.monitor_thread.start()
    
    def add_callback(self, event: str, callback: Callable):
        """添加状态变化回调"""
        if event in self.callbacks:
            self.callbacks[event].append(callback)
    
    def call(self, func: Callable, *args, **kwargs):
        """
        通过熔断器调用函数
        
        Args:
            func: 要调用的函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
            
        Raises:
            CircuitBreakerOpenException: 熔断器开启时
        """
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self._set_state(CircuitState.HALF_OPEN)
            else:
                raise CircuitBreakerOpenException("Circuit breaker is OPEN")
        
        if self.state == CircuitState.HALF_OPEN:
            if self.half_open_calls >= self.half_open_max_calls:
                raise CircuitBreakerOpenException("Half-open call limit exceeded")
        
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            latency = time.time() - start_time
            
            self.metrics_collector.record_latency(latency)
            self.metrics_collector.record_request(is_error=False)
            
            self._on_success()
            return result
            
        except Exception as e:
            latency = time.time() - start_time
            self.metrics_collector.record_latency(latency)
            self.metrics_collector.record_request(is_error=True)
            
            self._on_failure()
            raise e
    
    def _monitor_system(self):
        """系统监控循环"""
        while self.monitoring:
            try:
                metrics = self.metrics_collector.collect_system_metrics()
                
                if self._exceeds_threshold(metrics):
                    logger.warning(f"System metrics exceed threshold: {metrics}")
                    self._trigger_circuit_breaker("System metrics threshold exceeded")
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                logger.error(f"Error in system monitoring: {str(e)}")
                time.sleep(30)  # 出错时延长检查间隔
    
    def _exceeds_threshold(self, metrics: SystemMetrics) -> bool:
        """检查是否超过阈值"""
        thresholds = self.TRIGGER_THRESHOLDS
        
        if metrics.cpu_percent > thresholds['cpu']:
            logger.warning(f"CPU usage {metrics.cpu_percent}% exceeds threshold {thresholds['cpu']}%")
            return True
            
        if metrics.memory_percent > thresholds['memory']:
            logger.warning(f"Memory usage {metrics.memory_percent}% exceeds threshold {thresholds['memory']}%")
            return True
            
        if metrics.disk_percent > thresholds['disk']:
            logger.warning(f"Disk usage {metrics.disk_percent}% exceeds threshold {thresholds['disk']}%")
            return True
            
        if metrics.avg_latency > thresholds['latency']:
            logger.warning(f"Average latency {metrics.avg_latency}s exceeds threshold {thresholds['latency']}s")
            return True
            
        if metrics.error_rate > thresholds['error_rate']:
            logger.warning(f"Error rate {metrics.error_rate}% exceeds threshold {thresholds['error_rate']}%")
            return True
            
        if metrics.active_connections > thresholds['connections']:
            logger.warning(f"Active connections {metrics.active_connections} exceeds threshold {thresholds['connections']}")
            return True
        
        return False
    
    def _trigger_circuit_breaker(self, reason: str):
        """触发熔断器"""
        if self.state != CircuitState.OPEN:
            logger.critical(f"Circuit breaker triggered: {reason}")
            self._set_state(CircuitState.OPEN)
    
    def _on_success(self):
        """成功调用处理"""
        if self.state == CircuitState.HALF_OPEN:
            self.half_open_calls += 1
            if self.half_open_calls >= self.half_open_max_calls:
                self._set_state(CircuitState.CLOSED)
        
        self.failure_count = 0
    
    def _on_failure(self):
        """失败调用处理"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self._set_state(CircuitState.OPEN)
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        if self.last_failure_time is None:
            return False
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _set_state(self, new_state: CircuitState):
        """设置熔断器状态"""
        old_state = self.state
        self.state = new_state
        
        if new_state == CircuitState.HALF_OPEN:
            self.half_open_calls = 0
        
        logger.info(f"Circuit breaker state changed: {old_state.value} -> {new_state.value}")
        
        # 执行回调
        for callback in self.callbacks.get(f'on_{new_state.value.lower()}', []):
            try:
                callback()
            except Exception as e:
                logger.error(f"Error in circuit breaker callback: {str(e)}")
    
    def get_state(self) -> CircuitState:
        """获取当前状态"""
        return self.state
    
    def get_metrics(self) -> SystemMetrics:
        """获取当前系统指标"""
        return self.metrics_collector.collect_system_metrics()
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False


class CircuitBreakerOpenException(Exception):
    """熔断器开启异常"""
    pass


# 全局熔断器实例
global_circuit_breaker = CircuitBreaker()
