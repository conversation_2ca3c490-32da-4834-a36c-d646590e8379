"""
日志配置模块
提供统一的日志记录功能，支持文件轮转和多级别日志
"""

import logging
import os
from logging.handlers import RotatingFileHandler
from datetime import datetime

# 可选导入structlog
try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False


def setup_logger(name: str, log_file: str, level: int = logging.INFO) -> logging.Logger:
    """
    创建自定义logger
    
    Args:
        name: logger名称
        log_file: 日志文件路径
        level: 日志级别
        
    Returns:
        配置好的logger实例
    """
    # 确保日志目录存在
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # 文件日志处理器 - 10MB轮转，保留5个备份
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024, 
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    
    # 控制台日志处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 创建logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if not logger.handlers:
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger


def setup_structured_logger():
    """
    设置结构化日志记录
    使用structlog提供更好的日志结构
    """
    if not STRUCTLOG_AVAILABLE:
        print("Warning: structlog not available, skipping structured logging setup")
        return

    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


class LoggerManager:
    """日志管理器，统一管理各模块的日志"""
    
    _loggers = {}
    
    @classmethod
    def get_logger(cls, module_name: str) -> logging.Logger:
        """
        获取指定模块的logger

        Args:
            module_name: 模块名称

        Returns:
            logger实例
        """
        if module_name not in cls._loggers:
            # 使用项目目录下的logs文件夹，避免权限问题
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            log_dir = os.path.join(project_root, "logs")
            log_file = os.path.join(log_dir, f"{module_name}.log")

            cls._loggers[module_name] = setup_logger(
                module_name,
                log_file,
                level=logging.INFO
            )

        return cls._loggers[module_name]


# 预定义的logger实例
trade_logger = LoggerManager.get_logger('trading')
hotspot_logger = LoggerManager.get_logger('hotspot')
strategy_logger = LoggerManager.get_logger('strategy')
blockchain_logger = LoggerManager.get_logger('blockchain')
system_logger = LoggerManager.get_logger('system')
security_logger = LoggerManager.get_logger('security')


def log_function_call(func):
    """
    装饰器：记录函数调用
    """
    def wrapper(*args, **kwargs):
        logger = LoggerManager.get_logger(func.__module__)
        logger.info(f"Calling {func.__name__} with args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            logger.info(f"{func.__name__} completed successfully")
            return result
        except Exception as e:
            logger.error(f"{func.__name__} failed with error: {str(e)}")
            raise
    
    return wrapper


def log_performance(func):
    """
    装饰器：记录函数执行时间
    """
    def wrapper(*args, **kwargs):
        logger = LoggerManager.get_logger(func.__module__)
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"{func.__name__} executed in {duration:.3f} seconds")
            return result
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.error(f"{func.__name__} failed after {duration:.3f} seconds: {str(e)}")
            raise
    
    return wrapper


# 初始化结构化日志
setup_structured_logger()
