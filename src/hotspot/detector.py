"""
热点捕捉模块
多源数据采集与分析，包括社交媒体、暗网和视频内容分析
"""

import asyncio
import aiohttp
# import torch  # 延迟导入优化
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
from typing import Dict, List, Any, Optional
import time
import json
from dataclasses import dataclass
from ..utils.logger import LoggerManager
from ..utils.circuit_breaker import global_circuit_breaker

logger = LoggerManager.get_logger('hotspot')


@dataclass
class HotspotData:
    """热点数据结构"""
    source: str
    content: str
    sentiment_score: float
    viral_score: float
    timestamp: float
    metadata: Dict[str, Any]


class TorSession:
    """Tor会话管理器"""
    
    def __init__(self, proxy_url: str = "socks5://127.0.0.1:9050"):
        self.proxy_url = proxy_url
        self.session = None
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector()
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()


class SentimentAnalyzer:
    """情感分析器"""
    
    def __init__(self, model_name: str = "xlm-roberta-large"):
        self.device = 0 if torch.cuda.is_available() else -1
        logger.info(f"Loading sentiment model on device: {'GPU' if self.device == 0 else 'CPU'}")
        
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
            self.pipeline = pipeline(
                "sentiment-analysis",
                model=self.model,
                tokenizer=self.tokenizer,
                device=self.device
            )
            logger.info("Sentiment analyzer initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize sentiment analyzer: {str(e)}")
            # 回退到基础模型
            self.pipeline = pipeline("sentiment-analysis", device=self.device)
    
    def analyze(self, text: str) -> Dict[str, float]:
        """
        分析文本情感
        
        Args:
            text: 待分析文本
            
        Returns:
            情感分析结果
        """
        try:
            # 限制文本长度避免内存问题
            text = text[:512]
            result = self.pipeline(text)[0]
            
            # 标准化分数到-1到1之间
            score = result['score']
            if result['label'] == 'NEGATIVE':
                score = -score
            
            return {
                'sentiment': result['label'],
                'score': score,
                'confidence': result['score']
            }
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
            return {'sentiment': 'NEUTRAL', 'score': 0.0, 'confidence': 0.0}


class ViralityCalculator:
    """病毒性传播计算器"""
    
    @staticmethod
    def calculate_viral_score(data: Dict[str, Any]) -> float:
        """
        计算内容的病毒性传播分数
        
        Args:
            data: 包含互动数据的字典
            
        Returns:
            病毒性分数 (0-1)
        """
        try:
            # 基础指标
            likes = data.get('likes', 0)
            shares = data.get('shares', 0)
            comments = data.get('comments', 0)
            views = data.get('views', 1)  # 避免除零
            
            # 计算互动率
            engagement_rate = (likes + shares + comments) / views
            
            # 时间衰减因子
            timestamp = data.get('timestamp', time.time())
            time_decay = max(0, 1 - (time.time() - timestamp) / (24 * 3600))  # 24小时衰减
            
            # 增长率
            growth_rate = data.get('growth_rate', 0)
            
            # 综合计算病毒性分数
            viral_score = min(1.0, (
                engagement_rate * 0.4 +
                time_decay * 0.3 +
                growth_rate * 0.3
            ))
            
            return viral_score
            
        except Exception as e:
            logger.error(f"Viral score calculation failed: {str(e)}")
            return 0.0


class HotspotDetector:
    """热点检测器主类"""
    
    def __init__(self):
        self.sentiment_analyzer = SentimentAnalyzer()
        self.virality_calculator = ViralityCalculator()
        self.tor_session = TorSession()
        
        # 数据源配置
        self.sources = {
            'twitter': self._analyze_twitter,
            'reddit': self._analyze_reddit,
            'telegram': self._analyze_telegram,
            'darkweb': self._analyze_darkweb,
            'video': self._analyze_video,
            'news': self._analyze_news
        }
        
        logger.info("HotspotDetector initialized")
    
    async def analyze_source(self, source_url: str, source_type: str = None) -> Optional[HotspotData]:
        """
        分析指定数据源
        
        Args:
            source_url: 数据源URL
            source_type: 数据源类型
            
        Returns:
            热点数据或None
        """
        try:
            # 自动检测数据源类型
            if source_type is None:
                source_type = self._detect_source_type(source_url)
            
            if source_type not in self.sources:
                logger.warning(f"Unsupported source type: {source_type}")
                return None
            
            # 通过熔断器执行分析
            return await global_circuit_breaker.call(
                self.sources[source_type], 
                source_url
            )
            
        except Exception as e:
            logger.error(f"Source analysis failed for {source_url}: {str(e)}")
            return None
    
    def _detect_source_type(self, url: str) -> str:
        """检测数据源类型"""
        if 'twitter.com' in url or 'x.com' in url:
            return 'twitter'
        elif 'reddit.com' in url:
            return 'reddit'
        elif 't.me' in url:
            return 'telegram'
        elif 'youtube.com' in url or 'tiktok.com' in url:
            return 'video'
        elif any(dark in url for dark in ['onion', 'darkweb', 'tor']):
            return 'darkweb'
        else:
            return 'news'
    
    async def _analyze_twitter(self, url: str) -> Optional[HotspotData]:
        """分析Twitter数据"""
        try:
            # 模拟Twitter API调用
            async with aiohttp.ClientSession() as session:
                # 这里应该使用真实的Twitter API
                # 为了演示，使用模拟数据
                data = {
                    'text': 'Sample tweet content about crypto',
                    'likes': 1500,
                    'retweets': 300,
                    'comments': 150,
                    'timestamp': time.time() - 3600  # 1小时前
                }
                
                sentiment = self.sentiment_analyzer.analyze(data['text'])
                viral_score = self.virality_calculator.calculate_viral_score({
                    'likes': data['likes'],
                    'shares': data['retweets'],
                    'comments': data['comments'],
                    'views': data['likes'] * 10,  # 估算浏览量
                    'timestamp': data['timestamp']
                })
                
                return HotspotData(
                    source='twitter',
                    content=data['text'],
                    sentiment_score=sentiment['score'],
                    viral_score=viral_score,
                    timestamp=data['timestamp'],
                    metadata={
                        'url': url,
                        'likes': data['likes'],
                        'retweets': data['retweets'],
                        'sentiment_detail': sentiment
                    }
                )
                
        except Exception as e:
            logger.error(f"Twitter analysis failed: {str(e)}")
            return None
    
    async def _analyze_reddit(self, url: str) -> Optional[HotspotData]:
        """分析Reddit数据"""
        try:
            # 模拟Reddit API调用
            data = {
                'title': 'New meme coin discussion',
                'content': 'This new token has potential...',
                'upvotes': 2500,
                'comments': 400,
                'timestamp': time.time() - 7200  # 2小时前
            }
            
            full_text = f"{data['title']} {data['content']}"
            sentiment = self.sentiment_analyzer.analyze(full_text)
            viral_score = self.virality_calculator.calculate_viral_score({
                'likes': data['upvotes'],
                'shares': data['upvotes'] // 10,  # 估算分享数
                'comments': data['comments'],
                'views': data['upvotes'] * 20,
                'timestamp': data['timestamp']
            })
            
            return HotspotData(
                source='reddit',
                content=full_text,
                sentiment_score=sentiment['score'],
                viral_score=viral_score,
                timestamp=data['timestamp'],
                metadata={
                    'url': url,
                    'upvotes': data['upvotes'],
                    'comments': data['comments'],
                    'sentiment_detail': sentiment
                }
            )
            
        except Exception as e:
            logger.error(f"Reddit analysis failed: {str(e)}")
            return None
    
    async def _analyze_telegram(self, url: str) -> Optional[HotspotData]:
        """分析Telegram数据"""
        # 实现Telegram数据分析
        logger.info(f"Analyzing Telegram source: {url}")
        return None
    
    async def _analyze_darkweb(self, url: str) -> Optional[HotspotData]:
        """分析暗网数据"""
        try:
            async with self.tor_session as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._process_darkweb_data(data)
                    else:
                        logger.warning(f"Darkweb request failed with status: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Darkweb analysis failed: {str(e)}")
            return None
    
    def _process_darkweb_data(self, data: Dict[str, Any]) -> Optional[HotspotData]:
        """处理暗网数据"""
        try:
            content = data.get('content', '')
            sentiment = self.sentiment_analyzer.analyze(content)
            
            # 暗网数据的病毒性计算方式不同
            viral_score = min(1.0, data.get('activity_score', 0) / 100)
            
            return HotspotData(
                source='darkweb',
                content=content,
                sentiment_score=sentiment['score'],
                viral_score=viral_score,
                timestamp=data.get('timestamp', time.time()),
                metadata={
                    'activity_score': data.get('activity_score', 0),
                    'sentiment_detail': sentiment
                }
            )
            
        except Exception as e:
            logger.error(f"Darkweb data processing failed: {str(e)}")
            return None
    
    async def _analyze_video(self, url: str) -> Optional[HotspotData]:
        """分析视频内容"""
        # 这里应该实现视频内容分析，包括OCR和语音识别
        logger.info(f"Analyzing video source: {url}")
        return None
    
    async def _analyze_news(self, url: str) -> Optional[HotspotData]:
        """分析新闻内容"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        # 这里应该解析HTML内容
                        content = await response.text()
                        # 简化处理，实际应该使用BeautifulSoup等工具
                        
                        sentiment = self.sentiment_analyzer.analyze(content[:1000])
                        viral_score = 0.5  # 新闻的默认病毒性分数
                        
                        return HotspotData(
                            source='news',
                            content=content[:500],
                            sentiment_score=sentiment['score'],
                            viral_score=viral_score,
                            timestamp=time.time(),
                            metadata={
                                'url': url,
                                'sentiment_detail': sentiment
                            }
                        )
                        
        except Exception as e:
            logger.error(f"News analysis failed: {str(e)}")
            return None
    
    async def batch_analyze(self, sources: List[str]) -> List[HotspotData]:
        """
        批量分析多个数据源
        
        Args:
            sources: 数据源URL列表
            
        Returns:
            热点数据列表
        """
        tasks = [self.analyze_source(source) for source in sources]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉None和异常结果
        valid_results = [
            result for result in results 
            if isinstance(result, HotspotData)
        ]
        
        logger.info(f"Batch analysis completed: {len(valid_results)}/{len(sources)} successful")
        return valid_results
    
    def rank_hotspots(self, hotspots: List[HotspotData]) -> List[HotspotData]:
        """
        对热点进行排名
        
        Args:
            hotspots: 热点数据列表
            
        Returns:
            排序后的热点列表
        """
        def calculate_rank_score(hotspot: HotspotData) -> float:
            # 综合评分算法
            sentiment_weight = 0.3
            viral_weight = 0.5
            time_weight = 0.2
            
            # 时间衰减
            time_decay = max(0, 1 - (time.time() - hotspot.timestamp) / (6 * 3600))  # 6小时衰减
            
            score = (
                abs(hotspot.sentiment_score) * sentiment_weight +
                hotspot.viral_score * viral_weight +
                time_decay * time_weight
            )
            
            return score
        
        # 按综合评分排序
        ranked = sorted(hotspots, key=calculate_rank_score, reverse=True)
        
        logger.info(f"Ranked {len(ranked)} hotspots")
        return ranked
