"""
策略生成模块
基于热点数据生成代币发行和交易策略
"""

import time
import random
import hashlib
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import requests
from ..utils.logger import LoggerManager
from ..compliance.geofence import GeoFence
from ..compliance.trademark import TrademarkAPI

logger = LoggerManager.get_logger('strategy')


@dataclass
class TokenStrategy:
    """代币策略数据结构"""
    token_name: str
    symbol: str
    total_supply: int
    burn_rate: float
    launch_schedule: Dict[str, Any]
    economic_model: Dict[str, Any]
    marketing_plan: Dict[str, Any]
    risk_parameters: Dict[str, Any]
    compliance_status: str
    estimated_roi: float


class FearGreedIndex:
    """恐惧贪婪指数获取器"""
    
    API_URL = "https://api.alternative.me/fng/"
    
    @classmethod
    def fetch(cls) -> float:
        """
        获取当前恐惧贪婪指数
        
        Returns:
            指数值 (0-100)
        """
        try:
            response = requests.get(cls.API_URL, timeout=10)
            if response.status_code == 200:
                data = response.json()
                return float(data['data'][0]['value'])
            else:
                logger.warning(f"Failed to fetch fear greed index: {response.status_code}")
                return 50.0  # 默认中性值
                
        except Exception as e:
            logger.error(f"Error fetching fear greed index: {str(e)}")
            return 50.0


class EconomicModelCalculator:
    """经济模型计算器"""
    
    @staticmethod
    def calculate_burn_rate(greed_index: float, volatility: float = 0.5) -> float:
        """
        计算动态燃烧率
        
        Args:
            greed_index: 恐惧贪婪指数 (0-100)
            volatility: 市场波动率 (0-1)
            
        Returns:
            燃烧率 (0-1)
        """
        base_rate = 0.05  # 基础燃烧率5%
        
        # 贪婪指数调整：指数越高，燃烧率越低
        greed_adjustment = (50 - greed_index) * 0.001
        
        # 波动率调整：波动率越高，燃烧率越高
        volatility_adjustment = volatility * 0.03
        
        burn_rate = base_rate + greed_adjustment + volatility_adjustment
        
        # 限制在合理范围内
        return max(0.01, min(0.15, burn_rate))
    
    @staticmethod
    def calculate_supply_distribution(total_supply: int) -> Dict[str, int]:
        """
        计算代币供应分配
        
        Args:
            total_supply: 总供应量
            
        Returns:
            分配方案
        """
        return {
            'liquidity_pool': int(total_supply * 0.40),    # 40% 流动性池
            'marketing': int(total_supply * 0.20),         # 20% 营销
            'team': int(total_supply * 0.15),              # 15% 团队
            'development': int(total_supply * 0.10),       # 10% 开发
            'community': int(total_supply * 0.10),         # 10% 社区
            'reserve': int(total_supply * 0.05)            # 5% 储备
        }


class LaunchTimeOptimizer:
    """发布时间优化器"""
    
    # 不同时区的最佳发布时间 (UTC小时)
    OPTIMAL_HOURS = {
        'US_EAST': [13, 14, 15, 20, 21],      # 美东时间 9-11AM, 4-5PM
        'US_WEST': [16, 17, 18, 23, 0],       # 美西时间 9-11AM, 4-5PM
        'EUROPE': [8, 9, 10, 15, 16],         # 欧洲时间 9-11AM, 4-5PM
        'ASIA': [1, 2, 3, 8, 9]               # 亚洲时间 9-11AM, 4-5PM
    }
    
    @classmethod
    def optimize_launch_times(cls, target_regions: List[str] = None) -> Dict[str, datetime]:
        """
        优化发布时间
        
        Args:
            target_regions: 目标地区列表
            
        Returns:
            优化后的发布时间表
        """
        if target_regions is None:
            target_regions = ['US_EAST', 'EUROPE', 'ASIA']
        
        now = datetime.utcnow()
        launch_times = {}
        
        for region in target_regions:
            if region in cls.OPTIMAL_HOURS:
                optimal_hours = cls.OPTIMAL_HOURS[region]
                
                # 找到下一个最佳时间
                current_hour = now.hour
                next_optimal = None
                
                for hour in optimal_hours:
                    if hour > current_hour:
                        next_optimal = hour
                        break
                
                if next_optimal is None:
                    # 如果今天没有合适时间，选择明天的第一个
                    next_optimal = optimal_hours[0]
                    launch_date = now + timedelta(days=1)
                else:
                    launch_date = now
                
                launch_time = launch_date.replace(
                    hour=next_optimal, 
                    minute=0, 
                    second=0, 
                    microsecond=0
                )
                
                launch_times[region] = launch_time
        
        return launch_times


class StrategyGenerator:
    """策略生成器主类"""
    
    def __init__(self):
        self.geofence = GeoFence()
        self.trademark_api = TrademarkAPI()
        self.economic_calculator = EconomicModelCalculator()
        self.launch_optimizer = LaunchTimeOptimizer()
        
        logger.info("StrategyGenerator initialized")
    
    def generate_token_strategy(self, hotspot_data: Dict[str, Any]) -> Optional[TokenStrategy]:
        """
        生成代币策略
        
        Args:
            hotspot_data: 热点数据
            
        Returns:
            代币策略或None
        """
        try:
            # 地理围栏检查
            if self.geofence.is_restricted():
                raise RestrictedRegionError("Operation not allowed in your region")
            
            # 生成代币名称和符号
            token_name = self._generate_token_name(hotspot_data)
            symbol = self._generate_symbol(token_name)
            
            # 商标筛查
            if self.trademark_api.check_trademark(token_name):
                raise TrademarkViolation(f"Name conflict: {token_name}")
            
            # 获取市场指标
            greed_index = FearGreedIndex.fetch()
            volatility = self._estimate_volatility(hotspot_data)
            
            # 计算经济模型参数
            burn_rate = self.economic_calculator.calculate_burn_rate(greed_index, volatility)
            total_supply = self._calculate_total_supply(hotspot_data)
            supply_distribution = self.economic_calculator.calculate_supply_distribution(total_supply)
            
            # 优化发布时间
            launch_times = self.launch_optimizer.optimize_launch_times()
            
            # 生成营销计划
            marketing_plan = self._generate_marketing_plan(hotspot_data)
            
            # 风险参数
            risk_parameters = self._calculate_risk_parameters(hotspot_data, volatility)
            
            # 估算ROI
            estimated_roi = self._estimate_roi(hotspot_data, greed_index, volatility)
            
            strategy = TokenStrategy(
                token_name=token_name,
                symbol=symbol,
                total_supply=total_supply,
                burn_rate=burn_rate,
                launch_schedule={
                    'launch_times': launch_times,
                    'phases': self._generate_launch_phases()
                },
                economic_model={
                    'burn_rate': burn_rate,
                    'supply_distribution': supply_distribution,
                    'greed_index': greed_index,
                    'volatility': volatility
                },
                marketing_plan=marketing_plan,
                risk_parameters=risk_parameters,
                compliance_status="PASSED",
                estimated_roi=estimated_roi
            )
            
            logger.info(f"Generated strategy for token: {token_name}")
            return strategy
            
        except Exception as e:
            logger.error(f"Strategy generation failed: {str(e)}")
            return None
    
    def _generate_token_name(self, hotspot_data: Dict[str, Any]) -> str:
        """生成代币名称"""
        content = hotspot_data.get('content', '')
        
        # 提取关键词
        keywords = self._extract_keywords(content)
        
        # 生成创意名称
        if keywords:
            base_name = keywords[0].capitalize()
            suffixes = ['Coin', 'Token', 'Inu', 'Moon', 'Safe', 'Doge']
            suffix = random.choice(suffixes)
            return f"{base_name}{suffix}"
        else:
            # 回退到随机名称
            prefixes = ['Meme', 'Viral', 'Trend', 'Hype', 'Moon']
            suffixes = ['Coin', 'Token', 'Inu']
            return f"{random.choice(prefixes)}{random.choice(suffixes)}"
    
    def _generate_symbol(self, token_name: str) -> str:
        """生成代币符号"""
        # 取名称的前3-4个字符
        symbol = ''.join([c for c in token_name if c.isupper()])
        if len(symbol) < 3:
            symbol = token_name[:4].upper()
        elif len(symbol) > 5:
            symbol = symbol[:5]
        
        return symbol
    
    def _extract_keywords(self, content: str) -> List[str]:
        """提取关键词"""
        # 简化的关键词提取
        words = content.split()
        keywords = []
        
        for word in words:
            if len(word) > 3 and word.isalpha():
                keywords.append(word.lower())
        
        return keywords[:5]  # 返回前5个关键词
    
    def _calculate_total_supply(self, hotspot_data: Dict[str, Any]) -> int:
        """计算总供应量"""
        viral_score = hotspot_data.get('viral_score', 0.5)
        
        # 基于病毒性分数调整供应量
        base_supply = 1_000_000_000  # 10亿基础供应量
        
        if viral_score > 0.8:
            return base_supply * 10  # 高病毒性，更多供应量
        elif viral_score > 0.6:
            return base_supply * 5
        elif viral_score > 0.4:
            return base_supply * 2
        else:
            return base_supply
    
    def _estimate_volatility(self, hotspot_data: Dict[str, Any]) -> float:
        """估算波动率"""
        viral_score = hotspot_data.get('viral_score', 0.5)
        sentiment_score = abs(hotspot_data.get('sentiment_score', 0))
        
        # 病毒性和情感强度都会增加波动率
        volatility = (viral_score + sentiment_score) / 2
        
        return min(1.0, max(0.1, volatility))
    
    def _generate_marketing_plan(self, hotspot_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成营销计划"""
        return {
            'social_media': {
                'twitter': True,
                'telegram': True,
                'reddit': True,
                'discord': True
            },
            'influencer_budget': 50000,  # USDT
            'community_rewards': 100000,  # 代币数量
            'airdrop_allocation': 500000,
            'marketing_phases': [
                {'phase': 'pre_launch', 'duration_hours': 24, 'budget': 20000},
                {'phase': 'launch', 'duration_hours': 48, 'budget': 30000},
                {'phase': 'growth', 'duration_hours': 168, 'budget': 50000}
            ]
        }
    
    def _calculate_risk_parameters(self, hotspot_data: Dict[str, Any], volatility: float) -> Dict[str, Any]:
        """计算风险参数"""
        return {
            'max_slippage': min(0.15, volatility * 0.3),
            'stop_loss': 0.3,  # 30%止损
            'take_profit': 2.0,  # 200%止盈
            'max_position_size': 0.1,  # 最大仓位10%
            'volatility_threshold': 0.8,
            'liquidity_threshold': 100000  # 最小流动性要求
        }
    
    def _estimate_roi(self, hotspot_data: Dict[str, Any], greed_index: float, volatility: float) -> float:
        """估算ROI"""
        viral_score = hotspot_data.get('viral_score', 0.5)
        sentiment_score = abs(hotspot_data.get('sentiment_score', 0))
        
        # ROI基础计算
        base_roi = viral_score * 2 + sentiment_score * 1.5
        
        # 市场情绪调整
        if greed_index > 70:
            base_roi *= 1.5  # 贪婪市场，更高ROI
        elif greed_index < 30:
            base_roi *= 0.7  # 恐惧市场，降低ROI
        
        # 波动率调整
        base_roi *= (1 + volatility)
        
        return min(10.0, max(0.1, base_roi))  # 限制在0.1-10倍之间
    
    def _generate_launch_phases(self) -> List[Dict[str, Any]]:
        """生成发布阶段"""
        return [
            {
                'phase': 'preparation',
                'duration_hours': 2,
                'actions': ['deploy_contract', 'setup_liquidity', 'verify_contract']
            },
            {
                'phase': 'soft_launch',
                'duration_hours': 6,
                'actions': ['limited_trading', 'community_announcement', 'initial_marketing']
            },
            {
                'phase': 'public_launch',
                'duration_hours': 24,
                'actions': ['full_trading', 'major_marketing', 'influencer_campaign']
            },
            {
                'phase': 'growth',
                'duration_hours': 168,  # 1周
                'actions': ['sustained_marketing', 'community_building', 'partnership_development']
            }
        ]


class RestrictedRegionError(Exception):
    """受限地区错误"""
    pass


class TrademarkViolation(Exception):
    """商标冲突错误"""
    pass
