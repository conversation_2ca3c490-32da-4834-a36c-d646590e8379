"""
认证中间件
"""

from datetime import datetime
from typing import Optional, Callable
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..models.user import User, UserRole
from ..models.subscription import SubscriptionTier
from .auth_manager import get_current_active_user
from ..subscription.feature_checker import FeatureChecker


class AuthMiddleware:
    """认证中间件"""
    
    def __init__(self):
        self.security = HTTPBearer()
    
    async def verify_api_access(self, request: Request, user: User) -> bool:
        """验证API访问权限"""
        # 检查试用状态
        trial_status = FeatureChecker.check_trial_status(user)
        
        if trial_status["is_trial"] and trial_status["trial_expired"]:
            # 试用已过期，只允许查看功能
            if request.method != "GET":
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail="免费试用已过期，请升级订阅以继续使用"
                )
        
        return True
    
    async def check_daily_limits(self, user: User, feature_type: str) -> bool:
        """检查每日使用限制"""
        if feature_type == "hotspot_detection":
            # 检查每日热点检测限制
            limits = FeatureChecker.get_user_limits(user)
            daily_limit = limits.get("daily_hotspots", 3)
            
            if daily_limit != -1 and user.daily_hotspots_used >= daily_limit:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail=f"今日热点检测次数已用完（{daily_limit}次），请明天再试或升级订阅"
                )
        
        return True
    
    async def check_feature_quota(self, user: User, feature_name: str, 
                                current_usage: int = 0) -> bool:
        """检查功能配额"""
        if not FeatureChecker.check_usage_limit(user, feature_name, current_usage):
            limits = FeatureChecker.get_user_limits(user)
            limit = limits.get(feature_name, 0)
            
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"已达到 {feature_name} 使用限制（{limit}次），请升级订阅"
            )
        
        return True


# 中间件实例
auth_middleware = AuthMiddleware()


def require_subscription(min_tier: SubscriptionTier = SubscriptionTier.FREE):
    """要求特定订阅级别的装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            # 从参数中获取用户对象
            user = None
            for arg in args:
                if isinstance(arg, User):
                    user = arg
                    break
            
            if user is None:
                for key, value in kwargs.items():
                    if isinstance(value, User):
                        user = value
                        break
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要用户认证"
                )
            
            # 检查订阅级别
            user_tier = SubscriptionTier.FREE
            if user.role == UserRole.PRO:
                user_tier = SubscriptionTier.PRO
            elif user.role == UserRole.ENTERPRISE:
                user_tier = SubscriptionTier.ENTERPRISE
            
            tier_levels = {
                SubscriptionTier.FREE: 0,
                SubscriptionTier.PRO: 1,
                SubscriptionTier.ENTERPRISE: 2
            }
            
            if tier_levels[user_tier] < tier_levels[min_tier]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"此功能需要 {min_tier.value} 或更高级别的订阅"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_feature_access(feature_name: str):
    """要求特定功能权限的装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            user = None
            for arg in args:
                if isinstance(arg, User):
                    user = arg
                    break
            
            if user is None:
                for key, value in kwargs.items():
                    if isinstance(value, User):
                        user = value
                        break
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要用户认证"
                )
            
            if not FeatureChecker.check_feature_access(user, feature_name):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"您的订阅计划不支持 {feature_name} 功能，请升级订阅"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_paid_feature(feature_type: str, cost_sol: float = 0.0):
    """要求付费功能的装饰器"""
    def decorator(func: Callable) -> Callable:
        async def wrapper(*args, **kwargs):
            user = None
            for arg in args:
                if isinstance(arg, User):
                    user = arg
                    break
            
            if user is None:
                for key, value in kwargs.items():
                    if isinstance(value, User):
                        user = value
                        break
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要用户认证"
                )
            
            # 检查是否为免费用户且功能需要付费
            if user.role == UserRole.FREE and cost_sol > 0:
                raise HTTPException(
                    status_code=status.HTTP_402_PAYMENT_REQUIRED,
                    detail=f"此功能需要支付 {cost_sol} SOL，或升级到 Pro 版本"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 依赖项函数
async def get_authenticated_user(
    credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())
) -> User:
    """获取认证用户的依赖项"""
    return await get_current_active_user(credentials)


async def require_pro_subscription(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """要求Pro订阅的依赖项"""
    if current_user.role not in [UserRole.PRO, UserRole.ENTERPRISE]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此功能需要 Pro 或更高级别的订阅"
        )
    return current_user


async def require_enterprise_subscription(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """要求Enterprise订阅的依赖项"""
    if current_user.role != UserRole.ENTERPRISE:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="此功能需要 Enterprise 订阅"
        )
    return current_user


async def check_hotspot_quota(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """检查热点检测配额的依赖项"""
    await auth_middleware.check_daily_limits(current_user, "hotspot_detection")
    return current_user


async def check_deployment_access(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """检查部署权限的依赖项"""
    if not FeatureChecker.check_feature_access(current_user, "one_click_deployment"):
        if current_user.role == UserRole.FREE:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="一键部署功能需要支付 0.5 SOL，或升级到 Pro 版本"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="您的订阅计划不支持一键部署功能"
            )
    return current_user
