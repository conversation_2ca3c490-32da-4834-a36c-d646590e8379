"""
认证管理器
"""

import jwt
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON><PERSON>xception, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from ..models.user import User, UserRole
from ..database.user_db import UserDatabase
from .password import PasswordManager


class AuthManager:
    """认证管理器"""
    
    def __init__(self, secret_key: str = None, algorithm: str = "HS256"):
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.algorithm = algorithm
        self.access_token_expire_minutes = 30
        self.refresh_token_expire_days = 7
        self.user_db = UserDatabase()
        self.password_manager = PasswordManager()
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire, "type": "access"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """验证用户"""
        user = await self.user_db.get_user_by_email(email)
        if not user:
            return None
        
        if not self.password_manager.verify_password(password, user.hashed_password):
            return None
        
        return user
    
    async def register_user(self, email: str, username: str, password: str, 
                          full_name: Optional[str] = None, 
                          wallet_address: Optional[str] = None) -> User:
        """注册用户"""
        # 检查用户是否已存在
        existing_user = await self.user_db.get_user_by_email(email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
        
        existing_username = await self.user_db.get_user_by_username(username)
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已被使用"
            )
        
        # 验证密码强度
        is_strong, message = self.password_manager.validate_password_strength(password)
        if not is_strong:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        # 创建用户
        hashed_password = self.password_manager.hash_password(password)
        user_data = {
            "email": email,
            "username": username,
            "hashed_password": hashed_password,
            "full_name": full_name,
            "wallet_address": wallet_address,
            "role": UserRole.FREE,
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "daily_hotspots_reset": datetime.utcnow(),
            "free_trial_ends": datetime.utcnow() + timedelta(days=14)  # 14天免费试用
        }
        
        user = await self.user_db.create_user(user_data)
        return user
    
    async def get_user_by_token(self, token: str) -> Optional[User]:
        """通过令牌获取用户"""
        payload = self.verify_token(token)
        user_id = payload.get("sub")
        if user_id is None:
            return None
        
        user = await self.user_db.get_user_by_id(int(user_id))
        return user
    
    def create_password_reset_token(self, email: str) -> str:
        """创建密码重置令牌"""
        expire = datetime.utcnow() + timedelta(hours=1)  # 1小时有效期
        to_encode = {"email": email, "exp": expire, "type": "password_reset"}
        token = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return token
    
    async def reset_password(self, token: str, new_password: str) -> bool:
        """重置密码"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            email = payload.get("email")
            token_type = payload.get("type")
            
            if token_type != "password_reset":
                return False
            
            user = await self.user_db.get_user_by_email(email)
            if not user:
                return False
            
            # 验证新密码强度
            is_strong, _ = self.password_manager.validate_password_strength(new_password)
            if not is_strong:
                return False
            
            # 更新密码
            hashed_password = self.password_manager.hash_password(new_password)
            await self.user_db.update_user(user.id, {"hashed_password": hashed_password})
            
            return True
            
        except jwt.JWTError:
            return False


# 全局认证管理器实例
auth_manager = AuthManager()

# FastAPI依赖项
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """获取当前用户"""
    token = credentials.credentials
    user = await auth_manager.get_user_by_token(token)
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户账户已被禁用"
        )
    
    return current_user
