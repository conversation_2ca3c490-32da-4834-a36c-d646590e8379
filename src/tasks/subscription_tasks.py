"""
订阅相关定时任务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List

from ..models.user import UserRole
from ..models.subscription import SubscriptionStatus
from ..subscription.subscription_manager import SubscriptionManager
from ..database.user_db import UserDatabase
from ..database.subscription_db import SubscriptionDatabase

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SubscriptionTasks:
    """订阅相关定时任务"""
    
    def __init__(self):
        self.subscription_manager = SubscriptionManager()
        self.user_db = UserDatabase()
        self.subscription_db = SubscriptionDatabase()
    
    async def check_expiring_subscriptions(self) -> int:
        """检查即将过期的订阅并发送提醒"""
        try:
            expiring_subscriptions = await self.subscription_manager.check_expiring_subscriptions()
            
            logger.info(f"发现 {len(expiring_subscriptions)} 个即将过期的订阅")
            
            for subscription in expiring_subscriptions:
                user = await self.user_db.get_user_by_id(subscription.user_id)
                if user:
                    days_remaining = (subscription.expires_at - datetime.utcnow()).days
                    
                    logger.info(
                        f"用户 {user.email} 的 {subscription.tier.value} 订阅将在 {days_remaining} 天后过期"
                    )
                    
                    # 这里可以添加发送邮件提醒的逻辑
                    await self._send_expiry_reminder(user, subscription, days_remaining)
            
            return len(expiring_subscriptions)
            
        except Exception as e:
            logger.error(f"检查即将过期订阅失败: {e}")
            return 0
    
    async def process_expired_subscriptions(self) -> int:
        """处理已过期的订阅"""
        try:
            expired_count = await self.subscription_manager.expire_subscriptions()
            
            if expired_count > 0:
                logger.info(f"处理了 {expired_count} 个过期订阅")
            
            return expired_count
            
        except Exception as e:
            logger.error(f"处理过期订阅失败: {e}")
            return 0
    
    async def reset_daily_usage_limits(self) -> int:
        """重置每日使用限制"""
        try:
            # 获取所有用户
            users = await self.user_db.list_users(limit=1000)  # 假设不超过1000用户
            reset_count = 0
            
            for user in users:
                # 检查是否需要重置每日热点使用次数
                now = datetime.utcnow()
                if (now - user.daily_hotspots_reset).days >= 1:
                    await self.user_db.reset_daily_hotspots(user.id)
                    reset_count += 1
            
            if reset_count > 0:
                logger.info(f"重置了 {reset_count} 个用户的每日使用限制")
            
            return reset_count
            
        except Exception as e:
            logger.error(f"重置每日使用限制失败: {e}")
            return 0
    
    async def cleanup_old_records(self, days_old: int = 90) -> int:
        """清理旧记录"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            cleanup_count = 0
            
            # 清理旧的使用记录
            async with self.user_db.db.get_connection() as conn:
                cursor = await conn.execute("""
                    DELETE FROM usage_records 
                    WHERE created_at < ? AND status IN ('success', 'failed')
                """, (cutoff_date,))
                
                usage_deleted = cursor.rowcount
                cleanup_count += usage_deleted
                
                # 清理旧的支付记录（保留成功的支付记录）
                cursor = await conn.execute("""
                    DELETE FROM payments 
                    WHERE created_at < ? AND status = 'failed'
                """, (cutoff_date,))
                
                payments_deleted = cursor.rowcount
                cleanup_count += payments_deleted
                
                await conn.commit()
            
            if cleanup_count > 0:
                logger.info(f"清理了 {cleanup_count} 条旧记录")
            
            return cleanup_count
            
        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")
            return 0
    
    async def generate_daily_report(self) -> dict:
        """生成每日报告"""
        try:
            # 获取统计数据
            subscription_stats = await self.subscription_manager.get_subscription_analytics()
            
            # 获取今日新用户
            today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
            async with self.user_db.db.get_connection() as conn:
                cursor = await conn.execute("""
                    SELECT COUNT(*) FROM users WHERE created_at >= ?
                """, (today_start,))
                new_users_today = (await cursor.fetchone())[0]
                
                # 获取今日支付
                cursor = await conn.execute("""
                    SELECT COUNT(*), SUM(amount_sol) FROM payments 
                    WHERE created_at >= ? AND status = 'completed'
                """, (today_start,))
                payment_result = await cursor.fetchone()
                payments_today = payment_result[0] or 0
                revenue_today = payment_result[1] or 0
                
                # 获取今日使用量
                cursor = await conn.execute("""
                    SELECT COUNT(*) FROM usage_records 
                    WHERE created_at >= ? AND status = 'success'
                """, (today_start,))
                usage_today = (await cursor.fetchone())[0]
            
            report = {
                "date": datetime.utcnow().strftime("%Y-%m-%d"),
                "new_users": new_users_today,
                "payments_today": payments_today,
                "revenue_today_sol": revenue_today,
                "usage_today": usage_today,
                "active_subscriptions": subscription_stats["active_subscriptions"],
                "total_users": subscription_stats["free_tier_users"] + 
                              subscription_stats["pro_tier_users"] + 
                              subscription_stats["enterprise_tier_users"],
                "conversion_rate": subscription_stats["conversion_rate"]
            }
            
            logger.info(f"生成每日报告: {report}")
            return report
            
        except Exception as e:
            logger.error(f"生成每日报告失败: {e}")
            return {}
    
    async def _send_expiry_reminder(self, user, subscription, days_remaining: int):
        """发送过期提醒（模拟）"""
        # 这里应该集成真实的邮件发送服务
        logger.info(
            f"发送过期提醒邮件到 {user.email}: "
            f"{subscription.tier.value} 订阅将在 {days_remaining} 天后过期"
        )
        
        # 可以集成邮件服务如 SendGrid, AWS SES 等
        # await email_service.send_expiry_reminder(user.email, subscription, days_remaining)
    
    async def run_all_tasks(self):
        """运行所有定时任务"""
        logger.info("开始执行订阅相关定时任务")
        
        tasks_results = {}
        
        # 检查即将过期的订阅
        tasks_results["expiring_check"] = await self.check_expiring_subscriptions()
        
        # 处理已过期的订阅
        tasks_results["expired_processed"] = await self.process_expired_subscriptions()
        
        # 重置每日使用限制
        tasks_results["daily_reset"] = await self.reset_daily_usage_limits()
        
        # 清理旧记录
        tasks_results["cleanup"] = await self.cleanup_old_records()
        
        # 生成每日报告
        tasks_results["daily_report"] = await self.generate_daily_report()
        
        logger.info(f"定时任务执行完成: {tasks_results}")
        return tasks_results


async def run_subscription_tasks():
    """运行订阅定时任务的入口函数"""
    tasks = SubscriptionTasks()
    return await tasks.run_all_tasks()


# 定时任务调度器
class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        self.tasks = SubscriptionTasks()
        self.running = False
    
    async def start_scheduler(self):
        """启动调度器"""
        self.running = True
        logger.info("任务调度器启动")
        
        while self.running:
            try:
                current_time = datetime.utcnow()
                
                # 每小时检查一次即将过期的订阅
                if current_time.minute == 0:
                    await self.tasks.check_expiring_subscriptions()
                
                # 每天凌晨2点处理过期订阅和重置使用限制
                if current_time.hour == 2 and current_time.minute == 0:
                    await self.tasks.process_expired_subscriptions()
                    await self.tasks.reset_daily_usage_limits()
                
                # 每天凌晨3点清理旧记录
                if current_time.hour == 3 and current_time.minute == 0:
                    await self.tasks.cleanup_old_records()
                
                # 每天早上8点生成报告
                if current_time.hour == 8 and current_time.minute == 0:
                    await self.tasks.generate_daily_report()
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"调度器执行错误: {e}")
                await asyncio.sleep(60)
    
    def stop_scheduler(self):
        """停止调度器"""
        self.running = False
        logger.info("任务调度器停止")


if __name__ == "__main__":
    # 直接运行所有任务
    asyncio.run(run_subscription_tasks())
