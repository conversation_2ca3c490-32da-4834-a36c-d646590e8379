"""
功能权限检查器
"""

from datetime import datetime
from typing import Dict, Any, List
from fastapi import HTTPException, status, Depends

from ..models.user import User, UserRole
from ..models.subscription import SubscriptionTier, SUBSCRIPTION_PLANS
from ..auth.auth_manager import get_current_active_user


class FeatureChecker:
    """功能权限检查器"""
    
    @staticmethod
    def get_user_features(user: User, subscription_tier: SubscriptionTier = None) -> Dict[str, Any]:
        """获取用户功能权限"""
        # 如果没有指定订阅层级，使用用户角色
        if subscription_tier is None:
            if user.role == UserRole.FREE:
                subscription_tier = SubscriptionTier.FREE
            elif user.role == UserRole.PRO:
                subscription_tier = SubscriptionTier.PRO
            elif user.role == UserRole.ENTERPRISE:
                subscription_tier = SubscriptionTier.ENTERPRISE
            else:
                subscription_tier = SubscriptionTier.FREE
        
        plan = SUBSCRIPTION_PLANS.get(subscription_tier)
        if not plan:
            return SUBSCRIPTION_PLANS[SubscriptionTier.FREE].features
        
        return plan.features
    
    @staticmethod
    def get_user_limits(user: User, subscription_tier: SubscriptionTier = None) -> Dict[str, int]:
        """获取用户使用限制"""
        if subscription_tier is None:
            if user.role == UserRole.FREE:
                subscription_tier = SubscriptionTier.FREE
            elif user.role == UserRole.PRO:
                subscription_tier = SubscriptionTier.PRO
            elif user.role == UserRole.ENTERPRISE:
                subscription_tier = SubscriptionTier.ENTERPRISE
            else:
                subscription_tier = SubscriptionTier.FREE
        
        plan = SUBSCRIPTION_PLANS.get(subscription_tier)
        if not plan:
            return SUBSCRIPTION_PLANS[SubscriptionTier.FREE].usage_limits
        
        return plan.usage_limits
    
    @staticmethod
    def check_feature_access(user: User, feature_name: str, subscription_tier: SubscriptionTier = None) -> bool:
        """检查用户是否有权限使用某个功能"""
        features = FeatureChecker.get_user_features(user, subscription_tier)
        return features.get(feature_name, False)
    
    @staticmethod
    def check_usage_limit(user: User, limit_name: str, current_usage: int = 0, 
                         subscription_tier: SubscriptionTier = None) -> bool:
        """检查用户是否超过使用限制"""
        limits = FeatureChecker.get_user_limits(user, subscription_tier)
        limit = limits.get(limit_name, 0)
        
        # -1 表示无限制
        if limit == -1:
            return True
        
        return current_usage < limit
    
    @staticmethod
    def check_trial_status(user: User) -> Dict[str, Any]:
        """检查试用状态"""
        now = datetime.utcnow()
        
        if user.free_trial_ends is None:
            return {
                "is_trial": False,
                "trial_expired": False,
                "days_remaining": 0
            }
        
        trial_expired = now > user.free_trial_ends
        days_remaining = max(0, (user.free_trial_ends - now).days)
        
        return {
            "is_trial": True,
            "trial_expired": trial_expired,
            "days_remaining": days_remaining,
            "trial_ends": user.free_trial_ends
        }
    
    @staticmethod
    def get_content_type_access(user: User, subscription_tier: SubscriptionTier = None) -> List[str]:
        """获取用户可访问的内容类型"""
        limits = FeatureChecker.get_user_limits(user, subscription_tier)
        content_types_limit = limits.get("content_types", 2)
        
        all_content_types = ["politics", "technology", "entertainment", "sports", "subculture"]
        
        if content_types_limit == -1:
            return all_content_types
        elif content_types_limit == 2:
            return ["politics", "technology"]
        else:
            return all_content_types[:content_types_limit]
    
    @staticmethod
    def get_data_source_access(user: User, subscription_tier: SubscriptionTier = None) -> List[str]:
        """获取用户可访问的数据源"""
        limits = FeatureChecker.get_user_limits(user, subscription_tier)
        data_sources_limit = limits.get("data_sources", 2)
        features = FeatureChecker.get_user_features(user, subscription_tier)
        
        all_sources = ["reddit", "discord", "twitter", "telegram", "tiktok", "youtube"]
        darkweb_sources = ["darkweb_forums", "onion_sites"]
        
        if data_sources_limit == -1:
            sources = all_sources.copy()
            if features.get("darkweb_monitoring", False):
                sources.extend(darkweb_sources)
            return sources
        elif data_sources_limit == 2:
            return ["reddit", "discord"]
        else:
            sources = all_sources[:data_sources_limit]
            if features.get("darkweb_monitoring", False):
                sources.extend(darkweb_sources)
            return sources


def require_feature(feature_name: str):
    """装饰器：要求用户有特定功能权限"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取用户对象
            user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    user = value
                    break
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要用户认证"
                )
            
            if not FeatureChecker.check_feature_access(user, feature_name):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"您的订阅计划不支持 {feature_name} 功能"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_subscription_tier(min_tier: SubscriptionTier):
    """装饰器：要求用户有最低订阅层级"""
    tier_levels = {
        SubscriptionTier.FREE: 0,
        SubscriptionTier.PRO: 1,
        SubscriptionTier.ENTERPRISE: 2
    }
    
    def decorator(func):
        async def wrapper(*args, **kwargs):
            user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    user = value
                    break
            
            if user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="需要用户认证"
                )
            
            user_tier = SubscriptionTier.FREE
            if user.role == UserRole.PRO:
                user_tier = SubscriptionTier.PRO
            elif user.role == UserRole.ENTERPRISE:
                user_tier = SubscriptionTier.ENTERPRISE
            
            if tier_levels[user_tier] < tier_levels[min_tier]:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"此功能需要 {min_tier.value} 或更高级别的订阅"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# 便捷的依赖项函数
async def check_feature_access(
    feature_name: str,
    current_user: User = Depends(get_current_active_user)
) -> User:
    """检查功能访问权限的依赖项"""
    if not FeatureChecker.check_feature_access(current_user, feature_name):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"您的订阅计划不支持 {feature_name} 功能"
        )
    return current_user
