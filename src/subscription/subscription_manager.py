"""
订阅管理器
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, status

from ..models.user import User, UserRole
from ..models.subscription import (
    Subscription, SubscriptionTier, SubscriptionStatus, 
    SubscriptionCreate, SUBSCRIPTION_PLANS
)
from ..models.payment import Payment, PaymentCreate, PaymentType, PaymentStatus
from ..database.subscription_db import SubscriptionDatabase
from ..database.payment_db import PaymentDatabase
from ..database.user_db import UserDatabase


class SubscriptionManager:
    """订阅管理器"""
    
    def __init__(self):
        self.subscription_db = SubscriptionDatabase()
        self.payment_db = PaymentDatabase()
        self.user_db = UserDatabase()
    
    async def create_subscription(self, user_id: int, tier: SubscriptionTier,
                                payment_transaction_hash: Optional[str] = None) -> Subscription:
        """创建订阅"""
        plan = SUBSCRIPTION_PLANS.get(tier)
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的订阅计划"
            )
        
        now = datetime.utcnow()
        subscription_data = {
            "user_id": user_id,
            "tier": tier,
            "status": SubscriptionStatus.PENDING,
            "price_sol": plan.price_sol,
            "price_usd": plan.price_usd,
            "created_at": now,
            "updated_at": now,
            "starts_at": now,
            "expires_at": now + timedelta(days=plan.duration_days),
            "auto_renew": True,
            "payment_transaction_hash": payment_transaction_hash,
            "features": plan.features,
            "usage_limits": plan.usage_limits,
            "current_usage": {}
        }
        
        subscription = await self.subscription_db.create_subscription(subscription_data)
        return subscription
    
    async def activate_subscription(self, subscription_id: int) -> Subscription:
        """激活订阅"""
        subscription = await self.subscription_db.get_subscription_by_id(subscription_id)
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="订阅不存在"
            )
        
        # 更新订阅状态
        updated_subscription = await self.subscription_db.update_subscription(
            subscription_id, 
            {"status": SubscriptionStatus.ACTIVE}
        )
        
        # 更新用户角色
        if subscription.tier == SubscriptionTier.PRO:
            user_role = UserRole.PRO
        elif subscription.tier == SubscriptionTier.ENTERPRISE:
            user_role = UserRole.ENTERPRISE
        else:
            user_role = UserRole.FREE
        
        await self.user_db.update_user(subscription.user_id, {"role": user_role})
        
        return updated_subscription
    
    async def upgrade_subscription(self, user_id: int, target_tier: SubscriptionTier,
                                 wallet_address: Optional[str] = None) -> Dict[str, Any]:
        """升级订阅"""
        user = await self.user_db.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查当前订阅
        current_subscription = await self.subscription_db.get_user_active_subscription(user_id)
        current_tier = SubscriptionTier.FREE
        if current_subscription:
            current_tier = current_subscription.tier
        
        # 检查是否为有效升级
        tier_levels = {
            SubscriptionTier.FREE: 0,
            SubscriptionTier.PRO: 1,
            SubscriptionTier.ENTERPRISE: 2
        }
        
        if tier_levels[target_tier] <= tier_levels[current_tier]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只能升级到更高级别的订阅"
            )
        
        plan = SUBSCRIPTION_PLANS[target_tier]
        
        # 创建支付记录
        payment_data = {
            "user_id": user_id,
            "amount_sol": plan.price_sol,
            "amount_usd": plan.price_usd,
            "payment_type": PaymentType.SUBSCRIPTION,
            "description": f"升级到 {plan.name}",
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "wallet_address": wallet_address,
            "metadata": {
                "target_tier": target_tier.value,
                "plan_name": plan.name
            }
        }
        
        payment = await self.payment_db.create_payment(payment_data)
        
        # 创建待激活的订阅
        subscription = await self.create_subscription(user_id, target_tier)
        
        # 关联支付和订阅
        await self.payment_db.update_payment(payment.id, {
            "subscription_id": subscription.id
        })
        
        return {
            "subscription": subscription,
            "payment": payment,
            "payment_details": {
                "amount_sol": plan.price_sol,
                "amount_usd": plan.price_usd,
                "recipient_address": "MemeMasterAI_Payment_Address",  # 实际收款地址
                "memo": f"subscription_{user_id}_{target_tier.value}_{subscription.id}"
            }
        }
    
    async def confirm_subscription_payment(self, payment_id: int, 
                                         transaction_hash: str) -> Subscription:
        """确认订阅支付"""
        payment = await self.payment_db.get_payment_by_id(payment_id)
        if not payment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="支付记录不存在"
            )
        
        if payment.status != PaymentStatus.PENDING:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="支付已处理"
            )
        
        # 确认支付
        await self.payment_db.confirm_payment(payment_id, transaction_hash)
        
        # 激活订阅
        if payment.subscription_id:
            subscription = await self.activate_subscription(payment.subscription_id)
            return subscription
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="支付未关联订阅"
        )
    
    async def cancel_subscription(self, user_id: int) -> bool:
        """取消订阅"""
        subscription = await self.subscription_db.get_user_active_subscription(user_id)
        if not subscription:
            return False
        
        # 更新订阅状态为已取消
        await self.subscription_db.update_subscription(
            subscription.id,
            {
                "status": SubscriptionStatus.CANCELLED,
                "auto_renew": False
            }
        )
        
        return True
    
    async def renew_subscription(self, subscription_id: int) -> Optional[Subscription]:
        """续费订阅"""
        subscription = await self.subscription_db.get_subscription_by_id(subscription_id)
        if not subscription:
            return None
        
        if not subscription.auto_renew:
            return None
        
        plan = SUBSCRIPTION_PLANS.get(subscription.tier)
        if not plan:
            return None
        
        # 续费订阅
        renewed_subscription = await self.subscription_db.renew_subscription(
            subscription_id, 
            plan.duration_days
        )
        
        return renewed_subscription
    
    async def check_expiring_subscriptions(self) -> List[Subscription]:
        """检查即将过期的订阅"""
        expiring_subscriptions = await self.subscription_db.get_expiring_subscriptions(days_ahead=3)
        
        # 处理自动续费
        for subscription in expiring_subscriptions:
            if subscription.auto_renew:
                try:
                    await self.renew_subscription(subscription.id)
                except Exception as e:
                    print(f"自动续费失败 - 订阅ID: {subscription.id}, 错误: {e}")
        
        return expiring_subscriptions
    
    async def expire_subscriptions(self) -> int:
        """处理过期订阅"""
        now = datetime.utcnow()
        expired_count = 0
        
        # 获取所有活跃但已过期的订阅
        async with self.subscription_db.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT id, user_id FROM subscriptions 
                WHERE status = 'active' AND expires_at <= ?
            """, (now,))
            expired_subscriptions = await cursor.fetchall()
        
        for row in expired_subscriptions:
            subscription_id = row["id"]
            user_id = row["user_id"]
            
            # 使订阅过期
            await self.subscription_db.expire_subscription(subscription_id)
            
            # 将用户降级为免费用户
            await self.user_db.update_user(user_id, {"role": UserRole.FREE})
            
            expired_count += 1
        
        return expired_count
    
    async def get_user_subscription_history(self, user_id: int) -> List[Subscription]:
        """获取用户订阅历史"""
        return await self.subscription_db.get_user_subscriptions(user_id)
    
    async def get_subscription_analytics(self) -> Dict[str, Any]:
        """获取订阅分析数据"""
        stats = await self.subscription_db.get_subscription_stats()
        
        # 添加更多分析数据
        now = datetime.utcnow()
        
        # 本月新增订阅
        first_day_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        async with self.subscription_db.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM subscriptions 
                WHERE created_at >= ? AND status = 'active'
            """, (first_day_of_month,))
            new_subscriptions_this_month = (await cursor.fetchone())[0]
        
        # 流失率计算（简化版）
        last_month_start = (first_day_of_month - timedelta(days=1)).replace(day=1)
        async with self.subscription_db.db.get_connection() as conn:
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM subscriptions 
                WHERE created_at >= ? AND created_at < ? AND status = 'active'
            """, (last_month_start, first_day_of_month))
            last_month_active = (await cursor.fetchone())[0]
            
            cursor = await conn.execute("""
                SELECT COUNT(*) FROM subscriptions 
                WHERE created_at >= ? AND created_at < ? 
                AND (status = 'cancelled' OR status = 'expired')
            """, (last_month_start, first_day_of_month))
            last_month_churned = (await cursor.fetchone())[0]
        
        churn_rate = (last_month_churned / max(last_month_active, 1)) * 100
        
        stats.update({
            "new_subscriptions_this_month": new_subscriptions_this_month,
            "churn_rate": churn_rate,
            "average_revenue_per_user": stats["monthly_revenue_sol"] / max(stats["active_subscriptions"], 1)
        })
        
        return stats
    
    async def apply_discount(self, user_id: int, discount_code: str) -> Dict[str, Any]:
        """应用折扣码"""
        # 简化的折扣系统
        discount_codes = {
            "FIRST_TIME": {"discount": 0.2, "description": "首次订阅8折优惠"},
            "LOYAL_USER": {"discount": 0.1, "description": "忠实用户9折优惠"},
            "ENTERPRISE": {"discount": 0.15, "description": "企业客户85折优惠"}
        }
        
        discount = discount_codes.get(discount_code)
        if not discount:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的折扣码"
            )
        
        return {
            "valid": True,
            "discount_percentage": discount["discount"] * 100,
            "description": discount["description"]
        }
