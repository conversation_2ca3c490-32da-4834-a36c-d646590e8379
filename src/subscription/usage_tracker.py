"""
使用跟踪器
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional
from fastapi import HTTPEx<PERSON>, status

from ..models.user import User
from ..models.usage import UsageRecord, UsageType, UsageCreate
from ..models.payment import PaymentType, PAY_PER_USE_PRICES
from ..database.usage_db import UsageDatabase
from ..database.user_db import UserDatabase
from ..database.payment_db import PaymentDatabase
from .feature_checker import FeatureChecker


class UsageTracker:
    """使用跟踪器"""
    
    def __init__(self):
        self.usage_db = UsageDatabase()
        self.user_db = UserDatabase()
        self.payment_db = PaymentDatabase()
    
    async def track_usage(self, user: User, usage_type: UsageType, 
                         description: str, metadata: Optional[Dict[str, Any]] = None,
                         cost_sol: Optional[float] = None) -> UsageRecord:
        """跟踪使用记录"""
        now = datetime.utcnow()
        
        usage_data = {
            "user_id": user.id,
            "usage_type": usage_type,
            "description": description,
            "created_at": now,
            "updated_at": now,
            "metadata": metadata or {},
            "cost_sol": cost_sol
        }
        
        usage_record = await self.usage_db.create_usage_record(usage_data)
        
        # 更新用户统计
        await self._update_user_stats(user.id, usage_type)
        
        return usage_record
    
    async def track_hotspot_detection(self, user: User, hotspot_data: Dict[str, Any]) -> UsageRecord:
        """跟踪热点检测使用"""
        # 检查每日限制
        daily_count = await self.usage_db.get_user_daily_usage_count(
            user.id, UsageType.HOTSPOT_DETECTION
        )
        
        limits = FeatureChecker.get_user_limits(user)
        daily_limit = limits.get("daily_hotspots", 3)
        
        if daily_limit != -1 and daily_count >= daily_limit:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"今日热点检测次数已用完（{daily_limit}次）"
            )
        
        # 记录使用
        usage_record = await self.track_usage(
            user=user,
            usage_type=UsageType.HOTSPOT_DETECTION,
            description="热点检测",
            metadata={
                "hotspot_count": len(hotspot_data.get("hotspots", [])),
                "categories": hotspot_data.get("categories", []),
                "sources": hotspot_data.get("sources", [])
            }
        )
        
        # 更新用户每日热点使用次数
        await self.user_db.increment_daily_hotspots(user.id)
        
        return usage_record
    
    async def track_strategy_generation(self, user: User, strategy_data: Dict[str, Any]) -> UsageRecord:
        """跟踪策略生成使用"""
        # 检查策略数量限制
        monthly_count = await self.usage_db.get_user_monthly_usage_count(
            user.id, UsageType.STRATEGY_GENERATION
        )
        
        limits = FeatureChecker.get_user_limits(user)
        max_strategies = limits.get("max_strategies", 2)
        
        if max_strategies != -1 and monthly_count >= max_strategies:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"本月策略生成次数已用完（{max_strategies}次）"
            )
        
        usage_record = await self.track_usage(
            user=user,
            usage_type=UsageType.STRATEGY_GENERATION,
            description="策略生成",
            metadata={
                "strategy_type": strategy_data.get("type"),
                "hotspot_id": strategy_data.get("hotspot_id"),
                "parameters": strategy_data.get("parameters", {})
            }
        )
        
        # 更新用户策略创建统计
        await self.user_db.update_user(user.id, {
            "total_strategies_created": user.total_strategies_created + 1
        })
        
        return usage_record
    
    async def track_paid_feature(self, user: User, feature_type: PaymentType,
                                description: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """跟踪付费功能使用"""
        # 检查功能权限
        features = FeatureChecker.get_user_features(user)
        
        # 根据功能类型检查权限
        if feature_type == PaymentType.ONE_TIME_DEPLOYMENT:
            if not features.get("one_click_deployment", False):
                # 需要付费
                price_info = PAY_PER_USE_PRICES[feature_type]
                return {
                    "requires_payment": True,
                    "cost_sol": price_info["price_sol"],
                    "description": price_info["description"]
                }
        
        elif feature_type == PaymentType.DUPLICATE_CHECK:
            if not features.get("duplicate_checking", False):
                # 检查免费额度
                monthly_count = await self.usage_db.get_user_monthly_usage_count(
                    user.id, UsageType.DUPLICATE_CHECK
                )
                
                limits = FeatureChecker.get_user_limits(user)
                free_checks = limits.get("free_duplicate_checks", 0)
                
                if monthly_count >= free_checks:
                    price_info = PAY_PER_USE_PRICES[feature_type]
                    return {
                        "requires_payment": True,
                        "cost_sol": price_info["price_sol"],
                        "description": price_info["description"]
                    }
        
        # 记录使用
        usage_type_mapping = {
            PaymentType.ONE_TIME_DEPLOYMENT: UsageType.TOKEN_DEPLOYMENT,
            PaymentType.WALLET_CREATION: UsageType.WALLET_CREATION,
            PaymentType.LIQUIDITY_OPERATION: UsageType.LIQUIDITY_OPERATION,
            PaymentType.DUPLICATE_CHECK: UsageType.DUPLICATE_CHECK,
            PaymentType.EXIT_EXECUTION: UsageType.EXIT_EXECUTION
        }
        
        usage_type = usage_type_mapping.get(feature_type, UsageType.API_CALL)
        
        usage_record = await self.track_usage(
            user=user,
            usage_type=usage_type,
            description=description,
            metadata=metadata
        )
        
        return {
            "requires_payment": False,
            "usage_record": usage_record
        }
    
    async def create_paid_usage_record(self, user: User, payment_id: int,
                                     feature_type: PaymentType, description: str,
                                     metadata: Optional[Dict[str, Any]] = None) -> UsageRecord:
        """创建付费使用记录"""
        price_info = PAY_PER_USE_PRICES.get(feature_type)
        if not price_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的付费功能类型"
            )
        
        usage_type_mapping = {
            PaymentType.ONE_TIME_DEPLOYMENT: UsageType.TOKEN_DEPLOYMENT,
            PaymentType.WALLET_CREATION: UsageType.WALLET_CREATION,
            PaymentType.LIQUIDITY_OPERATION: UsageType.LIQUIDITY_OPERATION,
            PaymentType.DUPLICATE_CHECK: UsageType.DUPLICATE_CHECK,
            PaymentType.EXIT_EXECUTION: UsageType.EXIT_EXECUTION
        }
        
        usage_type = usage_type_mapping.get(feature_type, UsageType.API_CALL)
        
        usage_record = await self.track_usage(
            user=user,
            usage_type=usage_type,
            description=description,
            metadata=metadata,
            cost_sol=price_info["price_sol"]
        )
        
        # 关联支付记录
        await self.usage_db.update_usage_record(usage_record.id, {
            "payment_id": payment_id
        })
        
        # 更新用户付费功能使用统计
        await self.user_db.update_user(user.id, {
            "total_paid_features_used": user.total_paid_features_used + 1
        })
        
        return usage_record
    
    async def get_user_usage_summary(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """获取用户使用摘要"""
        user = await self.user_db.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 获取使用统计
        usage_summary = await self.usage_db.get_user_usage_summary(user_id, days)
        
        # 获取功能权限和限制
        features = FeatureChecker.get_user_features(user)
        limits = FeatureChecker.get_user_limits(user)
        trial_status = FeatureChecker.check_trial_status(user)
        
        # 获取当前使用情况
        daily_hotspots = await self.usage_db.get_user_daily_usage_count(
            user_id, UsageType.HOTSPOT_DETECTION
        )
        
        monthly_strategies = await self.usage_db.get_user_monthly_usage_count(
            user_id, UsageType.STRATEGY_GENERATION
        )
        
        monthly_deployments = await self.usage_db.get_user_monthly_usage_count(
            user_id, UsageType.TOKEN_DEPLOYMENT
        )
        
        return {
            "user_id": user_id,
            "subscription_tier": user.role.value,
            "trial_status": trial_status,
            "features": features,
            "usage_limits": limits,
            "current_usage": {
                "daily_hotspots_used": daily_hotspots,
                "daily_hotspots_limit": limits.get("daily_hotspots", 3),
                "monthly_strategies_created": monthly_strategies,
                "max_strategies_limit": limits.get("max_strategies", 2),
                "monthly_deployments": monthly_deployments,
                "total_paid_features_used": user.total_paid_features_used
            },
            "usage_summary": usage_summary,
            "can_use_features": {
                "hotspot_detection": daily_hotspots < limits.get("daily_hotspots", 3) or limits.get("daily_hotspots", 3) == -1,
                "strategy_generation": monthly_strategies < limits.get("max_strategies", 2) or limits.get("max_strategies", 2) == -1,
                "one_click_deployment": features.get("one_click_deployment", False),
                "duplicate_checking": features.get("duplicate_checking", False),
                "advanced_features": features.get("advanced_features", False)
            }
        }
    
    async def reset_user_daily_usage(self, user_id: int) -> bool:
        """重置用户每日使用量"""
        try:
            await self.user_db.reset_daily_hotspots(user_id)
            return True
        except Exception:
            return False
    
    async def get_usage_analytics(self, days: int = 30) -> Dict[str, Any]:
        """获取使用分析数据"""
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        stats = await self.usage_db.get_usage_stats(start_date, end_date)
        
        # 添加更多分析数据
        async with self.usage_db.db.get_connection() as conn:
            # 最活跃的功能
            cursor = await conn.execute("""
                SELECT usage_type, COUNT(*) as usage_count 
                FROM usage_records 
                WHERE status = 'success' AND created_at >= ?
                GROUP BY usage_type 
                ORDER BY usage_count DESC
            """, (start_date,))
            
            most_used_features = [
                {"feature": row[0], "count": row[1]} 
                for row in await cursor.fetchall()
            ]
            
            # 用户活跃度
            cursor = await conn.execute("""
                SELECT COUNT(DISTINCT user_id) as active_users
                FROM usage_records 
                WHERE created_at >= ?
            """, (start_date,))
            
            active_users = (await cursor.fetchone())[0]
        
        stats.update({
            "period_days": days,
            "most_used_features": most_used_features,
            "active_users": active_users,
            "average_usage_per_user": stats["total_usage"] / max(active_users, 1)
        })
        
        return stats
    
    async def _update_user_stats(self, user_id: int, usage_type: UsageType) -> None:
        """更新用户统计信息"""
        if usage_type == UsageType.TOKEN_DEPLOYMENT:
            user = await self.user_db.get_user_by_id(user_id)
            if user:
                await self.user_db.update_user(user_id, {
                    "total_deployments": user.total_deployments + 1
                })
