version: '3.8'

services:
  # 主应用服务
  mememaster-ai:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mememaster-ai
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - MONGODB_URL=mongodb://mongodb:27017/mememaster
      - POSTGRES_URL=********************************************/mememaster
    env_file:
      - .env
    volumes:
      - ./logs:/var/log/mememaster
      - ./data:/app/data
      - ./deployments:/app/deployments
    depends_on:
      - redis
      - mongodb
      - postgres
    networks:
      - mememaster-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7.0-alpine
    container_name: mememaster-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-defaultpassword}
    volumes:
      - redis-data:/data
    networks:
      - mememaster-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB数据库
  mongodb:
    image: mongo:6.0
    container_name: mememaster-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-password}
      MONGO_INITDB_DATABASE: mememaster
    volumes:
      - mongodb-data:/data/db
      - ./docker/mongodb/init.js:/docker-entrypoint-initdb.d/init.js:ro
    networks:
      - mememaster-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: mememaster-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: mememaster
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - mememaster-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: mememaster-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - mememaster-network
    depends_on:
      - mememaster-ai

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: mememaster-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - mememaster-network
    depends_on:
      - prometheus

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: mememaster-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - mememaster-network
    depends_on:
      - mememaster-ai

  # Tor代理服务
  tor:
    image: dperson/torproxy
    container_name: mememaster-tor
    restart: unless-stopped
    ports:
      - "9050:9050"
      - "9051:9051"
    environment:
      - TOR_CONTROL_PASSWORD=${TOR_PASSWORD:-defaultpassword}
    networks:
      - mememaster-network

  # 日志聚合
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: mememaster-fluentd
    restart: unless-stopped
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./docker/fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/mememaster
    networks:
      - mememaster-network

# 网络配置
networks:
  mememaster-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  redis-data:
    driver: local
  mongodb-data:
    driver: local
  postgres-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
