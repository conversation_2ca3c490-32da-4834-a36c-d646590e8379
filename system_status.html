<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 系统状态</title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(30, 41, 59, 0.6);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: rgba(71, 85, 105, 0.3);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(71, 85, 105, 0.5);
            transition: all 0.3s ease;
        }
        
        .status-card:hover {
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-2px);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        
        .status-title {
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .status-online {
            background: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }
        
        .status-offline {
            background: #ef4444;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }
        
        .status-warning {
            background: #f59e0b;
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
        }
        
        .status-info {
            margin-top: 10px;
            font-size: 0.9em;
            color: #94a3b8;
        }
        
        .links-section {
            margin-top: 40px;
        }
        
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .link-card {
            background: rgba(99, 102, 241, 0.1);
            border: 1px solid rgba(99, 102, 241, 0.3);
            padding: 20px;
            border-radius: 12px;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            display: block;
        }
        
        .link-card:hover {
            background: rgba(99, 102, 241, 0.2);
            border-color: rgba(99, 102, 241, 0.5);
            transform: translateY(-2px);
        }
        
        .link-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #a78bfa;
        }
        
        .link-desc {
            font-size: 0.9em;
            color: #cbd5e1;
        }
        
        .link-url {
            font-size: 0.8em;
            color: #94a3b8;
            margin-top: 5px;
            font-family: monospace;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .refresh-btn {
            background: linear-gradient(135deg, #6366f1 0%, #3b82f6 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .refresh-btn:hover {
            background: linear-gradient(135deg, #5b21b6 0%, #1d4ed8 100%);
            transform: translateY(-1px);
        }
        
        .timestamp {
            text-align: center;
            color: #94a3b8;
            font-size: 0.9em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 MemeMaster AI 系统状态</h1>
            <p>实时监控系统各组件运行状态</p>
            <button class="refresh-btn" onclick="checkAllStatus()">🔄 刷新状态</button>
        </div>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">🌐 React 前端</div>
                    <div class="status-indicator" id="frontend-status"></div>
                </div>
                <div class="status-info" id="frontend-info">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">⚡ FastAPI 后端</div>
                    <div class="status-indicator" id="backend-status"></div>
                </div>
                <div class="status-info" id="backend-info">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">📊 API 文档</div>
                    <div class="status-indicator" id="docs-status"></div>
                </div>
                <div class="status-info" id="docs-info">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">🎨 HTML 仪表盘</div>
                    <div class="status-indicator" id="html-status"></div>
                </div>
                <div class="status-info" id="html-info">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">🔍 测试页面</div>
                    <div class="status-indicator" id="test-status"></div>
                </div>
                <div class="status-info" id="test-info">检测中...</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <div class="status-title">💾 数据库</div>
                    <div class="status-indicator" id="db-status"></div>
                </div>
                <div class="status-info" id="db-info">检测中...</div>
            </div>
        </div>
        
        <div class="links-section">
            <h2>🔗 快速访问链接</h2>
            <div class="links-grid">
                <a href="http://localhost:3002/" target="_blank" class="link-card">
                    <div class="link-title">⚛️ React 前端</div>
                    <div class="link-desc">主要的React应用界面</div>
                    <div class="link-url">http://localhost:3002/</div>
                </a>
                
                <a href="http://localhost:8001/docs" target="_blank" class="link-card">
                    <div class="link-title">📚 API 文档</div>
                    <div class="link-desc">Swagger UI API文档</div>
                    <div class="link-url">http://localhost:8001/docs</div>
                </a>
                
                <a href="http://localhost:8090/simple_dashboard.html" target="_blank" class="link-card">
                    <div class="link-title">🎨 简化版仪表盘</div>
                    <div class="link-desc">HTML版本的现代化仪表盘</div>
                    <div class="link-url">http://localhost:8090/simple_dashboard.html</div>
                </a>
                
                <a href="http://localhost:8090/dashboard.html" target="_blank" class="link-card">
                    <div class="link-title">📊 完整版仪表盘</div>
                    <div class="link-desc">包含图表的完整仪表盘</div>
                    <div class="link-url">http://localhost:8090/dashboard.html</div>
                </a>
                
                <a href="http://localhost:8090/test_display.html" target="_blank" class="link-card">
                    <div class="link-title">🧪 显示测试</div>
                    <div class="link-desc">浏览器兼容性测试页面</div>
                    <div class="link-url">http://localhost:8090/test_display.html</div>
                </a>
                
                <a href="http://localhost:8001/health" target="_blank" class="link-card">
                    <div class="link-title">💚 健康检查</div>
                    <div class="link-desc">系统健康状态API</div>
                    <div class="link-url">http://localhost:8001/health</div>
                </a>
            </div>
        </div>
        
        <div class="timestamp" id="last-update">
            最后更新: 检测中...
        </div>
    </div>

    <script>
        async function checkStatus(url, timeout = 5000) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);
                
                const response = await fetch(url, {
                    signal: controller.signal,
                    mode: 'no-cors'
                });
                
                clearTimeout(timeoutId);
                return { status: 'online', response };
            } catch (error) {
                if (error.name === 'AbortError') {
                    return { status: 'timeout', error: '请求超时' };
                }
                return { status: 'offline', error: error.message };
            }
        }
        
        async function checkApiStatus(url, timeout = 5000) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);
                
                const response = await fetch(url, {
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (response.ok) {
                    return { status: 'online', response };
                } else {
                    return { status: 'warning', error: `HTTP ${response.status}` };
                }
            } catch (error) {
                if (error.name === 'AbortError') {
                    return { status: 'timeout', error: '请求超时' };
                }
                return { status: 'offline', error: error.message };
            }
        }
        
        function updateStatus(elementId, status, info) {
            const indicator = document.getElementById(elementId + '-status');
            const infoElement = document.getElementById(elementId + '-info');
            
            indicator.className = 'status-indicator';
            
            switch (status) {
                case 'online':
                    indicator.classList.add('status-online');
                    break;
                case 'warning':
                    indicator.classList.add('status-warning');
                    break;
                case 'timeout':
                    indicator.classList.add('status-warning');
                    break;
                default:
                    indicator.classList.add('status-offline');
            }
            
            infoElement.textContent = info;
        }
        
        async function checkAllStatus() {
            document.getElementById('last-update').textContent = '最后更新: 检测中...';
            
            // 检查React前端
            const frontendResult = await checkStatus('http://localhost:3002/');
            updateStatus('frontend', frontendResult.status, 
                frontendResult.status === 'online' ? '✅ React应用正常运行' : 
                `❌ ${frontendResult.error || '无法连接'}`);
            
            // 检查FastAPI后端
            const backendResult = await checkApiStatus('http://localhost:8001/health');
            updateStatus('backend', backendResult.status,
                backendResult.status === 'online' ? '✅ FastAPI服务正常' :
                `❌ ${backendResult.error || '无法连接'}`);
            
            // 检查API文档
            const docsResult = await checkStatus('http://localhost:8001/docs');
            updateStatus('docs', docsResult.status,
                docsResult.status === 'online' ? '✅ Swagger UI可访问' :
                `❌ ${docsResult.error || '无法连接'}`);
            
            // 检查HTML仪表盘
            const htmlResult = await checkStatus('http://localhost:8090/simple_dashboard.html');
            updateStatus('html', htmlResult.status,
                htmlResult.status === 'online' ? '✅ HTML仪表盘可访问' :
                `❌ ${htmlResult.error || '无法连接'}`);
            
            // 检查测试页面
            const testResult = await checkStatus('http://localhost:8090/test_display.html');
            updateStatus('test', testResult.status,
                testResult.status === 'online' ? '✅ 测试页面可访问' :
                `❌ ${testResult.error || '无法连接'}`);
            
            // 检查数据库（通过API）
            const dbResult = await checkApiStatus('http://localhost:8001/api/system/info');
            updateStatus('db', dbResult.status,
                dbResult.status === 'online' ? '✅ 数据库连接正常' :
                `❌ ${dbResult.error || '数据库连接失败'}`);
            
            document.getElementById('last-update').textContent = 
                `最后更新: ${new Date().toLocaleString('zh-CN')}`;
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', checkAllStatus);
        
        // 每30秒自动刷新
        setInterval(checkAllStatus, 30000);
        
        console.log('🎯 MemeMaster AI 系统状态监控已启动');
    </script>
</body>
</html>
