#!/usr/bin/env python3
"""
确保React前端始终可用的永久解决方案
每次新的对话线程都会自动检查并启动React前端
"""

import os
import sys
import time
import json
import psutil
import subprocess
import requests
from pathlib import Path
from datetime import datetime

class ReactFrontendEnsurer:
    """React前端确保器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.status_file = self.project_root / '.frontend_status.json'
        
    def log(self, message):
        """输出日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def check_port_available(self, port):
        """检查端口是否可用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
        except:
            pass
        return False
    
    def check_service_health(self, url, timeout=3):
        """检查服务健康状态"""
        try:
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except:
            return False
    
    def kill_process_on_port(self, port):
        """终止占用端口的进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                            self.log(f"🔄 终止占用端口{port}的进程 PID:{proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=3)
                            return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            self.log(f"清理端口{port}时出错: {e}")
        return False
    
    def ensure_dependencies(self):
        """确保依赖已安装"""
        # 检查Node.js
        try:
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, check=True)
            self.log(f"✅ npm {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.log("❌ npm未安装，请先安装Node.js")
            return False
        
        # 检查package.json
        package_json = self.project_root / 'package.json'
        if not package_json.exists():
            self.log("❌ package.json不存在")
            return False
        
        # 检查node_modules
        node_modules = self.project_root / 'node_modules'
        if not node_modules.exists():
            self.log("📦 安装Node.js依赖...")
            try:
                subprocess.run(['npm', 'install'], 
                             cwd=str(self.project_root), 
                             check=True, timeout=300)
                self.log("✅ Node.js依赖安装完成")
            except subprocess.TimeoutExpired:
                self.log("❌ 依赖安装超时")
                return False
            except subprocess.CalledProcessError as e:
                self.log(f"❌ 依赖安装失败: {e}")
                return False
        
        return True
    
    def start_backend_if_needed(self):
        """如果需要，启动后端服务"""
        if self.check_port_available(8001):
            if self.check_service_health("http://localhost:8001/health"):
                self.log("✅ 后端服务已运行")
                return True
        
        self.log("🚀 启动后端服务...")
        
        # 清理端口
        self.kill_process_on_port(8001)
        time.sleep(1)
        
        try:
            backend_script = self.project_root / 'working_server.py'
            if not backend_script.exists():
                self.log("❌ working_server.py不存在")
                return False
            
            # 启动后端
            process = subprocess.Popen([
                sys.executable, str(backend_script)
            ], cwd=str(self.project_root),
               stdout=subprocess.DEVNULL,
               stderr=subprocess.DEVNULL,
               start_new_session=True)
            
            # 等待启动
            for i in range(10):
                time.sleep(1)
                if self.check_service_health("http://localhost:8001/health"):
                    self.log(f"✅ 后端服务启动成功 (PID: {process.pid})")
                    return True
            
            self.log("❌ 后端服务启动超时")
            return False
            
        except Exception as e:
            self.log(f"❌ 后端启动失败: {e}")
            return False
    
    def start_react_frontend(self):
        """启动React前端"""
        self.log("🎨 启动React前端...")
        
        # 清理端口
        if self.check_port_available(3000):
            self.kill_process_on_port(3000)
            time.sleep(2)
        
        try:
            # 启动前端
            process = subprocess.Popen([
                'npm', 'run', 'frontend:dev'
            ], cwd=str(self.project_root),
               stdout=subprocess.DEVNULL,
               stderr=subprocess.DEVNULL,
               start_new_session=True)
            
            # 等待启动
            for i in range(20):
                time.sleep(1)
                if self.check_port_available(3000):
                    # 再等待2秒确保完全启动
                    time.sleep(2)
                    if self.check_service_health("http://localhost:3000/"):
                        self.log(f"✅ React前端启动成功 (PID: {process.pid})")
                        return True
            
            self.log("❌ React前端启动超时")
            return False
            
        except Exception as e:
            self.log(f"❌ React前端启动失败: {e}")
            return False
    
    def save_status(self, backend_ok, frontend_ok):
        """保存状态"""
        try:
            status = {
                'timestamp': datetime.now().isoformat(),
                'backend_running': backend_ok,
                'frontend_running': frontend_ok,
                'backend_url': 'http://localhost:8001/',
                'frontend_url': 'http://localhost:3000/',
                'last_check': datetime.now().isoformat()
            }
            
            with open(self.status_file, 'w') as f:
                json.dump(status, f, indent=2)
                
        except Exception as e:
            self.log(f"保存状态失败: {e}")
    
    def ensure_system_running(self):
        """确保整个系统运行"""
        self.log("🔍 检查MemeMaster AI系统状态...")
        
        # 确保依赖
        if not self.ensure_dependencies():
            return False
        
        # 确保后端运行
        backend_ok = self.start_backend_if_needed()
        
        # 确保前端运行
        frontend_ok = False
        if self.check_port_available(3000) and self.check_service_health("http://localhost:3000/"):
            self.log("✅ React前端已运行")
            frontend_ok = True
        else:
            frontend_ok = self.start_react_frontend()
        
        # 保存状态
        self.save_status(backend_ok, frontend_ok)
        
        return backend_ok and frontend_ok

def quick_check_and_start():
    """快速检查并启动系统"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║            MemeMaster AI React前端永久解决方案               ║
║                    自动检查和启动系统                        ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    ensurer = ReactFrontendEnsurer()
    
    try:
        if ensurer.ensure_system_running():
            print("\n🎉 系统启动完成！")
            print("="*50)
            print("🌐 React前端: http://localhost:3000/")
            print("📡 后端API: http://localhost:8001/")
            print("📚 API文档: http://localhost:8001/docs")
            print("="*50)
            print("💡 现在您可以正常访问React前端页面了！")
            
            # 自动打开浏览器
            try:
                import webbrowser
                webbrowser.open('http://localhost:3000/')
                print("🌐 已自动打开React前端页面")
            except:
                pass
            
            return True
        else:
            print("\n❌ 系统启动失败")
            print("💡 请检查错误信息并手动解决问题")
            return False
            
    except Exception as e:
        print(f"\n❌ 启动过程出错: {e}")
        return False

def check_status_only():
    """仅检查状态，不启动服务"""
    ensurer = ReactFrontendEnsurer()
    
    print("📊 MemeMaster AI 系统状态检查")
    print("="*40)
    
    # 检查后端
    backend_ok = ensurer.check_port_available(8001) and ensurer.check_service_health("http://localhost:8001/health")
    print(f"📡 后端服务: {'✅ 运行' if backend_ok else '❌ 停止'} (端口8001)")
    
    # 检查前端
    frontend_ok = ensurer.check_port_available(3000) and ensurer.check_service_health("http://localhost:3000/")
    print(f"🎨 前端服务: {'✅ 运行' if frontend_ok else '❌ 停止'} (端口3000)")
    
    if backend_ok and frontend_ok:
        print("\n🎉 系统运行正常！")
        print("🌐 React前端: http://localhost:3000/")
        print("📡 后端API: http://localhost:8001/")
    else:
        print("\n⚠️ 系统未完全运行")
        print("💡 运行 'python3 ensure_react_frontend.py start' 启动系统")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == 'status':
            check_status_only()
        elif command == 'start':
            quick_check_and_start()
        else:
            print("用法: python3 ensure_react_frontend.py [start|status]")
    else:
        # 默认启动系统
        quick_check_and_start()

if __name__ == "__main__":
    main()
