#!/usr/bin/env python3
"""
MemeMaster AI 参数优化API
提供参数优化、验证和配置管理功能
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
import json
import asyncio
import logging
from datetime import datetime
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from engines.optimization_engine import OptimizationEngine
from config.optimization_config import OptimizationConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="MemeMaster AI 参数优化API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化优化引擎
optimization_engine = OptimizationEngine()

# 请求模型
class HotspotData(BaseModel):
    keyword: str
    category: str
    score: float
    trend: str
    regions: List[str]

class CustomParameters(BaseModel):
    hotspot: Dict[str, Any]
    strategy: Dict[str, Any]
    liquidity: Dict[str, Any]
    exit: Dict[str, Any]

class SensitivityLevel(BaseModel):
    level: str  # conservative, balanced, aggressive

# 响应模型
class OptimizationResult(BaseModel):
    status: str
    recommendations: Dict[str, Any]
    confidence: float
    timestamp: str

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "optimization_api",
        "version": "1.0.0"
    }

@app.get("/optimization/market-sensitivity/{level}")
async def get_market_sensitivity(level: str):
    """获取市场灵敏度配置"""
    try:
        sensitivity_configs = {
            "conservative": {
                "hotspot_threshold": 0.85,
                "exit_threshold": 0.80,
                "stop_loss_multiplier": 1.2,
                "batch_size_multiplier": 0.8
            },
            "balanced": {
                "hotspot_threshold": 0.75,
                "exit_threshold": 0.75,
                "stop_loss_multiplier": 1.0,
                "batch_size_multiplier": 1.0
            },
            "aggressive": {
                "hotspot_threshold": 0.65,
                "exit_threshold": 0.70,
                "stop_loss_multiplier": 0.8,
                "batch_size_multiplier": 1.2
            }
        }
        
        if level not in sensitivity_configs:
            raise HTTPException(status_code=400, detail="Invalid sensitivity level")
        
        return {
            "status": "success",
            "configuration": sensitivity_configs[level],
            "level": level,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"获取灵敏度配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimization/analyze-hotspot")
async def analyze_hotspot_parameters(hotspot_data: HotspotData):
    """分析热点参数并生成优化建议"""
    try:
        # 转换为字典格式
        data_dict = hotspot_data.dict()
        
        # 调用优化引擎
        optimization_result = await optimization_engine.analyze_hotspot_parameters(data_dict)
        
        return {
            "status": "success",
            "recommendations": optimization_result,
            "confidence": 0.85,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"热点参数分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimization/strategy-parameters")
async def optimize_strategy_parameters(hotspot_data: HotspotData):
    """优化策略生成参数"""
    try:
        # 转换为字典格式
        data_dict = hotspot_data.dict()
        
        # 调用优化引擎
        optimization_result = await optimization_engine.optimize_strategy_parameters(data_dict)
        
        return {
            "status": "success",
            "allocation_optimization": optimization_result.get("allocation", {}),
            "burn_rate": optimization_result.get("burn_rate", 0.05),
            "hedging_params": optimization_result.get("hedging_params", {}),
            "confidence": 0.78,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"策略参数优化失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimization/validate-parameters")
async def validate_custom_parameters(params: CustomParameters):
    """验证自定义参数"""
    try:
        # 参数验证逻辑
        validated_params = params.dict()
        
        # 验证热点参数
        if validated_params["hotspot"]["min_virality"] < 0.5 or validated_params["hotspot"]["min_virality"] > 1.0:
            validated_params["hotspot"]["min_virality"] = max(0.5, min(1.0, validated_params["hotspot"]["min_virality"]))
        
        # 验证策略参数
        allocation_sum = sum([
            validated_params["strategy"]["airdrop_ratio"],
            validated_params["strategy"]["liquidity_ratio"],
            validated_params["strategy"]["marketing_ratio"],
            validated_params["strategy"]["team_ratio"]
        ])
        
        if abs(allocation_sum - 1.0) > 0.01:
            # 归一化分配比例
            factor = 1.0 / allocation_sum
            validated_params["strategy"]["airdrop_ratio"] *= factor
            validated_params["strategy"]["liquidity_ratio"] *= factor
            validated_params["strategy"]["marketing_ratio"] *= factor
            validated_params["strategy"]["team_ratio"] *= factor
        
        # 验证流动性参数
        validated_params["liquidity"]["slippage_tolerance"] = max(0.001, min(0.05, validated_params["liquidity"]["slippage_tolerance"]))
        
        # 验证退出策略参数
        validated_params["exit"]["stop_loss_default"] = max(0.05, min(0.25, validated_params["exit"]["stop_loss_default"]))
        
        return {
            "status": "success",
            "validated_params": validated_params,
            "validation_notes": [
                "参数已通过安全验证",
                "分配比例已自动归一化",
                "风险参数已调整到安全范围"
            ],
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"参数验证失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/optimization/custom-parameters/save")
async def save_custom_parameters(params: CustomParameters):
    """保存自定义参数配置"""
    try:
        # 这里可以保存到数据库或文件
        # 目前只是模拟保存
        config_data = {
            "parameters": params.dict(),
            "saved_at": datetime.now().isoformat(),
            "version": "1.0"
        }
        
        # 模拟保存到文件
        # with open("custom_optimization_config.json", "w") as f:
        #     json.dump(config_data, f, indent=2)
        
        return {
            "status": "success",
            "message": "参数配置已保存",
            "config_id": f"config_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"保存参数配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/optimization/custom-parameters/presets")
async def get_parameter_presets():
    """获取参数预设配置"""
    try:
        presets = {
            "conservative": {
                "name": "保守配置",
                "description": "低风险，稳定收益",
                "params": {
                    "hotspot": {
                        "min_virality": 0.90,
                        "cultural_fit_threshold": 0.80,
                        "response_timeout": 600
                    },
                    "strategy": {
                        "airdrop_ratio": 0.45,
                        "liquidity_ratio": 0.35,
                        "marketing_ratio": 0.15,
                        "team_ratio": 0.05
                    },
                    "exit": {
                        "stop_loss_default": 0.10,
                        "signal_threshold": 0.80
                    }
                }
            },
            "balanced": {
                "name": "平衡配置",
                "description": "中等风险，均衡收益",
                "params": {
                    "hotspot": {
                        "min_virality": 0.85,
                        "cultural_fit_threshold": 0.75,
                        "response_timeout": 300
                    },
                    "strategy": {
                        "airdrop_ratio": 0.50,
                        "liquidity_ratio": 0.30,
                        "marketing_ratio": 0.15,
                        "team_ratio": 0.05
                    },
                    "exit": {
                        "stop_loss_default": 0.15,
                        "signal_threshold": 0.75
                    }
                }
            },
            "aggressive": {
                "name": "激进配置",
                "description": "高风险，高收益潜力",
                "params": {
                    "hotspot": {
                        "min_virality": 0.75,
                        "cultural_fit_threshold": 0.65,
                        "response_timeout": 180
                    },
                    "strategy": {
                        "airdrop_ratio": 0.55,
                        "liquidity_ratio": 0.25,
                        "marketing_ratio": 0.15,
                        "team_ratio": 0.05
                    },
                    "exit": {
                        "stop_loss_default": 0.20,
                        "signal_threshold": 0.70
                    }
                }
            }
        }
        
        return {
            "status": "success",
            "presets": presets,
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        logger.error(f"获取预设配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
