#!/usr/bin/env python3
"""
React前端诊断脚本
检查React前端空白页问题
"""

import requests
import json
import time
import webbrowser
from pathlib import Path

def check_react_frontend():
    """检查React前端状态"""
    print("🔍 诊断React前端...")
    
    # 检查React开发服务器
    try:
        response = requests.get("http://localhost:3002/", timeout=5)
        print(f"✅ React服务器响应: HTTP {response.status_code}")
        
        # 检查响应内容
        content = response.text
        if "<!DOCTYPE html>" in content:
            print("✅ 返回HTML内容")
            if "root" in content:
                print("✅ 包含React根元素")
            else:
                print("❌ 缺少React根元素")
        else:
            print("❌ 响应不是HTML格式")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到React服务器 (端口3002)")
        return False
    except Exception as e:
        print(f"❌ 检查React服务器时出错: {e}")
        return False
    
    # 检查特定路由
    test_routes = [
        "/basic-test",
        "/test", 
        "/simple"
    ]
    
    print("\n🔍 检查测试路由...")
    for route in test_routes:
        try:
            response = requests.get(f"http://localhost:3002{route}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {route} - 正常")
            else:
                print(f"❌ {route} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {route} - 错误: {e}")
    
    return True

def check_backend_api():
    """检查后端API"""
    print("\n🔍 检查后端API...")
    
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API正常")
            return True
        else:
            print(f"❌ 后端API异常: HTTP {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 后端API无法连接")
    except Exception as e:
        print(f"❌ 后端API错误: {e}")
    
    return False

def check_file_structure():
    """检查文件结构"""
    print("\n🔍 检查文件结构...")
    
    required_files = [
        "frontend/App.jsx",
        "frontend/pages/BasicTest.jsx",
        "frontend/pages/SimpleDashboard.jsx",
        "frontend/pages/TestPage.jsx",
        "frontend/layouts/MainLayout.jsx",
        "frontend/components/Sidebar.jsx",
        "frontend/components/TopBar.jsx"
    ]
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")

def provide_solutions():
    """提供解决方案"""
    print("\n" + "="*60)
    print("🛠️  React前端空白页解决方案")
    print("="*60)
    
    print("\n📋 可能的原因和解决方案:")
    
    print("\n1. 🔧 JavaScript错误")
    print("   • 打开浏览器开发者工具 (F12)")
    print("   • 查看Console标签页的错误信息")
    print("   • 查看Network标签页的网络请求")
    
    print("\n2. 🔄 缓存问题")
    print("   • 按Ctrl+Shift+R强制刷新页面")
    print("   • 清除浏览器缓存")
    print("   • 尝试无痕模式")
    
    print("\n3. 📦 依赖问题")
    print("   • 重新安装依赖: npm install")
    print("   • 清除node_modules: rm -rf node_modules && npm install")
    
    print("\n4. 🚀 服务重启")
    print("   • 停止React服务 (Ctrl+C)")
    print("   • 重新启动: npm run frontend:dev")
    
    print("\n5. 🎯 直接测试路由")
    print("   • 基础测试: http://localhost:3002/basic-test")
    print("   • 简化仪表盘: http://localhost:3002/simple")
    print("   • 测试页面: http://localhost:3002/test")
    
    print("\n6. 📊 备用HTML仪表盘")
    print("   • 简化版: http://localhost:8090/simple_dashboard.html")
    print("   • 完整版: http://localhost:8090/dashboard.html")
    print("   • 状态页: http://localhost:8090/system_status.html")

def main():
    print("🎯 MemeMaster AI React前端诊断工具")
    print("="*50)
    
    # 检查React前端
    react_ok = check_react_frontend()
    
    # 检查后端API
    backend_ok = check_backend_api()
    
    # 检查文件结构
    check_file_structure()
    
    # 提供解决方案
    provide_solutions()
    
    # 自动打开测试页面
    if react_ok:
        print("\n🌐 自动打开测试页面...")
        try:
            webbrowser.open("http://localhost:3002/basic-test")
            print("✅ 已打开基础测试页面")
        except:
            print("⚠️ 无法自动打开浏览器")
    
    print("\n" + "="*50)
    print("📞 如果问题仍然存在，请:")
    print("1. 截图浏览器开发者工具的错误信息")
    print("2. 提供具体的错误描述")
    print("3. 说明您看到的具体现象")

if __name__ == "__main__":
    main()
