#!/bin/bash

# MemeMaster AI 策略管理模块停止脚本
# 用于停止所有相关服务

set -e

echo "🛑 停止 MemeMaster AI 策略管理模块服务"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止后端服务
stop_backend() {
    log_info "停止后端服务..."
    
    if [ -f ".backend.pid" ]; then
        BACKEND_PID=$(cat .backend.pid)
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
            log_success "后端服务已停止 (PID: $BACKEND_PID)"
        else
            log_warning "后端服务进程不存在 (PID: $BACKEND_PID)"
        fi
        rm .backend.pid
    else
        log_warning "未找到后端服务 PID 文件"
    fi
    
    # 查找并停止可能的 Python 进程
    PYTHON_PIDS=$(pgrep -f "python.*web_app.py" || true)
    if [ ! -z "$PYTHON_PIDS" ]; then
        echo $PYTHON_PIDS | xargs kill 2>/dev/null || true
        log_info "已停止相关 Python 进程"
    fi
}

# 停止前端服务
stop_frontend() {
    log_info "停止前端服务..."
    
    if [ -f ".frontend.pid" ]; then
        FRONTEND_PID=$(cat .frontend.pid)
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
            log_success "前端服务已停止 (PID: $FRONTEND_PID)"
        else
            log_warning "前端服务进程不存在 (PID: $FRONTEND_PID)"
        fi
        rm .frontend.pid
    else
        log_warning "未找到前端服务 PID 文件"
    fi
    
    # 查找并停止可能的 Node.js 进程
    NODE_PIDS=$(pgrep -f "node.*react-scripts" || true)
    if [ ! -z "$NODE_PIDS" ]; then
        echo $NODE_PIDS | xargs kill 2>/dev/null || true
        log_info "已停止相关 Node.js 进程"
    fi
}

# 停止端口占用的进程
stop_port_processes() {
    log_info "检查端口占用..."
    
    # 停止占用 8000 端口的进程
    PORT_8000_PID=$(lsof -ti:8000 || true)
    if [ ! -z "$PORT_8000_PID" ]; then
        kill $PORT_8000_PID 2>/dev/null || true
        log_info "已停止占用端口 8000 的进程 (PID: $PORT_8000_PID)"
    fi
    
    # 停止占用 3000 端口的进程
    PORT_3000_PID=$(lsof -ti:3000 || true)
    if [ ! -z "$PORT_3000_PID" ]; then
        kill $PORT_3000_PID 2>/dev/null || true
        log_info "已停止占用端口 3000 的进程 (PID: $PORT_3000_PID)"
    fi
}

# 清理临时文件
cleanup_files() {
    log_info "清理临时文件..."
    
    # 清理 PID 文件
    rm -f .backend.pid .frontend.pid
    
    # 清理日志文件（如果存在）
    rm -f logs/strategy-manager-*.log
    
    # 清理临时数据库文件（如果存在）
    rm -f strategy_manager.db*
    
    log_success "临时文件清理完成"
}

# 显示状态
show_status() {
    log_info "检查服务状态..."
    
    # 检查端口 8000
    if lsof -i:8000 >/dev/null 2>&1; then
        log_warning "端口 8000 仍被占用"
    else
        log_success "端口 8000 已释放"
    fi
    
    # 检查端口 3000
    if lsof -i:3000 >/dev/null 2>&1; then
        log_warning "端口 3000 仍被占用"
    else
        log_success "端口 3000 已释放"
    fi
    
    # 检查相关进程
    PYTHON_COUNT=$(pgrep -f "python.*web_app.py" | wc -l || echo "0")
    NODE_COUNT=$(pgrep -f "node.*react-scripts" | wc -l || echo "0")
    
    if [ "$PYTHON_COUNT" -eq 0 ] && [ "$NODE_COUNT" -eq 0 ]; then
        log_success "所有相关进程已停止"
    else
        log_warning "仍有 $PYTHON_COUNT 个 Python 进程和 $NODE_COUNT 个 Node.js 进程在运行"
    fi
}

# 强制停止
force_stop() {
    log_warning "执行强制停止..."
    
    # 强制停止所有相关进程
    pkill -f "python.*web_app.py" 2>/dev/null || true
    pkill -f "node.*react-scripts" 2>/dev/null || true
    pkill -f "uvicorn.*web_app" 2>/dev/null || true
    
    # 强制释放端口
    fuser -k 8000/tcp 2>/dev/null || true
    fuser -k 3000/tcp 2>/dev/null || true
    
    log_success "强制停止完成"
}

# 主函数
main() {
    # 解析命令行参数
    FORCE=false
    CLEAN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                FORCE=true
                shift
                ;;
            --clean)
                CLEAN=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --force    强制停止所有相关进程"
                echo "  --clean    清理临时文件"
                echo "  -h, --help 显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行停止步骤
    if [ "$FORCE" = true ]; then
        force_stop
    else
        stop_backend
        stop_frontend
        stop_port_processes
    fi
    
    if [ "$CLEAN" = true ]; then
        cleanup_files
    fi
    
    # 等待进程完全停止
    sleep 2
    
    show_status
    
    echo ""
    log_success "MemeMaster AI 策略管理模块已停止"
    echo ""
    echo "💡 使用以下命令重新启动:"
    echo "   ./scripts/deploy-strategy-manager.sh"
    echo ""
}

# 运行主函数
main "$@"
