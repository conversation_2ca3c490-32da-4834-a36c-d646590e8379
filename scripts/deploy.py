#!/usr/bin/env python3
"""
智能合约部署脚本
自动化部署SecureMemeToken合约并进行初始化配置
"""

import os
import sys
import json
import time
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.blockchain.ethereum import EthereumClient
from src.utils.logger import LoggerManager
from src.strategy.generator import TokenStrategy

logger = LoggerManager.get_logger('deployment')


class ContractDeployer:
    """合约部署器"""
    
    def __init__(self):
        self.ethereum_client = EthereumClient()
        self.contract_abi = self._load_contract_abi()
        self.contract_bytecode = self._load_contract_bytecode()
        
        logger.info("ContractDeployer initialized")
    
    def _load_contract_abi(self) -> list:
        """加载合约ABI"""
        try:
            abi_path = os.path.join(os.path.dirname(__file__), '..', 'contracts', 'SecureMemeToken.json')
            
            # 如果编译后的ABI文件不存在，使用预定义的ABI
            if not os.path.exists(abi_path):
                logger.warning("Compiled ABI not found, using predefined ABI")
                return self._get_predefined_abi()
            
            with open(abi_path, 'r') as f:
                contract_data = json.load(f)
                return contract_data['abi']
                
        except Exception as e:
            logger.error(f"Failed to load contract ABI: {str(e)}")
            return self._get_predefined_abi()
    
    def _load_contract_bytecode(self) -> str:
        """加载合约字节码"""
        try:
            bytecode_path = os.path.join(os.path.dirname(__file__), '..', 'contracts', 'SecureMemeToken.json')
            
            if not os.path.exists(bytecode_path):
                logger.error("Contract bytecode not found. Please compile the contract first.")
                raise FileNotFoundError("Contract bytecode not found")
            
            with open(bytecode_path, 'r') as f:
                contract_data = json.load(f)
                return contract_data['bytecode']
                
        except Exception as e:
            logger.error(f"Failed to load contract bytecode: {str(e)}")
            # 返回示例字节码（实际部署时需要真实的编译后字节码）
            return "0x608060405234801561001057600080fd5b50..."
    
    def _get_predefined_abi(self) -> list:
        """获取预定义的ABI"""
        return [
            {
                "inputs": [
                    {"internalType": "string", "name": "name", "type": "string"},
                    {"internalType": "string", "name": "symbol", "type": "string"},
                    {"internalType": "uint256", "name": "totalSupply", "type": "uint256"},
                    {"internalType": "uint256", "name": "_burnRate", "type": "uint256"},
                    {"internalType": "address", "name": "_taxWallet", "type": "address"},
                    {"internalType": "address", "name": "_marketingWallet", "type": "address"}
                ],
                "stateMutability": "nonpayable",
                "type": "constructor"
            },
            {
                "inputs": [],
                "name": "name",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "symbol",
                "outputs": [{"internalType": "string", "name": "", "type": "string"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "totalSupply",
                "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
                "stateMutability": "view",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "uint256", "name": "newRate", "type": "uint256"}],
                "name": "updateBurnRate",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [],
                "name": "enableTrading",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            },
            {
                "inputs": [{"internalType": "address", "name": "_liquidityPool", "type": "address"}],
                "name": "setLiquidityPool",
                "outputs": [],
                "stateMutability": "nonpayable",
                "type": "function"
            }
        ]
    
    def deploy_token(self, strategy: TokenStrategy) -> Optional[Dict[str, Any]]:
        """
        部署代币合约
        
        Args:
            strategy: 代币策略
            
        Returns:
            部署结果
        """
        try:
            logger.info(f"Starting deployment for token: {strategy.token_name}")
            
            # 准备构造函数参数
            constructor_args = self._prepare_constructor_args(strategy)
            
            # 检查账户余额
            balance = self.ethereum_client.get_balance()
            if balance < 0.1:  # 至少需要0.1 ETH
                logger.error(f"Insufficient balance for deployment: {balance} ETH")
                return None
            
            # 部署合约
            deployment_result = self.ethereum_client.deploy_contract(
                abi=self.contract_abi,
                bytecode=self.contract_bytecode,
                constructor_args=constructor_args
            )
            
            if not deployment_result:
                logger.error("Contract deployment failed")
                return None
            
            contract_address = deployment_result['contract_address']
            logger.info(f"Contract deployed successfully at: {contract_address}")
            
            # 等待确认
            if self.ethereum_client.wait_for_confirmations(
                deployment_result['transaction_hash'], 
                confirmations=3
            ):
                logger.info("Deployment confirmed")
                
                # 执行初始化配置
                init_result = self._initialize_contract(contract_address, strategy)
                
                if init_result:
                    logger.info("Contract initialization completed")
                    
                    # 保存部署信息
                    deployment_info = {
                        'contract_address': contract_address,
                        'transaction_hash': deployment_result['transaction_hash'],
                        'token_name': strategy.token_name,
                        'symbol': strategy.symbol,
                        'total_supply': strategy.total_supply,
                        'burn_rate': strategy.burn_rate,
                        'deployment_time': time.time(),
                        'network': self.ethereum_client.chain_id
                    }
                    
                    self._save_deployment_info(deployment_info)
                    return deployment_info
                else:
                    logger.error("Contract initialization failed")
                    return None
            else:
                logger.error("Deployment confirmation failed")
                return None
                
        except Exception as e:
            logger.error(f"Token deployment failed: {str(e)}")
            return None
    
    def _prepare_constructor_args(self, strategy: TokenStrategy) -> list:
        """
        准备构造函数参数
        
        Args:
            strategy: 代币策略
            
        Returns:
            构造函数参数列表
        """
        # 获取钱包地址
        tax_wallet = os.getenv('TAX_WALLET', self.ethereum_client.address)
        marketing_wallet = os.getenv('MARKETING_WALLET', self.ethereum_client.address)
        
        # 转换燃烧率为基点（10000 = 100%）
        burn_rate_bp = int(strategy.burn_rate * 10000)
        
        return [
            strategy.token_name,        # name
            strategy.symbol,            # symbol
            strategy.total_supply,      # totalSupply
            burn_rate_bp,              # burnRate (in basis points)
            tax_wallet,                # taxWallet
            marketing_wallet           # marketingWallet
        ]
    
    def _initialize_contract(self, contract_address: str, strategy: TokenStrategy) -> bool:
        """
        初始化合约配置
        
        Args:
            contract_address: 合约地址
            strategy: 代币策略
            
        Returns:
            是否成功
        """
        try:
            logger.info("Initializing contract configuration...")
            
            # 设置交易限制
            max_tx_amount = strategy.total_supply // 50  # 2%
            max_wallet_amount = strategy.total_supply // 33  # 3%
            
            # 更新限制
            result1 = self.ethereum_client.call_contract_function(
                contract_address=contract_address,
                abi=self.contract_abi,
                function_name="updateLimits",
                args=[max_tx_amount, max_wallet_amount]
            )
            
            if not result1:
                logger.error("Failed to update limits")
                return False
            
            time.sleep(15)  # 等待交易确认
            
            # 启用交易（如果策略要求立即启用）
            if strategy.launch_schedule.get('immediate_trading', False):
                result2 = self.ethereum_client.call_contract_function(
                    contract_address=contract_address,
                    abi=self.contract_abi,
                    function_name="enableTrading"
                )
                
                if not result2:
                    logger.error("Failed to enable trading")
                    return False
                
                logger.info("Trading enabled")
            
            return True
            
        except Exception as e:
            logger.error(f"Contract initialization failed: {str(e)}")
            return False
    
    def _save_deployment_info(self, deployment_info: Dict[str, Any]):
        """
        保存部署信息
        
        Args:
            deployment_info: 部署信息
        """
        try:
            deployments_dir = os.path.join(os.path.dirname(__file__), '..', 'deployments')
            os.makedirs(deployments_dir, exist_ok=True)
            
            filename = f"deployment_{int(time.time())}.json"
            filepath = os.path.join(deployments_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(deployment_info, f, indent=2)
            
            logger.info(f"Deployment info saved to: {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to save deployment info: {str(e)}")
    
    def verify_deployment(self, contract_address: str) -> bool:
        """
        验证部署结果
        
        Args:
            contract_address: 合约地址
            
        Returns:
            是否验证成功
        """
        try:
            logger.info(f"Verifying deployment at: {contract_address}")
            
            # 检查合约代码
            code = self.ethereum_client.w3.eth.get_code(contract_address)
            if code == b'':
                logger.error("No contract code found at address")
                return False
            
            # 读取基本信息
            name = self.ethereum_client.read_contract(
                contract_address, self.contract_abi, "name"
            )
            symbol = self.ethereum_client.read_contract(
                contract_address, self.contract_abi, "symbol"
            )
            total_supply = self.ethereum_client.read_contract(
                contract_address, self.contract_abi, "totalSupply"
            )
            
            if name and symbol and total_supply:
                logger.info(f"Contract verified - Name: {name}, Symbol: {symbol}, Supply: {total_supply}")
                return True
            else:
                logger.error("Failed to read contract information")
                return False
                
        except Exception as e:
            logger.error(f"Deployment verification failed: {str(e)}")
            return False


def main():
    """主函数"""
    try:
        # 示例：创建一个测试策略
        from src.strategy.generator import TokenStrategy
        
        test_strategy = TokenStrategy(
            token_name="TestMemeCoin",
            symbol="TMC",
            total_supply=1000000000,  # 10亿
            burn_rate=0.05,  # 5%
            launch_schedule={'immediate_trading': False},
            economic_model={},
            marketing_plan={},
            risk_parameters={},
            compliance_status="PASSED",
            estimated_roi=2.5
        )
        
        # 创建部署器并部署
        deployer = ContractDeployer()
        result = deployer.deploy_token(test_strategy)
        
        if result:
            print(f"✅ Deployment successful!")
            print(f"Contract Address: {result['contract_address']}")
            print(f"Transaction Hash: {result['transaction_hash']}")
            
            # 验证部署
            if deployer.verify_deployment(result['contract_address']):
                print("✅ Deployment verified!")
            else:
                print("❌ Deployment verification failed!")
        else:
            print("❌ Deployment failed!")
            
    except Exception as e:
        logger.error(f"Deployment script failed: {str(e)}")
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    main()
