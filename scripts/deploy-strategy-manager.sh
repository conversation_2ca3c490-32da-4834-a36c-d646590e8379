#!/bin/bash

# MemeMaster AI 策略管理模块部署脚本
# 用于快速部署和启动策略管理功能

set -e

echo "🚀 MemeMaster AI 策略管理模块部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    
    # 检查 npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    # 检查 Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python 3.8+"
        exit 1
    fi
    
    # 检查 pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装 pip3"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    
    cd frontend
    
    if [ ! -f "package.json" ]; then
        log_error "未找到 package.json 文件"
        exit 1
    fi
    
    npm install
    
    # 安装策略管理相关的额外依赖
    npm install @reduxjs/toolkit react-redux recharts lucide-react
    
    cd ..
    log_success "前端依赖安装完成"
}

# 安装后端依赖
install_backend_deps() {
    log_info "安装后端依赖..."
    
    # 检查是否存在虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建 Python 虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    pip install -r requirements.txt
    
    # 安装策略管理相关的额外依赖
    pip install fastapi uvicorn python-multipart aiofiles
    
    log_success "后端依赖安装完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    # 创建 .env 文件（如果不存在）
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# MemeMaster AI 环境配置
NODE_ENV=development
REACT_APP_API_URL=http://localhost:8000
PYTHON_ENV=development

# 策略管理配置
STRATEGY_REPOSITORY_SIZE=1000
MONITORING_REFRESH_INTERVAL=30
DEPLOYMENT_TIMEOUT=300

# 数据库配置（可选）
DATABASE_URL=sqlite:///./strategy_manager.db

# API 密钥（请替换为实际值）
OPENAI_API_KEY=your_openai_api_key_here
BLOCKCHAIN_API_KEY=your_blockchain_api_key_here
EOF
        log_warning "已创建 .env 文件，请根据需要修改配置"
    else
        log_info ".env 文件已存在，跳过创建"
    fi
    
    log_success "环境变量配置完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端应用..."
    
    cd frontend
    npm run build
    cd ..
    
    log_success "前端构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动后端服务
    log_info "启动后端服务 (端口 8000)..."
    source venv/bin/activate
    python web_app.py &
    BACKEND_PID=$!
    
    # 等待后端启动
    sleep 5
    
    # 检查后端是否启动成功
    if curl -s http://localhost:8000/health > /dev/null; then
        log_success "后端服务启动成功"
    else
        log_error "后端服务启动失败"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
    
    # 启动前端服务
    log_info "启动前端服务 (端口 3000)..."
    cd frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    sleep 10
    
    # 检查前端是否启动成功
    if curl -s http://localhost:3000 > /dev/null; then
        log_success "前端服务启动成功"
    else
        log_warning "前端服务可能需要更多时间启动"
    fi
    
    # 保存 PID 到文件
    echo $BACKEND_PID > .backend.pid
    echo $FRONTEND_PID > .frontend.pid
    
    log_success "服务启动完成"
    echo ""
    echo "🎉 MemeMaster AI 策略管理模块已成功部署！"
    echo ""
    echo "📱 前端地址: http://localhost:3000"
    echo "🔧 后端地址: http://localhost:8000"
    echo "📚 API 文档: http://localhost:8000/docs"
    echo ""
    echo "💡 使用以下命令停止服务:"
    echo "   ./scripts/stop-strategy-manager.sh"
    echo ""
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 运行前端测试
    cd frontend
    npm test -- --watchAll=false
    cd ..
    
    # 运行后端测试（如果存在）
    if [ -f "tests/test_strategy_manager.py" ]; then
        source venv/bin/activate
        python -m pytest tests/test_strategy_manager.py -v
    fi
    
    log_success "测试完成"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 停止服务
    if [ -f ".backend.pid" ]; then
        kill $(cat .backend.pid) 2>/dev/null || true
        rm .backend.pid
    fi
    
    if [ -f ".frontend.pid" ]; then
        kill $(cat .frontend.pid) 2>/dev/null || true
        rm .frontend.pid
    fi
}

# 信号处理
trap cleanup EXIT

# 主函数
main() {
    echo "开始部署 MemeMaster AI 策略管理模块..."
    echo ""
    
    # 解析命令行参数
    SKIP_DEPS=false
    SKIP_BUILD=false
    RUN_TESTS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --test)
                RUN_TESTS=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --skip-deps    跳过依赖安装"
                echo "  --skip-build   跳过前端构建"
                echo "  --test         运行测试"
                echo "  -h, --help     显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                exit 1
                ;;
        esac
    done
    
    # 执行部署步骤
    check_dependencies
    
    if [ "$SKIP_DEPS" = false ]; then
        install_frontend_deps
        install_backend_deps
    fi
    
    setup_environment
    
    if [ "$SKIP_BUILD" = false ]; then
        build_frontend
    fi
    
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi
    
    start_services
    
    # 保持脚本运行
    echo "按 Ctrl+C 停止服务..."
    wait
}

# 运行主函数
main "$@"
