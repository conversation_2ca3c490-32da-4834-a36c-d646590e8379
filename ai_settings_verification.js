/**
 * MemeMaster AI 舆情监控设置功能验证测试
 * 验证热点监控页面的AI舆情监控设置功能是否正常工作
 */

const puppeteer = require('puppeteer');

class AISettingsVerifier {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
  }

  async init() {
    console.log('🚀 启动AI舆情监控设置功能验证测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async navigateToHotspotPage() {
    console.log('📍 导航到热点监控页面...');
    
    try {
      await this.page.goto('http://localhost:3002/hotspot', {
        waitUntil: 'networkidle2',
        timeout: 30000
      });
      
      await this.page.waitForSelector('h1', { timeout: 10000 });
      const title = await this.page.$eval('h1', el => el.textContent);
      
      this.addResult('页面导航', title.includes('实时热点监控'), `页面标题: ${title}`);
      
    } catch (error) {
      this.addResult('页面导航', false, `导航失败: ${error.message}`);
    }
  }

  async verifySettingsTab() {
    console.log('🔍 验证设置标签功能...');
    
    try {
      // 检查设置标签是否存在
      const settingsTab = await this.page.$('button:has-text("设置")');
      this.addResult('设置标签存在', settingsTab !== null, '设置标签按钮存在');
      
      if (settingsTab) {
        // 点击设置标签
        await settingsTab.click();
        await this.page.waitForTimeout(1000);
        
        // 验证标签激活状态
        const isActive = await this.page.$eval('button:has-text("设置")', 
          el => el.classList.contains('bg-gradient-to-r')
        );
        
        this.addResult('设置标签激活', isActive, '设置标签成功激活');
      }
      
    } catch (error) {
      this.addResult('设置标签验证', false, `设置标签验证失败: ${error.message}`);
    }
  }

  async verifySettingsContent() {
    console.log('📊 验证设置内容...');
    
    try {
      // 确保在设置标签页
      await this.page.click('button:has-text("设置")');
      await this.page.waitForTimeout(1000);
      
      // 验证设置标题
      const settingsTitle = await this.page.$('h3:has-text("AI舆情监控设置")');
      this.addResult('设置标题', settingsTitle !== null, 'AI舆情监控设置标题显示正确');
      
      // 验证操作按钮
      const resetButton = await this.page.$('button:has-text("重置")');
      const saveButton = await this.page.$('button:has-text("保存设置")');
      
      this.addResult('操作按钮', resetButton && saveButton, '重置和保存按钮存在');
      
    } catch (error) {
      this.addResult('设置内容验证', false, `设置内容验证失败: ${error.message}`);
    }
  }

  async verifyDataSourceConfig() {
    console.log('🌐 验证数据源配置...');
    
    try {
      // 验证数据源配置标题
      const dataSourceTitle = await this.page.$('h4:has-text("数据源配置")');
      this.addResult('数据源标题', dataSourceTitle !== null, '数据源配置标题存在');
      
      // 验证数据源选项
      const sources = ['twitter', 'reddit', 'tiktok', 'youtube', 'discord', 'darkweb'];
      let sourceCount = 0;
      
      for (const source of sources) {
        const sourceElement = await this.page.$(`text=${source}`);
        if (sourceElement) sourceCount++;
      }
      
      this.addResult('数据源选项', sourceCount >= 4, `找到 ${sourceCount}/${sources.length} 个数据源`);
      
      // 测试数据源开关
      const checkboxes = await this.page.$$('input[type="checkbox"]');
      this.addResult('数据源开关', checkboxes.length > 0, `找到 ${checkboxes.length} 个开关控件`);
      
    } catch (error) {
      this.addResult('数据源配置验证', false, `数据源配置验证失败: ${error.message}`);
    }
  }

  async verifyAIModelConfig() {
    console.log('🧠 验证AI模型配置...');
    
    try {
      // 验证AI模型配置标题
      const modelTitle = await this.page.$('h4:has-text("AI模型配置")');
      this.addResult('AI模型标题', modelTitle !== null, 'AI模型配置标题存在');
      
      // 验证滑块控件
      const sliders = await this.page.$$('input[type="range"]');
      this.addResult('滑块控件', sliders.length > 0, `找到 ${sliders.length} 个滑块控件`);
      
      // 验证下拉选择框
      const selects = await this.page.$$('select');
      this.addResult('选择框', selects.length >= 2, `找到 ${selects.length} 个选择框`);
      
      // 验证预测准确率选项
      const predictionOptions = await this.page.$$('option');
      this.addResult('预测选项', predictionOptions.length > 0, `找到 ${predictionOptions.length} 个选项`);
      
    } catch (error) {
      this.addResult('AI模型配置验证', false, `AI模型配置验证失败: ${error.message}`);
    }
  }

  async verifyLanguageConfig() {
    console.log('🌍 验证语言配置...');
    
    try {
      // 验证语言配置标题
      const languageTitle = await this.page.$('h4:has-text("语言配置")');
      this.addResult('语言配置标题', languageTitle !== null, '语言配置标题存在');
      
      // 验证语言选项
      const languages = ['英语', '中文', '韩语', '日语', '西班牙语', '俄语'];
      let languageCount = 0;
      
      for (const lang of languages) {
        const langElement = await this.page.$(`text=${lang}`);
        if (langElement) languageCount++;
      }
      
      this.addResult('语言选项', languageCount >= 4, `找到 ${languageCount}/${languages.length} 种语言`);
      
      // 验证国旗图标
      const flags = await this.page.$$eval('span', spans => 
        spans.filter(span => /[🇺🇸🇨🇳🇰🇷🇯🇵🇪🇸🇷🇺]/.test(span.textContent)).length
      );
      
      this.addResult('国旗图标', flags > 0, `找到 ${flags} 个国旗图标`);
      
    } catch (error) {
      this.addResult('语言配置验证', false, `语言配置验证失败: ${error.message}`);
    }
  }

  async verifyMonitoringScope() {
    console.log('🎯 验证监控范围配置...');
    
    try {
      // 验证监控范围标题
      const scopeTitle = await this.page.$('h4:has-text("监控范围")');
      this.addResult('监控范围标题', scopeTitle !== null, '监控范围标题存在');
      
      // 验证关键词管理
      const keywordLabel = await this.page.$('label:has-text("监控关键词")');
      this.addResult('关键词管理', keywordLabel !== null, '关键词管理功能存在');
      
      // 验证关键词标签
      const keywordTags = await this.page.$$('.bg-blue-500\\/20');
      this.addResult('关键词标签', keywordTags.length > 0, `找到 ${keywordTags.length} 个关键词标签`);
      
      // 验证添加关键词输入框
      const keywordInput = await this.page.$('input[placeholder="添加关键词..."]');
      this.addResult('关键词输入', keywordInput !== null, '关键词输入框存在');
      
      // 验证暗网监控选项
      const darkwebOption = await this.page.$('span:has-text("暗网监控")');
      this.addResult('暗网监控', darkwebOption !== null, '暗网监控选项存在');
      
    } catch (error) {
      this.addResult('监控范围验证', false, `监控范围验证失败: ${error.message}`);
    }
  }

  async verifyNotificationSettings() {
    console.log('🔔 验证通知设置...');
    
    try {
      // 验证通知设置标题
      const notificationTitle = await this.page.$('h4:has-text("通知设置")');
      this.addResult('通知设置标题', notificationTitle !== null, '通知设置标题存在');
      
      // 验证通知选项
      const notifications = ['热点警报', '预测更新', '策略就绪', '风险警告', '邮件通知', '推送通知'];
      let notificationCount = 0;
      
      for (const notification of notifications) {
        const notifElement = await this.page.$(`span:has-text("${notification}")`);
        if (notifElement) notificationCount++;
      }
      
      this.addResult('通知选项', notificationCount >= 4, `找到 ${notificationCount}/${notifications.length} 个通知选项`);
      
    } catch (error) {
      this.addResult('通知设置验证', false, `通知设置验证失败: ${error.message}`);
    }
  }

  async verifyInteractivity() {
    console.log('🎯 验证交互功能...');
    
    try {
      // 测试保存按钮
      const saveButton = await this.page.$('button:has-text("保存设置")');
      if (saveButton) {
        await saveButton.click();
        await this.page.waitForTimeout(500);
        this.addResult('保存按钮', true, '保存按钮点击正常');
      }
      
      // 测试重置按钮
      const resetButton = await this.page.$('button:has-text("重置")');
      if (resetButton) {
        await resetButton.click();
        await this.page.waitForTimeout(500);
        this.addResult('重置按钮', true, '重置按钮点击正常');
      }
      
      // 测试数据源开关
      const firstCheckbox = await this.page.$('input[type="checkbox"]');
      if (firstCheckbox) {
        await firstCheckbox.click();
        await this.page.waitForTimeout(300);
        this.addResult('开关交互', true, '数据源开关交互正常');
      }
      
    } catch (error) {
      this.addResult('交互功能验证', false, `交互功能验证失败: ${error.message}`);
    }
  }

  async verifyUIConsistency() {
    console.log('🎨 验证UI一致性...');
    
    try {
      // 验证卡片设计
      const cards = await this.page.$$('.bg-gray-800\\/50');
      this.addResult('卡片设计', cards.length >= 5, `找到 ${cards.length} 个设置卡片`);
      
      // 验证颜色主题
      const blueElements = await this.page.$$('[class*="blue"]');
      this.addResult('蓝色主题', blueElements.length > 0, `蓝色主题元素: ${blueElements.length}`);
      
      // 验证图标使用
      const icons = await this.page.$$('svg');
      this.addResult('图标系统', icons.length > 10, `图标数量: ${icons.length}`);
      
      // 验证圆角设计
      const roundedElements = await this.page.$$('[class*="rounded"]');
      this.addResult('圆角设计', roundedElements.length > 5, `圆角元素: ${roundedElements.length}`);
      
    } catch (error) {
      this.addResult('UI一致性验证', false, `UI一致性验证失败: ${error.message}`);
    }
  }

  addResult(testName, passed, details) {
    this.testResults.push({
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details}`);
  }

  async generateReport() {
    console.log('\n📋 生成验证报告...');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 MemeMaster AI 舆情监控设置功能验证报告');
    console.log('='.repeat(60));
    console.log(`📊 总体结果: ${passedTests}/${totalTests} 项测试通过 (${successRate}%)`);
    console.log(`⏰ 测试时间: ${new Date().toLocaleString()}`);
    console.log('\n📝 详细结果:');
    
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.details}`);
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (successRate >= 90) {
      console.log('🎉 验证结果: 优秀! AI舆情监控设置功能运行良好');
    } else if (successRate >= 75) {
      console.log('👍 验证结果: 良好! 大部分功能正常，建议优化部分细节');
    } else {
      console.log('⚠️  验证结果: 需要改进! 存在多个问题需要修复');
    }
    
    return {
      totalTests,
      passedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    };
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullVerification() {
    try {
      await this.init();
      await this.navigateToHotspotPage();
      await this.verifySettingsTab();
      await this.verifySettingsContent();
      await this.verifyDataSourceConfig();
      await this.verifyAIModelConfig();
      await this.verifyLanguageConfig();
      await this.verifyMonitoringScope();
      await this.verifyNotificationSettings();
      await this.verifyInteractivity();
      await this.verifyUIConsistency();
      
      return await this.generateReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      this.addResult('整体验证', false, `验证失败: ${error.message}`);
      return await this.generateReport();
    } finally {
      await this.cleanup();
    }
  }
}

// 运行验证
async function main() {
  const verifier = new AISettingsVerifier();
  const report = await verifier.runFullVerification();
  
  // 保存报告到文件
  const fs = require('fs');
  const reportPath = 'ai_settings_verification_report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = AISettingsVerifier;
