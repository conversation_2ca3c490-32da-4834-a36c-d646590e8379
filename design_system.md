# MemeMaster AI 专业数据分析视觉系统

## 🎨 设计理念

### 核心原则
- **专业性**: 体现金融科技和数据分析的专业形象
- **可读性**: 确保复杂数据的清晰展示
- **现代感**: 采用当代数字化产品的设计语言
- **功能性**: 设计服务于功能，提升用户效率

### 视觉印象关键词
- 专业 (Professional)
- 精准 (Precise) 
- 智能 (Intelligent)
- 可信 (Trustworthy)
- 高效 (Efficient)

## 🌈 色彩系统

### 主色调 - 深蓝科技风
```css
/* 主品牌色 - 深邃蓝 */
--primary-900: #0F172A    /* 深海蓝 - 主背景 */
--primary-800: #1E293B    /* 钢铁蓝 - 卡片背景 */
--primary-700: #334155    /* 石墨蓝 - 边框 */
--primary-600: #475569    /* 中性蓝 - 次要文本 */

/* 功能色 - 数据可视化 */
--accent-blue: #3B82F6     /* 主要数据 */
--accent-cyan: #06B6D4     /* 次要数据 */
--accent-indigo: #6366F1   /* 强调数据 */
--accent-purple: #8B5CF6   /* 特殊数据 */
```

### 状态色系 - 金融级别
```css
/* 成功/盈利 - 翡翠绿系 */
--success-500: #10B981
--success-400: #34D399
--success-300: #6EE7B7

/* 警告/中性 - 琥珀黄系 */
--warning-500: #F59E0B
--warning-400: #FBBF24
--warning-300: #FCD34D

/* 危险/亏损 - 珊瑚红系 */
--danger-500: #EF4444
--danger-400: #F87171
--danger-300: #FCA5A5

/* 信息/中性 - 天空蓝系 */
--info-500: #3B82F6
--info-400: #60A5FA
--info-300: #93C5FD
```

### 中性色系 - 专业灰阶
```css
/* 文本层级 */
--text-primary: #FFFFFF      /* 主要文本 */
--text-secondary: #E2E8F0    /* 次要文本 */
--text-tertiary: #94A3B8     /* 辅助文本 */
--text-disabled: #64748B     /* 禁用文本 */

/* 背景层级 */
--bg-primary: #0F172A        /* 主背景 */
--bg-secondary: #1E293B      /* 卡片背景 */
--bg-tertiary: #334155       /* 悬浮背景 */
--bg-overlay: rgba(15, 23, 42, 0.95)  /* 遮罩背景 */
```

## 📝 字体系统

### 字体族
```css
/* 主字体 - 现代无衬线 */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

/* 数据字体 - 等宽字体 */
--font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;

/* 标题字体 - 几何字体 */
--font-display: 'Poppins', 'Inter', sans-serif;
```

### 字体尺寸 - 模块化比例
```css
/* 标题层级 */
--text-4xl: 2.25rem   /* 36px - 主标题 */
--text-3xl: 1.875rem  /* 30px - 页面标题 */
--text-2xl: 1.5rem    /* 24px - 区块标题 */
--text-xl: 1.25rem    /* 20px - 卡片标题 */
--text-lg: 1.125rem   /* 18px - 子标题 */

/* 正文层级 */
--text-base: 1rem     /* 16px - 正文 */
--text-sm: 0.875rem   /* 14px - 小文本 */
--text-xs: 0.75rem    /* 12px - 标签 */

/* 数据显示 */
--text-data-lg: 2rem  /* 32px - 大数据 */
--text-data-md: 1.5rem /* 24px - 中数据 */
--text-data-sm: 1.25rem /* 20px - 小数据 */
```

### 字重系统
```css
--font-light: 300     /* 轻量文本 */
--font-normal: 400    /* 正文 */
--font-medium: 500    /* 强调 */
--font-semibold: 600  /* 标题 */
--font-bold: 700      /* 重要标题 */
--font-black: 900     /* 超级标题 */
```

## 📐 间距系统

### 基础间距单位
```css
/* 基础单位 4px */
--space-1: 0.25rem    /* 4px */
--space-2: 0.5rem     /* 8px */
--space-3: 0.75rem    /* 12px */
--space-4: 1rem       /* 16px */
--space-5: 1.25rem    /* 20px */
--space-6: 1.5rem     /* 24px */
--space-8: 2rem       /* 32px */
--space-10: 2.5rem    /* 40px */
--space-12: 3rem      /* 48px */
--space-16: 4rem      /* 64px */
--space-20: 5rem      /* 80px */
```

### 组件间距
```css
/* 页面级间距 */
--spacing-page: var(--space-8)      /* 页面边距 */
--spacing-section: var(--space-12)  /* 区块间距 */

/* 组件级间距 */
--spacing-card: var(--space-6)      /* 卡片内边距 */
--spacing-element: var(--space-4)   /* 元素间距 */
--spacing-tight: var(--space-2)     /* 紧密间距 */
```

## 🔲 圆角系统

### 圆角规范
```css
--radius-none: 0
--radius-sm: 0.25rem    /* 4px - 小元素 */
--radius-md: 0.5rem     /* 8px - 按钮 */
--radius-lg: 0.75rem    /* 12px - 卡片 */
--radius-xl: 1rem       /* 16px - 大卡片 */
--radius-2xl: 1.5rem    /* 24px - 模态框 */
--radius-full: 9999px   /* 圆形 */
```

## 🌟 阴影系统

### 层级阴影
```css
/* 卡片阴影 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

/* 发光效果 */
--glow-blue: 0 0 20px rgba(59, 130, 246, 0.3);
--glow-green: 0 0 20px rgba(16, 185, 129, 0.3);
--glow-red: 0 0 20px rgba(239, 68, 68, 0.3);
```

## 📊 数据可视化色彩

### 图表色板
```css
/* 主要数据系列 */
--chart-primary: #3B82F6    /* 蓝色 */
--chart-secondary: #10B981  /* 绿色 */
--chart-tertiary: #F59E0B   /* 黄色 */
--chart-quaternary: #EF4444 /* 红色 */
--chart-quinary: #8B5CF6    /* 紫色 */

/* 渐变色板 */
--gradient-blue: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
--gradient-green: linear-gradient(135deg, #10B981 0%, #047857 100%);
--gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #5B21B6 100%);
```

### 热力图色板
```css
/* 从低到高的热力色彩 */
--heatmap-1: #1E293B  /* 最低 */
--heatmap-2: #334155
--heatmap-3: #475569
--heatmap-4: #64748B
--heatmap-5: #94A3B8
--heatmap-6: #CBD5E1
--heatmap-7: #E2E8F0  /* 最高 */
```

## 🎯 组件设计原则

### 卡片设计
- 背景: `--bg-secondary`
- 边框: `1px solid rgba(255, 255, 255, 0.1)`
- 圆角: `--radius-lg`
- 阴影: `--shadow-lg`
- 内边距: `--spacing-card`

### 按钮设计
- 主按钮: 蓝色渐变 + 白色文字
- 次按钮: 透明背景 + 蓝色边框
- 危险按钮: 红色渐变 + 白色文字
- 高度: 40px (中等), 32px (小), 48px (大)

### 表格设计
- 表头: 深色背景 + 中等字重
- 行间距: 16px
- 边框: 细线分割
- 悬浮效果: 背景变亮

### 图表设计
- 网格线: 半透明白色
- 坐标轴: 中性灰色
- 数据点: 品牌色系
- 工具提示: 深色背景 + 圆角

## 🔧 实现优先级

### Phase 1 - 基础系统
1. CSS变量定义
2. 基础组件样式
3. 布局系统

### Phase 2 - 高级组件
1. 数据可视化组件
2. 交互动效
3. 响应式适配

### Phase 3 - 优化完善
1. 性能优化
2. 可访问性
3. 主题切换
