/**
 * 快速验证AI舆情监控设置功能
 * 简化版验证脚本，用于快速检查核心功能
 */

const puppeteer = require('puppeteer');

async function quickSettingsVerification() {
  console.log('🚀 开始快速验证AI舆情监控设置功能...');
  
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 导航到页面
    console.log('📍 导航到热点监控页面...');
    await page.goto('http://localhost:3002/hotspot', { waitUntil: 'networkidle2' });
    
    // 2. 验证页面加载
    await page.waitForSelector('h1', { timeout: 10000 });
    const title = await page.$eval('h1', el => el.textContent);
    console.log(`✅ 页面标题: ${title}`);
    
    // 3. 点击设置标签
    console.log('🔄 切换到设置标签...');
    await page.click('button:has-text("设置")');
    await page.waitForTimeout(2000);
    
    // 4. 验证设置内容
    const settingsTitle = await page.$('h3:has-text("AI舆情监控设置")');
    if (settingsTitle) {
      console.log('✅ AI舆情监控设置标题显示正确');
    } else {
      console.log('❌ AI舆情监控设置标题未找到');
    }
    
    // 5. 验证操作按钮
    const resetButton = await page.$('button:has-text("重置")');
    const saveButton = await page.$('button:has-text("保存设置")');
    
    if (resetButton && saveButton) {
      console.log('✅ 重置和保存按钮显示正确');
    } else {
      console.log('❌ 操作按钮缺失');
    }
    
    // 6. 验证设置模块
    const modules = [
      '数据源配置',
      'AI模型配置', 
      '语言配置',
      '监控范围',
      '通知设置'
    ];
    
    for (const module of modules) {
      const moduleElement = await page.$(`h4:has-text("${module}")`);
      if (moduleElement) {
        console.log(`✅ ${module} 模块存在`);
      } else {
        console.log(`❌ ${module} 模块缺失`);
      }
    }
    
    // 7. 验证数据源配置
    console.log('🌐 验证数据源配置...');
    const sources = ['twitter', 'reddit', 'tiktok', 'youtube'];
    let sourceCount = 0;
    
    for (const source of sources) {
      const sourceElement = await page.$(`text=${source}`);
      if (sourceElement) sourceCount++;
    }
    console.log(`✅ 找到 ${sourceCount}/${sources.length} 个数据源`);
    
    // 8. 验证控件数量
    const checkboxes = await page.$$('input[type="checkbox"]');
    const sliders = await page.$$('input[type="range"]');
    const selects = await page.$$('select');
    
    console.log(`✅ 控件统计: ${checkboxes.length} 个开关, ${sliders.length} 个滑块, ${selects.length} 个选择框`);
    
    // 9. 测试交互功能
    console.log('🎯 测试交互功能...');
    
    // 测试保存按钮
    if (saveButton) {
      await saveButton.click();
      await page.waitForTimeout(500);
      console.log('✅ 保存按钮交互正常');
    }
    
    // 测试重置按钮
    if (resetButton) {
      await resetButton.click();
      await page.waitForTimeout(500);
      console.log('✅ 重置按钮交互正常');
    }
    
    // 测试数据源开关
    if (checkboxes.length > 0) {
      await checkboxes[0].click();
      await page.waitForTimeout(300);
      console.log('✅ 数据源开关交互正常');
    }
    
    // 10. 验证语言配置
    console.log('🌍 验证语言配置...');
    const languages = ['英语', '中文', '韩语', '日语'];
    let languageCount = 0;
    
    for (const lang of languages) {
      const langElement = await page.$(`text=${lang}`);
      if (langElement) languageCount++;
    }
    console.log(`✅ 找到 ${languageCount}/${languages.length} 种语言选项`);
    
    // 11. 验证关键词管理
    console.log('🎯 验证关键词管理...');
    const keywordInput = await page.$('input[placeholder="添加关键词..."]');
    const keywordTags = await page.$$('.bg-blue-500\\/20');
    
    if (keywordInput) {
      console.log('✅ 关键词输入框存在');
    }
    console.log(`✅ 找到 ${keywordTags.length} 个关键词标签`);
    
    // 12. 验证通知设置
    console.log('🔔 验证通知设置...');
    const notifications = ['热点警报', '预测更新', '策略就绪'];
    let notificationCount = 0;
    
    for (const notification of notifications) {
      const notifElement = await page.$(`span:has-text("${notification}")`);
      if (notifElement) notificationCount++;
    }
    console.log(`✅ 找到 ${notificationCount}/${notifications.length} 个通知选项`);
    
    // 13. 截图保存
    await page.screenshot({ 
      path: 'ai_settings_verification.png',
      fullPage: true 
    });
    console.log('📸 验证截图已保存: ai_settings_verification.png');
    
    console.log('\n🎉 快速验证完成! AI舆情监控设置功能基本正常');
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  } finally {
    await browser.close();
  }
}

// 运行快速验证
quickSettingsVerification().catch(console.error);
