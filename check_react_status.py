#!/usr/bin/env python3
"""
React前端状态检查脚本
检查React应用是否正常运行
"""

import requests
import time
import webbrowser
from pathlib import Path

def check_react_status():
    """检查React前端状态"""
    print("🔍 检查React前端状态...")
    
    react_urls = [
        "http://localhost:3002/",
        "http://localhost:3002/test",
        "http://localhost:3002/dashboard"
    ]
    
    results = {}
    
    for url in react_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                results[url] = "✅ 正常"
            else:
                results[url] = f"❌ HTTP {response.status_code}"
        except requests.exceptions.ConnectionError:
            results[url] = "❌ 连接失败"
        except requests.exceptions.Timeout:
            results[url] = "⏰ 请求超时"
        except Exception as e:
            results[url] = f"❌ 错误: {str(e)}"
    
    return results

def check_backend_status():
    """检查后端API状态"""
    print("🔍 检查后端API状态...")
    
    api_urls = [
        "http://localhost:8001/health",
        "http://localhost:8001/docs",
        "http://localhost:8001/api/system/info"
    ]
    
    results = {}
    
    for url in api_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                results[url] = "✅ 正常"
            else:
                results[url] = f"❌ HTTP {response.status_code}"
        except requests.exceptions.ConnectionError:
            results[url] = "❌ 连接失败"
        except requests.exceptions.Timeout:
            results[url] = "⏰ 请求超时"
        except Exception as e:
            results[url] = f"❌ 错误: {str(e)}"
    
    return results

def main():
    print("🎯 MemeMaster AI React前端状态检查")
    print("=" * 50)
    
    # 检查React前端
    react_results = check_react_status()
    print("\n📱 React前端状态:")
    for url, status in react_results.items():
        print(f"  {url} - {status}")
    
    # 检查后端API
    backend_results = check_backend_status()
    print("\n⚡ 后端API状态:")
    for url, status in backend_results.items():
        print(f"  {url} - {status}")
    
    # 总结
    print("\n" + "=" * 50)
    react_working = all("✅" in status for status in react_results.values())
    backend_working = any("✅" in status for status in backend_results.values())
    
    if react_working:
        print("🎉 React前端工作正常！")
        print("📱 推荐访问页面:")
        print("   • 主页: http://localhost:3002/")
        print("   • 测试页: http://localhost:3002/test")
        print("   • 仪表盘: http://localhost:3002/dashboard")
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:3002/test")
            print("🌐 已自动打开测试页面")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问")
            
    else:
        print("❌ React前端存在问题")
        print("🔧 建议解决方案:")
        print("   1. 检查npm run frontend:dev是否正常运行")
        print("   2. 检查端口3002是否被占用")
        print("   3. 查看控制台错误信息")
        print("   4. 尝试重启前端服务")
    
    if backend_working:
        print("✅ 后端API工作正常")
    else:
        print("❌ 后端API存在问题")
        print("🔧 建议启动后端: python3 working_server.py")
    
    print("\n📊 HTML仪表盘备用方案:")
    print("   • 简化版: http://localhost:8090/simple_dashboard.html")
    print("   • 完整版: http://localhost:8090/dashboard.html")
    print("   • 状态页: http://localhost:8090/system_status.html")

if __name__ == "__main__":
    main()
