# MemeMaster AI - Python Dependencies
# 核心Web框架 (必需)
fastapi>=0.100.0
uvicorn[standard]>=0.22.0
pydantic>=2.0.0

# 基础工具库 (必需)
python-dotenv>=1.0.0
requests>=2.31.0
aiohttp>=3.8.0
click>=8.1.0

# 数据处理 (核心功能)
pandas>=2.0.0
numpy>=1.24.0

# AI和机器学习 (可选 - 按需安装)
# torch>=2.0.0
# torchvision>=0.15.0
# torchaudio>=2.0.0
# transformers>=4.30.0
# xgboost>=1.7.0
# scikit-learn>=1.3.0

# 区块链相关 (可选 - 按需安装)
# web3>=6.5.0
# eth-account>=0.8.0
# solana>=0.30.0
# py-solc-x>=1.12.0

# 网络爬虫 (可选 - 按需安装)
# beautifulsoup4>=4.12.0
# selenium>=4.10.0

# 数据库 (可选 - 按需安装)
# redis>=4.5.0
# pymongo>=4.4.0
# psycopg2-binary>=2.9.0
# sqlalchemy>=2.0.0

# 任务队列 (可选)
# celery>=5.3.0

# 监控和日志 (可选)
# prometheus-client>=0.17.0
# structlog>=23.1.0
# sentry-sdk>=1.28.0

# 安全和加密 (可选)
# cryptography>=41.0.0
# pycryptodome>=3.18.0
# python-jose>=3.3.0
# pyjwt>=2.8.0
# bcrypt>=4.0.0
# passlib>=1.7.4

# HTTP客户端 (可选)
# httpx>=0.24.0
# tenacity>=8.2.0

# 测试框架 (开发环境)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# pytest-cov>=4.1.0
# pytest-mock>=3.11.0

# 开发工具 (开发环境)
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.4.0
# pre-commit>=3.3.0

# 注意：
# 1. 注释掉的包可以根据实际需要安装
# 2. 建议使用虚拟环境管理依赖
# 3. 生产环境只安装必需的包以减少体积和安全风险
