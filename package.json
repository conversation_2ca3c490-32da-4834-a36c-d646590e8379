{"name": "mememaster-ai", "version": "2.0.0", "type": "module", "description": "AI-powered meme token creation and trading system", "main": "src/app.js", "scripts": {"start": "python web_app.py", "frontend:dev": "vite --host 0.0.0.0 --port 3000", "frontend:build": "vite build", "frontend:preview": "vite preview", "dev": "python optimized_start.py", "status": "python optimized_start.py status", "stop": "python optimized_start.py stop", "test": "jest", "lint": "eslint frontend/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint frontend/ --ext .js,.jsx,.ts,.tsx --fix", "clean": "rm -rf dist node_modules/.vite", "deps:install": "npm install && pip install -r requirements.txt"}, "dependencies": {"@reduxjs/toolkit": "^1.9.5", "@types/node": "^24.0.1", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "chart.js": "^4.4.9", "clsx": "^2.0.0", "date-fns": "^2.30.0", "ethers": "^6.14.3", "lucide-react": "^0.263.1", "postcss": "^8.4.24", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-redux": "^8.1.1", "react-router-dom": "^6.14.0", "recharts": "^2.7.2", "socket.io-client": "^4.8.1", "tailwindcss": "^3.3.3", "typescript": "^5.8.3", "zustand": "^5.0.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.27.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.5.0", "nodemon": "^2.0.0", "vite": "^4.4.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/mememaster-ai.git"}, "keywords": ["meme", "token", "ai", "blockchain", "ethereum", "solana", "defi", "trading"], "author": "MemeMaster Dev Team", "license": "MIT"}