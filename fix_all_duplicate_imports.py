#!/usr/bin/env python3
"""
一次性修复所有重复的图标导入问题
"""

import os
import re
from pathlib import Path

def fix_duplicate_imports_in_file(file_path):
    """修复单个文件中的重复导入"""
    print(f"🔍 检查文件: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 查找lucide-react导入语句
        import_pattern = r"import\s*\{\s*([^}]+)\s*\}\s*from\s*['\"]lucide-react['\"]"
        match = re.search(import_pattern, content, re.MULTILINE | re.DOTALL)
        
        if not match:
            print(f"   ✅ 无lucide-react导入")
            return False
        
        # 提取导入的图标列表
        imports_str = match.group(1)
        
        # 分割并清理图标名称
        icons = []
        for line in imports_str.split('\n'):
            line = line.strip()
            if line:
                # 移除注释
                line = re.sub(r'//.*$', '', line)
                # 分割逗号分隔的图标
                for icon in line.split(','):
                    icon = icon.strip()
                    if icon and not icon.startswith('//'):
                        icons.append(icon)
        
        # 去重并保持顺序
        unique_icons = []
        seen = set()
        for icon in icons:
            if icon not in seen:
                unique_icons.append(icon)
                seen.add(icon)
            else:
                print(f"   🔧 发现重复图标: {icon}")
        
        if len(unique_icons) != len(icons):
            # 重新构建导入语句
            new_imports = ',\n  '.join(unique_icons)
            new_import_statement = f"import {{\n  {new_imports}\n}} from 'lucide-react'"
            
            # 替换原来的导入语句
            new_content = re.sub(import_pattern, new_import_statement, content, flags=re.MULTILINE | re.DOTALL)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"   ✅ 修复了 {len(icons) - len(unique_icons)} 个重复导入")
            return True
        else:
            print(f"   ✅ 无重复导入")
            return False
            
    except Exception as e:
        print(f"   ❌ 处理文件失败: {e}")
        return False

def find_jsx_files():
    """查找所有JSX文件"""
    jsx_files = []
    for root, dirs, files in os.walk('frontend'):
        for file in files:
            if file.endswith(('.jsx', '.js')):
                jsx_files.append(os.path.join(root, file))
    return jsx_files

def main():
    print("🔧 修复重复图标导入工具")
    print("=" * 50)
    
    # 切换到项目目录
    project_root = Path(__file__).parent.absolute()
    os.chdir(project_root)
    
    # 查找所有JSX文件
    jsx_files = find_jsx_files()
    print(f"📁 找到 {len(jsx_files)} 个JSX文件")
    
    # 修复每个文件
    fixed_files = []
    for file_path in jsx_files:
        if fix_duplicate_imports_in_file(file_path):
            fixed_files.append(file_path)
    
    print("\n" + "=" * 50)
    print("📋 修复结果")
    print("=" * 50)
    
    if fixed_files:
        print(f"✅ 修复了 {len(fixed_files)} 个文件:")
        for file_path in fixed_files:
            print(f"   • {file_path}")
    else:
        print("✅ 所有文件都正常，无需修复")
    
    print(f"\n💡 建议:")
    print("   1. 重新加载React应用")
    print("   2. 检查浏览器控制台")

if __name__ == "__main__":
    main()
