<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI - 专业数据分析平台</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="frontend/styles/mememaster-design-system.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <aside class="sidebar" id="sidebar">
            <!-- 品牌标识区 -->
            <div class="logo">
                <i class="fas fa-robot"></i>
                <h1>MemeMaster AI</h1>
            </div>
            
            <!-- 主导航菜单 -->
            <nav>
                <a href="#" class="nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>仪表盘</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-chart-bar"></i>
                    <span>市场分析</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-brain"></i>
                    <span>交易策略</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-cogs"></i>
                    <span>自动化交易</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-credit-card"></i>
                    <span>支付与订阅</span>
                </a>
                
                <div class="divider"></div>
                
                <a href="#" class="nav-item">
                    <i class="fas fa-users"></i>
                    <span>用户管理</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>安全设置</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
            </nav>
            
            <!-- 用户信息区 -->
            <div class="user-info">
                <div class="plan">PRO会员</div>
                <div class="info">
                    <div style="font-weight: 600; margin-bottom: 4px;">John Doe</div>
                    <div style="font-size: 12px; opacity: 0.7;"><EMAIL></div>
                </div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部功能区 -->
            <header class="header">
                <div class="search-bar">
                    <i class="fas fa-search" style="color: var(--text-tertiary); margin-right: 8px;"></i>
                    <input type="text" placeholder="搜索代币、策略或数据...">
                </div>
                
                <div class="header-actions">
                    <div class="notification">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </header>

            <!-- 仪表盘主体 -->
            <div class="dashboard-content">
                <!-- 欢迎横幅 -->
                <div class="welcome-banner animate-fade-in">
                    <div class="welcome-text">
                        <h2>欢迎回来，John! 🚀</h2>
                        <p>今日发现 <strong>12个</strong> 新交易机会，市场情绪指数 <strong style="color: #10b981;">78.5</strong>，建议关注DeFi和GameFi板块</p>
                    </div>
                    <div class="welcome-actions">
                        <button class="btn btn-primary btn-lg">
                            <i class="fas fa-eye"></i>
                            查看交易机会
                        </button>
                    </div>
                </div>

                <!-- 统计卡片区 -->
                <div class="stats-container">
                    <!-- AI分析使用卡片 -->
                    <div class="stat-card animate-slide-in-left delay-100">
                        <div class="stat-header">
                            <div class="stat-icon icon-ai">
                                <i class="fas fa-brain"></i>
                            </div>
                        </div>
                        <div class="stat-value">42/∞</div>
                        <div class="stat-title">AI分析使用</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+12% 本周使用率</span>
                        </div>
                    </div>

                    <!-- 活跃策略卡片 -->
                    <div class="stat-card animate-slide-in-left delay-200">
                        <div class="stat-header">
                            <div class="stat-icon icon-trade">
                                <i class="fas fa-robot"></i>
                            </div>
                        </div>
                        <div class="stat-value">7</div>
                        <div class="stat-title">活跃策略</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-plus"></i>
                            <span>今日收益 +3.2 SOL</span>
                        </div>
                    </div>

                    <!-- 订阅状态卡片 -->
                    <div class="stat-card animate-slide-in-left delay-300">
                        <div class="stat-header">
                            <div class="stat-icon icon-sub">
                                <i class="fas fa-crown"></i>
                            </div>
                        </div>
                        <div class="stat-value">PRO</div>
                        <div class="stat-title">订阅状态</div>
                        <div class="stat-trend trend-neutral">
                            <i class="fas fa-calendar"></i>
                            <span>到期 2023-12-15</span>
                        </div>
                    </div>

                    <!-- 钱包余额卡片 -->
                    <div class="stat-card animate-slide-in-left delay-400">
                        <div class="stat-header">
                            <div class="stat-icon icon-wallet">
                                <i class="fas fa-wallet"></i>
                            </div>
                        </div>
                        <div class="stat-value">48.75</div>
                        <div class="stat-title">钱包余额 (SOL)</div>
                        <div class="stat-trend trend-up">
                            <i class="fas fa-dollar-sign"></i>
                            <span>≈$1,250 USD</span>
                        </div>
                    </div>
                </div>

                <!-- 图表分析区 -->
                <div class="charts-container">
                    <!-- 市场情绪与交易趋势图表 -->
                    <div class="chart-card animate-scale-in delay-500">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-area"></i>
                                市场情绪与交易趋势
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-outline">7天</button>
                                <button class="btn btn-sm btn-primary">30天</button>
                                <button class="btn btn-sm btn-outline">90天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>

                    <!-- 热门Meme币列表 -->
                    <div class="chart-card animate-scale-in delay-600">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-fire"></i>
                                热门Meme币
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-outline">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看更多
                                </button>
                            </div>
                        </div>
                        <div class="hot-coins">
                            <div class="hot-coin">
                                <div class="coin-icon">DOGE</div>
                                <div class="coin-info">
                                    <div class="coin-name">Dogecoin</div>
                                    <div class="coin-symbol">DOGE</div>
                                </div>
                                <div class="coin-change change-up">+12.4%</div>
                            </div>
                            <div class="hot-coin">
                                <div class="coin-icon">SHIB</div>
                                <div class="coin-info">
                                    <div class="coin-name">Shiba Inu</div>
                                    <div class="coin-symbol">SHIB</div>
                                </div>
                                <div class="coin-change change-up">+8.2%</div>
                            </div>
                            <div class="hot-coin">
                                <div class="coin-icon">PEPE</div>
                                <div class="coin-info">
                                    <div class="coin-name">Pepe Coin</div>
                                    <div class="coin-symbol">PEPE</div>
                                </div>
                                <div class="coin-change change-down">-3.1%</div>
                            </div>
                            <div class="hot-coin">
                                <div class="coin-icon">FLOKI</div>
                                <div class="coin-info">
                                    <div class="coin-name">Floki Inu</div>
                                    <div class="coin-symbol">FLOKI</div>
                                </div>
                                <div class="coin-change change-up">+5.7%</div>
                            </div>
                            <div class="hot-coin">
                                <div class="coin-icon">BONK</div>
                                <div class="coin-info">
                                    <div class="coin-name">Bonk</div>
                                    <div class="coin-symbol">BONK</div>
                                </div>
                                <div class="coin-change change-up">+21.8%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 核心功能模块 -->
                <div class="features-grid">
                    <!-- 热点检测功能卡 -->
                    <div class="feature-card animate-fade-in delay-700">
                        <div class="feature-icon">
                            <i class="fas fa-fire"></i>
                        </div>
                        <div class="feature-title">热点检测</div>
                        <div class="feature-desc">实时监测社交媒体趋势，捕捉病毒式传播的Meme币机会</div>
                        <button class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            立即扫描
                        </button>
                    </div>

                    <!-- 策略生成功能卡 -->
                    <div class="feature-card animate-fade-in delay-800">
                        <div class="feature-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="feature-title">策略生成</div>
                        <div class="feature-desc">AI驱动的交易策略生成，基于市场数据和情绪分析</div>
                        <button class="btn btn-secondary">
                            <i class="fas fa-plus"></i>
                            创建策略
                        </button>
                    </div>

                    <!-- 一键部署功能卡 -->
                    <div class="feature-card animate-fade-in delay-900">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="feature-title">一键部署</div>
                        <div class="feature-desc">自动化部署代币到pump.fun和RAYDIUM平台</div>
                        <button class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            立即部署
                        </button>
                    </div>

                    <!-- 流动性管理功能卡 -->
                    <div class="feature-card animate-fade-in delay-1000">
                        <div class="feature-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="feature-title">流动性管理</div>
                        <div class="feature-desc">智能管理资金池，优化流动性配置和风险控制</div>
                        <button class="btn btn-outline">
                            <i class="fas fa-cogs"></i>
                            管理流动性
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 图表初始化
        const ctx = document.getElementById('trendChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                datasets: [
                    {
                        label: '市场情绪指数',
                        data: [65, 72, 68, 78, 75, 82],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '交易量 (百万)',
                        data: [28, 35, 42, 38, 45, 52],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '策略成功率 (%)',
                        data: [82, 85, 78, 88, 92, 87],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#f1f5f9',
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(241, 245, 249, 0.1)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(241, 245, 249, 0.1)'
                        },
                        ticks: {
                            color: '#94a3b8'
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        // 实时数据更新模拟
        setInterval(() => {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(value => {
                value.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    value.style.transform = 'scale(1)';
                }, 200);
            });
        }, 10000);

        // 添加页面加载完成后的动画
        document.addEventListener('DOMContentLoaded', () => {
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>
