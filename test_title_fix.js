// 自动化验证页面标题修复的脚本
// 使用Puppeteer检查页面标题是否正确显示为水平方向

import puppeteer from 'puppeteer';

async function testTitleFix() {
    console.log('🔍 开始验证页面标题修复...');
    
    const browser = await puppeteer.launch({
        headless: false, // 显示浏览器以便观察
        defaultViewport: { width: 1920, height: 1080 }
    });
    
    try {
        const page = await browser.newPage();
        
        // 访问策略管理页面
        console.log('📱 访问策略管理页面...');
        await page.goto('http://localhost:8001/strategy', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查标题元素
        console.log('🔍 检查页面标题...');
        
        const titleElement = await page.$('h1');
        if (!titleElement) {
            console.log('❌ 未找到h1标题元素');
            return false;
        }
        
        // 获取标题的计算样式
        const titleStyles = await page.evaluate((element) => {
            const computedStyle = window.getComputedStyle(element);
            return {
                writingMode: computedStyle.writingMode,
                textOrientation: computedStyle.textOrientation,
                direction: computedStyle.direction,
                display: computedStyle.display,
                width: computedStyle.width,
                height: computedStyle.height,
                fontSize: computedStyle.fontSize,
                lineHeight: computedStyle.lineHeight
            };
        }, titleElement);
        
        console.log('📊 标题样式信息:', titleStyles);
        
        // 获取标题文本内容
        const titleText = await page.evaluate((element) => {
            return element.textContent;
        }, titleElement);
        
        console.log('📝 标题文本:', titleText);
        
        // 获取标题的边界框信息
        const boundingBox = await titleElement.boundingBox();
        console.log('📐 标题边界框:', boundingBox);
        
        // 验证标题是否水平显示
        const isHorizontal = titleStyles.writingMode === 'horizontal-tb' && 
                           boundingBox.width > boundingBox.height;
        
        if (isHorizontal) {
            console.log('✅ 标题正确显示为水平方向');
        } else {
            console.log('❌ 标题仍然显示为垂直方向');
            console.log('🔧 需要进一步修复');
        }
        
        // 截图保存验证结果
        await page.screenshot({ 
            path: 'title_fix_verification.png',
            fullPage: true 
        });
        console.log('📸 验证截图已保存: title_fix_verification.png');
        
        // 检查其他页面
        console.log('🔍 检查热点监控页面...');
        await page.goto('http://localhost:8001/hotspot', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const hotspotTitle = await page.$('h1');
        if (hotspotTitle) {
            const hotspotTitleText = await page.evaluate((element) => {
                return element.textContent;
            }, hotspotTitle);
            console.log('📝 热点页面标题:', hotspotTitleText);
            
            const hotspotBoundingBox = await hotspotTitle.boundingBox();
            const hotspotIsHorizontal = hotspotBoundingBox.width > hotspotBoundingBox.height;
            
            if (hotspotIsHorizontal) {
                console.log('✅ 热点页面标题正确显示为水平方向');
            } else {
                console.log('❌ 热点页面标题仍然显示为垂直方向');
            }
        }
        
        return isHorizontal;
        
    } catch (error) {
        console.error('❌ 验证过程中出现错误:', error);
        return false;
    } finally {
        await browser.close();
    }
}

// 运行验证
testTitleFix().then(success => {
    if (success) {
        console.log('🎉 页面标题修复验证成功！');
        process.exit(0);
    } else {
        console.log('💥 页面标题修复验证失败，需要进一步调试');
        process.exit(1);
    }
}).catch(error => {
    console.error('💥 验证脚本执行失败:', error);
    process.exit(1);
});
