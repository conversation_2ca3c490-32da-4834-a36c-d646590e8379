#!/usr/bin/env python3
"""
MemeMaster AI 系统守护进程
确保React前端和后端服务持续运行，即使在新的对话线程中也能正常工作
"""

import os
import sys
import time
import json
import psutil
import subprocess
import signal
from datetime import datetime
from pathlib import Path

class MemeMasterDaemon:
    """MemeMaster AI 守护进程"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.daemon_file = self.project_root / '.daemon_status.json'
        self.log_file = self.project_root / 'daemon.log'
        self.running = True
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        # 写入日志文件
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message)
        except:
            pass
        
        # 同时输出到控制台
        print(f"[{timestamp}] {message}")
    
    def check_port(self, port):
        """检查端口是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                    return True
        except:
            pass
        return False
    
    def get_process_on_port(self, port):
        """获取占用端口的进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    for conn in proc.info['connections'] or []:
                        if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                            return proc
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except:
            pass
        return None
    
    def ensure_backend_running(self):
        """确保后端服务运行"""
        if not self.check_port(8001):
            self.log("🚀 启动后端服务...")
            try:
                backend_script = self.project_root / 'working_server.py'
                if not backend_script.exists():
                    self.log("❌ working_server.py 不存在")
                    return False
                
                # 启动后端进程
                process = subprocess.Popen([
                    sys.executable, str(backend_script)
                ], cwd=str(self.project_root), 
                   stdout=subprocess.DEVNULL, 
                   stderr=subprocess.DEVNULL,
                   start_new_session=True)
                
                # 等待启动
                for i in range(10):
                    time.sleep(1)
                    if self.check_port(8001):
                        self.log(f"✅ 后端服务启动成功 (PID: {process.pid})")
                        return True
                
                self.log("❌ 后端服务启动超时")
                return False
                
            except Exception as e:
                self.log(f"❌ 后端启动异常: {e}")
                return False
        else:
            return True
    
    def ensure_frontend_running(self):
        """确保前端服务运行"""
        if not self.check_port(3000):
            self.log("🎨 启动React前端...")
            try:
                # 检查package.json是否存在
                package_json = self.project_root / 'package.json'
                if not package_json.exists():
                    self.log("❌ package.json 不存在")
                    return False
                
                # 检查node_modules
                node_modules = self.project_root / 'node_modules'
                if not node_modules.exists():
                    self.log("📦 安装Node.js依赖...")
                    subprocess.run(['npm', 'install'], 
                                 cwd=str(self.project_root), 
                                 check=True,
                                 stdout=subprocess.DEVNULL, 
                                 stderr=subprocess.DEVNULL)
                
                # 启动前端进程
                process = subprocess.Popen([
                    'npm', 'run', 'frontend:dev'
                ], cwd=str(self.project_root),
                   stdout=subprocess.DEVNULL, 
                   stderr=subprocess.DEVNULL,
                   start_new_session=True)
                
                # 等待启动
                for i in range(15):
                    time.sleep(1)
                    if self.check_port(3000):
                        self.log(f"✅ React前端启动成功 (PID: {process.pid})")
                        return True
                
                self.log("❌ 前端服务启动超时")
                return False
                
            except Exception as e:
                self.log(f"❌ 前端启动异常: {e}")
                return False
        else:
            return True
    
    def save_status(self):
        """保存守护进程状态"""
        try:
            status = {
                'daemon_pid': os.getpid(),
                'last_check': datetime.now().isoformat(),
                'backend_running': self.check_port(8001),
                'frontend_running': self.check_port(3000),
                'backend_pid': None,
                'frontend_pid': None
            }
            
            # 获取进程PID
            backend_proc = self.get_process_on_port(8001)
            if backend_proc:
                status['backend_pid'] = backend_proc.pid
            
            frontend_proc = self.get_process_on_port(3000)
            if frontend_proc:
                status['frontend_pid'] = frontend_proc.pid
            
            with open(self.daemon_file, 'w') as f:
                json.dump(status, f, indent=2)
                
        except Exception as e:
            self.log(f"保存状态失败: {e}")
    
    def cleanup_old_processes(self):
        """清理旧的进程"""
        try:
            if self.daemon_file.exists():
                with open(self.daemon_file, 'r') as f:
                    old_status = json.load(f)
                
                # 检查旧的守护进程
                old_daemon_pid = old_status.get('daemon_pid')
                if old_daemon_pid and old_daemon_pid != os.getpid():
                    try:
                        old_proc = psutil.Process(old_daemon_pid)
                        self.log(f"🔄 终止旧的守护进程 PID:{old_daemon_pid}")
                        old_proc.terminate()
                        old_proc.wait(timeout=3)
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                
        except Exception as e:
            self.log(f"清理旧进程失败: {e}")
    
    def run_daemon(self):
        """运行守护进程"""
        self.log("🚀 MemeMaster AI 守护进程启动")
        self.log(f"📁 项目目录: {self.project_root}")
        self.log(f"🆔 守护进程 PID: {os.getpid()}")
        
        # 清理旧进程
        self.cleanup_old_processes()
        
        check_count = 0
        while self.running:
            try:
                check_count += 1
                
                # 每10次检查输出一次状态
                if check_count % 10 == 1:
                    self.log("🔍 检查服务状态...")
                
                # 确保后端运行
                backend_ok = self.ensure_backend_running()
                
                # 确保前端运行
                frontend_ok = self.ensure_frontend_running()
                
                # 保存状态
                self.save_status()
                
                # 每10次检查输出一次状态
                if check_count % 10 == 0:
                    backend_status = "✅" if backend_ok else "❌"
                    frontend_status = "✅" if frontend_ok else "❌"
                    self.log(f"📊 状态检查 #{check_count}: 后端{backend_status} 前端{frontend_status}")
                
                # 等待30秒再次检查
                time.sleep(30)
                
            except KeyboardInterrupt:
                self.log("收到中断信号，正在退出...")
                break
            except Exception as e:
                self.log(f"守护进程运行错误: {e}")
                time.sleep(10)
        
        self.log("🛑 守护进程已停止")
    
    def stop_daemon(self):
        """停止守护进程"""
        self.running = False
        
        # 清理状态文件
        if self.daemon_file.exists():
            self.daemon_file.unlink()

def get_daemon_status():
    """获取守护进程状态"""
    daemon_file = Path(__file__).parent / '.daemon_status.json'
    
    if not daemon_file.exists():
        return None
    
    try:
        with open(daemon_file, 'r') as f:
            status = json.load(f)
        
        # 检查守护进程是否还在运行
        daemon_pid = status.get('daemon_pid')
        if daemon_pid:
            try:
                proc = psutil.Process(daemon_pid)
                if proc.is_running():
                    return status
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # 守护进程已停止，删除状态文件
        daemon_file.unlink()
        return None
        
    except Exception:
        return None

def start_daemon():
    """启动守护进程"""
    # 检查是否已有守护进程运行
    status = get_daemon_status()
    if status:
        print(f"✅ 守护进程已在运行 (PID: {status['daemon_pid']})")
        print(f"📊 后端状态: {'运行' if status['backend_running'] else '停止'}")
        print(f"📊 前端状态: {'运行' if status['frontend_running'] else '停止'}")
        return
    
    print("🚀 启动MemeMaster AI守护进程...")
    
    # 启动守护进程
    daemon = MemeMasterDaemon()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在停止守护进程...")
        daemon.stop_daemon()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        daemon.run_daemon()
    except Exception as e:
        print(f"守护进程运行失败: {e}")
        sys.exit(1)

def stop_daemon():
    """停止守护进程"""
    status = get_daemon_status()
    if not status:
        print("❌ 没有运行中的守护进程")
        return
    
    daemon_pid = status['daemon_pid']
    try:
        proc = psutil.Process(daemon_pid)
        print(f"🛑 停止守护进程 (PID: {daemon_pid})")
        proc.terminate()
        proc.wait(timeout=5)
        print("✅ 守护进程已停止")
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        print("❌ 无法停止守护进程")
    except psutil.TimeoutExpired:
        print("⚠️ 强制终止守护进程")
        proc.kill()

def show_status():
    """显示系统状态"""
    status = get_daemon_status()
    
    print("📊 MemeMaster AI 系统状态")
    print("="*40)
    
    if status:
        print(f"🤖 守护进程: 运行中 (PID: {status['daemon_pid']})")
        print(f"📡 后端服务: {'✅ 运行' if status['backend_running'] else '❌ 停止'} (端口8001)")
        print(f"🎨 前端服务: {'✅ 运行' if status['frontend_running'] else '❌ 停止'} (端口3000)")
        print(f"⏰ 最后检查: {status['last_check']}")
        
        if status['backend_running'] and status['frontend_running']:
            print("\n🌐 访问地址:")
            print("   • React前端: http://localhost:3000/")
            print("   • 后端API: http://localhost:8001/")
    else:
        print("❌ 守护进程未运行")
        print("💡 使用 'python3 system_daemon.py start' 启动")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_status()
        return
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        start_daemon()
    elif command == 'stop':
        stop_daemon()
    elif command == 'status':
        show_status()
    elif command == 'restart':
        stop_daemon()
        time.sleep(2)
        start_daemon()
    else:
        print("用法: python3 system_daemon.py [start|stop|status|restart]")

if __name__ == "__main__":
    main()
