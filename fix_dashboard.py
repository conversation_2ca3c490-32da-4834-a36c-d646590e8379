#!/usr/bin/env python3
"""
MemeMaster AI 仪表盘修复脚本
检查并修复前端页面服务问题，确保本地和服务器文件一致性
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path

def check_and_fix_package_json():
    """检查并修复 package.json"""
    print("🔧 检查 package.json...")
    
    try:
        with open('package.json', 'r') as f:
            package_data = json.load(f)
        
        # 检查脚本是否存在
        scripts = package_data.get('scripts', {})
        if 'frontend:dev' not in scripts:
            print("   ❌ 缺失 frontend:dev 脚本")
            scripts['frontend:dev'] = 'vite'
            scripts['frontend:build'] = 'vite build'
            scripts['frontend:preview'] = 'vite preview'
            package_data['scripts'] = scripts
            
            # 保存修复后的 package.json
            with open('package.json', 'w') as f:
                json.dump(package_data, f, indent=2)
            print("   ✅ 已修复 package.json 脚本")
        else:
            print("   ✅ package.json 脚本正常")
        
        return True
        
    except Exception as e:
        print(f"   ❌ package.json 错误: {e}")
        return False

def check_vite_config():
    """检查 vite.config.js"""
    print("🔧 检查 vite.config.js...")
    
    if not Path('vite.config.js').exists():
        print("   ❌ vite.config.js 不存在，正在创建...")
        
        vite_config = '''import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true
  }
})'''
        
        with open('vite.config.js', 'w') as f:
            f.write(vite_config)
        print("   ✅ 已创建 vite.config.js")
    else:
        print("   ✅ vite.config.js 存在")
    
    return True

def check_frontend_structure():
    """检查前端目录结构"""
    print("🔧 检查前端目录结构...")
    
    required_dirs = [
        'frontend',
        'frontend/components',
        'frontend/pages',
        'frontend/store',
        'frontend/store/slices',
        'frontend/services',
        'frontend/layouts'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    if missing_dirs:
        print(f"   ✅ 已创建缺失目录: {missing_dirs}")
    else:
        print("   ✅ 前端目录结构完整")
    
    return True

def check_index_html():
    """检查 index.html"""
    print("🔧 检查 index.html...")
    
    if not Path('index.html').exists():
        print("   ❌ index.html 不存在，正在创建...")
        
        index_html = '''<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MemeMaster AI - 智能Meme币交易仪表盘</title>
    <meta name="description" content="MemeMaster AI - 下一代智能Meme币分析与交易平台" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/frontend/main.jsx"></script>
  </body>
</html>'''
        
        with open('index.html', 'w') as f:
            f.write(index_html)
        print("   ✅ 已创建 index.html")
    else:
        print("   ✅ index.html 存在")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    
    if not Path('node_modules').exists():
        print("   📦 安装 Node.js 依赖...")
        try:
            subprocess.run(['npm', 'install', '--legacy-peer-deps'], check=True)
            print("   ✅ Node.js 依赖安装完成")
        except subprocess.CalledProcessError:
            print("   ❌ Node.js 依赖安装失败")
            return False
    else:
        print("   ✅ Node.js 依赖已存在")
    
    return True

def check_services():
    """检查服务状态"""
    print("🌐 检查服务状态...")
    
    # 检查后端服务
    try:
        import requests
        response = requests.get('http://localhost:8000/health', timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端服务 (8000) 正常")
        else:
            print("   ⚠️  后端服务响应异常")
    except:
        print("   ❌ 后端服务 (8000) 未运行")
    
    # 检查前端服务
    try:
        import requests
        response = requests.get('http://localhost:5173/', timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务 (5173) 正常")
        else:
            print("   ⚠️  前端服务响应异常")
    except:
        print("   ❌ 前端服务 (5173) 未运行")
    
    # 检查备用前端服务
    try:
        import requests
        response = requests.get('http://localhost:3000/', timeout=5)
        if response.status_code == 200:
            print("   ✅ 备用前端服务 (3000) 正常")
        else:
            print("   ⚠️  备用前端服务响应异常")
    except:
        print("   ❌ 备用前端服务 (3000) 未运行")

def create_simple_frontend():
    """创建简单的前端入口文件"""
    print("🎨 创建简单前端入口...")
    
    # 创建 main.jsx
    main_jsx = '''import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'

function App() {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e293b, #7c3aed, #1e293b)',
      color: 'white',
      fontFamily: 'Arial, sans-serif',
      padding: '20px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <header style={{
          textAlign: 'center',
          marginBottom: '30px',
          padding: '20px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '15px',
          backdropFilter: 'blur(10px)'
        }}>
          <h1 style={{ fontSize: '2.5rem', marginBottom: '10px' }}>
            🚀 MemeMaster AI 仪表盘
          </h1>
          <p style={{ opacity: 0.8 }}>智能Meme币交易系统 - 实时监控与控制</p>
        </header>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '15px',
            padding: '20px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)'
          }}>
            <h3 style={{ color: '#60a5fa', marginBottom: '15px' }}>📊 系统状态</h3>
            <div style={{ marginBottom: '10px' }}>
              <span>运行状态: </span>
              <span style={{ color: '#34d399' }}>正常</span>
            </div>
            <div style={{ marginBottom: '10px' }}>
              <span>活跃策略: </span>
              <span style={{ color: '#34d399' }}>3</span>
            </div>
            <div>
              <span>钱包数量: </span>
              <span style={{ color: '#34d399' }}>25</span>
            </div>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '15px',
            padding: '20px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)'
          }}>
            <h3 style={{ color: '#60a5fa', marginBottom: '15px' }}>📈 舆情监控</h3>
            <p>实时监控社交媒体热点趋势</p>
            <div style={{ marginTop: '15px' }}>
              <div style={{ marginBottom: '5px' }}>政治: 45%</div>
              <div style={{ marginBottom: '5px' }}>亚文化: 30%</div>
              <div style={{ marginBottom: '5px' }}>科技: 15%</div>
              <div>体育: 10%</div>
            </div>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '15px',
            padding: '20px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)'
          }}>
            <h3 style={{ color: '#60a5fa', marginBottom: '15px' }}>🎯 策略管理</h3>
            <p>AI驱动的代币策略生成与管理</p>
            <button style={{
              background: '#3b82f6',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '8px',
              cursor: 'pointer',
              marginTop: '15px'
            }}>
              查看策略
            </button>
          </div>
          
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '15px',
            padding: '20px',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)'
          }}>
            <h3 style={{ color: '#60a5fa', marginBottom: '15px' }}>💰 钱包管理</h3>
            <p>多钱包自动化管理与资金分配</p>
            <div style={{ marginTop: '15px' }}>
              <div>总余额: $12,450</div>
              <div>活跃钱包: 25/30</div>
            </div>
          </div>
        </div>
        
        <footer style={{
          textAlign: 'center',
          marginTop: '40px',
          padding: '20px',
          opacity: 0.6
        }}>
          <p>MemeMaster AI v2.0.0 - 完整版仪表盘正在加载中...</p>
          <p>如需访问完整功能，请访问: <a href="http://localhost:8000" style={{color: '#60a5fa'}}>http://localhost:8000</a></p>
        </footer>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById('root')).render(<App />)'''
    
    with open('frontend/main.jsx', 'w') as f:
        f.write(main_jsx)
    
    # 创建 index.css
    index_css = '''* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #1e293b, #7c3aed, #1e293b);
  color: white;
  min-height: 100vh;
}'''
    
    with open('frontend/index.css', 'w') as f:
        f.write(index_css)
    
    print("   ✅ 已创建简单前端入口文件")

def main():
    """主修复流程"""
    print("🔧 MemeMaster AI 仪表盘修复工具")
    print("=" * 50)
    
    # 检查当前目录
    if not Path('package.json').exists():
        print("❌ 未在正确的项目目录中运行")
        print("请在 MemeMaster AI 项目根目录中运行此脚本")
        return False
    
    # 执行修复步骤
    steps = [
        ("检查 package.json", check_and_fix_package_json),
        ("检查 Vite 配置", check_vite_config),
        ("检查前端结构", check_frontend_structure),
        ("检查 index.html", check_index_html),
        ("创建简单前端", create_simple_frontend),
        ("安装依赖", install_dependencies),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n{step_name}:")
        if step_func():
            success_count += 1
        else:
            print(f"   ❌ {step_name} 失败")
    
    print(f"\n📊 修复结果: {success_count}/{len(steps)} 步骤成功")
    
    # 检查服务状态
    print("\n" + "=" * 50)
    check_services()
    
    # 提供启动建议
    print("\n🚀 启动建议:")
    print("1. 启动后端: python3 simple_dashboard.py")
    print("2. 启动前端: npx vite (或 npm run frontend:dev)")
    print("3. 访问地址:")
    print("   - 简化版: http://localhost:8000")
    print("   - React版: http://localhost:5173")
    print("   - 备用版: http://localhost:3000")
    
    return success_count == len(steps)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
