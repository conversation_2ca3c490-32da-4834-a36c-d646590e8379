#!/usr/bin/env python3
"""
MemeMaster AI 用户管理系统初始化脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def init_user_management_system():
    """初始化用户管理系统"""
    try:
        print("🚀 开始初始化 MemeMaster AI 用户管理系统...")
        
        # 导入必要的模块
        from src.database.connection import init_database
        from src.auth.auth_manager import auth_manager
        from src.models.user import UserRole
        
        # 1. 初始化数据库
        print("📊 初始化数据库...")
        await init_database()
        print("✅ 数据库初始化完成")
        
        # 2. 创建管理员账户（如果不存在）
        print("👤 检查管理员账户...")
        try:
            admin_user = await auth_manager.register_user(
                email="<EMAIL>",
                username="admin",
                password="Admin123!@#",
                full_name="系统管理员"
            )
            
            # 更新为管理员角色
            from src.database.user_db import UserDatabase
            user_db = UserDatabase()
            await user_db.update_user(admin_user.id, {"role": UserRole.ENTERPRISE})
            
            print("✅ 管理员账户创建成功")
            print(f"   邮箱: <EMAIL>")
            print(f"   密码: Admin123!@#")
            
        except Exception as e:
            if "邮箱已被注册" in str(e):
                print("ℹ️  管理员账户已存在")
            else:
                print(f"⚠️  创建管理员账户失败: {e}")
        
        # 3. 创建测试用户（可选）
        print("🧪 创建测试用户...")
        test_users = [
            {
                "email": "<EMAIL>",
                "username": "testuser",
                "password": "Test123!@#",
                "full_name": "测试用户"
            },
            {
                "email": "<EMAIL>", 
                "username": "prouser",
                "password": "Pro123!@#",
                "full_name": "Pro用户"
            }
        ]
        
        for user_data in test_users:
            try:
                test_user = await auth_manager.register_user(**user_data)
                
                # 将第二个用户设为Pro用户
                if user_data["username"] == "prouser":
                    from src.database.user_db import UserDatabase
                    user_db = UserDatabase()
                    await user_db.update_user(test_user.id, {"role": UserRole.PRO})
                
                print(f"✅ 测试用户创建成功: {user_data['email']}")
                
            except Exception as e:
                if "邮箱已被注册" in str(e):
                    print(f"ℹ️  测试用户已存在: {user_data['email']}")
                else:
                    print(f"⚠️  创建测试用户失败: {e}")
        
        print("\n🎉 用户管理系统初始化完成！")
        print("\n📋 系统信息:")
        print("   • 数据库: SQLite (mememaster.db)")
        print("   • 认证方式: JWT Token")
        print("   • 支付方式: Solana (SOL)")
        print("   • 订阅层级: 免费版 / Pro版 / 机构版")
        
        print("\n🔑 默认账户:")
        print("   管理员: <EMAIL> / Admin123!@#")
        print("   测试用户: <EMAIL> / Test123!@#")
        print("   Pro用户: <EMAIL> / Pro123!@#")
        
        print("\n🌐 访问地址:")
        print("   • 主应用: http://localhost:8001/")
        print("   • 用户登录: http://localhost:8001/login")
        print("   • 用户中心: http://localhost:8001/user-dashboard")
        print("   • 订阅管理: http://localhost:8001/subscription")
        
        print("\n📚 API端点:")
        print("   • POST /api/auth/register - 用户注册")
        print("   • POST /api/auth/login - 用户登录")
        print("   • GET /api/auth/me - 获取用户信息")
        print("   • GET /api/subscription/plans - 获取订阅计划")
        print("   • POST /api/subscription/upgrade - 升级订阅")
        print("   • GET /api/usage/summary - 获取使用摘要")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_system_status():
    """检查系统状态"""
    try:
        print("🔍 检查系统状态...")
        
        # 检查数据库连接
        from src.database.connection import db_connection
        async with db_connection.get_connection() as conn:
            cursor = await conn.execute("SELECT COUNT(*) FROM users")
            user_count = (await cursor.fetchone())[0]
            print(f"✅ 数据库连接正常，用户数量: {user_count}")
        
        # 检查认证系统
        from src.auth.auth_manager import auth_manager
        test_token = auth_manager.create_access_token(data={"sub": "1"})
        if test_token:
            print("✅ 认证系统正常")
        
        # 检查订阅计划
        from src.models.subscription import SUBSCRIPTION_PLANS
        print(f"✅ 订阅计划配置正常，共 {len(SUBSCRIPTION_PLANS)} 个计划")
        
        print("🎉 系统状态检查完成，一切正常！")
        return True
        
    except Exception as e:
        print(f"❌ 系统状态检查失败: {e}")
        return False

def print_usage():
    """打印使用说明"""
    print("""
MemeMaster AI 用户管理系统初始化工具

使用方法:
    python init_user_system.py [命令]

命令:
    init     - 初始化用户管理系统（默认）
    check    - 检查系统状态
    help     - 显示此帮助信息

示例:
    python init_user_system.py init
    python init_user_system.py check
    """)

async def main():
    """主函数"""
    command = sys.argv[1] if len(sys.argv) > 1 else "init"
    
    if command == "help":
        print_usage()
        return
    
    elif command == "check":
        success = await check_system_status()
        sys.exit(0 if success else 1)
    
    elif command == "init":
        success = await init_user_management_system()
        if success:
            print("\n✨ 现在您可以启动 MemeMaster AI 并使用用户管理功能了！")
            print("   运行: python web_app.py")
        sys.exit(0 if success else 1)
    
    else:
        print(f"❌ 未知命令: {command}")
        print_usage()
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)
