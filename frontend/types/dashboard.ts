import { IconType } from 'react-icons';

// 统计卡片数据类型
export interface StatCard {
  title: string;
  value: string | number;
  icon: IconType;
  trend: {
    direction: 'up' | 'down' | 'neutral';
    text: string;
    percentage?: number;
  };
  iconBg: string;
  iconColor: string;
  description?: string;
}

// 热门代币数据类型
export interface HotCoin {
  symbol: string;
  name: string;
  change: number; // 百分比数值
  price?: string;
  volume?: string;
  marketCap?: string;
  chartData?: number[];
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension?: number;
    fill?: boolean;
  }[];
}

// 功能卡片数据类型
export interface FeatureCard {
  title: string;
  description: string;
  icon: IconType;
  actionText: string;
  actionType: 'primary' | 'secondary';
  onClick: () => void;
  status?: 'active' | 'inactive' | 'pending';
  badge?: string;
}

// 市场数据类型
export interface MarketData {
  sentiment: number; // 0-100
  opportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  lastUpdate: string;
  totalVolume: string;
  activeStrategies: number;
}

// 用户数据类型
export interface UserData {
  name: string;
  avatar?: string;
  subscription: {
    plan: 'FREE' | 'PRO' | 'ENTERPRISE';
    expiryDate: string;
    features: string[];
  };
  wallet: {
    balance: string;
    currency: 'SOL' | 'ETH' | 'USDC';
    usdValue: string;
    connected: boolean;
  };
  aiUsage: {
    current: number;
    total: number | '∞';
    resetDate: string;
  };
}

// 仪表盘状态类型
export interface DashboardState {
  user: UserData;
  market: MarketData;
  stats: StatCard[];
  hotCoins: HotCoin[];
  features: FeatureCard[];
  chartData: ChartData;
  isLoading: boolean;
  lastUpdated: string;

  // 方法
  updateMarketData: (data: Partial<MarketData>) => void;
  updateUserData: (data: Partial<UserData>) => void;
  updateWalletBalance: (balance: string, usdValue: string) => void;
  updateHotCoins: (coins: HotCoin[]) => void;
  updateChartData: (data: ChartData) => void;
  setLoading: (isLoading: boolean) => void;
}

// 响应式断点类型
export type Breakpoint = 'mobile' | 'tablet' | 'desktop' | 'large';

// 主题类型
export interface Theme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

// 动画配置类型
export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing: string;
}

// 组件通用属性
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  loading?: boolean;
  error?: string | null;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

// 实时数据更新类型
export interface RealtimeUpdate {
  type: 'market' | 'wallet' | 'strategy' | 'notification';
  data: any;
  timestamp: string;
}

// 导出所有类型
export type {
  IconType
};
