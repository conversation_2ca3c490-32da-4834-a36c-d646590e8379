import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from './App.jsx'
import { store } from './store/index.js'
import ErrorBoundary from './components/ErrorBoundary.jsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ErrorBoundary>
      <Provider store={store}>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </Provider>
    </ErrorBoundary>
  </React.StrictMode>,
)