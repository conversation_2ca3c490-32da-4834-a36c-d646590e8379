import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { exitAPI } from '../../services/api'

export const fetchExitSignals = createAsyncThunk(
  'exit/fetchSignals',
  async () => {
    const response = await exitAPI.getSignals()
    return response.data
  }
)

export const fetchMultiIndicators = createAsyncThunk(
  'exit/fetchMultiIndicators',
  async () => {
    const response = await exitAPI.getMultiIndicators()
    return response.data
  }
)

export const updateXGBoostModel = createAsyncThunk(
  'exit/updateXGBoostModel',
  async () => {
    const response = await exitAPI.retrainXGBoost()
    return response.data
  }
)

export const executeEmergencyExit = createAsyncThunk(
  'exit/executeEmergencyExit',
  async (params) => {
    const response = await exitAPI.emergencyExit(params)
    return response.data
  }
)

export const activateHedging = createAsyncThunk(
  'exit/activateHedging',
  async (params) => {
    const response = await exitAPI.activateHedging(params)
    return response.data
  }
)

const initialState = {
  signalStrength: 0.78,
  recommendedAction: 'partial_sell',
  estimatedROI: 15.6,
  settings: {
    exitThreshold: 0.75,
    stopLoss: 15,
    hedgeRatio: 30,
    volatilityParams: {
      high: { batchSize: 7, interval: 20 },
      medium: { batchSize: 9, interval: 40 },
      low: { batchSize: 12, interval: 60 }
    }
  },
  // 新增的多指标数据
  multiIndicators: {
    technical: {
      rsi: 68.5,
      macd: 0.12,
      bollinger: 'upper',
      volume_spike: 2.3
    },
    onchain: {
      whale_activity: 0.78,
      transaction_volume: 1.45,
      holder_distribution: 0.65,
      smart_money_flow: 0.82
    },
    sentiment: {
      social_score: 0.73,
      news_sentiment: 0.68,
      fear_greed: 0.45,
      polymarket_prob: 0.67
    },
    events: {
      regulatory_risk: 0.23,
      market_correlation: 0.89,
      volatility_forecast: 0.72,
      liquidity_depth: 0.91
    }
  },
  // XGBoost模型数据
  xgboostPrediction: {
    prediction: 0.78,
    confidence: 0.85,
    feature_importance: {
      'RSI': 0.18,
      'Whale Activity': 0.22,
      'Sentiment': 0.15,
      'Volume': 0.12,
      'MACD': 0.10,
      'Polymarket': 0.23
    },
    model_accuracy: 0.847,
    last_retrain: new Date().toISOString()
  },
  // 期权对冲状态
  hedgingStatus: {
    active: true,
    positions: [
      { type: 'PUT', strike: 0.0208, expiry: '7d', amount: 30, premium: 0.0012, status: 'active' },
      { type: 'PUT', strike: 0.0195, expiry: '14d', amount: 20, premium: 0.0018, status: 'active' }
    ]
  },
  // 紧急退出状态
  emergencyExitStatus: {
    active: false,
    lastExecution: null,
    preSignedTxs: 5
  },
  // 波动率级别
  volatilityLevel: 'medium',
  // 分批卖出数据
  sellBatches: [
    { batch: 1, amount: 7, price: 0.0245, status: 'completed', time: '10:15' },
    { batch: 2, amount: 7, price: 0.0238, status: 'completed', time: '10:35' },
    { batch: 3, amount: 9, price: 0.0232, status: 'pending', time: '10:55' },
    { batch: 4, amount: 9, price: 0.0225, status: 'queued', time: '11:15' },
    { batch: 5, amount: 12, price: 0.0218, status: 'queued', time: '11:35' }
  ],
  // 期权持仓
  optionPositions: [
    { type: 'PUT', strike: 0.0208, expiry: '7d', amount: 30, premium: 0.0012, status: 'active' },
    { type: 'PUT', strike: 0.0195, expiry: '14d', amount: 20, premium: 0.0018, status: 'active' }
  ],
  isLoading: false,
  error: null
}

const exitSlice = createSlice({
  name: 'exit',
  initialState,
  reducers: {
    updateSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload }
    },
    updateVolatilityParams: (state, action) => {
      state.settings.volatilityParams = {
        ...state.settings.volatilityParams,
        ...action.payload
      }
    },
    updateMultiIndicators: (state, action) => {
      state.multiIndicators = { ...state.multiIndicators, ...action.payload }
    },
    updateXGBoostPrediction: (state, action) => {
      state.xgboostPrediction = { ...state.xgboostPrediction, ...action.payload }
    },
    updateHedgingStatus: (state, action) => {
      state.hedgingStatus = { ...state.hedgingStatus, ...action.payload }
    },
    updateSellBatches: (state, action) => {
      state.sellBatches = action.payload
    },
    updateVolatilityLevel: (state, action) => {
      state.volatilityLevel = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchExitSignals.pending, (state) => {
        state.isLoading = true
      })
      .addCase(fetchExitSignals.fulfilled, (state, action) => {
        state.isLoading = false
        state.signalStrength = action.payload.strength || state.signalStrength
        state.recommendedAction = action.payload.action || state.recommendedAction
        state.estimatedROI = action.payload.roi || state.estimatedROI
      })
      .addCase(fetchExitSignals.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
      })
      .addCase(fetchMultiIndicators.fulfilled, (state, action) => {
        state.multiIndicators = action.payload
      })
      .addCase(updateXGBoostModel.fulfilled, (state, action) => {
        state.xgboostPrediction = {
          ...state.xgboostPrediction,
          ...action.payload,
          last_retrain: new Date().toISOString()
        }
      })
      .addCase(executeEmergencyExit.fulfilled, (state, action) => {
        state.emergencyExitStatus = {
          active: true,
          lastExecution: new Date().toISOString(),
          preSignedTxs: action.payload.executedTxs || 5
        }
      })
      .addCase(activateHedging.fulfilled, (state, action) => {
        state.hedgingStatus = {
          active: true,
          positions: action.payload.positions || state.hedgingStatus.positions
        }
        state.optionPositions = action.payload.positions || state.optionPositions
      })
  }
})

export const {
  updateSettings,
  updateVolatilityParams,
  updateMultiIndicators,
  updateXGBoostPrediction,
  updateHedgingStatus,
  updateSellBatches,
  updateVolatilityLevel
} = exitSlice.actions

export default exitSlice.reducer
