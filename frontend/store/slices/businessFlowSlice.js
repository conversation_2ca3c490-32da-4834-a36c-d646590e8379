import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { businessFlowAPI } from '../../services/api'

// 异步操作
export const triggerHotspotAlert = createAsyncThunk(
  'businessFlow/triggerHotspotAlert',
  async (hotspotData) => {
    const response = await businessFlowAPI.triggerHotspotAlert(hotspotData)
    return response.data
  }
)

export const generateAIStrategy = createAsyncThunk(
  'businessFlow/generateAIStrategy',
  async (hotspotData) => {
    const response = await businessFlowAPI.generateAIStrategy(hotspotData)
    return response.data
  }
)

export const deployMemeToken = createAsyncThunk(
  'businessFlow/deployMemeToken',
  async (deploymentConfig) => {
    const response = await businessFlowAPI.deployMemeToken(deploymentConfig)
    return response.data
  }
)

export const initializeLiquidityControl = createAsyncThunk(
  'businessFlow/initializeLiquidityControl',
  async (tokenData) => {
    const response = await businessFlowAPI.initializeLiquidityControl(tokenData)
    return response.data
  }
)

export const createExitStrategy = createAsyncThunk(
  'businessFlow/createExitStrategy',
  async (tokenData) => {
    const response = await businessFlowAPI.createExitStrategy(tokenData)
    return response.data
  }
)

const initialState = {
  // 业务流程状态
  currentFlow: null, // 当前活跃的业务流程
  flowHistory: [], // 历史流程记录
  
  // 各阶段状态
  hotspotAlert: null,
  aiStrategy: null,
  deploymentResult: null,
  liquidityControl: null,
  exitStrategy: null,
  
  // 已发行代币管理
  deployedTokens: [],
  activeTokens: [], // 正在管理的代币
  
  // 流程控制
  isProcessing: false,
  currentStep: 'idle', // idle, hotspot, strategy, deployment, liquidity, exit
  error: null,
  
  // 自动化设置
  autoSettings: {
    enableAutoStrategy: true,
    enableAutoDeploy: false,
    enableAutoLiquidity: true,
    enableAutoExit: false,
    riskLevel: 'medium' // low, medium, high
  }
}

const businessFlowSlice = createSlice({
  name: 'businessFlow',
  initialState,
  reducers: {
    // 启动新的业务流程
    startNewFlow: (state, action) => {
      const flowId = `flow_${Date.now()}`
      state.currentFlow = {
        id: flowId,
        startTime: new Date().toISOString(),
        hotspotData: action.payload,
        status: 'started',
        steps: []
      }
      state.currentStep = 'hotspot'
      state.isProcessing = true
    },
    
    // 更新流程步骤
    updateFlowStep: (state, action) => {
      if (state.currentFlow) {
        state.currentFlow.steps.push({
          step: action.payload.step,
          timestamp: new Date().toISOString(),
          data: action.payload.data,
          status: action.payload.status
        })
        state.currentStep = action.payload.nextStep || state.currentStep
      }
    },
    
    // 完成流程
    completeFlow: (state) => {
      if (state.currentFlow) {
        state.currentFlow.status = 'completed'
        state.currentFlow.endTime = new Date().toISOString()
        state.flowHistory.push(state.currentFlow)
        state.currentFlow = null
      }
      state.currentStep = 'idle'
      state.isProcessing = false
    },
    
    // 添加已部署代币
    addDeployedToken: (state, action) => {
      const token = {
        ...action.payload,
        id: `token_${Date.now()}`,
        deployedAt: new Date().toISOString(),
        status: 'active',
        liquidityControlEnabled: false,
        exitStrategyEnabled: false
      }
      state.deployedTokens.push(token)
      state.activeTokens.push(token)
    },
    
    // 更新代币状态
    updateTokenStatus: (state, action) => {
      const { tokenId, updates } = action.payload
      const tokenIndex = state.deployedTokens.findIndex(t => t.id === tokenId)
      if (tokenIndex !== -1) {
        state.deployedTokens[tokenIndex] = { ...state.deployedTokens[tokenIndex], ...updates }
      }
      
      const activeIndex = state.activeTokens.findIndex(t => t.id === tokenId)
      if (activeIndex !== -1) {
        state.activeTokens[activeIndex] = { ...state.activeTokens[activeIndex], ...updates }
      }
    },
    
    // 更新自动化设置
    updateAutoSettings: (state, action) => {
      state.autoSettings = { ...state.autoSettings, ...action.payload }
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null
    }
  },
  
  extraReducers: (builder) => {
    builder
      // 热点警报
      .addCase(triggerHotspotAlert.fulfilled, (state, action) => {
        state.hotspotAlert = action.payload
        state.currentStep = 'strategy'
      })
      
      // AI策略生成
      .addCase(generateAIStrategy.pending, (state) => {
        state.isProcessing = true
      })
      .addCase(generateAIStrategy.fulfilled, (state, action) => {
        state.aiStrategy = action.payload
        state.currentStep = 'deployment'
        state.isProcessing = false
      })
      .addCase(generateAIStrategy.rejected, (state, action) => {
        state.error = action.error.message
        state.isProcessing = false
      })
      
      // 代币部署
      .addCase(deployMemeToken.fulfilled, (state, action) => {
        state.deploymentResult = action.payload
        state.currentStep = 'liquidity'
        // 自动添加到已部署代币列表
        businessFlowSlice.caseReducers.addDeployedToken(state, { payload: action.payload })
      })
      
      // 流动性控制初始化
      .addCase(initializeLiquidityControl.fulfilled, (state, action) => {
        state.liquidityControl = action.payload
        state.currentStep = 'exit'
      })
      
      // 退出策略创建
      .addCase(createExitStrategy.fulfilled, (state, action) => {
        state.exitStrategy = action.payload
        // 完成整个流程
        businessFlowSlice.caseReducers.completeFlow(state)
      })
  }
})

export const {
  startNewFlow,
  updateFlowStep,
  completeFlow,
  addDeployedToken,
  updateTokenStatus,
  updateAutoSettings,
  clearError
} = businessFlowSlice.actions

export default businessFlowSlice.reducer
