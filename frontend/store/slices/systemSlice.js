import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { systemAPI } from '../../services/api'

// 异步操作
export const fetchSystemStatus = createAsyncThunk(
  'system/fetchStatus',
  async () => {
    const response = await systemAPI.getStatus()
    return response.data
  }
)

export const fetchSystemInfo = createAsyncThunk(
  'system/fetchInfo',
  async () => {
    const response = await systemAPI.getInfo()
    return response.data
  }
)

const initialState = {
  status: 'offline',
  uptime: 0,
  cpuUsage: 0,
  memoryUsage: 0,
  requestCount: 0,
  lastUpdate: null,
  isLoading: false,
  error: null,
  connectionStatus: 'disconnected',
  serverTime: null,
  version: '2.0.0'
}

const systemSlice = createSlice({
  name: 'system',
  initialState,
  reducers: {
    updateConnectionStatus: (state, action) => {
      state.connectionStatus = action.payload
    },
    updateServerTime: (state, action) => {
      state.serverTime = action.payload
    },
    incrementRequestCount: (state) => {
      state.requestCount += 1
    },
    resetError: (state) => {
      state.error = null
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSystemStatus.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchSystemStatus.fulfilled, (state, action) => {
        state.isLoading = false
        state.status = action.payload.status
        state.lastUpdate = new Date().toISOString()
      })
      .addCase(fetchSystemStatus.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
        state.status = 'offline'
      })
      .addCase(fetchSystemInfo.fulfilled, (state, action) => {
        state.uptime = action.payload.uptime
        state.cpuUsage = action.payload.cpu_usage
        state.memoryUsage = action.payload.memory_usage
        state.requestCount = action.payload.request_count
      })
  }
})

export const { 
  updateConnectionStatus, 
  updateServerTime, 
  incrementRequestCount, 
  resetError 
} = systemSlice.actions

export default systemSlice.reducer
