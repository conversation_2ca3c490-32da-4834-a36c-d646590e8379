import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { hotspotAPI } from '../../services/api'

// 异步操作
export const fetchHotspots = createAsyncThunk(
  'hotspot/fetchHotspots',
  async () => {
    const response = await hotspotAPI.getHotspots()
    return response.data
  }
)

export const fetchAdvancedAnalytics = createAsyncThunk(
  'hotspot/fetchAdvancedAnalytics',
  async () => {
    const response = await hotspotAPI.getAdvancedAnalytics()
    return response.data
  }
)

export const updateHotspotSettings = createAsyncThunk(
  'hotspot/updateSettings',
  async (settings) => {
    const response = await hotspotAPI.updateSettings(settings)
    return response.data
  }
)

export const updateMLSettings = createAsyncThunk(
  'hotspot/updateMLSettings',
  async (settings) => {
    const response = await hotspotAPI.updateMLSettings(settings)
    return response.data
  }
)

export const toggleDarkwebMonitoring = createAsyncThunk(
  'hotspot/toggleDarkweb',
  async () => {
    const response = await hotspotAPI.toggleDarkwebMonitoring()
    return response.data
  }
)

export const updateLanguageSettings = createAsyncThunk(
  'hotspot/updateLanguages',
  async (languageSettings) => {
    const response = await hotspotAPI.updateLanguageSettings(languageSettings)
    return response.data
  }
)

const initialState = {
  hotspots: [],
  distribution: {
    politics: 45,
    subculture: 30,
    technology: 15,
    metaverse: 12,
    gamefi: 8,
    nft: 6,
    sports: 10
  },
  sources: {
    twitter: { enabled: true, weight: 50 },
    reddit: { enabled: true, weight: 30 },
    tiktok: { enabled: true, weight: 25 },
    youtube: { enabled: true, weight: 20 },
    discord: { enabled: false, weight: 15 },
    darkweb: { enabled: false, weight: 10 }
  },
  settings: {
    minAdaptation: 0.7,
    monitoringFrequency: 'realtime', // realtime, 5min, 30min
    keywords: ['meme', 'crypto', 'token', 'viral', 'Trump', 'Musk', 'NFT', 'GameFi'],
    predictionTarget: '65',
    captureWindow: '3-15min',
    videoAnalysis: true,
    darkwebEnabled: false,
    languages: {
      en: true,
      zh: true,
      ko: false,
      ja: false,
      es: false,
      ru: false
    }
  },
  // 新增的高级分析数据
  mlMetrics: {
    viralityModel: 95.2,
    resonanceModel: 88.7,
    celebrityModel: 76.3,
    culturalModel: 92.1
  },
  sentimentIndex: {
    overall: 0.82,
    politics: 0.85,
    subculture: 0.90,
    technology: 0.78
  },
  languageDistribution: {
    en: 45,
    zh: 25,
    ko: 12,
    ja: 8,
    es: 6,
    ru: 4
  },
  darkwebSignals: [
    {
      title: "Underground Meme Emergence",
      confidence: 0.78,
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15分钟前
      source: "tor_forum",
      risk_level: "medium"
    },
    {
      title: "Early Token Discussion",
      confidence: 0.65,
      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45分钟前
      source: "encrypted_chat",
      risk_level: "low"
    },
    {
      title: "Regulatory Evasion Strategy",
      confidence: 0.89,
      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2小时前
      source: "dark_market",
      risk_level: "high"
    }
  ],
  videoAnalysis: {
    processedFrames: 1247,
    recognitionAccuracy: 94.8,
    keywordsExtracted: 856,
    visualMemes: 23
  },
  regulatoryAlerts: [],
  predictionAccuracy: 67.8,
  isLoading: false,
  error: null,
  lastUpdate: null
}

const hotspotSlice = createSlice({
  name: 'hotspot',
  initialState,
  reducers: {
    updateSources: (state, action) => {
      state.sources = { ...state.sources, ...action.payload }
    },
    updateSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload }
    },
    addKeyword: (state, action) => {
      if (!state.settings.keywords.includes(action.payload)) {
        state.settings.keywords.push(action.payload)
      }
    },
    removeKeyword: (state, action) => {
      state.settings.keywords = state.settings.keywords.filter(
        keyword => keyword !== action.payload
      )
    },
    updateDistribution: (state, action) => {
      state.distribution = { ...state.distribution, ...action.payload }
    },
    updateMLMetrics: (state, action) => {
      state.mlMetrics = { ...state.mlMetrics, ...action.payload }
    },
    updateSentimentIndex: (state, action) => {
      state.sentimentIndex = { ...state.sentimentIndex, ...action.payload }
    },
    updateLanguageDistribution: (state, action) => {
      state.languageDistribution = { ...state.languageDistribution, ...action.payload }
    },
    addDarkwebSignal: (state, action) => {
      state.darkwebSignals.unshift(action.payload)
      // 保持最新的50条信号
      if (state.darkwebSignals.length > 50) {
        state.darkwebSignals = state.darkwebSignals.slice(0, 50)
      }
    },
    updateVideoAnalysis: (state, action) => {
      state.videoAnalysis = { ...state.videoAnalysis, ...action.payload }
    },
    addRegulatoryAlert: (state, action) => {
      state.regulatoryAlerts.unshift(action.payload)
      // 保持最新的20条警报
      if (state.regulatoryAlerts.length > 20) {
        state.regulatoryAlerts = state.regulatoryAlerts.slice(0, 20)
      }
    },
    updatePredictionAccuracy: (state, action) => {
      state.predictionAccuracy = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchHotspots.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchHotspots.fulfilled, (state, action) => {
        state.isLoading = false
        state.hotspots = action.payload.hotspots || []
        state.distribution = action.payload.distribution || state.distribution
        state.lastUpdate = new Date().toISOString()
      })
      .addCase(fetchHotspots.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
      })
      .addCase(fetchAdvancedAnalytics.fulfilled, (state, action) => {
        state.mlMetrics = action.payload.mlMetrics || state.mlMetrics
        state.sentimentIndex = action.payload.sentimentIndex || state.sentimentIndex
        state.languageDistribution = action.payload.languageDistribution || state.languageDistribution
        state.videoAnalysis = action.payload.videoAnalysis || state.videoAnalysis
        state.predictionAccuracy = action.payload.predictionAccuracy || state.predictionAccuracy
        if (action.payload.darkwebSignals) {
          state.darkwebSignals = action.payload.darkwebSignals
        }
        if (action.payload.regulatoryAlerts) {
          state.regulatoryAlerts = action.payload.regulatoryAlerts
        }
      })
      .addCase(updateHotspotSettings.fulfilled, (state, action) => {
        state.settings = { ...state.settings, ...action.payload }
      })
      .addCase(updateMLSettings.fulfilled, (state, action) => {
        state.settings = { ...state.settings, ...action.payload }
      })
      .addCase(toggleDarkwebMonitoring.fulfilled, (state, action) => {
        state.settings.darkwebEnabled = action.payload.enabled
      })
      .addCase(updateLanguageSettings.fulfilled, (state, action) => {
        state.settings.languages = { ...state.settings.languages, ...action.payload }
      })
  }
})

export const {
  updateSources,
  updateSettings,
  addKeyword,
  removeKeyword,
  updateDistribution,
  updateMLMetrics,
  updateSentimentIndex,
  updateLanguageDistribution,
  addDarkwebSignal,
  updateVideoAnalysis,
  addRegulatoryAlert,
  updatePredictionAccuracy
} = hotspotSlice.actions

export default hotspotSlice.reducer
