import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { liquidityAPI } from '../../services/api'

export const fetchLiquidityData = createAsyncThunk(
  'liquidity/fetchData',
  async () => {
    const response = await liquidityAPI.getData()
    return response.data
  }
)

export const enableMEVProtection = createAsyncThunk(
  'liquidity/enableMEVProtection',
  async () => {
    const response = await liquidityAPI.enableMEVProtection()
    return response.data
  }
)

export const triggerAutoRefill = createAsyncThunk(
  'liquidity/triggerAutoRefill',
  async () => {
    const response = await liquidityAPI.triggerAutoRefill()
    return response.data
  }
)

export const fetchWhaleActivity = createAsyncThunk(
  'liquidity/fetchWhaleActivity',
  async () => {
    const response = await liquidityAPI.getWhaleActivity()
    return response.data
  }
)

const initialState = {
  currentTVL: 0,
  tvlChange24h: 0,
  optimalSlippage: 0.3,
  settings: {
    tvlAlertThreshold: 1000000,
    slippageTolerance: 0.5,
    autoRefillEnabled: true,
    mevProtectionLevel: 'high', // high, medium, low
    flashbotsEnabled: true,
    whaleMonitoringEnabled: true,
    aggregatorPreference: 'both' // 1inch, paraswap, both
  },
  mevProtectionStats: {
    lossReduction: 84.2,
    flashbotsUsage: 76,
    privateMempool: true,
    randomization: true,
    frontrunDetection: 12
  },
  whaleActivity: [],
  autoRefillStatus: {
    active: true,
    lastRefill: '2小时前',
    lastAmount: 500000,
    nextRefill: '4小时后',
    nextAmount: 300000
  },
  slippageOptimization: {
    aggregatorSavings: 23.4,
    oneinchActive: true,
    paraswapActive: true,
    orderSplitting: 3
  },
  isLoading: false,
  error: null
}

const liquiditySlice = createSlice({
  name: 'liquidity',
  initialState,
  reducers: {
    updateSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload }
    },
    updateMEVProtectionStats: (state, action) => {
      state.mevProtectionStats = { ...state.mevProtectionStats, ...action.payload }
    },
    updateWhaleActivity: (state, action) => {
      state.whaleActivity = action.payload
    },
    updateAutoRefillStatus: (state, action) => {
      state.autoRefillStatus = { ...state.autoRefillStatus, ...action.payload }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLiquidityData.pending, (state) => {
        state.isLoading = true
      })
      .addCase(fetchLiquidityData.fulfilled, (state, action) => {
        state.isLoading = false
        state.currentTVL = action.payload.tvl
        state.tvlChange24h = action.payload.change24h
        state.optimalSlippage = action.payload.slippage
      })
      .addCase(fetchLiquidityData.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
      })
      .addCase(enableMEVProtection.fulfilled, (state, action) => {
        state.mevProtectionStats = { ...state.mevProtectionStats, ...action.payload }
      })
      .addCase(triggerAutoRefill.fulfilled, (state, action) => {
        state.autoRefillStatus = { ...state.autoRefillStatus, ...action.payload }
        state.currentTVL = action.payload.newTVL || state.currentTVL
      })
      .addCase(fetchWhaleActivity.fulfilled, (state, action) => {
        state.whaleActivity = action.payload
      })
  }
})

export const {
  updateSettings,
  updateMEVProtectionStats,
  updateWhaleActivity,
  updateAutoRefillStatus
} = liquiditySlice.actions
export default liquiditySlice.reducer
