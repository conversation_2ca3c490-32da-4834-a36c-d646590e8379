import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { walletAPI } from '../../services/api'

export const fetchWallets = createAsyncThunk(
  'wallet/fetchWallets',
  async () => {
    const response = await walletAPI.getWallets()
    return response.data
  }
)

export const generateWallets = createAsyncThunk(
  'wallet/generateWallets',
  async (config, { dispatch }) => {
    const response = await walletAPI.generateWallets(config)
    return response.data
  }
)

export const distributeRandomFunds = createAsyncThunk(
  'wallet/distributeRandomFunds',
  async (_, { getState }) => {
    const { wallet } = getState()
    const response = await walletAPI.distributeRandomFunds({
      wallets: wallet.wallets,
      fundingConfig: wallet.settings.initialFunding,
      randomVariation: wallet.settings.randomVariation
    })
    return response.data
  }
)

export const enableRentRecovery = createAsyncThunk(
  'wallet/enableRentRecovery',
  async () => {
    const response = await walletAPI.enableRentRecovery()
    return response.data
  }
)

const initialState = {
  wallets: [],
  settings: {
    generateCount: 50,
    initialFunding: {
      eth: 0.1,
      sol: 1.0,
      usdc: 100
    },
    randomVariation: 10, // ±10%
    supportedChains: ['ethereum', 'solana'],
    rentRecoveryThreshold: 0.001,
    antiBanStrategy: 'standard', // aggressive, standard, conservative
    enableIPRotation: true,
    enableBehaviorDisguise: true,
    enableRateAdaptive: true,
    proxyRotationInterval: 15 // minutes
  },
  generationProgress: null,
  proxyStatus: {
    residential: 'active',
    datacenter: 'active',
    currentIP: '*************'
  },
  antiBanMetrics: {
    evasionRate: 92,
    behaviorDisguise: true,
    currentRate: 2.5,
    platformResponse: '正常',
    adaptiveStatus: '监控中'
  },
  isLoading: false,
  error: null
}

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    updateSettings: (state, action) => {
      state.settings = { ...state.settings, ...action.payload }
    },
    updateInitialFunding: (state, action) => {
      state.settings.initialFunding = { ...state.settings.initialFunding, ...action.payload }
    },
    updateGenerationProgress: (state, action) => {
      state.generationProgress = action.payload
    },
    updateProxyStatus: (state, action) => {
      state.proxyStatus = { ...state.proxyStatus, ...action.payload }
    },
    updateAntiBanMetrics: (state, action) => {
      state.antiBanMetrics = { ...state.antiBanMetrics, ...action.payload }
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchWallets.pending, (state) => {
        state.isLoading = true
      })
      .addCase(fetchWallets.fulfilled, (state, action) => {
        state.isLoading = false
        state.wallets = action.payload
      })
      .addCase(fetchWallets.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
      })
      .addCase(generateWallets.pending, (state) => {
        state.isLoading = true
        state.generationProgress = { current: 0, total: state.settings.generateCount, status: '开始生成钱包...' }
      })
      .addCase(generateWallets.fulfilled, (state, action) => {
        state.isLoading = false
        state.wallets = [...state.wallets, ...action.payload]
        state.generationProgress = null
      })
      .addCase(generateWallets.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
        state.generationProgress = null
      })
      .addCase(distributeRandomFunds.fulfilled, (state, action) => {
        state.wallets = action.payload
      })
      .addCase(enableRentRecovery.fulfilled, (state, action) => {
        // 更新租金回收状态
        state.settings.rentRecoveryEnabled = true
      })
  }
})

export const {
  updateSettings,
  updateInitialFunding,
  updateGenerationProgress,
  updateProxyStatus,
  updateAntiBanMetrics
} = walletSlice.actions
export default walletSlice.reducer
