import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import { strategyAPI } from '../../services/api'

export const fetchStrategies = createAsyncThunk(
  'strategy/fetchStrategies',
  async () => {
    const response = await strategyAPI.getStrategies()
    return response.data
  }
)

export const deployToken = createAsyncThunk(
  'strategy/deployToken',
  async (tokenData) => {
    const response = await strategyAPI.deployToken(tokenData)
    return response.data
  }
)

export const runSecurityAudit = createAsyncThunk(
  'strategy/runSecurityAudit',
  async (tokenData) => {
    const response = await strategyAPI.runSecurityAudit(tokenData)
    return response.data
  }
)

export const optimizeGas = createAsyncThunk(
  'strategy/optimizeGas',
  async (tokenData) => {
    const response = await strategyAPI.optimizeGas(tokenData)
    return response.data
  }
)

export const generateContract = createAsyncThunk(
  'strategy/generateContract',
  async (tokenData) => {
    const response = await strategyAPI.generateContract(tokenData)
    return response.data
  }
)

export const createStrategy = createAsyncThunk(
  'strategy/createStrategy',
  async (strategyData) => {
    const response = await strategyAPI.createStrategy(strategyData)
    return response.data
  }
)

const initialState = {
  strategies: [
    {
      id: 1,
      name: "MEME Momentum Strategy",
      type: "momentum",
      status: "active",
      performance: {
        roi: 156.7,
        winRate: 78.3,
        totalTrades: 247,
        avgHoldTime: "4.2h"
      },
      allocation: 35,
      lastUpdate: "2024-01-15T10:30:00Z",
      description: "基于社交媒体情绪和交易量的动量策略"
    },
    {
      id: 2,
      name: "AI Sentiment Analysis",
      type: "sentiment",
      status: "active",
      performance: {
        roi: 89.4,
        winRate: 65.8,
        totalTrades: 189,
        avgHoldTime: "6.7h"
      },
      allocation: 25,
      lastUpdate: "2024-01-15T09:45:00Z",
      description: "AI驱动的情绪分析和预测策略"
    },
    {
      id: 3,
      name: "Whale Tracking Bot",
      type: "whale",
      status: "paused",
      performance: {
        roi: 234.1,
        winRate: 82.1,
        totalTrades: 156,
        avgHoldTime: "2.8h"
      },
      allocation: 20,
      lastUpdate: "2024-01-14T16:20:00Z",
      description: "跟踪大户交易行为的策略"
    },
    {
      id: 4,
      name: "Cross-Chain Arbitrage",
      type: "arbitrage",
      status: "active",
      performance: {
        roi: 67.3,
        winRate: 91.2,
        totalTrades: 423,
        avgHoldTime: "0.5h"
      },
      allocation: 15,
      lastUpdate: "2024-01-15T11:15:00Z",
      description: "跨链套利机会捕获策略"
    },
    {
      id: 5,
      name: "DeFi Yield Farming",
      type: "yield",
      status: "testing",
      performance: {
        roi: 45.2,
        winRate: 73.6,
        totalTrades: 98,
        avgHoldTime: "24.3h"
      },
      allocation: 5,
      lastUpdate: "2024-01-15T08:30:00Z",
      description: "DeFi收益农场优化策略"
    }
  ],
  activeStrategy: 1,
  tokenSettings: {
    totalSupply: { min: 1000000000, max: 100000000000, current: 10000000000 },
    allocation: {
      airdrop: 20,
      liquidity: 40,
      marketing: 25,
      team: 15
    },
    burnRate: 0.02,
    timezone: 'UTC'
  },
  // 新增发币相关状态
  deploymentStatus: {
    isDeploying: false,
    progress: 0,
    currentStep: '',
    deployedContracts: [
      {
        id: 1,
        name: "MemeCoin Pro",
        symbol: "MEMP",
        chains: ["ethereum", "bsc"],
        addresses: {
          ethereum: "******************************************",
          bsc: "******************************************"
        },
        deployTime: 11.2,
        gasUsed: 2847392,
        status: "success",
        auditScore: 96.8,
        deployedAt: "2024-01-15T09:30:00Z"
      },
      {
        id: 2,
        name: "AI Trader Token",
        symbol: "AITT",
        chains: ["solana"],
        addresses: {
          solana: "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU"
        },
        deployTime: 8.7,
        gasUsed: 1234567,
        status: "success",
        auditScore: 94.2,
        deployedAt: "2024-01-14T14:20:00Z"
      },
      {
        id: 3,
        name: "DeFi Yield Token",
        symbol: "DYT",
        chains: ["polygon"],
        addresses: {
          polygon: "******************************************"
        },
        deployTime: 9.5,
        gasUsed: 1876543,
        status: "success",
        auditScore: 98.1,
        deployedAt: "2024-01-13T16:45:00Z"
      }
    ],
    totalDeployments: 1247,
    successRate: 99.92,
    avgDeployTime: 12.3,
    gasSavings: 42.1
  },
  securityAudit: {
    certikScore: 96.8,
    vulnerabilities: 0,
    codeCoverage: 94.2,
    auditScore: 98.5,
    status: 'passed',
    reports: [
      { auditor: 'CertiK', date: '2024-01-15', score: 96.8, status: 'passed', issues: 0 },
      { auditor: 'ConsenSys Diligence', date: '2024-01-12', score: 94.2, status: 'passed', issues: 1 },
      { auditor: 'Trail of Bits', date: '2024-01-10', score: 97.5, status: 'passed', issues: 0 }
    ]
  },
  gasOptimization: {
    currentGas: 32,
    optimizedGas: 21,
    savings: 42.1,
    bestTime: '02:00-06:00 UTC',
    predictions: [
      { time: '00:00', standard: 45, optimized: 27 },
      { time: '04:00', standard: 38, optimized: 23 },
      { time: '08:00', standard: 52, optimized: 31 },
      { time: '12:00', standard: 48, optimized: 29 },
      { time: '16:00', standard: 41, optimized: 25 },
      { time: '20:00', standard: 35, optimized: 21 }
    ]
  },
  contractGeneration: {
    template: 'ERC-20',
    features: {
      pausable: true,
      burnable: true,
      mintable: false,
      accessControl: true
    },
    generated: false,
    code: ''
  },
  isLoading: false,
  error: null
}

const strategySlice = createSlice({
  name: 'strategy',
  initialState,
  reducers: {
    updateTokenSettings: (state, action) => {
      state.tokenSettings = { ...state.tokenSettings, ...action.payload }
    },
    updateAllocation: (state, action) => {
      state.tokenSettings.allocation = { ...state.tokenSettings.allocation, ...action.payload }
    },
    setActiveStrategy: (state, action) => {
      state.activeStrategy = action.payload
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchStrategies.pending, (state) => {
        state.isLoading = true
      })
      .addCase(fetchStrategies.fulfilled, (state, action) => {
        state.isLoading = false
        state.strategies = action.payload
      })
      .addCase(fetchStrategies.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.error.message
      })
      .addCase(deployToken.pending, (state) => {
        state.deploymentStatus.isDeploying = true
        state.deploymentStatus.progress = 0
      })
      .addCase(deployToken.fulfilled, (state, action) => {
        state.deploymentStatus.isDeploying = false
        state.deploymentStatus.progress = 100
        state.deploymentStatus.deployedContracts.push(action.payload)
      })
      .addCase(runSecurityAudit.fulfilled, (state, action) => {
        state.securityAudit = { ...state.securityAudit, ...action.payload }
      })
      .addCase(optimizeGas.fulfilled, (state, action) => {
        state.gasOptimization = { ...state.gasOptimization, ...action.payload }
      })
      .addCase(generateContract.fulfilled, (state, action) => {
        state.contractGeneration = { ...state.contractGeneration, ...action.payload, generated: true }
      })
  }
})

export const {
  updateTokenSettings,
  updateAllocation,
  setActiveStrategy
} = strategySlice.actions

export default strategySlice.reducer
