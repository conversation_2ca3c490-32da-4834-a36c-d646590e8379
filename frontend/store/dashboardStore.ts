import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { 
  DashboardState, 
  StatCard, 
  HotCoin, 
  FeatureCard, 
  ChartData,
  MarketData,
  UserData
} from '../types/dashboard';
import {
  FaBrain,
  FaCogs,
  FaCrown,
  FaWallet,
  FaFire,
  FaRobot,
  FaBolt,
  FaChartPie,
  FaSlidersH
} from 'react-icons/fa';

// 初始市场数据
const initialMarketData: MarketData = {
  sentiment: 78.5,
  opportunities: 12,
  riskLevel: 'medium',
  lastUpdate: new Date().toLocaleTimeString(),
  totalVolume: '1.2M SOL',
  activeStrategies: 7
};

// 初始用户数据
const initialUserData: UserData = {
  name: '<PERSON>',
  avatar: 'https://i.pravatar.cc/150?img=8',
  subscription: {
    plan: 'PRO',
    expiryDate: '2023-12-15',
    features: ['AI Analysis', 'Strategy Creation', 'Deployment', 'Liquidity Management']
  },
  wallet: {
    balance: '48.75',
    currency: 'SOL',
    usdValue: '$1,250',
    connected: true
  },
  aiUsage: {
    current: 42,
    total: '∞',
    resetDate: '2023-12-01'
  }
};

// 初始统计卡片数据
const initialStats: StatCard[] = [
  {
    title: 'AI分析使用',
    value: '42/∞',
    icon: FaBrain,
    trend: {
      direction: 'up',
      text: '+12% 本周使用率',
      percentage: 12
    },
    iconBg: 'bg-indigo-500/10',
    iconColor: 'text-indigo-500'
  },
  {
    title: '活跃策略',
    value: 7,
    icon: FaCogs,
    trend: {
      direction: 'up',
      text: '+3.2 SOL 今日收益',
      percentage: 3.2
    },
    iconBg: 'bg-green-500/10',
    iconColor: 'text-green-500'
  },
  {
    title: '参数优化',
    value: '85%',
    icon: FaSlidersH,
    trend: {
      direction: 'up',
      text: '+15% 收益提升',
      percentage: 15
    },
    iconBg: 'bg-purple-500/10',
    iconColor: 'text-purple-500'
  },
  {
    title: '钱包余额',
    value: '48.75 SOL',
    icon: FaWallet,
    trend: {
      direction: 'up',
      text: '≈$1,250 USD',
      percentage: 5.2
    },
    iconBg: 'bg-blue-500/10',
    iconColor: 'text-blue-500'
  }
];

// 初始热门代币数据
const initialHotCoins: HotCoin[] = [
  { 
    symbol: 'DOGE', 
    name: 'Dogecoin', 
    change: 12.4,
    price: '$0.12',
    chartData: [65, 68, 72, 70, 75, 78, 82]
  },
  { 
    symbol: 'SHIB', 
    name: 'Shiba Inu', 
    change: 8.2,
    price: '$0.000021',
    chartData: [45, 48, 52, 55, 58, 60, 62]
  },
  { 
    symbol: 'PEPE', 
    name: 'Pepe Coin', 
    change: -3.1,
    price: '$0.000003',
    chartData: [85, 82, 78, 75, 72, 70, 68]
  },
  { 
    symbol: 'FLOKI', 
    name: 'Floki Inu', 
    change: 5.7,
    price: '$0.000015',
    chartData: [35, 38, 42, 45, 48, 50, 52]
  },
  { 
    symbol: 'BONK', 
    name: 'Bonk', 
    change: 21.8,
    price: '$0.000002',
    chartData: [25, 30, 35, 42, 48, 55, 65]
  }
];

// 初始功能卡片数据
const initialFeatures: FeatureCard[] = [
  {
    title: '热点检测',
    description: '实时监测社交媒体趋势，捕捉病毒式传播的Meme币机会',
    icon: FaFire,
    actionText: '立即扫描',
    actionType: 'primary',
    onClick: () => console.log('热点检测'),
    status: 'active',
    badge: '实时'
  },
  {
    title: '策略生成',
    description: 'AI驱动的交易策略生成，基于市场数据和情绪分析',
    icon: FaRobot,
    actionText: '创建策略',
    actionType: 'secondary',
    onClick: () => console.log('策略生成'),
    status: 'active'
  },
  {
    title: '参数优化',
    description: '智能优化系统参数，提升策略成功率和收益表现',
    icon: FaSlidersH,
    actionText: '优化参数',
    actionType: 'primary',
    onClick: () => console.log('参数优化'),
    status: 'active',
    badge: '智能'
  },
  {
    title: '流动性管理',
    description: '智能管理资金池，优化流动性配置和风险控制',
    icon: FaChartPie,
    actionText: '管理流动性',
    actionType: 'secondary',
    onClick: () => console.log('流动性管理'),
    status: 'active'
  }
];

// 初始图表数据
const initialChartData: ChartData = {
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [
    {
      label: '市场情绪',
      data: [65, 72, 68, 78, 75, 82],
      borderColor: '#6366f1',
      backgroundColor: 'rgba(99, 102, 241, 0.1)',
      tension: 0.4,
      fill: true
    },
    {
      label: '交易量',
      data: [45, 52, 58, 62, 68, 72],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
      fill: true
    },
    {
      label: '策略成功率',
      data: [75, 70, 65, 72, 78, 82],
      borderColor: '#f59e0b',
      backgroundColor: 'rgba(245, 158, 11, 0.1)',
      tension: 0.4,
      fill: true
    }
  ]
};

// 创建仪表盘状态
export const useDashboardStore = create<DashboardState>()(
  devtools(
    (set) => ({
      user: initialUserData,
      market: initialMarketData,
      stats: initialStats,
      hotCoins: initialHotCoins,
      features: initialFeatures,
      chartData: initialChartData,
      isLoading: false,
      lastUpdated: new Date().toISOString(),
      
      // 更新市场数据
      updateMarketData: (data: Partial<MarketData>) => 
        set((state) => ({ 
          market: { ...state.market, ...data },
          lastUpdated: new Date().toISOString()
        })),
      
      // 更新用户数据
      updateUserData: (data: Partial<UserData>) => 
        set((state) => ({ 
          user: { ...state.user, ...data },
          lastUpdated: new Date().toISOString()
        })),
      
      // 更新钱包余额
      updateWalletBalance: (balance: string, usdValue: string) => 
        set((state) => ({
          user: {
            ...state.user,
            wallet: {
              ...state.user.wallet,
              balance,
              usdValue
            }
          },
          lastUpdated: new Date().toISOString()
        })),
      
      // 更新热门代币
      updateHotCoins: (coins: HotCoin[]) => 
        set(() => ({ 
          hotCoins: coins,
          lastUpdated: new Date().toISOString()
        })),
      
      // 更新图表数据
      updateChartData: (data: ChartData) => 
        set(() => ({ 
          chartData: data,
          lastUpdated: new Date().toISOString()
        })),
      
      // 设置加载状态
      setLoading: (isLoading: boolean) => 
        set(() => ({ isLoading }))
    }),
    { name: 'dashboard-store' }
  )
);
