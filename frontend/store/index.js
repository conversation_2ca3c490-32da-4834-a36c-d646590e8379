import { configureStore } from '@reduxjs/toolkit'

// 创建简化的reducers来避免导入错误
const createSimpleReducer = (initialState) => (state = initialState, action) => state

const initialStates = {
  auth: {
    user: null,
    isAuthenticated: false,
    token: null
  },
  system: {
    status: 'online',
    uptime: 3600,
    cpuUsage: 25,
    memoryUsage: 60,
    requestCount: 1234,
    connectionStatus: 'connected',
    serverTime: new Date().toISOString(),
    version: '2.0.0',
    isLoading: false,
    error: null
  },
  hotspot: {
    hotspots: [
      {
        id: 1,
        title: 'DOGE价格突破关键阻力位',
        description: 'DOGE在社交媒体热度推动下突破0.08美元阻力位',
        category: 'politics',
        source: 'Twitter',
        score: 0.85,
        volume: 1000000,
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        language: 'EN'
      },
      {
        id: 2,
        title: 'SHIB社区发起新提案',
        description: 'SHIB社区提议新的销毁机制以减少总供应量',
        category: 'subculture',
        source: 'Reddit',
        score: 0.72,
        volume: 800000,
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        language: 'EN'
      },
      {
        id: 3,
        title: 'PEPE迷因文化爆发',
        description: 'PEPE相关迷因在TikTok上病毒式传播',
        category: 'technology',
        source: 'TikTok',
        score: 0.91,
        volume: 1200000,
        timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        language: 'EN'
      }
    ],
    distribution: {
      politics: 25,
      subculture: 20,
      technology: 18,
      metaverse: 12,
      gamefi: 8,
      nft: 6,
      sports: 11
    },
    sources: {
      twitter: { enabled: true, weight: 0.3 },
      reddit: { enabled: true, weight: 0.25 },
      tiktok: { enabled: true, weight: 0.2 },
      youtube: { enabled: true, weight: 0.15 },
      discord: { enabled: true, weight: 0.1 }
    },
    settings: {
      updateInterval: 30000,
      confidenceThreshold: 0.7,
      enableDarkweb: false,
      darkwebEnabled: false
    },
    mlMetrics: {
      accuracy: 0.785,
      precision: 0.823,
      recall: 0.756
    },
    sentimentIndex: 0.76,
    languageDistribution: {
      en: 45,
      zh: 25,
      ja: 15,
      ko: 10,
      es: 5
    },
    darkwebSignals: [],
    videoAnalysis: [],
    regulatoryAlerts: [],
    predictionAccuracy: 78.5,
    isLoading: false,
    error: null
  },
  strategy: {
    strategies: [
      { id: 1, name: 'DOGE Strategy', status: 'active', profit: 15.2 },
      { id: 2, name: 'SHIB Strategy', status: 'paused', profit: -2.1 },
      { id: 3, name: 'PEPE Strategy', status: 'active', profit: 28.7 }
    ],
    activeStrategy: { id: 1, name: 'DOGE Strategy' },
    isLoading: false,
    error: null
  },
  wallet: {
    wallets: [
      { id: 1, address: '0x123...abc', balance: 100.5, status: 'active' },
      { id: 2, address: '0x456...def', balance: 250.8, status: 'active' },
      { id: 3, address: '0x789...ghi', balance: 75.2, status: 'inactive' }
    ],
    totalBalance: 426.5,
    isLoading: false,
    error: null
  },
  liquidity: {
    currentTVL: 1500000,
    tvlChange24h: 5.2,
    optimalSlippage: 0.45,
    settings: {
      tvlAlertThreshold: 1000000,
      slippageTolerance: 1.0,
      autoRefillEnabled: true,
      mevProtectionLevel: 'medium'
    },
    mevProtectionStats: {
      lossReduction: 84.2,
      flashbotsUsage: 76,
      privateMempool: true,
      randomization: true,
      frontrunDetection: 12
    },
    whaleActivity: [
      { address: '0x1234...5678', action: '大额买入', amount: '$2.4M', impact: '+3.2%', time: '2分钟前' },
      { address: '0x9876...4321', action: '流动性撤出', amount: '$1.8M', impact: '-2.1%', time: '5分钟前' },
      { address: '0xabcd...efgh', action: '大额卖出', amount: '$3.1M', impact: '-4.5%', time: '8分钟前' }
    ],
    autoRefillStatus: {
      active: true,
      lastRefill: '2小时前',
      lastAmount: 500000,
      nextRefill: '4小时后',
      nextAmount: 300000
    },
    slippageOptimization: {
      aggregatorSavings: 23.4,
      oneinchActive: true,
      paraswapActive: true,
      orderSplitting: 3
    },
    pools: [
      { name: 'DOGE/USDT', tvl: 500000, apy: 12.5 },
      { name: 'SHIB/USDT', tvl: 300000, apy: 8.7 },
      { name: 'PEPE/USDT', tvl: 700000, apy: 15.3 }
    ],
    isLoading: false,
    error: null
  },
  exit: {
    signalStrength: 0.75,
    recommendedAction: 'HOLD',
    signals: [
      { type: 'RSI', value: 65, status: 'neutral' },
      { type: 'MACD', value: 0.02, status: 'bullish' },
      { type: 'Volume', value: 1.2, status: 'high' }
    ],
    isLoading: false,
    error: null
  },
  notifications: {
    notifications: [],
    unreadCount: 0
  },
  businessFlow: {
    currentFlow: null,
    currentStep: 'hotspot',
    isProcessing: false,
    autoSettings: {
      enableAutoStrategy: true,
      enableAutoDeploy: false,
      enableAutoLiquidity: true,
      enableAutoExit: false,
      riskLevel: 'medium'
    },
    aiStrategy: null,
    flowHistory: [],
    isLoading: false,
    error: null
  }
}

export const store = configureStore({
  reducer: {
    auth: createSimpleReducer(initialStates.auth),
    system: createSimpleReducer(initialStates.system),
    hotspot: createSimpleReducer(initialStates.hotspot),
    strategy: createSimpleReducer(initialStates.strategy),
    wallet: createSimpleReducer(initialStates.wallet),
    liquidity: createSimpleReducer(initialStates.liquidity),
    exit: createSimpleReducer(initialStates.exit),
    notifications: createSimpleReducer(initialStates.notifications),
    businessFlow: createSimpleReducer(initialStates.businessFlow),
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})
