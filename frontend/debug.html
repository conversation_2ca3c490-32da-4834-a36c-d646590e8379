<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI舆情监控中心 - 数据调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #444;
        }
        .hotspot {
            background: #333;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #4CAF50;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        .category {
            display: inline-block;
            background: #555;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin: 5px 5px 5px 0;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #888;
        }
        .error {
            color: #ff6b6b;
            background: #2a1a1a;
            border: 1px solid #ff6b6b;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success {
            color: #4CAF50;
            background: #1a2a1a;
            border: 1px solid #4CAF50;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #4CAF50;
        }
        .stat-label {
            color: #aaa;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI舆情监控中心 - 数据调试页面</h1>
        
        <div class="card">
            <h2>API连接测试</h2>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="fetchHotspots()">获取热点数据</button>
            <button onclick="fetchAnalytics()">获取分析数据</button>
            <div id="api-status"></div>
        </div>

        <div class="card">
            <h2>数据统计</h2>
            <div class="stats" id="stats">
                <div class="stat-card">
                    <div class="stat-value" id="total-hotspots">-</div>
                    <div class="stat-label">总热点数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="total-volume">-</div>
                    <div class="stat-label">总讨论量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avg-sentiment">-</div>
                    <div class="stat-label">平均情绪</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="market-impact">-</div>
                    <div class="stat-label">市值影响</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>热点数据</h2>
            <div id="hotspots-container">
                <div class="loading">正在加载热点数据...</div>
            </div>
        </div>

        <div class="card">
            <h2>原始数据 (JSON)</h2>
            <pre id="raw-data" style="background: #1a1a1a; padding: 15px; border-radius: 6px; overflow-x: auto; font-size: 12px;"></pre>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';
        let hotspotsData = null;
        let analyticsData = null;

        async function testAPI() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="loading">测试API连接...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    statusDiv.innerHTML = '<div class="success">✅ API连接正常</div>';
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ API响应异常</div>';
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ API连接失败: ${error.message}</div>`;
            }
        }

        async function fetchHotspots() {
            const container = document.getElementById('hotspots-container');
            const rawData = document.getElementById('raw-data');
            
            container.innerHTML = '<div class="loading">正在获取热点数据...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/hotspot/current`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    hotspotsData = data;
                    displayHotspots(data.hotspots);
                    updateStats(data);
                    rawData.textContent = JSON.stringify(data, null, 2);
                } else {
                    container.innerHTML = '<div class="error">❌ 获取热点数据失败</div>';
                }
            } catch (error) {
                container.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }

        async function fetchAnalytics() {
            try {
                const response = await fetch(`${API_BASE}/hotspot/analytics`);
                const data = await response.json();
                analyticsData = data;
                console.log('Analytics data:', data);
            } catch (error) {
                console.error('获取分析数据失败:', error);
            }
        }

        function displayHotspots(hotspots) {
            const container = document.getElementById('hotspots-container');
            
            if (!hotspots || hotspots.length === 0) {
                container.innerHTML = '<div class="error">❌ 没有热点数据</div>';
                return;
            }

            const hotspotsHTML = hotspots.map(hotspot => `
                <div class="hotspot">
                    <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                        <div>
                            <h3 style="margin: 0; color: #4CAF50;">${hotspot.title}</h3>
                            <p style="margin: 5px 0; color: #ccc;">${hotspot.description}</p>
                        </div>
                        <div class="score">${hotspot.score}</div>
                    </div>
                    
                    <div style="margin: 10px 0;">
                        <span class="category">${hotspot.category}</span>
                        <span class="category">情绪: ${(hotspot.sentiment * 100).toFixed(0)}%</span>
                        <span class="category">讨论量: ${(hotspot.volume / 1000).toFixed(0)}K</span>
                        <span class="category">增长: +${(hotspot.growth_rate * 100).toFixed(0)}%</span>
                    </div>
                    
                    <div style="font-size: 12px; color: #888;">
                        <div>关键词: ${hotspot.related_keywords.join(', ')}</div>
                        <div>地区: ${hotspot.region} | 来源: ${hotspot.source}</div>
                        <div>价格影响: <span style="color: #4CAF50;">${hotspot.price_impact}</span> | 市值变化: <span style="color: #4CAF50;">${hotspot.market_cap_change}</span></div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = hotspotsHTML;
        }

        function updateStats(data) {
            document.getElementById('total-hotspots').textContent = data.total_count || 0;
            document.getElementById('total-volume').textContent = data.total_volume ? `${(data.total_volume / 1000).toFixed(0)}K` : '-';
            document.getElementById('avg-sentiment').textContent = data.average_sentiment ? `${(data.average_sentiment * 100).toFixed(0)}%` : '-';
            document.getElementById('market-impact').textContent = data.summary?.total_market_impact ? `$${(data.summary.total_market_impact / 1000000000).toFixed(1)}B` : '-';
        }

        // 页面加载时自动测试
        window.onload = function() {
            testAPI();
            setTimeout(fetchHotspots, 1000);
        };

        // 每30秒自动刷新数据
        setInterval(fetchHotspots, 30000);
    </script>
</body>
</html>
