/* MemeMaster AI 专业数据分析视觉系统 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* 导入MemeMaster AI设计系统 */
@import './styles/mememaster-design-system.css';

/* 导入增强滚动条样式 */
@import './styles/enhanced-scrollbar.css';

/* 导入侧边栏交互修复样式 */
@import './styles/sidebar-interaction-fix.css';

/* 导入策略管理器样式 */
@import './styles/strategy-manager.css';

/* 导入侧边栏文字修复样式 */
@import './styles/sidebar-text-fix.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全站全屏布局修复 - 桌面端优化 */
html, body {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #1a202c;
  color: white;
}

/* 主内容区滚动控制 */
main {
  max-width: 100vw;
  overflow-x: hidden;
}

/* 简单的卡片样式 */
.card {
  background: rgba(45, 55, 72, 0.8);
  border: 1px solid rgba(74, 85, 104, 0.6);
  border-radius: 8px;
  padding: 1rem;
}

/* 按钮样式 */
.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover {
  background: #3182ce;
}

/* 状态指示器 */
.status-online {
  color: #48bb78;
}

.status-offline {
  color: #f56565;
}

.status-warning {
  color: #ed8936;
}
