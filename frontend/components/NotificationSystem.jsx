import React, { useState, useEffect } from 'react'

const NotificationSystem = () => {
  const [notifications, setNotifications] = useState([])

  // 模拟通知数据
  const mockNotifications = [
    {
      id: 1,
      type: 'success',
      title: '策略执行成功',
      message: '策略 #001 已成功执行，收益 +2.4 SOL',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      read: false
    },
    {
      id: 2,
      type: 'info',
      title: '新热点发现',
      message: 'AI检测到 GameFi 板块异常活跃',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      read: false
    },
    {
      id: 3,
      type: 'warning',
      title: '风险提醒',
      message: '钱包余额低于安全阈值',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: true
    }
  ]

  useEffect(() => {
    setNotifications(mockNotifications)
  }, [])

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle'
      case 'warning':
        return 'fas fa-exclamation-triangle'
      case 'error':
        return 'fas fa-times-circle'
      case 'info':
      default:
        return 'fas fa-info-circle'
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'success':
        return 'var(--success)'
      case 'warning':
        return 'var(--warning)'
      case 'error':
        return 'var(--danger)'
      case 'info':
      default:
        return 'var(--info)'
    }
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    
    if (minutes < 1) return '刚刚'
    if (minutes < 60) return `${minutes}分钟前`
    
    const hours = Math.floor(minutes / 60)
    if (hours < 24) return `${hours}小时前`
    
    const days = Math.floor(hours / 24)
    return `${days}天前`
  }

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    )
  }

  const unreadCount = notifications.filter(n => !n.read).length

  return (
    <div className="notification-system">
      {/* 通知面板 */}
      <div 
        className="notification-panel"
        style={{
          position: 'absolute',
          top: '100%',
          right: '0',
          width: '360px',
          maxHeight: '500px',
          background: 'var(--card-bg)',
          backdropFilter: 'blur(20px)',
          border: '1px solid var(--glass-border)',
          borderRadius: 'var(--radius-xl)',
          boxShadow: 'var(--shadow-2xl)',
          zIndex: 1000,
          overflow: 'hidden'
        }}
      >
        {/* 头部 */}
        <div style={{
          padding: 'var(--space-4) var(--space-5)',
          borderBottom: '1px solid var(--glass-border)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <h3 style={{ 
            fontSize: 'var(--text-lg)', 
            fontWeight: 'var(--font-semibold)',
            color: 'var(--text-primary)'
          }}>
            通知中心
          </h3>
          {unreadCount > 0 && (
            <span style={{
              background: 'var(--danger)',
              color: 'white',
              padding: '2px 8px',
              borderRadius: 'var(--radius-full)',
              fontSize: 'var(--text-xs)',
              fontWeight: 'var(--font-semibold)'
            }}>
              {unreadCount}
            </span>
          )}
        </div>

        {/* 通知列表 */}
        <div style={{
          maxHeight: '400px',
          overflowY: 'auto',
          padding: 'var(--space-2)'
        }}>
          {notifications.length === 0 ? (
            <div style={{
              padding: 'var(--space-8)',
              textAlign: 'center',
              color: 'var(--text-tertiary)'
            }}>
              <i className="fas fa-bell-slash" style={{ fontSize: '2rem', marginBottom: '1rem' }}></i>
              <p>暂无通知</p>
            </div>
          ) : (
            notifications.map(notification => (
              <div
                key={notification.id}
                className="notification-item interactive-element"
                onClick={() => markAsRead(notification.id)}
                style={{
                  padding: 'var(--space-4)',
                  margin: 'var(--space-2) 0',
                  borderRadius: 'var(--radius-lg)',
                  background: notification.read ? 'transparent' : 'rgba(67, 56, 202, 0.05)',
                  border: `1px solid ${notification.read ? 'transparent' : 'rgba(67, 56, 202, 0.1)'}`,
                  cursor: 'pointer',
                  transition: 'all var(--duration-normal) var(--ease-out)',
                  position: 'relative'
                }}
              >
                <div style={{ display: 'flex', alignItems: 'flex-start', gap: 'var(--space-3)' }}>
                  {/* 图标 */}
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: 'var(--radius-full)',
                    background: `rgba(${getNotificationColor(notification.type).replace('var(--', '').replace(')', '')}, 0.2)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0
                  }}>
                    <i 
                      className={getNotificationIcon(notification.type)}
                      style={{ 
                        color: getNotificationColor(notification.type),
                        fontSize: '14px'
                      }}
                    ></i>
                  </div>

                  {/* 内容 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div style={{
                      fontSize: 'var(--text-sm)',
                      fontWeight: 'var(--font-semibold)',
                      color: 'var(--text-primary)',
                      marginBottom: 'var(--space-1)'
                    }}>
                      {notification.title}
                    </div>
                    <div style={{
                      fontSize: 'var(--text-sm)',
                      color: 'var(--text-secondary)',
                      lineHeight: 1.4,
                      marginBottom: 'var(--space-2)'
                    }}>
                      {notification.message}
                    </div>
                    <div style={{
                      fontSize: 'var(--text-xs)',
                      color: 'var(--text-tertiary)'
                    }}>
                      {formatTimeAgo(notification.timestamp)}
                    </div>
                  </div>

                  {/* 未读指示器 */}
                  {!notification.read && (
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      background: 'var(--primary)',
                      flexShrink: 0,
                      marginTop: '4px'
                    }}></div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* 底部操作 */}
        <div style={{
          padding: 'var(--space-4)',
          borderTop: '1px solid var(--glass-border)',
          display: 'flex',
          justifyContent: 'space-between'
        }}>
          <button 
            className="btn btn-outline btn-sm"
            onClick={() => setNotifications(prev => prev.map(n => ({ ...n, read: true })))}
          >
            全部已读
          </button>
          <button className="btn btn-primary btn-sm">
            查看全部
          </button>
        </div>
      </div>
    </div>
  )
}

export default NotificationSystem
