import React from 'react';
import { Link } from 'react-router-dom';
import { FaHome, FaExclamationTriangle } from 'react-icons/fa';

const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* 404图标 */}
        <div className="mb-8">
          <div className="w-24 h-24 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaExclamationTriangle className="text-4xl text-red-400" />
          </div>
          <h1 className="text-6xl font-bold text-white mb-2">404</h1>
          <h2 className="text-2xl font-semibold text-gray-300 mb-4">页面未找到</h2>
        </div>

        {/* 错误信息 */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 mb-8">
          <p className="text-gray-400 mb-4">
            抱歉，您访问的页面不存在或已被移动。
          </p>
          <div className="text-sm text-gray-500">
            <p>当前路径: <code className="bg-gray-700 px-2 py-1 rounded">{window.location.pathname}</code></p>
            <p className="mt-2">时间: {new Date().toLocaleString()}</p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="space-y-4">
          <Link 
            to="/"
            className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-400 hover:to-purple-500 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105"
          >
            <FaHome />
            <span>返回首页</span>
          </Link>
          
          <div className="flex justify-center space-x-4 text-sm">
            <Link to="/dashboard" className="text-indigo-400 hover:text-indigo-300 transition-colors">
              仪表盘
            </Link>
            <Link to="/strategy" className="text-indigo-400 hover:text-indigo-300 transition-colors">
              策略管理
            </Link>
            <Link to="/hotspot" className="text-indigo-400 hover:text-indigo-300 transition-colors">
              热点监控
            </Link>
            <Link to="/settings" className="text-indigo-400 hover:text-indigo-300 transition-colors">
              系统设置
            </Link>
          </div>
        </div>

        {/* 调试信息 */}
        <div className="mt-8 p-4 bg-gray-800/30 rounded-xl text-xs text-gray-500">
          <p>如果您认为这是一个错误，请联系技术支持</p>
          <p className="mt-1">错误代码: 404_PAGE_NOT_FOUND</p>
        </div>
      </div>
    </div>
  );
};

export default NotFoundPage;
