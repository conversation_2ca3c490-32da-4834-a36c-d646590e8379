import React from 'react'

// 系统状态指示器
export const SystemStatus = ({ status = 'online', label, showLabel = true }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'online':
        return {
          color: 'var(--success)',
          bgColor: 'rgba(16, 185, 129, 0.1)',
          borderColor: 'rgba(16, 185, 129, 0.3)',
          icon: 'fas fa-check-circle',
          text: '系统正常'
        }
      case 'warning':
        return {
          color: 'var(--warning)',
          bgColor: 'rgba(245, 158, 11, 0.1)',
          borderColor: 'rgba(245, 158, 11, 0.3)',
          icon: 'fas fa-exclamation-triangle',
          text: '需要注意'
        }
      case 'error':
        return {
          color: 'var(--danger)',
          bgColor: 'rgba(239, 68, 68, 0.1)',
          borderColor: 'rgba(239, 68, 68, 0.3)',
          icon: 'fas fa-times-circle',
          text: '系统异常'
        }
      case 'offline':
      default:
        return {
          color: 'var(--text-tertiary)',
          bgColor: 'rgba(107, 114, 128, 0.1)',
          borderColor: 'rgba(107, 114, 128, 0.3)',
          icon: 'fas fa-circle',
          text: '连接中断'
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <div 
      className="status-indicator"
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: 'var(--space-2)',
        padding: 'var(--space-2) var(--space-3)',
        background: config.bgColor,
        border: `1px solid ${config.borderColor}`,
        borderRadius: 'var(--radius-full)',
        fontSize: 'var(--text-sm)',
        fontWeight: 'var(--font-medium)',
        color: config.color
      }}
    >
      <div 
        style={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: config.color,
          animation: status === 'online' ? 'pulse 2s infinite' : 'none'
        }}
      ></div>
      {showLabel && <span>{label || config.text}</span>}
    </div>
  )
}

// 数据趋势指示器
export const TrendIndicator = ({ trend = 'up', value, label }) => {
  const getTrendConfig = (trend) => {
    switch (trend) {
      case 'up':
        return {
          color: 'var(--success)',
          icon: 'fas fa-arrow-up',
          bgColor: 'rgba(16, 185, 129, 0.1)'
        }
      case 'down':
        return {
          color: 'var(--danger)',
          icon: 'fas fa-arrow-down',
          bgColor: 'rgba(239, 68, 68, 0.1)'
        }
      case 'stable':
      default:
        return {
          color: 'var(--text-tertiary)',
          icon: 'fas fa-minus',
          bgColor: 'rgba(107, 114, 128, 0.1)'
        }
    }
  }

  const config = getTrendConfig(trend)

  return (
    <div 
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: 'var(--space-2)',
        padding: 'var(--space-1) var(--space-2)',
        background: config.bgColor,
        borderRadius: 'var(--radius-md)',
        fontSize: 'var(--text-sm)',
        fontWeight: 'var(--font-medium)',
        color: config.color
      }}
    >
      <i className={config.icon} style={{ fontSize: '12px' }}></i>
      <span>{value}</span>
      {label && <span style={{ color: 'var(--text-tertiary)' }}>{label}</span>}
    </div>
  )
}

// 进度指示器
export const ProgressIndicator = ({ 
  value = 0, 
  max = 100, 
  label, 
  showPercentage = true,
  variant = 'primary',
  size = 'md'
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  
  const getVariantColor = (variant) => {
    switch (variant) {
      case 'success':
        return 'var(--success)'
      case 'warning':
        return 'var(--warning)'
      case 'danger':
        return 'var(--danger)'
      case 'primary':
      default:
        return 'var(--primary)'
    }
  }

  const getSizeConfig = (size) => {
    switch (size) {
      case 'sm':
        return { height: '4px', fontSize: 'var(--text-xs)' }
      case 'lg':
        return { height: '12px', fontSize: 'var(--text-base)' }
      case 'md':
      default:
        return { height: '8px', fontSize: 'var(--text-sm)' }
    }
  }

  const sizeConfig = getSizeConfig(size)
  const color = getVariantColor(variant)

  return (
    <div style={{ width: '100%' }}>
      {(label || showPercentage) && (
        <div 
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 'var(--space-2)',
            fontSize: sizeConfig.fontSize,
            color: 'var(--text-secondary)'
          }}
        >
          {label && <span>{label}</span>}
          {showPercentage && <span>{percentage.toFixed(1)}%</span>}
        </div>
      )}
      
      <div 
        style={{
          width: '100%',
          height: sizeConfig.height,
          background: 'var(--bg-tertiary)',
          borderRadius: 'var(--radius-full)',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <div 
          style={{
            width: `${percentage}%`,
            height: '100%',
            background: `linear-gradient(90deg, ${color}, ${color}dd)`,
            borderRadius: 'var(--radius-full)',
            transition: 'width var(--duration-normal) var(--ease-out)',
            position: 'relative'
          }}
        >
          {/* 发光效果 */}
          <div 
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
              animation: percentage > 0 ? 'shimmer 2s infinite' : 'none'
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}

// 活动指示器
export const ActivityIndicator = ({ active = false, label, pulse = true }) => {
  return (
    <div 
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        gap: 'var(--space-2)',
        fontSize: 'var(--text-sm)',
        color: active ? 'var(--success)' : 'var(--text-tertiary)'
      }}
    >
      <div 
        style={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: active ? 'var(--success)' : 'var(--text-tertiary)',
          animation: active && pulse ? 'pulse 2s infinite' : 'none'
        }}
      ></div>
      {label && <span>{label}</span>}
    </div>
  )
}

// 徽章指示器
export const Badge = ({ 
  children, 
  variant = 'primary', 
  size = 'md',
  dot = false,
  count = 0
}) => {
  const getVariantConfig = (variant) => {
    switch (variant) {
      case 'success':
        return {
          bg: 'var(--success)',
          color: 'white'
        }
      case 'warning':
        return {
          bg: 'var(--warning)',
          color: 'white'
        }
      case 'danger':
        return {
          bg: 'var(--danger)',
          color: 'white'
        }
      case 'secondary':
        return {
          bg: 'var(--bg-tertiary)',
          color: 'var(--text-secondary)'
        }
      case 'primary':
      default:
        return {
          bg: 'var(--primary)',
          color: 'white'
        }
    }
  }

  const getSizeConfig = (size) => {
    switch (size) {
      case 'sm':
        return { padding: '2px 6px', fontSize: 'var(--text-xs)' }
      case 'lg':
        return { padding: '6px 12px', fontSize: 'var(--text-base)' }
      case 'md':
      default:
        return { padding: '4px 8px', fontSize: 'var(--text-sm)' }
    }
  }

  const variantConfig = getVariantConfig(variant)
  const sizeConfig = getSizeConfig(size)

  if (dot) {
    return (
      <div 
        style={{
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          backgroundColor: variantConfig.bg
        }}
      ></div>
    )
  }

  return (
    <span 
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: sizeConfig.padding,
        background: variantConfig.bg,
        color: variantConfig.color,
        borderRadius: 'var(--radius-full)',
        fontSize: sizeConfig.fontSize,
        fontWeight: 'var(--font-semibold)',
        lineHeight: 1,
        minWidth: count > 0 ? '20px' : 'auto'
      }}
    >
      {count > 0 ? count : children}
    </span>
  )
}
