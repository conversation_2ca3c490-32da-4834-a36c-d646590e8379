import React, { useState, useEffect } from 'react';
import { FaChartLine, FaRocket, FaBrain, FaFire } from 'react-icons/fa';
import { MarketData, UserData } from '../../types/dashboard';

interface WelcomeBannerProps {
  user: UserData;
  market: MarketData;
  className?: string;
}

const WelcomeBanner: React.FC<WelcomeBannerProps> = ({ 
  user, 
  market, 
  className = '' 
}) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return '早上好';
    if (hour < 18) return '下午好';
    return '晚上好';
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getRiskLevelText = (level: string) => {
    switch (level) {
      case 'low': return '低风险';
      case 'medium': return '中等风险';
      case 'high': return '高风险';
      default: return '未知';
    }
  };

  return (
    <div className={`
      relative overflow-hidden rounded-3xl bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600
      p-8 text-white ${className}
    `}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-black/20"></div>
      <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
      
      {/* 内容 */}
      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          {/* 左侧：问候和信息 */}
          <div className="flex-1 mb-6 lg:mb-0">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                <FaBrain className="text-2xl text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {getGreeting()}，{user.name}! 🚀
                </h1>
                <p className="text-white/80 text-lg">
                  欢迎回到 MemeMaster AI 智能交易系统
                </p>
              </div>
            </div>

            {/* 市场概况 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <FaChartLine className="text-2xl text-green-400" />
                  <div>
                    <p className="text-sm text-white/70">市场情绪</p>
                    <p className="text-xl font-bold text-green-400">{market.sentiment}%</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <FaFire className="text-2xl text-orange-400" />
                  <div>
                    <p className="text-sm text-white/70">交易机会</p>
                    <p className="text-xl font-bold text-orange-400">{market.opportunities}个</p>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center space-x-3">
                  <FaRocket className="text-2xl text-blue-400" />
                  <div>
                    <p className="text-sm text-white/70">活跃策略</p>
                    <p className="text-xl font-bold text-blue-400">{market.activeStrategies}个</p>
                  </div>
                </div>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-white/80">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>实时监控中</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>风险等级:</span>
                <span className={`font-semibold ${getRiskLevelColor(market.riskLevel)}`}>
                  {getRiskLevelText(market.riskLevel)}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span>总交易量:</span>
                <span className="font-semibold text-blue-300">{market.totalVolume}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>最后更新:</span>
                <span className="font-mono">{market.lastUpdate}</span>
              </div>
            </div>
          </div>

          {/* 右侧：操作按钮 */}
          <div className="flex flex-col space-y-4 lg:ml-8">
            <button className="
              bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30
              px-6 py-3 rounded-xl font-semibold transition-all duration-300
              hover:scale-105 hover:shadow-lg flex items-center space-x-2
            ">
              <FaChartLine />
              <span>查看交易机会</span>
            </button>

            <button className="
              bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400
              px-6 py-3 rounded-xl font-semibold transition-all duration-300
              hover:scale-105 hover:shadow-lg flex items-center space-x-2
            ">
              <FaRocket />
              <span>创建新策略</span>
            </button>

            <button className="
              bg-transparent border-2 border-white/30 hover:border-white/50 hover:bg-white/10
              px-6 py-3 rounded-xl font-semibold transition-all duration-300
              hover:scale-105 flex items-center space-x-2
            ">
              <FaBrain />
              <span>AI 分析报告</span>
            </button>
          </div>
        </div>

        {/* 底部状态栏 */}
        <div className="mt-8 pt-6 border-t border-white/20">
          <div className="flex flex-wrap items-center justify-between text-sm text-white/70">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                <span>系统正常运行</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                <span>钱包已连接</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                <span>AI 引擎活跃</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 mt-4 lg:mt-0">
              <span>当前时间: {currentTime.toLocaleTimeString('zh-CN')}</span>
              <span>|</span>
              <span>会员等级: {user.subscription.plan}</span>
            </div>
          </div>
        </div>
      </div>

      {/* 动态粒子效果 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 6 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default WelcomeBanner;
