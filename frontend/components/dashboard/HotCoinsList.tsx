import React, { useState } from 'react';
import { FaFire, FaArrowUp, FaArrowDown, FaEye, FaHeart, FaShare } from 'react-icons/fa';
import { HotCoin } from '../../types/dashboard';

interface HotCoinsListProps {
  coins: HotCoin[];
  loading?: boolean;
  onCoinClick?: (coin: HotCoin) => void;
  className?: string;
}

// 迷你图表组件
const MiniChart: React.FC<{ data: number[]; color: string }> = ({ data, color }) => {
  const max = Math.max(...data);
  const min = Math.min(...data);
  const range = max - min;
  
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * 100;
    const y = range === 0 ? 50 : ((max - value) / range) * 100;
    return `${x},${y}`;
  }).join(' ');

  return (
    <svg width="60" height="30" className="overflow-visible">
      <polyline
        fill="none"
        stroke={color}
        strokeWidth="2"
        points={points}
        className="drop-shadow-sm"
      />
      {/* 渐变填充 */}
      <defs>
        <linearGradient id={`gradient-${color}`} x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stopColor={color} stopOpacity="0.3" />
          <stop offset="100%" stopColor={color} stopOpacity="0" />
        </linearGradient>
      </defs>
      <polyline
        fill={`url(#gradient-${color})`}
        stroke="none"
        points={`0,100 ${points} 100,100`}
      />
    </svg>
  );
};

const HotCoinsList: React.FC<HotCoinsListProps> = ({ 
  coins, 
  loading = false, 
  onCoinClick,
  className = ''
}) => {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  const toggleFavorite = (symbol: string, e: React.MouseEvent) => {
    e.stopPropagation();
    const newFavorites = new Set(favorites);
    if (newFavorites.has(symbol)) {
      newFavorites.delete(symbol);
    } else {
      newFavorites.add(symbol);
    }
    setFavorites(newFavorites);
  };

  const handleShare = (coin: HotCoin, e: React.MouseEvent) => {
    e.stopPropagation();
    if (navigator.share) {
      navigator.share({
        title: `${coin.name} (${coin.symbol})`,
        text: `${coin.name} 价格变化 ${coin.change > 0 ? '+' : ''}${coin.change}%`,
        url: window.location.href
      });
    } else {
      // 复制到剪贴板
      navigator.clipboard.writeText(`${coin.name} (${coin.symbol}) ${coin.change > 0 ? '+' : ''}${coin.change}%`);
    }
  };

  if (loading) {
    return (
      <div className={`bg-gray-800/50 border border-gray-700 rounded-2xl p-6 ${className}`}>
        <div className="flex items-center space-x-3 mb-6">
          <div className="w-8 h-8 bg-gray-700 rounded-lg animate-pulse"></div>
          <div className="w-32 h-6 bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-4 p-4 bg-gray-700/30 rounded-xl animate-pulse">
              <div className="w-10 h-10 bg-gray-600 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="w-20 h-4 bg-gray-600 rounded"></div>
                <div className="w-16 h-3 bg-gray-600 rounded"></div>
              </div>
              <div className="w-16 h-8 bg-gray-600 rounded"></div>
              <div className="w-12 h-6 bg-gray-600 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800/50 border border-gray-700 rounded-2xl p-6 ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-500/10 text-orange-400 rounded-lg">
            <FaFire className="text-lg" />
          </div>
          <h3 className="text-lg font-semibold text-white">热门代币</h3>
        </div>
        
        <button className="text-sm text-indigo-400 hover:text-indigo-300 transition-colors">
          查看全部
        </button>
      </div>

      {/* 代币列表 */}
      <div className="space-y-3">
        {coins.map((coin, index) => {
          const isPositive = coin.change > 0;
          const changeColor = isPositive ? 'text-green-400' : 'text-red-400';
          const bgColor = isPositive ? 'bg-green-500/10' : 'bg-red-500/10';
          const chartColor = isPositive ? '#10b981' : '#ef4444';
          
          return (
            <div
              key={coin.symbol}
              className="group relative flex items-center space-x-4 p-4 rounded-xl bg-gray-700/30 hover:bg-gray-700/50 transition-all duration-300 cursor-pointer hover:scale-[1.02]"
              onClick={() => onCoinClick?.(coin)}
            >
              {/* 排名 */}
              <div className="flex-shrink-0 w-8 text-center">
                <span className="text-sm font-bold text-gray-400">#{index + 1}</span>
              </div>

              {/* 代币图标和信息 */}
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {coin.symbol.slice(0, 2)}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-semibold text-white truncate">{coin.symbol}</h4>
                  <p className="text-xs text-gray-400 truncate">{coin.name}</p>
                </div>
              </div>

              {/* 价格信息 */}
              {coin.price && (
                <div className="text-right">
                  <p className="text-sm font-medium text-white">{coin.price}</p>
                  <p className="text-xs text-gray-400">价格</p>
                </div>
              )}

              {/* 迷你图表 */}
              {coin.chartData && (
                <div className="hidden sm:block">
                  <MiniChart data={coin.chartData} color={chartColor} />
                </div>
              )}

              {/* 变化百分比 */}
              <div className={`flex items-center space-x-1 px-3 py-1 rounded-lg ${bgColor}`}>
                {isPositive ? (
                  <FaArrowUp className={`text-xs ${changeColor}`} />
                ) : (
                  <FaArrowDown className={`text-xs ${changeColor}`} />
                )}
                <span className={`text-sm font-semibold ${changeColor}`}>
                  {Math.abs(coin.change).toFixed(1)}%
                </span>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  className={`p-2 rounded-lg transition-colors ${
                    favorites.has(coin.symbol) 
                      ? 'text-red-400 bg-red-500/10' 
                      : 'text-gray-400 hover:text-red-400 hover:bg-red-500/10'
                  }`}
                  onClick={(e) => toggleFavorite(coin.symbol, e)}
                >
                  <FaHeart className="text-xs" />
                </button>
                
                <button
                  className="p-2 text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 rounded-lg transition-colors"
                  onClick={(e) => handleShare(coin, e)}
                >
                  <FaShare className="text-xs" />
                </button>
                
                <button className="p-2 text-gray-400 hover:text-indigo-400 hover:bg-indigo-500/10 rounded-lg transition-colors">
                  <FaEye className="text-xs" />
                </button>
              </div>

              {/* 悬停效果 */}
              <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none"></div>
            </div>
          );
        })}
      </div>

      {/* 底部统计 */}
      <div className="mt-6 pt-4 border-t border-gray-700">
        <div className="flex items-center justify-between text-sm text-gray-400">
          <span>共 {coins.length} 个热门代币</span>
          <div className="flex items-center space-x-4">
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>上涨: {coins.filter(c => c.change > 0).length}</span>
            </span>
            <span className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
              <span>下跌: {coins.filter(c => c.change < 0).length}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default React.memo(HotCoinsList);
