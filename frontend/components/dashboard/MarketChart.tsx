import React, { useState, useEffect, useRef } from 'react';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  Title, 
  Tooltip, 
  Legend, 
  Filler 
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { ChartData } from '../../types/dashboard';
import { FaChartLine, FaCalendarAlt, FaFilter, FaDownload } from 'react-icons/fa';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface MarketChartProps {
  data: ChartData;
  title?: string;
  loading?: boolean;
  height?: number;
  className?: string;
}

// 防抖函数
const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const MarketChart: React.FC<MarketChartProps> = ({ 
  data, 
  title = '市场情绪与交易趋势', 
  loading = false,
  height = 300,
  className = ''
}) => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [chartData, setChartData] = useState<ChartData>(data);
  const chartRef = useRef<ChartJS>(null);
  
  // 使用防抖处理数据更新
  const debouncedData = useDebounce(data, 300);
  
  useEffect(() => {
    setChartData(debouncedData);
  }, [debouncedData]);

  // 图表配置
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: '#e2e8f0',
          font: {
            family: 'Inter, sans-serif',
            size: 12
          },
          boxWidth: 12,
          usePointStyle: true,
          pointStyle: 'circle'
        }
      },
      tooltip: {
        backgroundColor: 'rgba(17, 24, 39, 0.9)',
        titleColor: '#f3f4f6',
        bodyColor: '#e2e8f0',
        borderColor: 'rgba(99, 102, 241, 0.3)',
        borderWidth: 1,
        padding: 12,
        cornerRadius: 8,
        titleFont: {
          family: 'Inter, sans-serif',
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          family: 'Inter, sans-serif',
          size: 13
        },
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y.toFixed(1);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
          drawBorder: false
        },
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11
          }
        }
      },
      y: {
        grid: {
          color: 'rgba(255, 255, 255, 0.05)',
          drawBorder: false
        },
        ticks: {
          color: '#94a3b8',
          font: {
            family: 'Inter, sans-serif',
            size: 11
          },
          callback: function(value: any) {
            return value;
          }
        }
      }
    },
    elements: {
      line: {
        tension: 0.4
      },
      point: {
        radius: 2,
        hoverRadius: 6,
        borderWidth: 2,
        hoverBorderWidth: 2
      }
    },
    animations: {
      tension: {
        duration: 1000,
        easing: 'linear',
        from: 0.4,
        to: 0.4,
        loop: false
      }
    }
  };

  // 处理时间范围变化
  const handleTimeRangeChange = (range: '7d' | '30d' | '90d') => {
    setTimeRange(range);
    // 这里可以添加根据时间范围获取不同数据的逻辑
  };

  // 导出图表为图片
  const handleExportChart = () => {
    if (chartRef.current) {
      const url = chartRef.current.toBase64Image();
      const link = document.createElement('a');
      link.download = `market-chart-${new Date().toISOString().split('T')[0]}.png`;
      link.href = url;
      link.click();
    }
  };

  if (loading) {
    return (
      <div className={`bg-gray-800/50 border border-gray-700 rounded-2xl p-6 animate-pulse ${className}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="w-48 h-6 bg-gray-700 rounded"></div>
          <div className="flex space-x-2">
            <div className="w-16 h-8 bg-gray-700 rounded"></div>
            <div className="w-16 h-8 bg-gray-700 rounded"></div>
            <div className="w-16 h-8 bg-gray-700 rounded"></div>
          </div>
        </div>
        <div className="w-full h-64 bg-gray-700/50 rounded-xl"></div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800/50 border border-gray-700 rounded-2xl p-6 ${className}`}>
      {/* 头部 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-indigo-500/10 text-indigo-400 rounded-lg">
            <FaChartLine className="text-lg" />
          </div>
          <h3 className="text-lg font-semibold text-white">{title}</h3>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* 时间范围选择 */}
          <div className="flex bg-gray-700/50 rounded-lg p-1">
            <button
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                timeRange === '7d' ? 'bg-indigo-500 text-white' : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => handleTimeRangeChange('7d')}
            >
              7天
            </button>
            <button
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                timeRange === '30d' ? 'bg-indigo-500 text-white' : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => handleTimeRangeChange('30d')}
            >
              30天
            </button>
            <button
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                timeRange === '90d' ? 'bg-indigo-500 text-white' : 'text-gray-400 hover:text-white'
              }`}
              onClick={() => handleTimeRangeChange('90d')}
            >
              90天
            </button>
          </div>
          
          {/* 更多操作 */}
          <div className="flex space-x-2">
            <button className="p-2 bg-gray-700/50 text-gray-400 hover:text-white rounded-lg transition-colors">
              <FaCalendarAlt />
            </button>
            <button className="p-2 bg-gray-700/50 text-gray-400 hover:text-white rounded-lg transition-colors">
              <FaFilter />
            </button>
            <button 
              className="p-2 bg-gray-700/50 text-gray-400 hover:text-white rounded-lg transition-colors"
              onClick={handleExportChart}
            >
              <FaDownload />
            </button>
          </div>
        </div>
      </div>
      
      {/* 图表 */}
      <div style={{ height: `${height}px` }}>
        <Line 
          ref={chartRef}
          data={chartData} 
          options={options} 
        />
      </div>
    </div>
  );
};

export default React.memo(MarketChart);
