import React from 'react';
import { FaArrowUp, FaArrowDown, FaMinus } from 'react-icons/fa';
import { StatCard as StatCardType } from '../../types/dashboard';

interface StatCardProps {
  data: StatCardType;
  loading?: boolean;
  onClick?: () => void;
}

const StatCard: React.FC<StatCardProps> = ({ data, loading = false, onClick }) => {
  const { title, value, icon: Icon, trend, iconBg, iconColor, description } = data;

  const getTrendIcon = () => {
    switch (trend.direction) {
      case 'up':
        return <FaArrowUp className="text-green-500" />;
      case 'down':
        return <FaArrowDown className="text-red-500" />;
      default:
        return <FaMinus className="text-gray-400" />;
    }
  };

  const getTrendColor = () => {
    switch (trend.direction) {
      case 'up':
        return 'text-green-500';
      case 'down':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gray-700 rounded-xl"></div>
          <div className="w-16 h-6 bg-gray-700 rounded"></div>
        </div>
        <div className="space-y-3">
          <div className="w-20 h-8 bg-gray-700 rounded"></div>
          <div className="w-24 h-4 bg-gray-700 rounded"></div>
          <div className="w-32 h-4 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`
        group relative bg-gray-800/50 border border-gray-700 rounded-2xl p-6
        hover:-translate-y-1 hover:border-indigo-500/50 hover:shadow-xl hover:shadow-indigo-500/10
        transition-all duration-300 ease-out cursor-pointer
        ${onClick ? 'hover:bg-gray-800/70' : ''}
      `}
      onClick={onClick}
    >
      {/* 背景渐变效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      
      {/* 内容 */}
      <div className="relative z-10">
        {/* 头部：图标和状态 */}
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-xl ${iconBg} ${iconColor} group-hover:scale-110 transition-transform duration-300`}>
            <Icon className="text-xl" />
          </div>
          
          {/* 状态指示器 */}
          <div className="flex items-center space-x-2">
            {trend.percentage && (
              <span className={`text-sm font-medium ${getTrendColor()}`}>
                {trend.direction === 'up' ? '+' : trend.direction === 'down' ? '-' : ''}
                {Math.abs(trend.percentage)}%
              </span>
            )}
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* 主要数值 */}
        <div className="mb-3">
          <h3 className="text-2xl font-bold text-white mb-1 group-hover:text-indigo-300 transition-colors">
            {value}
          </h3>
          <p className="text-sm text-gray-400 font-medium">{title}</p>
        </div>

        {/* 趋势信息 */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            {getTrendIcon()}
            <span className={`text-sm font-medium ${getTrendColor()}`}>
              {trend.text}
            </span>
          </div>
        </div>

        {/* 描述信息 */}
        {description && (
          <p className="text-xs text-gray-500 mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {description}
          </p>
        )}

        {/* 悬停时的额外信息 */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="w-1 h-8 bg-gradient-to-b from-indigo-500 to-purple-500 rounded-full"></div>
        </div>
      </div>

      {/* 底部装饰线 */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

// 统计卡片网格容器
interface StatCardsGridProps {
  cards: StatCardType[];
  loading?: boolean;
  onCardClick?: (cardId: string) => void;
}

export const StatCardsGrid: React.FC<StatCardsGridProps> = ({ 
  cards, 
  loading = false, 
  onCardClick 
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
      {loading ? (
        // 加载状态
        Array.from({ length: 4 }).map((_, index) => (
          <StatCard 
            key={index} 
            data={{} as StatCardType} 
            loading={true} 
          />
        ))
      ) : (
        // 正常状态
        cards.map((card, index) => (
          <StatCard 
            key={card.title} 
            data={card} 
            onClick={() => onCardClick?.(card.title)}
          />
        ))
      )}
    </div>
  );
};

export default StatCard;
