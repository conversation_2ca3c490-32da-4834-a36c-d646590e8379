import React from 'react';
import { FeatureCard as FeatureCardType } from '../../types/dashboard';

interface FeatureCardProps {
  data: FeatureCardType;
  loading?: boolean;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ data, loading = false }) => {
  const { title, description, icon: Icon, actionText, actionType, onClick, status, badge } = data;

  const getStatusColor = () => {
    switch (status) {
      case 'active': return 'border-green-500/30 bg-green-500/5';
      case 'inactive': return 'border-gray-500/30 bg-gray-500/5';
      case 'pending': return 'border-yellow-500/30 bg-yellow-500/5';
      default: return 'border-gray-700';
    }
  };

  const getButtonStyle = () => {
    if (actionType === 'primary') {
      return 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-400 hover:to-purple-500 text-white';
    }
    return 'bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white border border-gray-600';
  };

  if (loading) {
    return (
      <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gray-700 rounded-xl"></div>
          <div className="w-16 h-6 bg-gray-700 rounded"></div>
        </div>
        <div className="space-y-3">
          <div className="w-32 h-6 bg-gray-700 rounded"></div>
          <div className="w-full h-12 bg-gray-700 rounded"></div>
          <div className="w-24 h-8 bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`
      group relative bg-gray-800/50 border rounded-2xl p-6
      hover:-translate-y-1 hover:border-indigo-500/50 hover:shadow-xl hover:shadow-indigo-500/10
      transition-all duration-300 ease-out cursor-pointer
      ${getStatusColor()}
    `}>
      {/* 背景渐变效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      
      {/* 内容 */}
      <div className="relative z-10">
        {/* 头部：图标和徽章 */}
        <div className="flex items-center justify-between mb-4">
          <div className="p-3 bg-indigo-500/10 text-indigo-400 rounded-xl group-hover:scale-110 transition-transform duration-300">
            <Icon className="text-2xl" />
          </div>
          
          {badge && (
            <span className="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs font-medium rounded-lg border border-orange-500/30">
              {badge}
            </span>
          )}
        </div>

        {/* 标题和描述 */}
        <div className="mb-6">
          <h3 className="text-xl font-bold text-white mb-3 group-hover:text-indigo-300 transition-colors">
            {title}
          </h3>
          <p className="text-sm text-gray-400 leading-relaxed">
            {description}
          </p>
        </div>

        {/* 操作按钮 */}
        <button
          onClick={onClick}
          className={`
            w-full px-4 py-3 rounded-xl font-semibold transition-all duration-300
            hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2
            ${getButtonStyle()}
          `}
        >
          <span>{actionText}</span>
        </button>

        {/* 状态指示器 */}
        {status && (
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className={`w-2 h-2 rounded-full ${
              status === 'active' ? 'bg-green-500' : 
              status === 'pending' ? 'bg-yellow-500' : 'bg-gray-500'
            } animate-pulse`}></div>
          </div>
        )}
      </div>

      {/* 底部装饰线 */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
};

// 功能卡片网格容器
interface FeatureCardsGridProps {
  cards: FeatureCardType[];
  loading?: boolean;
}

export const FeatureCardsGrid: React.FC<FeatureCardsGridProps> = ({ 
  cards, 
  loading = false 
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {loading ? (
        // 加载状态
        Array.from({ length: 4 }).map((_, index) => (
          <FeatureCard 
            key={index} 
            data={{} as FeatureCardType} 
            loading={true} 
          />
        ))
      ) : (
        // 正常状态
        cards.map((card) => (
          <FeatureCard 
            key={card.title} 
            data={card} 
          />
        ))
      )}
    </div>
  );
};

export default FeatureCard;
