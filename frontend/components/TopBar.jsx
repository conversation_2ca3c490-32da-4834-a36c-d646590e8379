import React, { useEffect, useState, useRef } from 'react'
import {
  Wifi,
  Clock,
  Server,
  AlertTriangle,
  Zap,
  RefreshCw,
  User,
  UserCog,
  Settings,
  HelpCircle
} from 'lucide-react'

const TopBar = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [user, setUser] = useState(null)
  const [showUserMenu, setShowUserMenu] = useState(false)

  // 创建ref用于检测点击外部
  const userMenuRef = useRef(null)

  // 模拟系统状态
  const status = 'online'
  const connectionStatus = 'connected'

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    // 检查用户登录状态
    const checkUserStatus = () => {
      const token = localStorage.getItem('access_token')
      const userInfo = localStorage.getItem('user_info')

      if (token && userInfo) {
        try {
          setUser(JSON.parse(userInfo))
        } catch (error) {
          console.error('解析用户信息失败:', error)
          localStorage.removeItem('access_token')
          localStorage.removeItem('user_info')
        }
      }
    }

    checkUserStatus()

    return () => clearInterval(timer)
  }, [])

  // 点击外部关闭用户菜单的逻辑
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false)
      }
    }

    // 只有当用户菜单打开时才添加事件监听器
    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu])

  const handleRefresh = () => {
    console.log('刷新系统状态')
  }



  const getUserBadge = (role) => {
    const badges = {
      free: { color: 'bg-gray-500', label: '免费' },
      pro: { color: 'bg-blue-500', label: 'Pro' },
      enterprise: { color: 'bg-purple-500', label: '企业' }
    }
    return badges[role] || badges.free
  }

  const getStatusColor = () => {
    switch (status) {
      case 'online':
        return 'text-green-400'
      case 'offline':
        return 'text-red-400'
      default:
        return 'text-yellow-400'
    }
  }

  const getConnectionIcon = () => {
    return <Wifi className="w-4 h-4 text-green-400" />
  }

  return (
    <div
      className="px-6 py-4 relative z-30"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        backdropFilter: 'blur(24px)',
        WebkitBackdropFilter: 'blur(24px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.15)'
      }}
    >
      <div className="flex items-center justify-between">
        {/* 左侧：系统状态 */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2">
            <div className={`status-indicator ${status === 'online' ? 'status-online' : 'status-offline'}`}></div>
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              系统{status === 'online' ? '运行正常' : '离线'}
            </span>
          </div>

          <div className="flex items-center space-x-2 text-white/70">
            {getConnectionIcon()}
            <span className="text-sm">
              网络{connectionStatus === 'connected' ? '已连接' : '断开'}
            </span>
          </div>

          <div className="flex items-center space-x-2 text-white/70">
            <Server className="w-4 h-4" />
            <span className="text-sm">168.100.11.142:8000</span>
          </div>
        </div>

        {/* 中间：时间信息 */}
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-2 text-white/70">
            <Clock className="w-4 h-4" />
            <span className="text-sm">
              {currentTime.toLocaleTimeString('zh-CN')}
            </span>
          </div>

          <div className="text-xs text-white/50">
            服务器时间: {currentTime.toLocaleTimeString('zh-CN')}
          </div>
        </div>

        {/* 右侧：用户信息和快速操作 */}
        <div className="flex items-center space-x-3">
          {/* 用户信息 */}
          {user ? (
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(true)}
                className="flex items-center space-x-2 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
              >
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div className="text-left">
                  <div className="text-sm text-white font-medium">{user.username}</div>
                  <div className={`text-xs px-2 py-0.5 rounded-full text-white ${getUserBadge(user.role).color}`}>
                    {getUserBadge(user.role).label}
                  </div>
                </div>
              </button>

              {/* 用户菜单 */}
              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                  <a
                    href="/account"
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <UserCog className="w-4 h-4 mr-2" />
                    账户管理
                  </a>
                  <a
                    href="/settings"
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    系统设置
                  </a>
                  <a
                    href="/help"
                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <HelpCircle className="w-4 h-4 mr-2" />
                    帮助中心
                  </a>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <a
                href="/login"
                className="px-3 py-1 text-sm text-white/70 hover:text-white transition-colors"
              >
                登录
              </a>
              <a
                href="/register"
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                注册
              </a>
            </div>
          )}

          <button
            onClick={handleRefresh}
            className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors duration-200"
            title="刷新数据"
          >
            <RefreshCw className="w-4 h-4 text-white/70" />
          </button>

          <button
            className="p-2 rounded-lg bg-yellow-500/20 hover:bg-yellow-500/30 transition-colors duration-200"
            title="熔断"
          >
            <AlertTriangle className="w-4 h-4 text-yellow-400" />
          </button>

          <button
            className="p-2 rounded-lg bg-red-500/20 hover:bg-red-500/30 transition-colors duration-200"
            title="紧急停止"
          >
            <Zap className="w-4 h-4 text-red-400" />
          </button>
        </div>
      </div>
    </div>
  )
}

export default TopBar
