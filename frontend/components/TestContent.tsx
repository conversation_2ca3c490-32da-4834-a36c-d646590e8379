import React from 'react';

const TestContent: React.FC = () => {
  return (
    <div className="p-8">
      <h1 className="text-4xl font-bold text-white mb-4">测试页面</h1>
      <p className="text-gray-400 mb-6">这是一个测试页面，用于验证GlobalLayout是否正常工作。</p>
      
      <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
        <h2 className="text-2xl font-bold text-white mb-4">功能测试</h2>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-white">GlobalLayout 加载成功</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-white">路由系统正常工作</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-white">Tailwind CSS 样式正常</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-white">TypeScript 编译正常</span>
          </div>
        </div>
      </div>

      <div className="mt-8 bg-indigo-500/10 border border-indigo-500/30 rounded-2xl p-6">
        <h3 className="text-xl font-bold text-indigo-400 mb-3">调试信息</h3>
        <div className="text-sm text-gray-300 space-y-2">
          <p>当前时间: {new Date().toLocaleString()}</p>
          <p>用户代理: {navigator.userAgent}</p>
          <p>屏幕尺寸: {window.innerWidth} x {window.innerHeight}</p>
          <p>当前路径: {window.location.pathname}</p>
        </div>
      </div>
    </div>
  );
};

export default TestContent;
