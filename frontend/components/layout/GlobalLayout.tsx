import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';

interface GlobalLayoutProps {
  children?: React.ReactNode;
}

const GlobalLayout: React.FC<GlobalLayoutProps> = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const location = useLocation();

  // 响应式处理
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      const tablet = window.innerWidth < 1200;
      
      setIsMobile(mobile);
      
      if (mobile) {
        setSidebarCollapsed(true);
        setSidebarOpen(false);
      } else {
        setSidebarCollapsed(tablet);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 路由变化时关闭移动端侧边栏
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);

  // 切换侧边栏
  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  return (
    <div className="h-screen w-screen flex bg-gradient-to-br from-gray-900 to-gray-800 overflow-hidden">
      {/* 侧边栏 */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        isMobile={isMobile}
        isOpen={sidebarOpen}
      />

      {/* 主内容区 */}
      <div className="flex-1 flex flex-col relative min-w-0">
        {/* 主要内容 - 可滚动区域，内容可以滚动到header下方 */}
        <main className="flex-1 overflow-y-auto overflow-x-hidden enhanced-scrollbar relative">
          {/* 顶部导航 - 固定在内容区顶部，半透明 */}
          <Header
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
            isMobile={isMobile}
            className="sticky top-0 z-50 w-full"
          />

          <div className="p-3 sm:p-4 lg:p-6 xl:p-8 min-h-full">
            {/* 使用Outlet渲染子路由内容 */}
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default React.memo(GlobalLayout);
