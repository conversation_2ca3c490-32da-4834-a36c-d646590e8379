import React, { useState, useRef, useEffect } from 'react';
import {
  FaBars,
  FaSearch,
  FaBell,
  FaUserCog,
  FaQuestionCircle,
  FaChevronDown,
  FaWallet,
  FaCog,
  FaGlobe
} from 'react-icons/fa';
import { BaseComponentProps } from '../../types/dashboard';

interface HeaderProps extends BaseComponentProps {
  onToggleSidebar?: () => void;
  sidebarCollapsed?: boolean;
  isMobile?: boolean;
  style?: React.CSSProperties;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  read: boolean;
}

const Header: React.FC<HeaderProps> = ({
  onToggleSidebar,
  sidebarCollapsed = false,
  isMobile = false,
  className = '',
  style = {}
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // 创建refs用于检测点击外部
  const notificationsRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  const notifications: Notification[] = [
    {
      id: '1',
      title: '新的交易机会',
      message: 'DOGE 价格上涨 12.4%，建议关注',
      type: 'success',
      timestamp: '2分钟前',
      read: false
    },
    {
      id: '2',
      title: '策略执行完成',
      message: '策略 #007 已成功执行，收益 **** SOL',
      type: 'success',
      timestamp: '5分钟前',
      read: false
    },
    {
      id: '3',
      title: '风险提醒',
      message: '市场波动较大，建议调整仓位',
      type: 'warning',
      timestamp: '10分钟前',
      read: true
    }
  ];

  const unreadCount = notifications.filter(n => !n.read).length;

  // 点击外部关闭浮窗的逻辑
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查通知浮窗
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }

      // 检查用户菜单浮窗
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    // 只有当浮窗打开时才添加事件监听器
    if (showNotifications || showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotifications, showUserMenu]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('搜索:', searchQuery);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  };

  return (
    <header
      className={`
        sticky top-0 z-40 transition-all duration-300 w-full ${className}
      `}
      style={{
        // 增强的半透明毛玻璃效果
        backgroundColor: 'rgba(17, 24, 39, 0.6)', // 降低不透明度到60%
        backdropFilter: 'blur(20px) saturate(180%) brightness(1.1)',
        WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(1.1)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        // 合并传入的style属性
        ...style
      }}
    >
      <div className="flex items-center justify-between px-3 sm:px-4 lg:px-6 py-3 lg:py-4">
        {/* 左侧：菜单按钮和搜索 */}
        <div className="flex items-center space-x-2 lg:space-x-4">
          {/* 菜单切换按钮 */}
          <button
            onClick={onToggleSidebar}
            className="p-2 rounded-lg hover:bg-gray-700 transition-colors text-gray-400 hover:text-white"
          >
            <FaBars className="text-lg" />
          </button>

          {/* 搜索框 - 桌面端显示 */}
          {!isMobile && (
            <form onSubmit={handleSearch} className="relative block">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm z-10" />
                <input
                  type="text"
                  placeholder="搜索代币、策略..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="
                    w-48 md:w-56 lg:w-64 xl:w-80 pl-10 pr-4 py-2 rounded-xl
                    text-white placeholder-gray-400 focus:outline-none focus:ring-2
                    focus:ring-indigo-500 focus:border-transparent transition-all
                  "
                  style={{
                    backgroundColor: 'rgba(31, 41, 55, 0.7)',
                    border: 'none',
                    backdropFilter: 'blur(12px) saturate(150%)',
                    WebkitBackdropFilter: 'blur(12px) saturate(150%)',
                    minHeight: '40px',
                    fontSize: '14px',
                    boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
                  }}
                />
              </div>
            </form>
          )}
        </div>

        {/* 右侧：通知、用户菜单 */}
        <div className="flex items-center space-x-2 lg:space-x-4">
          {/* 网络状态 */}
          <div className="hidden lg:flex items-center space-x-2 px-3 py-2 bg-green-500/10 border border-green-500/20 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-400">Mainnet</span>
            <FaGlobe className="text-green-400 text-sm" />
          </div>

          {/* 钱包连接状态 */}
          <div className="hidden lg:flex items-center space-x-2 px-3 py-2 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <FaWallet className="text-blue-400 text-sm" />
            <span className="text-sm text-blue-400">48.75 SOL</span>
          </div>

          {/* 通知铃铛 */}
          <div className="relative" ref={notificationsRef}>
            <button
              onClick={() => setShowNotifications(true)}
              className="relative p-2 rounded-lg hover:bg-gray-700 transition-colors text-gray-400 hover:text-white"
            >
              <FaBell className="text-lg" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount}
                </span>
              )}
            </button>

            {/* 通知下拉菜单 */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="text-lg font-semibold text-white">通知</h3>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div 
                      key={notification.id}
                      className={`p-4 border-b border-gray-700 hover:bg-gray-700/50 transition-colors ${
                        !notification.read ? 'bg-indigo-500/5' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <span className="text-lg">{getNotificationIcon(notification.type)}</span>
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                          <p className="text-sm text-gray-400 mt-1">{notification.message}</p>
                          <p className="text-xs text-gray-500 mt-2">{notification.timestamp}</p>
                        </div>
                        {!notification.read && (
                          <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-4 border-t border-gray-700">
                  <button className="w-full text-center text-sm text-indigo-400 hover:text-indigo-300 transition-colors">
                    查看所有通知
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 用户头像和菜单 */}
          <div className="relative" ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(true)}
              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <img 
                src="https://i.pravatar.cc/32?img=8" 
                alt="User Avatar" 
                className="w-8 h-8 rounded-full border-2 border-indigo-500"
              />
              {!isMobile && (
                <>
                  <div className="text-left">
                    <p className="text-sm font-medium text-white">John Doe</p>
                    <p className="text-xs text-gray-400">PRO 会员</p>
                  </div>
                  <FaChevronDown className="text-gray-400 text-sm" />
                </>
              )}
            </button>

            {/* 用户下拉菜单 */}
            {showUserMenu && (
              <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50">
                <div className="p-4 border-b border-gray-700">
                  <div className="flex items-center space-x-3">
                    <img 
                      src="https://i.pravatar.cc/40?img=8" 
                      alt="User Avatar" 
                      className="w-10 h-10 rounded-full border-2 border-indigo-500"
                    />
                    <div>
                      <p className="text-sm font-medium text-white">John Doe</p>
                      <p className="text-xs text-gray-400"><EMAIL></p>
                    </div>
                  </div>
                </div>
                
                <div className="py-2">
                  <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-700 transition-colors">
                    <FaUserCog className="text-gray-400" />
                    <span className="text-sm text-white">账户管理</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-700 transition-colors">
                    <FaCog className="text-gray-400" />
                    <span className="text-sm text-white">系统设置</span>
                  </button>
                  <button className="w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-700 transition-colors">
                    <FaQuestionCircle className="text-gray-400" />
                    <span className="text-sm text-white">帮助中心</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 移动端搜索框 */}
      {isMobile && (
        <div className="px-3 sm:px-4 lg:px-6 pb-3 md:hidden">
          <form onSubmit={handleSearch} className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm z-10" />
            <input
              type="text"
              placeholder="搜索代币、策略..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="
                w-full pl-10 pr-4 py-2 rounded-xl
                text-white placeholder-gray-400 focus:outline-none focus:ring-2
                focus:ring-indigo-500 focus:border-transparent transition-all
              "
              style={{
                backgroundColor: 'rgba(31, 41, 55, 0.7)',
                border: 'none',
                backdropFilter: 'blur(12px) saturate(150%)',
                WebkitBackdropFilter: 'blur(12px) saturate(150%)',
                minHeight: '40px',
                fontSize: '14px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
              }}
            />
          </form>
        </div>
      )}
    </header>
  );
};

export default Header;
