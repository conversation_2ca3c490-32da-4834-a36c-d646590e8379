import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaHome,
  FaFire,
  FaCogs,
  FaRocket,
  FaChartLine,
  FaBullseye,
  FaRobot,
  FaUserCog,
  FaCog,
  FaQuestionCircle,
  FaTimes
} from 'react-icons/fa';
import { BaseComponentProps } from '../../types/dashboard';

interface SidebarProps extends BaseComponentProps {
  collapsed?: boolean;
  onToggle?: () => void;
  isMobile?: boolean;
  isOpen?: boolean;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  href: string;
  active?: boolean;
  badge?: string;
}

const Sidebar: React.FC<SidebarProps> = ({
  collapsed = false,
  onToggle,
  isMobile = false,
  isOpen = false,
  className = ''
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeItem, setActiveItem] = useState('dashboard');

  const navItems: NavItem[] = [
    { id: 'dashboard', label: '仪表盘', icon: FaHome, href: '/', active: true },
    { id: 'hotspot', label: '趋势热点', icon: FaFire, href: '/hotspot', badge: '12' },
    { id: 'strategy', label: '策略引擎', icon: FaCogs, href: '/strategy' },
    { id: 'deploy', label: '一键部署', icon: FaRocket, href: '/deploy' },
    { id: 'liquidity', label: '流动性管理', icon: FaChartLine, href: '/liquidity' },
    { id: 'exit', label: '动态退出', icon: FaBullseye, href: '/exit' },
    { id: 'automation', label: '自动化工具', icon: FaRobot, href: '/automation' },
  ];

  const bottomNavItems: NavItem[] = [
    { id: 'account', label: '账户管理', icon: FaUserCog, href: '/account' },
    { id: 'settings', label: '系统设置', icon: FaCog, href: '/settings' },
    { id: 'help', label: '帮助中心', icon: FaQuestionCircle, href: '/help' },
  ];

  // 根据当前路径设置活跃状态
  useEffect(() => {
    const currentPath = location.pathname;
    const allItems = [...navItems, ...bottomNavItems];
    const currentItem = allItems.find(item => item.href === currentPath);
    if (currentItem) {
      setActiveItem(currentItem.id);
    } else if (currentPath === '/') {
      setActiveItem('dashboard');
    }
  }, [location.pathname]);

  // 处理屏幕尺寸变化
  useEffect(() => {
    const handleResize = () => {
      // 在移动端自动关闭侧边栏
      if (window.innerWidth < 768 && isOpen) {
        onToggle?.();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen, onToggle]);

  const handleNavClick = (itemId: string) => {
    // 找到对应的菜单项
    const allItems = [...navItems, ...bottomNavItems];
    const targetItem = allItems.find(item => item.id === itemId);

    if (targetItem) {
      // 设置活跃状态
      setActiveItem(itemId);
      // 进行路由跳转
      navigate(targetItem.href);
    }

    // 在移动端点击导航项后关闭侧边栏
    if (isMobile && isOpen) {
      onToggle?.();
    }
  };

  const sidebarClasses = `
    ${isMobile ? 'fixed left-0 top-0 h-screen z-50' : 'relative h-screen'}
    bg-gradient-to-b from-gray-950 to-slate-900
    transition-all duration-300 ease-in-out flex flex-col
    ${isMobile ? 'w-full' : collapsed ? 'w-16 lg:w-20' : 'w-56 lg:w-64'}
    ${isMobile ? (isOpen ? 'translate-x-0' : '-translate-x-full') : 'translate-x-0'}
    flex-shrink-0
    ${className}
  `;

  return (
    <>
      {/* 移动端遮罩 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* 侧边栏 */}
      <div className={sidebarClasses}>
        {/* 头部 */}
        <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-800 flex-shrink-0">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <FaRobot className="text-white text-xl" />
            </div>
            {(!collapsed || isMobile) && (
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                  MemeX
                </h1>
              </div>
            )}
          </div>
          
          {/* 移动端关闭按钮 */}
          {isMobile && (
            <button
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <FaTimes className="text-gray-400" />
            </button>
          )}
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-hidden">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeItem === item.id;

            return (
              <button
                key={item.id}
                onClick={() => handleNavClick(item.id)}
                className={`
                  w-full flex items-center space-x-3 px-4 py-3 rounded-xl
                  transition-all duration-200 group relative focus:outline-none
                  ${isActive
                    ? 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
                  }
                `}
              >
                <Icon className={`text-lg ${isActive ? 'text-indigo-400' : ''}`} />

                {(!collapsed || isMobile) && (
                  <>
                    <span className="font-medium">{item.label}</span>
                    {item.badge && (
                      <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </>
                )}

                {/* 折叠状态下的提示 */}
                {collapsed && !isMobile && (
                  <div className="absolute left-full ml-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                    {item.label}
                    {item.badge && (
                      <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </div>
                )}
              </button>
            );
          })}
        </nav>

        {/* 底部菜单 */}
        <div className="px-4 py-4 space-y-2 relative flex-shrink-0">
          {/* 自定义分隔线 - 两边保留10px间隔 */}
          <div className="absolute top-0 left-2.5 right-2.5 h-px bg-gray-800"></div>
          {bottomNavItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeItem === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => handleNavClick(item.id)}
                className={`
                  w-full flex items-center space-x-3 px-4 py-3 rounded-xl
                  transition-all duration-200 group relative focus:outline-none
                  ${isActive
                    ? 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/60'
                  }
                `}
              >
                <Icon className={`text-lg ${isActive ? 'text-indigo-400' : ''}`} />
                
                {(!collapsed || isMobile) && (
                  <span className="font-medium">{item.label}</span>
                )}

                {/* 折叠状态下的提示 */}
                {collapsed && !isMobile && (
                  <div className="absolute left-full ml-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                    {item.label}
                  </div>
                )}
              </button>
            );
          })}
        </div>


      </div>
    </>
  );
};

export default Sidebar;
