import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  TrendingUp,
  Zap,
  Droplets,
  LogOut,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  Sliders
} from 'lucide-react'
import OptimizationPanel from './OptimizationPanel'

// 创建模拟的action函数
const deployMemeToken = (payload) => ({ type: 'DEPLOY_MEME_TOKEN', payload })
const updateAutoSettings = (payload) => ({ type: 'UPDATE_AUTO_SETTINGS', payload })

const BusinessFlowCenter = () => {
  const dispatch = useDispatch()
  // 安全地获取Redux状态，提供默认值
  const {
    currentFlow = null,
    currentStep = 'hotspot',
    isProcessing = false,
    autoSettings = {
      enableAutoStrategy: true,
      enableAutoDeploy: false,
      enableAutoLiquidity: true,
      enableAutoExit: false,
      riskLevel: 'medium'
    },
    aiStrategy = null
  } = useSelector(state => state.businessFlow || {})
  
  const [showSettings, setShowSettings] = useState(false)
  const [showOptimization, setShowOptimization] = useState(false)

  // 流程步骤配置
  const flowSteps = [
    { id: 'hotspot', name: '舆情监测', icon: TrendingUp, color: 'text-blue-400' },
    { id: 'strategy', name: 'AI策略生成', icon: Zap, color: 'text-yellow-400' },
    { id: 'deployment', name: '一键部署', icon: CheckCircle, color: 'text-purple-400' },
    { id: 'liquidity', name: '流动性控制', icon: Droplets, color: 'text-green-400' },
    { id: 'exit', name: '退出策略', icon: LogOut, color: 'text-red-400' }
  ]

  // 处理AI策略生成完成
  useEffect(() => {
    if (aiStrategy && autoSettings.enableAutoDeploy && currentStep === 'deployment') {
      // 自动部署
      setTimeout(() => {
        dispatch(deployMemeToken({
          strategy: aiStrategy,
          platform: aiStrategy.recommendedPlatform || 'pump.fun'
        }))
      }, 1000)
    }
  }, [aiStrategy, autoSettings.enableAutoDeploy, currentStep, dispatch])

  // 获取步骤状态
  const getStepStatus = (stepId) => {
    if (!currentFlow) return 'pending'

    const stepIndex = flowSteps.findIndex(s => s.id === stepId)
    const currentIndex = flowSteps.findIndex(s => s.id === currentStep)

    if (stepIndex < currentIndex) return 'completed'
    if (stepIndex === currentIndex) return isProcessing ? 'processing' : 'active'
    return 'pending'
  }



  // 渲染步骤状态图标
  const renderStepIcon = (step, status) => {
    const IconComponent = step.icon
    
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-400" />
      case 'processing':
        return <Clock className="w-6 h-6 text-yellow-400 animate-spin" />
      case 'active':
        return <IconComponent className={`w-6 h-6 ${step.color}`} />
      default:
        return <IconComponent className="w-6 h-6 text-gray-400" />
    }
  }

  return (
    <div className="space-y-6">
      {/* 业务流程控制面板 */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white">业务流程控制中心</h2>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowOptimization(!showOptimization)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Sliders className="w-4 h-4" />
              <span>参数优化</span>
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>自动化设置</span>
            </button>
            
            {currentFlow ? (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-green-400 text-sm">流程运行中</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                <span className="text-gray-400 text-sm">等待热点触发</span>
              </div>
            )}
          </div>
        </div>

        {/* 流程步骤可视化 */}
        <div className="relative">
          <div className="flex items-center justify-between">
            {flowSteps.map((step, index) => {
              const status = getStepStatus(step.id)
              
              return (
                <div key={step.id} className="flow-step-container">
                  {/* 步骤图标 */}
                  <div className={`flow-step-icon ${
                    status === 'completed' ? 'completed' :
                    status === 'processing' ? 'processing' :
                    status === 'active' ? 'active' :
                    'pending'
                  }`}>
                    {renderStepIcon(step, status)}
                  </div>
                  
                  {/* 步骤名称 */}
                  <div className="text-center">
                    <div className={`text-sm font-medium ${
                      status === 'completed' ? 'text-green-400' :
                      status === 'processing' || status === 'active' ? step.color :
                      'text-gray-400'
                    }`}>
                      {step.name}
                    </div>
                    <div className="text-xs text-white/50">
                      {status === 'completed' ? '已完成' :
                       status === 'processing' ? '处理中' :
                       status === 'active' ? '进行中' : '等待中'}
                    </div>
                  </div>
                  
                  {/* 连接线 */}
                  {index < flowSteps.length - 1 && (
                    <div className={`flow-connection-line ${
                      getStepStatus(flowSteps[index + 1].id) === 'completed' ? 'completed' : 'pending'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* 当前流程信息 */}
        {currentFlow && (
          <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
            <h3 className="text-white font-medium mb-2">当前流程信息</h3>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div>
                <div className="text-white/60">流程ID</div>
                <div className="text-blue-400 font-mono">{currentFlow.id}</div>
              </div>
              <div>
                <div className="text-white/60">开始时间</div>
                <div className="text-white">{new Date(currentFlow.startTime).toLocaleTimeString()}</div>
              </div>
              <div>
                <div className="text-white/60">当前步骤</div>
                <div className="text-yellow-400">{flowSteps.find(s => s.id === currentStep)?.name}</div>
              </div>
              <div>
                <div className="text-white/60">热点关键词</div>
                <div className="text-purple-400">{currentFlow.hotspotData?.keyword || 'AI'}</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 自动化设置面板 */}
      {showSettings && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4">自动化设置</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-white font-medium">自动化开关</h4>
              
              {[
                { key: 'enableAutoStrategy', label: '自动AI策略生成', desc: '热点触发后自动生成策略' },
                { key: 'enableAutoDeploy', label: '自动部署', desc: '策略生成后自动部署代币' },
                { key: 'enableAutoLiquidity', label: '自动流动性管理', desc: '部署后自动启动流动性控制' },
                { key: 'enableAutoExit', label: '自动退出策略', desc: '达到条件时自动执行退出' }
              ].map((setting) => (
                <div key={setting.key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div>
                    <div className="text-white font-medium">{setting.label}</div>
                    <div className="text-white/60 text-sm">{setting.desc}</div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={autoSettings[setting.key]}
                      onChange={(e) => dispatch(updateAutoSettings({ [setting.key]: e.target.checked }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
            
            <div className="space-y-4">
              <h4 className="text-white font-medium">风险设置</h4>
              
              <div className="space-y-3">
                <label className="block text-white/70 text-sm">风险等级</label>
                <select
                  value={autoSettings.riskLevel}
                  onChange={(e) => dispatch(updateAutoSettings({ riskLevel: e.target.value }))}
                  className="input-field w-full"
                >
                  <option value="low">低风险 - 保守策略</option>
                  <option value="medium">中风险 - 平衡策略</option>
                  <option value="high">高风险 - 激进策略</option>
                </select>
              </div>
              
              <div className="p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
                  <div>
                    <div className="text-yellow-400 font-medium text-sm">风险提示</div>
                    <div className="text-white/70 text-xs mt-1">
                      自动化交易存在风险，请根据市场情况合理设置参数
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 参数优化面板 */}
      {showOptimization && <OptimizationPanel />}
    </div>
  )
}

export default BusinessFlowCenter
