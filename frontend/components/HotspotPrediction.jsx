import React, { useState, useEffect } from 'react'
import {
  Calendar,
  Brain,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle,
  Zap,
  Target,
  Bell,
  Edit3,
  Plus,
  BarChart3,
  Activity,
  Sparkles,
  X,
  Globe,
  Shield,
  Eye,
  MapPin,
  Users,
  MessageSquare,
  Wifi,
  Lock
} from 'lucide-react'
import TimelineView from './TimelineView'

const HotspotPrediction = () => {
  const [activeView, setActiveView] = useState('calendar')
  const [predictions, setPredictions] = useState([])
  const [events, setEvents] = useState([])
  const [selectedEvent, setSelectedEvent] = useState(null)
  const [showEventModal, setShowEventModal] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [generatedStrategy, setGeneratedStrategy] = useState(null)
  const [showStrategyModal, setShowStrategyModal] = useState(false)
  const [isGeneratingStrategy, setIsGeneratingStrategy] = useState(false)
  const [selectedHotspot, setSelectedHotspot] = useState(null)
  const [globalData, setGlobalData] = useState(null)
  const [darkwebData, setDarkwebData] = useState(null)
  const [analytics, setAnalytics] = useState(null)

  // 获取预测数据
  useEffect(() => {
    fetchPredictions()
    fetchEvents()
    fetchGlobalData()
    fetchDarkwebData()
    fetchAnalytics()
  }, [])

  const fetchPredictions = async () => {
    try {
      const response = await fetch('http://localhost:8001/prediction/hotspots')
      const data = await response.json()
      if (data.status === 'success') {
        setPredictions(data.predictions)
      }
    } catch (error) {
      console.error('获取预测数据失败:', error)
    }
  }

  const fetchEvents = async () => {
    try {
      const response = await fetch('http://localhost:8001/prediction/events')
      const data = await response.json()
      if (data.status === 'success') {
        setEvents(data.events)
      }
    } catch (error) {
      console.error('获取事件数据失败:', error)
    }
  }

  const fetchGlobalData = async () => {
    try {
      const response = await fetch('http://localhost:8001/hotspot/global')
      const data = await response.json()
      setGlobalData(data)
    } catch (error) {
      console.error('获取全球数据失败:', error)
    }
  }

  const fetchDarkwebData = async () => {
    try {
      const response = await fetch('http://localhost:8001/hotspot/darkweb')
      const data = await response.json()
      setDarkwebData(data)
    } catch (error) {
      console.error('获取暗网数据失败:', error)
    }
  }

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('http://localhost:8001/hotspot/analytics')
      const data = await response.json()
      setAnalytics(data)
    } catch (error) {
      console.error('获取分析数据失败:', error)
    }
  }

  // 运行AI预测分析
  const runPredictionAnalysis = async () => {
    setIsAnalyzing(true)
    try {
      const response = await fetch('http://localhost:8001/prediction/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      const data = await response.json()
      if (data.status === 'success') {
        setPredictions(data.predictions)
        alert('AI预测分析完成！')
      }
    } catch (error) {
      console.error('预测分析失败:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  // 生成热点策略
  const generateHotspotStrategy = async (hotspot) => {
    setIsGeneratingStrategy(true)
    setSelectedHotspot(hotspot)

    try {
      const response = await fetch('http://localhost:8001/prediction/generate-hotspot-strategy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(hotspot)
      })
      const data = await response.json()

      if (data.status === 'success') {
        setGeneratedStrategy(data.strategy)
        setShowStrategyModal(true)
      } else {
        alert('策略生成失败：' + data.message)
      }
    } catch (error) {
      console.error('策略生成失败:', error)
      alert('策略生成失败，请重试')
    } finally {
      setIsGeneratingStrategy(false)
    }
  }

  // 一键部署策略
  const deployStrategy = async (strategy) => {
    try {
      // 跳转到部署页面并传递策略数据
      sessionStorage.setItem('deployStrategy', JSON.stringify(strategy))
      window.location.href = '/deploy'
    } catch (error) {
      console.error('部署跳转失败:', error)
      alert('部署跳转失败，请重试')
    }
  }

  // 获取置信度颜色
  const getConfidenceColor = (confidence) => {
    switch (confidence) {
      case 'HIGH': return 'text-red-400 bg-red-500/20 border-red-500/30'
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
      case 'LOW': return 'text-green-400 bg-green-500/20 border-green-500/30'
      default: return 'text-gray-400 bg-gray-500/20 border-gray-500/30'
    }
  }

  // 获取预测概率颜色
  const getProbabilityColor = (prob) => {
    if (prob >= 0.8) return 'text-red-400'
    if (prob >= 0.6) return 'text-yellow-400'
    return 'text-green-400'
  }

  // 格式化时间
  const formatTime = (timeStr) => {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 计算距离事件的时间
  const getTimeToEvent = (eventTime) => {
    const now = new Date()
    const event = new Date(eventTime)
    const diff = event - now
    
    if (diff < 0) return '已过期'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}天后`
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟后`
    } else {
      return `${minutes}分钟后`
    }
  }

  return (
    <div className="space-y-6">
      {/* 标题和控制栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
            <Brain className="w-5 h-5 text-purple-400" />
            <span>热点预测与策略自动化</span>
          </h3>
          <p className="text-white/60 text-sm mt-1">
            AI驱动的热点预测 • 智能策略生成 • 自动化提醒系统
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={runPredictionAnalysis}
            disabled={isAnalyzing}
            className="btn-secondary flex items-center space-x-2"
          >
            {isAnalyzing ? (
              <Activity className="w-4 h-4 animate-pulse" />
            ) : (
              <Sparkles className="w-4 h-4" />
            )}
            <span>{isAnalyzing ? '分析中...' : 'AI预测分析'}</span>
          </button>
          
          <button
            onClick={() => setShowEventModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>添加事件</span>
          </button>
        </div>
      </div>

      {/* 视图切换 */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        {[
          { id: 'calendar', name: '智能日历', icon: Calendar },
          { id: 'timeline', name: '时间轴视图', icon: Clock },
          { id: 'predictions', name: '智能分析', icon: TrendingUp },
          { id: 'global', name: '全球监控', icon: Globe },
          { id: 'darkweb', name: '暗网信号', icon: Shield },
          { id: 'analytics', name: '深度分析', icon: BarChart3 }
        ].map((view) => (
          <button
            key={view.id}
            onClick={() => setActiveView(view.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
              activeView === view.id
                ? 'bg-primary-500 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            <view.icon className="w-4 h-4" />
            <span>{view.name}</span>
          </button>
        ))}
      </div>

      {/* 智能日历视图 */}
      {activeView === 'calendar' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 事件列表 */}
          <div className="lg:col-span-2 card p-6">
            <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-blue-400" />
              <span>事件时间线</span>
            </h4>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {events.map((event, index) => {
                const prediction = predictions.find(p => p.event_id === event.id)

                return (
                  <div
                    key={event.id}
                    className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                    onClick={() => setSelectedEvent(event)}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h5 className="text-white font-medium">{event.title}</h5>
                        <p className="text-white/60 text-sm mt-1">{event.description}</p>
                      </div>

                      <div className="text-right ml-4">
                        <div className="text-white/70 text-sm">{formatTime(event.event_time)}</div>
                        <div className="text-xs text-white/50 mt-1">{getTimeToEvent(event.event_time)}</div>
                      </div>
                    </div>

                    {prediction && (
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-white/70 text-sm">预测概率:</span>
                          <span className={`font-bold ${getProbabilityColor(prediction.hotspot_prob)}`}>
                            {(prediction.hotspot_prob * 100).toFixed(1)}%
                          </span>
                        </div>

                        <div className={`px-2 py-1 rounded text-xs border ${getConfidenceColor(prediction.confidence)}`}>
                          {prediction.confidence}
                        </div>
                      </div>
                    )}

                    {event.key_person && (
                      <div className="mt-2 flex items-center space-x-2">
                        <span className="text-white/50 text-xs">关键人物:</span>
                        <span className="text-blue-400 text-xs">{event.key_person}</span>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* 预测统计 */}
          <div className="space-y-6">
            {/* 预测概览 */}
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-green-400" />
                <span>预测概览</span>
              </h4>

              <div className="space-y-4">
                <div className="bg-red-500/10 rounded-lg p-3 border border-red-500/30">
                  <div className="flex items-center justify-between">
                    <span className="text-red-400 text-sm">高概率事件</span>
                    <span className="text-red-400 font-bold">
                      {predictions.filter(p => p.confidence === 'HIGH').length}
                    </span>
                  </div>
                </div>

                <div className="bg-yellow-500/10 rounded-lg p-3 border border-yellow-500/30">
                  <div className="flex items-center justify-between">
                    <span className="text-yellow-400 text-sm">中等概率事件</span>
                    <span className="text-yellow-400 font-bold">
                      {predictions.filter(p => p.confidence === 'MEDIUM').length}
                    </span>
                  </div>
                </div>

                <div className="bg-green-500/10 rounded-lg p-3 border border-green-500/30">
                  <div className="flex items-center justify-between">
                    <span className="text-green-400 text-sm">低概率事件</span>
                    <span className="text-green-400 font-bold">
                      {predictions.filter(p => p.confidence === 'LOW').length}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* 智能提醒 */}
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Bell className="w-5 h-5 text-yellow-400" />
                <span>智能提醒</span>
              </h4>

              <div className="space-y-3">
                {predictions
                  .filter(p => p.confidence === 'HIGH')
                  .slice(0, 3)
                  .map((prediction, index) => {
                    const event = events.find(e => e.id === prediction.event_id)
                    return (
                      <div key={index} className="bg-white/5 rounded-lg p-3">
                        <div className="flex items-start space-x-2">
                          <AlertTriangle className="w-4 h-4 text-red-400 mt-0.5" />
                          <div className="flex-1">
                            <div className="text-white text-sm font-medium">
                              {event?.title}
                            </div>
                            <div className="text-white/60 text-xs mt-1">
                              预计 {getTimeToEvent(prediction.predicted_time)} 爆发
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Zap className="w-5 h-5 text-purple-400" />
                <span>快速操作</span>
              </h4>

              <div className="space-y-2">
                <button className="w-full btn-secondary text-sm py-2">
                  生成策略草稿
                </button>
                <button className="w-full btn-primary text-sm py-2">
                  设置提醒
                </button>
                <button className="w-full btn-secondary text-sm py-2">
                  导出日历
                </button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* 时间轴视图 */}
      {activeView === 'timeline' && (
        <TimelineView
          events={events}
          predictions={predictions}
          onEventUpdate={(updatedEvent) => {
            setEvents(prev => prev.map(e => e.id === updatedEvent.id ? updatedEvent : e))
          }}
          onEventCreate={(newEvent) => {
            setEvents(prev => [...prev, newEvent])
          }}
        />
      )}

      {/* 智能分析视图 - 合并热点策略生成和预测事件分析 */}
      {activeView === 'predictions' && (
        <div className="space-y-6">
          {/* 预测分析概览 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <Brain className="w-8 h-8 text-purple-400" />
                <div>
                  <div className="text-white font-medium">预测事件</div>
                  <div className="text-purple-400 text-2xl font-bold">{predictions.length}</div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <Sparkles className="w-8 h-8 text-yellow-400" />
                <div>
                  <div className="text-white font-medium">策略就绪</div>
                  <div className="text-yellow-400 text-2xl font-bold">
                    {predictions.filter(p => p.strategy_ready).length}
                  </div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <Target className="w-8 h-8 text-blue-400" />
                <div>
                  <div className="text-white font-medium">高置信度</div>
                  <div className="text-blue-400 text-2xl font-bold">
                    {predictions.filter(p => p.confidence === 'HIGH').length}
                  </div>
                </div>
              </div>
            </div>
          </div>



          {/* 预测事件分析区域 */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-white font-medium flex items-center space-x-2">
                <Brain className="w-5 h-5 text-purple-400" />
                <span>未来事件预测分析</span>
              </h4>
              <div className="text-white/60 text-sm">
                基于AI模型的事件预测和策略建议
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {predictions.map((prediction, index) => {
                const event = events.find(e => e.id === prediction.event_id)

                return (
                  <div key={index} className="bg-white/5 rounded-lg p-5 border border-white/10 hover:bg-white/10 transition-all duration-200">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h5 className="text-white font-medium">{event?.title}</h5>
                        <p className="text-white/60 text-sm mt-1">{event?.description}</p>
                      </div>
                      <div className={`px-3 py-1 rounded text-sm border ${getConfidenceColor(prediction.confidence)}`}>
                        {prediction.confidence}
                      </div>
                    </div>

                    <div className="space-y-4">
                      {/* 预测概率 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-white/70 text-sm">热点爆发概率</span>
                          <span className={`font-bold text-xl ${getProbabilityColor(prediction.hotspot_prob)}`}>
                            {(prediction.hotspot_prob * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              prediction.hotspot_prob >= 0.8 ? 'bg-red-400' :
                              prediction.hotspot_prob >= 0.6 ? 'bg-yellow-400' : 'bg-green-400'
                            }`}
                            style={{ width: `${prediction.hotspot_prob * 100}%` }}
                          ></div>
                        </div>

                        {/* 策略建议 */}
                        {prediction.strategy_ready && (
                          <div className="mt-3 p-3 bg-green-500/10 border border-green-500/30 rounded">
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4 text-green-400" />
                              <span className="text-green-400 text-sm font-medium">策略就绪</span>
                            </div>
                            <p className="text-white/70 text-xs mt-1">
                              AI已生成完整发币策略，可立即部署
                            </p>
                          </div>
                        )}
                      </div>

                      {/* 预测因子分析 */}
                      <div className="bg-white/5 rounded-lg p-4">
                        <h6 className="text-white font-medium mb-3 text-sm">AI预测因子</h6>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-white/70 text-sm">时间序列分析</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-white/20 rounded-full h-1.5">
                                <div
                                  className="h-1.5 bg-blue-400 rounded-full"
                                  style={{ width: `${(prediction.factors?.temporal_score || 0) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-blue-400 text-sm font-medium w-10">
                                {((prediction.factors?.temporal_score || 0) * 100).toFixed(0)}%
                              </span>
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-white/70 text-sm">影响力评估</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-white/20 rounded-full h-1.5">
                                <div
                                  className="h-1.5 bg-green-400 rounded-full"
                                  style={{ width: `${(prediction.factors?.influence_score || 0) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-green-400 text-sm font-medium w-10">
                                {((prediction.factors?.influence_score || 0) * 100).toFixed(0)}%
                              </span>
                            </div>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-white/70 text-sm">情感分析</span>
                            <div className="flex items-center space-x-2">
                              <div className="w-16 bg-white/20 rounded-full h-1.5">
                                <div
                                  className="h-1.5 bg-purple-400 rounded-full"
                                  style={{ width: `${(prediction.factors?.sentiment_score || 0) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-purple-400 text-sm font-medium w-10">
                                {((prediction.factors?.sentiment_score || 0) * 100).toFixed(0)}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 时间和操作 */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-white/70 text-xs">预测峰值时间</div>
                          <div className="text-white text-sm font-medium">{formatTime(prediction.predicted_time)}</div>
                        </div>

                        {prediction.strategy_ready && (
                          <button
                            onClick={() => generateHotspotStrategy({
                              id: event?.id,
                              title: event?.title,
                              keyword: event?.title,
                              score: prediction.hotspot_prob * 100,
                              category: event?.category || 'general',
                              sentiment: prediction.factors?.sentiment_score || 0.75,
                              volume: 50000,
                              description: event?.description
                            })}
                            className="btn-primary text-xs py-2 px-4 flex items-center space-x-2"
                          >
                            <Sparkles className="w-3 h-3" />
                            <span>生成策略</span>
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}

      {/* 策略生成视图 */}
      {activeView === 'strategies' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {predictions
            .filter(p => p.confidence === 'HIGH')
            .map((prediction, index) => {
              const event = events.find(e => e.id === prediction.event_id)

              return (
                <div key={index} className="card p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="text-white font-medium">{event?.title}</h4>
                      <p className="text-white/60 text-sm mt-1">基于AI预测自动生成策略</p>
                    </div>
                    <div className="text-right">
                      <div className="text-green-400 text-sm font-bold">
                        {(prediction.hotspot_prob * 100).toFixed(1)}%
                      </div>
                      <div className="text-white/50 text-xs">成功概率</div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {/* 策略建议 */}
                    <div className="bg-white/5 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-2">推荐策略</h5>
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-white/70">代币名称:</span>
                          <div className="text-blue-400 font-medium">
                            {event?.category === 'politics' ? 'TRUMPWIN' :
                             event?.category === 'technology' ? 'AIREV' : 'MEMECOIN'}
                          </div>
                        </div>
                        <div>
                          <span className="text-white/70">发行时机:</span>
                          <div className="text-green-400 font-medium">
                            事件前{prediction.confidence === 'HIGH' ? '1小时' : '2小时'}
                          </div>
                        </div>
                        <div>
                          <span className="text-white/70">预期ROI:</span>
                          <div className="text-yellow-400 font-medium">
                            {prediction.confidence === 'HIGH' ? '400-1000%' : '200-600%'}
                          </div>
                        </div>
                        <div>
                          <span className="text-white/70">风险等级:</span>
                          <div className="text-purple-400 font-medium">中等</div>
                        </div>
                      </div>
                    </div>

                    {/* 策略详情 */}
                    <div className="bg-white/5 rounded-lg p-4">
                      <h5 className="text-white font-medium mb-2">执行计划</h5>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-white/70">总供应量:</span>
                          <span className="text-white">
                            {event?.category === 'politics' ? '1B' : '5B'} 代币
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">初始价格:</span>
                          <span className="text-white">$0.001</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">流动性池:</span>
                          <span className="text-white">30%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">空投分配:</span>
                          <span className="text-white">45%</span>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-2">
                      <button className="flex-1 btn-secondary text-sm py-2">
                        查看详情
                      </button>
                      <button className="flex-1 btn-primary text-sm py-2">
                        一键部署
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}

          {predictions.filter(p => p.confidence === 'HIGH').length === 0 && (
            <div className="lg:col-span-2 text-center py-12">
              <Target className="w-12 h-12 mx-auto mb-4 text-white/30" />
              <p className="text-white/60">暂无高置信度预测</p>
              <p className="text-white/40 text-sm">运行AI分析后将显示策略建议</p>
            </div>
          )}
        </div>
      )}

      {/* 事件创建模态框 */}
      {showEventModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">添加预测事件</h3>
              <button
                onClick={() => setShowEventModal(false)}
                className="text-white/50 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form className="space-y-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">事件标题</label>
                <input
                  type="text"
                  className="input-field w-full"
                  placeholder="输入事件标题..."
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">事件描述</label>
                <textarea
                  className="input-field w-full h-20 resize-none"
                  placeholder="描述事件详情..."
                ></textarea>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">事件时间</label>
                  <input
                    type="datetime-local"
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">事件类型</label>
                  <select className="input-field w-full">
                    <option value="POLITICAL">政治</option>
                    <option value="TECHNOLOGY">科技</option>
                    <option value="ENTERTAINMENT">娱乐</option>
                    <option value="SPORTS">体育</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">关键人物</label>
                <input
                  type="text"
                  className="input-field w-full"
                  placeholder="相关的关键人物..."
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowEventModal(false)}
                  className="flex-1 btn-secondary"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="flex-1 btn-primary"
                >
                  创建事件
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 全球监控视图 */}
      {activeView === 'global' && globalData && (
        <div className="space-y-6">
          {/* 全球趋势概览 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {globalData.global_trends.map((region, index) => (
              <div key={index} className="card p-6">
                <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                  <MapPin className="w-5 h-5 text-blue-400" />
                  <span>{region.region}</span>
                </h4>

                <div className="space-y-4">
                  <div className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70 text-sm">市场情绪</span>
                      <span className={`font-bold ${getProbabilityColor(region.market_sentiment)}`}>
                        {(region.market_sentiment * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70 text-sm">总讨论量</span>
                      <span className="text-blue-400 font-medium">
                        {(region.total_volume / 1000000).toFixed(1)}M
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-white/70 text-sm">活跃用户</span>
                      <span className="text-green-400 font-medium">
                        {(region.active_users / 1000000).toFixed(1)}M
                      </span>
                    </div>
                  </div>

                  <div>
                    <h5 className="text-white text-sm font-medium mb-2">热门话题</h5>
                    <div className="space-y-2">
                      {region.trending_topics.slice(0, 3).map((topic, idx) => (
                        <div key={idx} className="bg-white/5 rounded p-2">
                          <div className="flex items-center justify-between">
                            <span className="text-white text-xs">{topic.topic}</span>
                            <span className={`text-xs font-medium ${getProbabilityColor(topic.growth)}`}>
                              +{(topic.growth * 100).toFixed(0)}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 跨平台分析 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <MessageSquare className="w-5 h-5 text-purple-400" />
                <span>跨平台分析</span>
              </h4>

              <div className="space-y-3">
                {Object.entries(globalData.cross_platform_analysis).map(([platform, data]) => (
                  <div key={platform} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium capitalize">{platform}</span>
                      <span className="text-blue-400 text-sm">{data.volume}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">情绪: {(data.sentiment * 100).toFixed(0)}%</span>
                      <span className="text-white/70">参与: {(data.engagement * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Globe className="w-5 h-5 text-green-400" />
                <span>语言分布</span>
              </h4>

              <div className="space-y-3">
                {Object.entries(globalData.language_insights).map(([lang, data]) => (
                  <div key={lang} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-white font-medium uppercase">{lang}</span>
                      <span className="text-blue-400 text-sm">{data.volume}%</span>
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-white/70">情绪: {(data.sentiment * 100).toFixed(0)}%</span>
                      <span className="text-purple-400">{data.top_topic}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 时区活动分析 */}
          <div className="card p-6">
            <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
              <Clock className="w-5 h-5 text-yellow-400" />
              <span>全球时区活动</span>
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {Object.entries(globalData.time_zone_activity).map(([timezone, data]) => (
                <div key={timezone} className="bg-white/5 rounded-lg p-4">
                  <div className="text-center">
                    <div className="text-white font-medium">{timezone}</div>
                    <div className="text-blue-400 text-sm mt-1">峰值: {data.peak_time}</div>
                    <div className="text-green-400 text-lg font-bold mt-2">
                      {(data.peak_volume / 1000).toFixed(0)}K
                    </div>
                    <div className="text-white/70 text-xs mt-1">
                      情绪: {(data.sentiment * 100).toFixed(0)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 暗网信号视图 */}
      {activeView === 'darkweb' && darkwebData && (
        <div className="space-y-6">
          {/* 监控状态 */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <div className={`w-3 h-3 rounded-full ${
                  darkwebData.monitoring_status === 'active' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <div>
                  <div className="text-white font-medium">监控状态</div>
                  <div className="text-white/70 text-sm capitalize">{darkwebData.monitoring_status}</div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <Eye className="w-5 h-5 text-blue-400" />
                <div>
                  <div className="text-white font-medium">活跃源</div>
                  <div className="text-blue-400 text-lg font-bold">
                    {darkwebData.active_sources}/{darkwebData.total_sources}
                  </div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <Wifi className="w-5 h-5 text-green-400" />
                <div>
                  <div className="text-white font-medium">信号强度</div>
                  <div className="text-green-400 text-lg font-bold">
                    {(darkwebData.signal_strength * 100).toFixed(0)}%
                  </div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-purple-400" />
                <div>
                  <div className="text-white font-medium">准确率</div>
                  <div className="text-purple-400 text-lg font-bold">
                    {(darkwebData.source_reliability.average_accuracy * 100).toFixed(0)}%
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 信号列表 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-red-400" />
                <span>暗网信号</span>
              </h4>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {darkwebData.signals.map((signal, index) => (
                  <div key={signal.id} className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h5 className="text-white font-medium">{signal.title}</h5>
                        <p className="text-white/60 text-sm mt-1">{signal.description}</p>
                      </div>
                      <div className={`px-2 py-1 rounded text-xs border ${
                        signal.risk_level === 'high' ? 'text-red-400 bg-red-500/20 border-red-500/30' :
                        signal.risk_level === 'medium' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                        'text-green-400 bg-green-500/20 border-green-500/30'
                      }`}>
                        {signal.risk_level}
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70 text-sm">置信度</span>
                      <span className={`font-bold ${getProbabilityColor(signal.confidence)}`}>
                        {(signal.confidence * 100).toFixed(0)}%
                      </span>
                    </div>

                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70 text-sm">预计浮现</span>
                      <span className="text-blue-400 text-sm">{signal.estimated_surface_time}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-white/70 text-sm">潜在影响</span>
                      <span className={`text-sm font-medium ${
                        signal.potential_impact === 'very_high' ? 'text-red-400' :
                        signal.potential_impact === 'high' ? 'text-orange-400' :
                        signal.potential_impact === 'medium' ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {signal.potential_impact}
                      </span>
                    </div>

                    <div className="mt-3">
                      <div className="flex flex-wrap gap-1">
                        {signal.keywords.map((keyword, idx) => (
                          <span key={idx} className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-400" />
                <span>威胁分析</span>
              </h4>

              <div className="space-y-4">
                {Object.entries(darkwebData.threat_analysis).map(([threat, data]) => (
                  <div key={threat} className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-medium capitalize">
                        {threat.replace('_', ' ')}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs border ${
                        data.level === 'high' ? 'text-red-400 bg-red-500/20 border-red-500/30' :
                        data.level === 'medium' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                        'text-green-400 bg-green-500/20 border-green-500/30'
                      }`}>
                        {data.level}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-white/70">来源: {data.sources}</span>
                      <span className="text-blue-400">置信度: {(data.confidence * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6">
                <h5 className="text-white font-medium mb-3">来源可靠性</h5>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">已验证源</span>
                    <span className="text-green-400 font-medium">
                      {darkwebData.source_reliability.verified_sources}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">未验证源</span>
                    <span className="text-yellow-400 font-medium">
                      {darkwebData.source_reliability.unverified_sources}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">黑名单源</span>
                    <span className="text-red-400 font-medium">
                      {darkwebData.source_reliability.blacklisted_sources}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 深度分析视图 */}
      {activeView === 'analytics' && analytics && (
        <div className="space-y-6">
          {/* ML模型指标 */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {Object.entries(analytics.mlMetrics).map(([model, score]) => (
              <div key={model} className="card p-6">
                <div className="text-center">
                  <div className="text-white font-medium capitalize mb-2">
                    {model.replace('Model', ' 模型')}
                  </div>
                  <div className={`text-2xl font-bold ${getProbabilityColor(score / 100)}`}>
                    {score}%
                  </div>
                  <div className="text-white/50 text-xs mt-1">准确率</div>
                </div>
              </div>
            ))}
          </div>

          {/* 情绪分析 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Brain className="w-5 h-5 text-purple-400" />
                <span>分类情绪分析</span>
              </h4>

              <div className="space-y-3">
                {Object.entries(analytics.sentimentIndex).map(([category, sentiment]) => (
                  <div key={category} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-white font-medium capitalize">{category}</span>
                      <span className={`font-bold ${getProbabilityColor(sentiment)}`}>
                        {(sentiment * 100).toFixed(0)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Globe className="w-5 h-5 text-blue-400" />
                <span>语言分布</span>
              </h4>

              <div className="space-y-3">
                {Object.entries(analytics.languageDistribution).map(([lang, percentage]) => (
                  <div key={lang} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-white font-medium uppercase">{lang}</span>
                      <span className="text-blue-400 font-bold">{percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 视频分析和预测准确率 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Eye className="w-5 h-5 text-green-400" />
                <span>视频分析</span>
              </h4>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">处理帧数</span>
                  <span className="text-green-400 font-medium">
                    {analytics.videoAnalysis.processedFrames.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">识别准确率</span>
                  <span className="text-blue-400 font-medium">
                    {analytics.videoAnalysis.recognitionAccuracy}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">关键词提取</span>
                  <span className="text-purple-400 font-medium">
                    {analytics.videoAnalysis.keywordsExtracted.toLocaleString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">视觉Meme</span>
                  <span className="text-yellow-400 font-medium">
                    {analytics.videoAnalysis.visualMemes}
                  </span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-red-400" />
                <span>预测准确率</span>
              </h4>

              <div className="text-center">
                <div className={`text-4xl font-bold ${getProbabilityColor(analytics.predictionAccuracy / 100)}`}>
                  {analytics.predictionAccuracy}%
                </div>
                <div className="text-white/70 text-sm mt-2">整体预测准确率</div>
              </div>

              <div className="mt-6 space-y-2">
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-white/70 text-sm">监管预警</div>
                  <div className="text-orange-400 font-medium">
                    {analytics.regulatoryAlerts.length} 个活跃预警
                  </div>
                </div>
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-white/70 text-sm">暗网信号</div>
                  <div className="text-red-400 font-medium">
                    {analytics.darkwebSignals.length} 个检测信号
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 策略展示模态框 */}
      {showStrategyModal && generatedStrategy && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-gray-900 border-b border-white/10 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-semibold text-white flex items-center space-x-2">
                    <Sparkles className="w-6 h-6 text-yellow-400" />
                    <span>AI策略生成成功</span>
                  </h3>
                  <p className="text-white/60 text-sm mt-1">
                    基于热点"{selectedHotspot?.keyword}"生成的智能发币策略
                  </p>
                </div>
                <button
                  onClick={() => setShowStrategyModal(false)}
                  className="text-white/50 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* 策略概览 */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  {/* 代币信息 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Target className="w-5 h-5 text-blue-400" />
                      <span>代币信息</span>
                    </h4>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-white/70 text-sm">代币名称</label>
                        <div className="text-white font-medium text-lg">
                          {generatedStrategy.token_info.name}
                        </div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">代币符号</label>
                        <div className="text-white font-medium text-lg">
                          {generatedStrategy.token_info.symbol}
                        </div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">总供应量</label>
                        <div className="text-blue-400 font-medium">
                          {(generatedStrategy.token_info.total_supply / 1000000000).toFixed(1)}B
                        </div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">初始价格</label>
                        <div className="text-green-400 font-medium">
                          ${generatedStrategy.token_info.initial_price}
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="text-white/70 text-sm">描述</label>
                      <div className="text-white/80 text-sm mt-1">
                        {generatedStrategy.token_info.description}
                      </div>
                    </div>
                  </div>

                  {/* 分配策略 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5 text-purple-400" />
                      <span>代币分配</span>
                    </h4>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="text-white/70 text-sm">空投</div>
                        <div className="text-blue-400 font-bold text-lg">
                          {(generatedStrategy.allocation.airdrop * 100).toFixed(0)}%
                        </div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="text-white/70 text-sm">流动性</div>
                        <div className="text-green-400 font-bold text-lg">
                          {(generatedStrategy.allocation.liquidity * 100).toFixed(0)}%
                        </div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="text-white/70 text-sm">营销</div>
                        <div className="text-yellow-400 font-bold text-lg">
                          {(generatedStrategy.allocation.marketing * 100).toFixed(0)}%
                        </div>
                      </div>
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="text-white/70 text-sm">团队</div>
                        <div className="text-purple-400 font-bold text-lg">
                          {(generatedStrategy.allocation.team * 100).toFixed(0)}%
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 营销策略 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-yellow-400" />
                      <span>营销策略</span>
                    </h4>

                    <div className="space-y-3">
                      <div>
                        <label className="text-white/70 text-sm">目标受众</label>
                        <div className="text-white">{generatedStrategy.marketing_strategy.target_audience}</div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">主要渠道</label>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {generatedStrategy.marketing_strategy.primary_channels.map((channel, idx) => (
                            <span key={idx} className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-sm">
                              {channel}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">发布时机</label>
                        <div className="text-green-400">{generatedStrategy.marketing_strategy.launch_timing}</div>
                      </div>
                      <div>
                        <label className="text-white/70 text-sm">病毒传播潜力</label>
                        <div className="text-yellow-400 font-medium">
                          {generatedStrategy.marketing_strategy.viral_potential}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 右侧信息 */}
                <div className="space-y-6">
                  {/* 成功指标 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <TrendingUp className="w-5 h-5 text-green-400" />
                      <span>成功预期</span>
                    </h4>

                    <div className="space-y-4">
                      <div className="text-center">
                        <div className="text-white/70 text-sm">预期ROI</div>
                        <div className="text-green-400 font-bold text-xl">
                          {generatedStrategy.success_metrics.expected_roi}
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-white/70 text-sm">成功概率</div>
                        <div className="text-blue-400 font-bold text-lg">
                          {generatedStrategy.success_metrics.success_probability}
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-white/70 text-sm">目标市值</div>
                        <div className="text-purple-400 font-bold">
                          {generatedStrategy.success_metrics.target_market_cap}
                        </div>
                      </div>

                      <div className="text-center">
                        <div className="text-white/70 text-sm">风险等级</div>
                        <div className={`font-bold ${
                          generatedStrategy.success_metrics.risk_level === '低' ? 'text-green-400' :
                          generatedStrategy.success_metrics.risk_level === '中等' ? 'text-yellow-400' : 'text-red-400'
                        }`}>
                          {generatedStrategy.success_metrics.risk_level}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 部署信息 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-orange-400" />
                      <span>部署配置</span>
                    </h4>

                    <div className="space-y-3 text-sm">
                      <div>
                        <div className="text-white/70">推荐平台</div>
                        <div className="text-blue-400 font-medium">
                          {generatedStrategy.deployment.recommended_platform}
                        </div>
                      </div>

                      <div>
                        <div className="text-white/70">预估成本</div>
                        <div className="text-green-400 font-medium">
                          {generatedStrategy.deployment.estimated_cost.solana}
                        </div>
                      </div>

                      <div>
                        <div className="text-white/70">预估时间</div>
                        <div className="text-yellow-400 font-medium">
                          {generatedStrategy.deployment.estimated_time}
                        </div>
                      </div>

                      <div>
                        <div className="text-white/70">Gas优化</div>
                        <div className="text-purple-400 font-medium">
                          {generatedStrategy.deployment.gas_optimization}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 技术特性 */}
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-green-400" />
                      <span>安全特性</span>
                    </h4>

                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">防Rug保护</span>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">流动性锁定</span>
                        <span className="text-green-400">{generatedStrategy.technical_features.liquidity_lock}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">所有权放弃</span>
                        <CheckCircle className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-white/70">审计评分</span>
                        <span className="text-blue-400">{generatedStrategy.technical_features.audit_score}/100</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex space-x-4 pt-6 border-t border-white/10">
                <button
                  onClick={() => setShowStrategyModal(false)}
                  className="flex-1 btn-secondary"
                >
                  稍后部署
                </button>
                <button
                  onClick={() => deployStrategy(generatedStrategy)}
                  className="flex-1 btn-primary flex items-center justify-center space-x-2"
                >
                  <Zap className="w-5 h-5" />
                  <span>一键部署</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default HotspotPrediction
