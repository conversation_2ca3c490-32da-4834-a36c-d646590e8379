import React from 'react';
import { FaBrain, FaCogs, FaCrown, FaWallet, FaFire, FaChartLine } from 'react-icons/fa';
import { useDashboardStore } from '../store/dashboardStore';
import WelcomeBanner from './dashboard/WelcomeBanner';
import { StatCardsGrid } from './dashboard/StatCard';
import MarketChart from './dashboard/MarketChart';
import HotCoinsList from './dashboard/HotCoinsList';
import { FeatureCardsGrid } from './dashboard/FeatureCard';

const SimpleDashboardContent: React.FC = () => {
  // 从Zustand store获取数据
  const {
    user,
    market,
    stats,
    hotCoins,
    features,
    chartData,
    isLoading,
    lastUpdated
  } = useDashboardStore();

  // 处理统计卡片点击
  const handleStatCardClick = (cardTitle: string) => {
    console.log('点击统计卡片:', cardTitle);
    // 这里可以添加导航逻辑
  };

  // 处理代币点击
  const handleCoinClick = (coin: any) => {
    console.log('点击代币:', coin);
    // 这里可以添加代币详情页面导航
  };

  return (
    <div className="space-y-6 md:space-y-8">


      {/* 欢迎横幅 - 使用原始完整设计 */}
      <section className="animate-fade-in">
        <WelcomeBanner
          user={user}
          market={market}
        />
      </section>

      {/* 统计卡片 - 使用原始StatCardsGrid组件 */}
      <section className="animate-fade-in delay-100">
        <StatCardsGrid
          cards={stats}
          loading={isLoading}
          onCardClick={handleStatCardClick}
        />
      </section>

      {/* 图表和热门代币 - 使用原始组件 */}
      <section className="flex flex-col lg:flex-row gap-6 md:gap-8 animate-fade-in delay-200">
        {/* 市场图表 */}
        <div className="w-full lg:w-2/3 min-h-[400px]">
          <MarketChart
            data={chartData}
            loading={isLoading}
            height={400}
            className="h-full"
          />
        </div>

        {/* 热门代币列表 */}
        <div className="w-full lg:w-1/3 min-h-[400px]">
          <HotCoinsList
            coins={hotCoins}
            loading={isLoading}
            onCoinClick={handleCoinClick}
            className="h-full"
          />
        </div>
      </section>

      {/* 功能卡片 - 使用原始FeatureCardsGrid组件 */}
      <section className="animate-fade-in delay-300">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">核心功能</h2>
          <p className="text-gray-400">快速访问MemeMaster AI的主要功能模块</p>
        </div>
        <FeatureCardsGrid
          cards={features}
          loading={isLoading}
        />
      </section>

      {/* 底部信息 */}
      <footer className="pt-8 border-t border-gray-700 animate-fade-in delay-400">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm text-gray-400">
          <div className="mb-4 md:mb-0">
            <p>© 2023 MemeMaster AI. 智能Meme币交易系统</p>
            <p className="mt-1">最后更新: {new Date().toLocaleString('zh-CN')}</p>
          </div>
          
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>系统运行正常</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>实时数据同步</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span>AI引擎活跃</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SimpleDashboardContent;
