import React, { useState } from 'react';
import { Settings, User, Wallet, Bell, Lock, Palette, Globe, Shield, Key } from 'lucide-react';

const SettingsFixed = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [notifications, setNotifications] = useState({
    email: true,
    push: false,
    sms: true,
    trading: true,
    security: true
  });

  const tabs = [
    { id: 'profile', label: '个人资料', icon: User },
    { id: 'wallet', label: '钱包设置', icon: Wallet },
    { id: 'notifications', label: '通知设置', icon: Bell },
    { id: 'security', label: '安全设置', icon: Lock },
    { id: 'api', label: 'API管理', icon: Key },
    { id: 'appearance', label: '外观设置', icon: Palette },
    { id: 'language', label: '语言设置', icon: Globe }
  ];

  const handleNotificationChange = (key: string) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof prev]
    }));
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">个人资料</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">用户名</label>
                <input 
                  type="text" 
                  defaultValue="MemeMaster Pro"
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">邮箱地址</label>
                <input 
                  type="email" 
                  defaultValue="<EMAIL>"
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">手机号码</label>
                <input 
                  type="tel" 
                  defaultValue="+86 138 0013 8000"
                  className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">会员等级</label>
                <div className="px-4 py-3 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 border border-indigo-500/30 rounded-xl">
                  <span className="text-indigo-400 font-semibold">PRO 会员</span>
                  <span className="text-gray-400 text-sm ml-2">到期: 2024-12-31</span>
                </div>
              </div>
            </div>
            
            <div className="pt-6 border-t border-gray-700">
              <button className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-400 hover:to-purple-500 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                保存更改
              </button>
            </div>
          </div>
        );

      case 'wallet':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">钱包设置</h3>
            <div className="space-y-6">
              <div className="bg-gray-700/30 rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h4 className="text-lg font-semibold text-white">主钱包</h4>
                    <p className="text-gray-400">0x1234...5678</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-green-400">已连接</span>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <button className="px-4 py-2 bg-blue-500/10 text-blue-400 border border-blue-500/30 rounded-lg hover:bg-blue-500/20 transition-colors">
                    查看详情
                  </button>
                  <button className="px-4 py-2 bg-red-500/10 text-red-400 border border-red-500/30 rounded-lg hover:bg-red-500/20 transition-colors">
                    断开连接
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">交易设置</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">默认滑点容忍度</label>
                    <select className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option>0.5%</option>
                      <option>1.0%</option>
                      <option>2.0%</option>
                      <option>5.0%</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">Gas 费用设置</label>
                    <select className="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-indigo-500">
                      <option>标准</option>
                      <option>快速</option>
                      <option>极速</option>
                    </select>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">支持的网络</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {['Ethereum', 'BSC', 'Polygon', 'Solana'].map((network) => (
                    <div key={network} className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-white">{network}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'notifications':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">通知设置</h3>
            <div className="space-y-4">
              {Object.entries(notifications).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between bg-gray-700/30 rounded-xl p-4">
                  <div>
                    <h4 className="text-white font-medium">
                      {key === 'email' ? '邮件通知' : 
                       key === 'push' ? '推送通知' : 
                       key === 'sms' ? '短信通知' : 
                       key === 'trading' ? '交易通知' : '安全通知'}
                    </h4>
                    <p className="text-gray-400 text-sm">
                      {key === 'email' ? '接收重要更新和报告' : 
                       key === 'push' ? '浏览器推送通知' : 
                       key === 'sms' ? '重要事件短信提醒' : 
                       key === 'trading' ? '交易执行和结果通知' : '安全警报和风险提醒'}
                    </p>
                  </div>
                  <button
                    onClick={() => handleNotificationChange(key)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      value ? 'bg-indigo-600' : 'bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        value ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        );

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">安全设置</h3>
            <div className="space-y-6">
              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">密码设置</h4>
                <div className="space-y-4">
                  <button className="bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-xl transition-colors">
                    修改密码
                  </button>
                </div>
              </div>
              
              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">两步验证</h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white">Google Authenticator</p>
                    <p className="text-gray-400 text-sm">使用手机应用进行二次验证</p>
                  </div>
                  <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                    已启用
                  </button>
                </div>
              </div>

              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">登录历史</h4>
                <div className="space-y-3">
                  {[
                    { location: '北京, 中国', time: '2023-12-19 10:30', device: 'Chrome on Windows' },
                    { location: '上海, 中国', time: '2023-12-18 15:45', device: 'Safari on iPhone' },
                    { location: '深圳, 中国', time: '2023-12-17 09:20', device: 'Chrome on macOS' }
                  ].map((login, index) => (
                    <div key={index} className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-white text-sm">{login.location}</p>
                        <p className="text-gray-400 text-xs">{login.device}</p>
                      </div>
                      <span className="text-gray-400 text-sm">{login.time}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case 'api':
        return (
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-white mb-6">API管理</h3>
            <div className="space-y-6">
              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">API密钥</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-white">主API密钥</p>
                      <p className="text-gray-400 text-sm">sk-***************</p>
                    </div>
                    <div className="flex space-x-2">
                      <button className="px-3 py-1 bg-blue-500/10 text-blue-400 border border-blue-500/30 rounded text-sm">
                        复制
                      </button>
                      <button className="px-3 py-1 bg-red-500/10 text-red-400 border border-red-500/30 rounded text-sm">
                        重置
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-700/30 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">API使用统计</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-indigo-400">1,247</p>
                    <p className="text-gray-400 text-sm">今日调用</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-400">98.5%</p>
                    <p className="text-gray-400 text-sm">成功率</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-yellow-400">45ms</p>
                    <p className="text-gray-400 text-sm">平均响应</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-400">该功能正在开发中...</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center space-x-4 mb-8">
          <div className="p-3 bg-indigo-500/10 text-indigo-400 rounded-xl">
            <Settings className="text-2xl" />
          </div>
          <div>
            <h1 className="text-4xl font-bold text-white">系统设置</h1>
            <p className="text-gray-400 text-lg">管理您的账户和系统偏好设置</p>
          </div>
        </div>

        {/* 设置内容 */}
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 左侧导航 */}
          <div className="lg:w-1/4">
            <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-colors ${
                        activeTab === tab.id
                          ? 'bg-indigo-500/20 text-indigo-400 border border-indigo-500/30'
                          : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* 右侧内容 */}
          <div className="lg:w-3/4">
            <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsFixed;
