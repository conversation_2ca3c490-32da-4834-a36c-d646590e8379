import React from 'react'

const LoadingSpinner = ({ size = 'md', text = '加载中...', variant = 'primary' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colorClasses = {
    primary: 'border-primary',
    secondary: 'border-secondary',
    white: 'border-white'
  }

  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <div className="relative">
        {/* 外圈 */}
        <div 
          className={`${sizeClasses[size]} border-2 border-gray-600 rounded-full animate-spin`}
          style={{
            borderTopColor: variant === 'primary' ? 'var(--primary)' : 
                           variant === 'secondary' ? 'var(--secondary)' : 'white',
            animation: 'spin 1s linear infinite'
          }}
        ></div>
        
        {/* 内圈发光效果 */}
        <div 
          className={`absolute inset-2 border border-opacity-30 rounded-full`}
          style={{
            borderColor: variant === 'primary' ? 'var(--primary)' : 
                        variant === 'secondary' ? 'var(--secondary)' : 'white',
            animation: 'pulse 2s ease-in-out infinite'
          }}
        ></div>
      </div>
      
      {text && (
        <div 
          className="text-sm font-medium"
          style={{ 
            color: 'var(--text-secondary)',
            animation: 'pulse 2s ease-in-out infinite'
          }}
        >
          {text}
        </div>
      )}
    </div>
  )
}

// 骨架屏加载组件
export const SkeletonLoader = ({ className = '', lines = 3 }) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div 
          key={index}
          className="h-4 bg-gray-600 rounded mb-3 last:mb-0"
          style={{
            width: `${Math.random() * 40 + 60}%`,
            background: 'linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%)',
            backgroundSize: '200% 100%',
            animation: 'shimmer 2s infinite'
          }}
        ></div>
      ))}
    </div>
  )
}

// 卡片骨架屏
export const CardSkeleton = () => {
  return (
    <div className="stat-card animate-pulse">
      <div className="stat-header">
        <div 
          className="stat-icon"
          style={{ 
            background: 'var(--bg-tertiary)',
            animation: 'pulse 2s ease-in-out infinite'
          }}
        ></div>
      </div>
      <div 
        className="h-8 bg-gray-600 rounded mb-2"
        style={{
          background: 'linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%)',
          backgroundSize: '200% 100%',
          animation: 'shimmer 2s infinite'
        }}
      ></div>
      <div 
        className="h-4 bg-gray-600 rounded mb-3"
        style={{
          width: '70%',
          background: 'linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%)',
          backgroundSize: '200% 100%',
          animation: 'shimmer 2s infinite'
        }}
      ></div>
      <div 
        className="h-3 bg-gray-600 rounded"
        style={{
          width: '50%',
          background: 'linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-quaternary) 50%, var(--bg-tertiary) 75%)',
          backgroundSize: '200% 100%',
          animation: 'shimmer 2s infinite'
        }}
      ></div>
    </div>
  )
}

// 数据加载状态
export const DataLoader = ({ isLoading, children, fallback }) => {
  if (isLoading) {
    return fallback || <LoadingSpinner text="数据加载中..." />
  }
  
  return children
}

export default LoadingSpinner
