import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON><PERSON>, CircularProgress, MiniChart } from './DataVisualization'
import { ProgressIndicator, TrendIndicator } from './StatusIndicators'

const Dashboard = () => {
  const [stats, setStats] = useState({
    aiUsage: { current: 42, total: '∞', trend: '+12%', label: '本周使用率', growth: 'up' },
    activeStrategies: { value: 7, trend: '+3.2 SOL', label: '今日收益', growth: 'up' },
    subscription: { value: 'PRO', status: '2023-12-15', label: '到期日期', growth: 'stable' },
    walletBalance: { value: '48.75', usd: '$1,250', label: '钱包余额 (SOL)', growth: 'up' }
  })

  const [marketData, setMarketData] = useState({
    sentiment: 78.5,
    opportunities: 12,
    riskLevel: 'medium',
    lastUpdate: new Date().toLocaleTimeString()
  })

  const [hotCoins] = useState([
    { symbol: 'DO<PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>', change: '+12.4%', positive: true, chartData: [65, 68, 72, 70, 75, 78, 82] },
    { symbol: 'SHIB', name: 'Shiba Inu', change: '+8.2%', positive: true, chartData: [45, 48, 52, 55, 58, 60, 62] },
    { symbol: 'PEPE', name: 'Pepe Coin', change: '-3.1%', positive: false, chartData: [85, 82, 78, 75, 72, 70, 68] },
    { symbol: 'FLOKI', name: 'Floki Inu', change: '+5.7%', positive: true, chartData: [35, 38, 42, 45, 48, 50, 52] },
    { symbol: 'BONK', name: 'Bonk', change: '+21.8%', positive: true, chartData: [25, 30, 35, 42, 48, 55, 65] }
  ])

  // 模拟图表数据
  const [chartData] = useState([
    { label: '1月', value: 65 },
    { label: '2月', value: 72 },
    { label: '3月', value: 68 },
    { label: '4月', value: 78 },
    { label: '5月', value: 75 },
    { label: '6月', value: 82 }
  ])

  const features = [
    {
      icon: 'fas fa-fire',
      title: '热点检测',
      desc: '实时监测社交媒体趋势，捕捉病毒式传播的Meme币机会',
      action: '立即扫描',
      primary: true
    },
    {
      icon: 'fas fa-robot',
      title: '策略生成',
      desc: 'AI驱动的交易策略生成，基于市场数据和情绪分析',
      action: '创建策略',
      primary: false
    },
    {
      icon: 'fas fa-bolt',
      title: '一键部署',
      desc: '自动化部署代币到pump.fun和RAYDIUM平台',
      action: '立即部署',
      primary: true
    },
    {
      icon: 'fas fa-chart-pie',
      title: '流动性管理',
      desc: '智能管理资金池，优化流动性配置和风险控制',
      action: '管理流动性',
      primary: false
    }
  ]

  // 模拟实时数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => {
        const currentBalance = parseFloat(prev.walletBalance.value) || 48.75
        const newBalance = Math.max(0, currentBalance + (Math.random() - 0.5) * 0.1)
        return {
          ...prev,
          walletBalance: {
            ...prev.walletBalance,
            value: newBalance.toFixed(2),
            usd: `$${(newBalance * 25.64).toFixed(0)}`
          }
        }
      })

      // 更新市场数据
      setMarketData(prev => {
        const currentSentiment = parseFloat(prev.sentiment) || 78.5
        const newSentiment = Math.max(0, Math.min(100, currentSentiment + (Math.random() - 0.5) * 2))
        return {
          ...prev,
          sentiment: newSentiment.toFixed(1),
          lastUpdate: new Date().toLocaleTimeString()
        }
      })
    }, 10000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="animate-fade-in">
      {/* 增强版欢迎横幅 */}
      <div className="welcome-banner animate-fade-in">
        <div className="welcome-text">
          <h2 className="text-responsive-xl">欢迎回来，John! 🚀</h2>
          <p className="text-responsive-base">
            今日发现 <strong>{marketData.opportunities}个</strong> 新交易机会，市场情绪指数{' '}
            <strong style={{ color: '#10b981' }}>{marketData.sentiment}</strong>，建议关注DeFi和GameFi板块
          </p>
          <div className="flex-responsive" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            marginTop: '12px',
            fontSize: '14px',
            opacity: 0.9,
            flexWrap: 'wrap'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: '#10b981',
                animation: 'pulse 2s infinite'
              }}></div>
              <span className="hidden-mobile">实时监控</span>
              <span className="block-mobile">监控中</span>
            </div>
            <div className="hidden-mobile">风险等级: <span style={{ color: '#f59e0b' }}>中等</span></div>
            <div className="text-responsive-sm">更新: {marketData.lastUpdate}</div>
          </div>
        </div>
        <div className="welcome-actions">
          <button className="btn btn-primary btn-lg">
            <i className="fas fa-chart-line"></i>
            <span className="hidden-mobile">查看交易机会</span>
            <span className="block-mobile">查看机会</span>
          </button>
        </div>
      </div>

      {/* 统计卡片区 */}
      <div className="stats-container">
        {/* AI分析使用卡片 */}
        <div className="stat-card animate-slide-in-left delay-100">
          <div className="stat-header">
            <div className="stat-icon icon-ai">
              <i className="fas fa-brain"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--text-tertiary)',
              background: 'rgba(67, 56, 202, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(67, 56, 202, 0.2)'
            }}>
              无限制
            </div>
          </div>
          <div className="stat-value">{stats.aiUsage.current}/{stats.aiUsage.total}</div>
          <div className="stat-title">AI分析使用</div>
          <div className="stat-trend trend-up">
            <i className="fas fa-trending-up"></i>
            <span>{stats.aiUsage.trend} {stats.aiUsage.label}</span>
          </div>
          <div style={{ marginTop: 'var(--space-4)' }}>
            <ProgressIndicator
              value={85}
              max={100}
              label="使用率"
              variant="primary"
              size="sm"
            />
          </div>
        </div>

        {/* 活跃策略卡片 */}
        <div className="stat-card animate-slide-in-left delay-200">
          <div className="stat-header">
            <div className="stat-icon icon-trade">
              <i className="fas fa-cogs"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--success)',
              background: 'rgba(16, 185, 129, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(16, 185, 129, 0.2)'
            }}>
              运行中
            </div>
          </div>
          <div className="stat-value">{stats.activeStrategies.value}</div>
          <div className="stat-title">活跃策略</div>
          <div className="stat-trend trend-up">
            <i className="fas fa-coins"></i>
            <span>{stats.activeStrategies.trend}</span>
          </div>
          <div style={{
            position: 'absolute',
            top: 'var(--space-4)',
            right: 'var(--space-4)'
          }}>
            <CircularProgress
              value={87}
              max={100}
              size={60}
              strokeWidth={4}
              color="var(--success)"
              showValue={false}
            />
          </div>
        </div>

        {/* 订阅状态卡片 */}
        <div className="stat-card animate-slide-in-left delay-300">
          <div className="stat-header">
            <div className="stat-icon icon-sub">
              <i className="fas fa-crown"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--warning)',
              background: 'rgba(245, 158, 11, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(245, 158, 11, 0.2)'
            }}>
              高级会员
            </div>
          </div>
          <div className="stat-value" style={{ color: 'var(--warning)' }}>{stats.subscription.value}</div>
          <div className="stat-title">订阅状态</div>
          <div className="stat-trend trend-neutral">
            <i className="fas fa-calendar-check"></i>
            <span>到期 {stats.subscription.status}</span>
          </div>
        </div>

        {/* 钱包余额卡片 */}
        <div className="stat-card animate-slide-in-left delay-400">
          <div className="stat-header">
            <div className="stat-icon icon-wallet">
              <i className="fas fa-wallet"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--info)',
              background: 'rgba(2, 132, 199, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(2, 132, 199, 0.2)'
            }}>
              已连接
            </div>
          </div>
          <div className="stat-value" style={{ display: 'flex', alignItems: 'baseline', gap: '8px' }}>
            <span>{stats.walletBalance.value}</span>
            <span style={{ fontSize: '16px', color: 'var(--text-tertiary)' }}>SOL</span>
          </div>
          <div className="stat-title">钱包余额</div>
          <div className="stat-trend trend-up">
            <i className="fas fa-dollar-sign"></i>
            <span>≈{stats.walletBalance.usd} USD</span>
          </div>
        </div>
      </div>

      {/* 图表分析区 */}
      <div className="charts-container">
        {/* 市场情绪与交易趋势图表 */}
        <div className="chart-card animate-scale-in delay-500">
          <div className="card-header">
            <div className="card-title">
              <i className="fas fa-chart-area"></i>
              市场情绪与交易趋势
            </div>
            <div className="card-actions">
              <button className="btn btn-sm btn-outline">7天</button>
              <button className="btn btn-sm btn-primary">30天</button>
              <button className="btn btn-sm btn-outline">90天</button>
            </div>
          </div>
          <div className="chart-container">
            <LineChart
              data={chartData}
              width={600}
              height={300}
              color="var(--primary)"
              gradient={true}
              animated={true}
            />
          </div>
        </div>

        {/* 热门Meme币列表 */}
        <div className="chart-card animate-scale-in delay-600">
          <div className="card-header">
            <div className="card-title">
              <i className="fas fa-fire"></i>
              热门Meme币
            </div>
            <div className="card-actions">
              <button className="btn btn-sm btn-outline">
                <i className="fas fa-external-link-alt"></i>
                查看更多
              </button>
            </div>
          </div>
          <div className="hot-coins">
            {hotCoins.map((coin) => (
              <div key={coin.symbol} className="hot-coin">
                <div className="coin-icon">{coin.symbol}</div>
                <div className="coin-info">
                  <div className="coin-name">{coin.name}</div>
                  <div className="coin-symbol">{coin.symbol}</div>
                </div>
                <div style={{ width: '60px', height: '30px', margin: '0 8px' }}>
                  <MiniChart
                    data={coin.chartData}
                    type="line"
                    width={60}
                    height={30}
                    color={coin.positive ? 'var(--success)' : 'var(--danger)'}
                  />
                </div>
                <div className={`coin-change ${coin.positive ? 'change-up' : 'change-down'}`}>
                  {coin.change}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 核心功能模块 */}
      <div className="features-grid">
        {features.map((feature, index) => (
          <div
            key={feature.title}
            className={`feature-card hover-lift interactive-element animate-fade-in delay-${700 + index * 100}`}
            style={{ animationDelay: `${0.7 + index * 0.1}s` }}
          >
            <div className="feature-icon">
              <i className={feature.icon}></i>
            </div>
            <div className="feature-title">{feature.title}</div>
            <div className="feature-desc">{feature.desc}</div>
            <button
              className={`btn ${feature.primary ? 'btn-primary' : 'btn-secondary'} interactive-element`}
              onClick={() => {
                // 添加点击反馈
                console.log(`点击了 ${feature.title}`)
              }}
            >
              <i className={feature.icon}></i>
              {feature.action}
            </button>

            {/* 添加状态指示器 */}
            <div style={{
              position: 'absolute',
              top: '16px',
              right: '16px',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: feature.primary ? 'var(--success)' : 'var(--warning)',
              animation: 'pulse 2s infinite'
            }}></div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default Dashboard
