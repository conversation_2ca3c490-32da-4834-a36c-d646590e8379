import React from 'react'
import { NavLink } from 'react-router-dom'

const Sidebar = ({ collapsed, open, isMobile, onToggle, connectionStatus }) => {
  const navItems = [
    { path: '/', icon: 'fas fa-chart-line', label: '仪表盘' },
    { path: '/hotspot', icon: 'fas fa-chart-bar', label: '趋势热点' },
    { path: '/business-flow', icon: 'fas fa-brain', label: '交易策略' },
    { path: '/strategy', icon: 'fas fa-cogs', label: '自动化交易' },
    { path: '/wallet', icon: 'fas fa-credit-card', label: '支付与订阅' },
    { path: '/liquidity', icon: 'fas fa-users', label: '用户管理' },
    { path: '/exit', icon: 'fas fa-shield-alt', label: '安全设置' },
    { path: '/settings', icon: 'fas fa-cog', label: '系统设置' },
  ]

  // 生成侧边栏类名
  const getSidebarClasses = () => {
    let classes = 'sidebar'
    if (isMobile) {
      classes += open ? ' open' : ''
    } else {
      classes += collapsed ? ' collapsed' : ''
    }
    return classes
  }

  return (
    <aside className={getSidebarClasses()}>
      {/* 品牌标识区 */}
      <div className="logo">
        <i className="fas fa-robot"></i>
        <h1>MemeMaster AI</h1>
        {/* 移动端关闭按钮 */}
        {isMobile && (
          <button
            className="mobile-close-btn hidden-desktop"
            onClick={onToggle}
            aria-label="关闭菜单"
          >
            <i className="fas fa-times"></i>
          </button>
        )}
      </div>

      {/* 主导航菜单 */}
      <nav>
        {navItems.slice(0, 5).map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `nav-item ${isActive ? 'active' : ''}`
            }
          >
            <i className={item.icon}></i>
            <span>{item.label}</span>
          </NavLink>
        ))}

        <div className="divider"></div>

        {navItems.slice(5).map((item) => (
          <NavLink
            key={item.path}
            to={item.path}
            className={({ isActive }) =>
              `nav-item ${isActive ? 'active' : ''}`
            }
          >
            <i className={item.icon}></i>
            <span>{item.label}</span>
          </NavLink>
        ))}
      </nav>

      {/* 用户信息区 */}
      <div className="user-info">
        <div className="plan">PRO会员</div>
        <div className="info">
          <div style={{ fontWeight: 600, marginBottom: '4px' }}>John Doe</div>
          <div style={{ fontSize: '12px', opacity: 0.7 }}><EMAIL></div>
        </div>
      </div>
    </aside>
  )
}

export default Sidebar
