import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  Home,
  TrendingUp,
  Shield,
  Rocket,
  Workflow,
  Droplets,
  Target,
  Bot,
  LogOut,
  Settings,
  Zap
} from 'lucide-react'

const Sidebar = () => {
  const location = useLocation()
  const navigate = useNavigate()

  const menuItems = [
    { icon: Home, label: '主仪表盘', path: '/', active: location.pathname === '/' },
    { icon: TrendingUp, label: '趋势热点', path: '/hotspot', active: location.pathname === '/hotspot' },
    { icon: Shield, label: '策略引擎', path: '/strategy', active: location.pathname === '/strategy' },
    { icon: Rocket, label: '一键部署', path: '/deploy', active: location.pathname === '/deploy' },
    { icon: Workflow, label: '业务流程中心', path: '/business-flow', active: location.pathname === '/business-flow' },
    { icon: Droplets, label: '流动性管理', path: '/liquidity', active: location.pathname === '/liquidity' },
    { icon: Target, label: '动态退出', path: '/exit', active: location.pathname === '/exit' },
    { icon: Bot, label: '自动化工具', path: '/automation', active: location.pathname === '/automation' },
    { icon: Settings, label: '设置', path: '/settings', active: location.pathname === '/settings' }
  ]

  const handleNavigation = (path) => {
    navigate(path)
  }

  return (
    <div className="w-64 bg-black/20 backdrop-blur-md border-r border-white/10 h-screen flex flex-col relative z-30">
      {/* Logo区域 */}
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">MemeX</h1>
            <p className="text-xs text-white/60">AI Memetic Expert</p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {menuItems.map((item, index) => {
            const Icon = item.icon
            return (
              <button
                key={index}
                onClick={() => handleNavigation(item.path)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 focus:outline-none ${
                  item.active
                    ? 'bg-white/20 text-white shadow-lg'
                    : 'text-white/70 hover:bg-white/10 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </button>
            )
          })}
        </div>
      </nav>

      {/* 底部状态 */}
      <div className="p-4 border-t border-white/10">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
          <span className="text-sm font-medium text-green-400">系统运行正常</span>
        </div>
        <div className="text-xs text-white/60 mt-1">
          简化模式 - 无Redux依赖
        </div>
      </div>
    </div>
  )
}

export default Sidebar
