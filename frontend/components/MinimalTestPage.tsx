import React from 'react';

const MinimalTestPage: React.FC = () => {
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#1a202c', 
      color: 'white', 
      minHeight: '100vh',
      fontSize: '18px'
    }}>
      <h1 style={{ color: '#4299e1', fontSize: '32px', marginBottom: '20px' }}>
        🔍 最小化测试页面
      </h1>
      
      <div style={{ 
        backgroundColor: '#2d3748', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#68d391', marginBottom: '15px' }}>✅ 基础功能测试</h2>
        <ul style={{ listStyle: 'none', padding: 0 }}>
          <li style={{ marginBottom: '10px' }}>
            <span style={{ color: '#68d391' }}>✓</span> React 组件渲染正常
          </li>
          <li style={{ marginBottom: '10px' }}>
            <span style={{ color: '#68d391' }}>✓</span> TypeScript 编译成功
          </li>
          <li style={{ marginBottom: '10px' }}>
            <span style={{ color: '#68d391' }}>✓</span> CSS 样式应用正常
          </li>
          <li style={{ marginBottom: '10px' }}>
            <span style={{ color: '#68d391' }}>✓</span> 路由系统工作正常
          </li>
        </ul>
      </div>

      <div style={{ 
        backgroundColor: '#2a4365', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#63b3ed', marginBottom: '15px' }}>📊 调试信息</h2>
        <div style={{ fontSize: '14px', fontFamily: 'monospace' }}>
          <p><strong>当前时间:</strong> {new Date().toLocaleString()}</p>
          <p><strong>当前路径:</strong> {window.location.pathname}</p>
          <p><strong>用户代理:</strong> {navigator.userAgent.substring(0, 50)}...</p>
          <p><strong>屏幕尺寸:</strong> {window.innerWidth} x {window.innerHeight}</p>
          <p><strong>React 版本:</strong> {React.version}</p>
        </div>
      </div>

      <div style={{ 
        backgroundColor: '#553c9a', 
        padding: '20px', 
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h2 style={{ color: '#b794f6', marginBottom: '15px' }}>🔗 导航测试</h2>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {[
            { path: '/', label: '首页' },
            { path: '/dashboard', label: '仪表盘' },
            { path: '/strategy', label: '策略' },
            { path: '/hotspot', label: '热点' },
            { path: '/settings', label: '设置' },
            { path: '/test', label: '测试' }
          ].map((link) => (
            <a 
              key={link.path}
              href={link.path}
              style={{
                padding: '8px 16px',
                backgroundColor: '#4c51bf',
                color: 'white',
                textDecoration: 'none',
                borderRadius: '4px',
                fontSize: '14px',
                border: window.location.pathname === link.path ? '2px solid #68d391' : '2px solid transparent'
              }}
            >
              {link.label}
            </a>
          ))}
        </div>
      </div>

      <div style={{ 
        backgroundColor: '#742a2a', 
        padding: '20px', 
        borderRadius: '8px'
      }}>
        <h2 style={{ color: '#fc8181', marginBottom: '15px' }}>⚠️ 如果看到此页面</h2>
        <p style={{ marginBottom: '10px' }}>
          说明基础路由系统正常工作，问题可能在于：
        </p>
        <ul style={{ paddingLeft: '20px' }}>
          <li>GlobalLayout 组件未正确渲染</li>
          <li>Outlet 组件未正确显示子内容</li>
          <li>特定页面组件存在错误</li>
          <li>CSS 样式覆盖导致内容不可见</li>
        </ul>
      </div>
    </div>
  );
};

export default MinimalTestPage;
