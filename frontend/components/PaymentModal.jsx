import React, { useState } from 'react';
import { X, Copy, ExternalLink, CheckCircle, AlertCircle, Wallet } from 'lucide-react';

const PaymentModal = ({ isOpen, onClose, paymentDetails, onPaymentConfirm }) => {
  const [transactionHash, setTransactionHash] = useState('');
  const [isConfirming, setIsConfirming] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState('pending'); // pending, confirming, success, error

  if (!isOpen || !paymentDetails) return null;

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // 可以添加一个临时的复制成功提示
  };

  const handleConfirmPayment = async () => {
    if (!transactionHash.trim()) {
      alert('请输入交易哈希');
      return;
    }

    setIsConfirming(true);
    setPaymentStatus('confirming');

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/payment/confirm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          payment_id: paymentDetails.payment_id,
          transaction_hash: transactionHash
        })
      });

      if (response.ok) {
        setPaymentStatus('success');
        setTimeout(() => {
          onPaymentConfirm && onPaymentConfirm();
          onClose();
        }, 2000);
      } else {
        setPaymentStatus('error');
      }
    } catch (error) {
      console.error('确认支付失败:', error);
      setPaymentStatus('error');
    } finally {
      setIsConfirming(false);
    }
  };

  const openSolanaExplorer = () => {
    if (transactionHash) {
      window.open(`https://explorer.solana.com/tx/${transactionHash}`, '_blank');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-md w-full mx-4 p-6">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {paymentStatus === 'success' ? '支付成功' : 'Solana 支付'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {paymentStatus === 'success' ? (
          /* 支付成功状态 */
          <div className="text-center py-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h4 className="text-xl font-semibold text-gray-900 mb-2">支付确认成功！</h4>
            <p className="text-gray-600">您的功能已激活，感谢您的支付。</p>
          </div>
        ) : paymentStatus === 'error' ? (
          /* 支付错误状态 */
          <div className="text-center py-8">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h4 className="text-xl font-semibold text-gray-900 mb-2">支付确认失败</h4>
            <p className="text-gray-600 mb-4">请检查交易哈希是否正确，或稍后重试。</p>
            <button
              onClick={() => setPaymentStatus('pending')}
              className="text-blue-600 hover:text-blue-800"
            >
              重新尝试
            </button>
          </div>
        ) : (
          /* 正常支付流程 */
          <div className="space-y-6">
            {/* 支付信息 */}
            <div className="bg-blue-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <Wallet className="h-5 w-5 text-blue-600 mr-2" />
                <span className="font-medium text-blue-900">支付详情</span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">金额:</span>
                  <span className="font-medium">{paymentDetails.amount_sol} SOL</span>
                </div>
                
                {paymentDetails.amount_usd && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">约:</span>
                    <span className="text-gray-500">${paymentDetails.amount_usd}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-gray-600">功能:</span>
                  <span className="font-medium">{paymentDetails.description}</span>
                </div>
              </div>
            </div>

            {/* 收款地址 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                收款地址
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={paymentDetails.recipient_address}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                />
                <button
                  onClick={() => copyToClipboard(paymentDetails.recipient_address)}
                  className="p-2 text-gray-500 hover:text-gray-700"
                  title="复制地址"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </div>

            {/* 备注信息 */}
            {paymentDetails.memo && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  备注 (Memo)
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={paymentDetails.memo}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
                  />
                  <button
                    onClick={() => copyToClipboard(paymentDetails.memo)}
                    className="p-2 text-gray-500 hover:text-gray-700"
                    title="复制备注"
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}

            {/* 支付说明 */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">支付说明</h4>
              <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
                <li>使用您的Solana钱包向上述地址发送 {paymentDetails.amount_sol} SOL</li>
                <li>确保包含备注信息（如果有）</li>
                <li>复制交易哈希并在下方输入</li>
                <li>点击确认支付完成激活</li>
              </ol>
            </div>

            {/* 交易哈希输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                交易哈希 (Transaction Hash)
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={transactionHash}
                  onChange={(e) => setTransactionHash(e.target.value)}
                  placeholder="输入您的交易哈希..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                {transactionHash && (
                  <button
                    onClick={openSolanaExplorer}
                    className="p-2 text-gray-500 hover:text-gray-700"
                    title="在区块链浏览器中查看"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleConfirmPayment}
                disabled={!transactionHash.trim() || isConfirming}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isConfirming ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    确认中...
                  </div>
                ) : (
                  '确认支付'
                )}
              </button>
            </div>

            {/* 帮助链接 */}
            <div className="text-center">
              <p className="text-xs text-gray-500">
                需要帮助？
                <a href="#" className="text-blue-600 hover:text-blue-800 ml-1">
                  查看支付教程
                </a>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentModal;
