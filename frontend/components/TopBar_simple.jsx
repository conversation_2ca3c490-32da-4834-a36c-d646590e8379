import React, { useState, useEffect } from 'react'
import {
  Bell,
  User,
  Wifi,
  WifiOff,
  Activity,
  Clock
} from 'lucide-react'

const TopBar = () => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isOnline, setIsOnline] = useState(true)

  // 更新时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // 模拟连接状态
  useEffect(() => {
    const statusTimer = setInterval(() => {
      setIsOnline(Math.random() > 0.1) // 90% 概率在线
    }, 5000)

    return () => clearInterval(statusTimer)
  }, [])

  const formatTime = (date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  return (
    <div className="bg-black/20 backdrop-blur-md border-b border-white/10 px-6 py-4 relative z-30">
      <div className="flex items-center justify-between">
        {/* 左侧状态信息 */}
        <div className="flex items-center space-x-6">
          {/* 连接状态 */}
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <>
                <Wifi className="w-4 h-4 text-green-400" />
                <span className="text-sm font-medium text-green-400">在线</span>
              </>
            ) : (
              <>
                <WifiOff className="w-4 h-4 text-red-400" />
                <span className="text-sm font-medium text-red-400">离线</span>
              </>
            )}
          </div>

          {/* 系统状态 */}
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
            <span className="text-sm font-medium text-green-400">系统运行正常</span>
          </div>

          {/* 活动指示器 */}
          <div className="flex items-center space-x-2">
            <Activity className="w-4 h-4 text-blue-400" />
            <span className="text-sm text-blue-400">活跃</span>
          </div>
        </div>

        {/* 右侧信息 */}
        <div className="flex items-center space-x-6">
          {/* 时间显示 */}
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-white/60" />
            <div className="text-right">
              <div className="text-sm font-medium text-white">
                {formatTime(currentTime)}
              </div>
              <div className="text-xs text-white/60">
                {formatDate(currentTime)}
              </div>
            </div>
          </div>

          {/* 通知 */}
          <button className="relative p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
            <Bell className="w-4 h-4 text-white/70" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-white font-bold">3</span>
            </div>
          </button>

          {/* 用户信息 */}
          <button className="flex items-center space-x-2 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
            <User className="w-4 h-4 text-white/70" />
            <span className="text-sm text-white/70">管理员</span>
          </button>
        </div>
      </div>

      {/* 系统信息栏 */}
      <div className="mt-3 pt-3 border-t border-white/10">
        <div className="flex items-center justify-between text-xs text-white/60">
          <div className="flex items-center space-x-4">
            <span>CPU: 45%</span>
            <span>内存: 62%</span>
            <span>网络: 正常</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>版本: v2.0.0</span>
            <span>模式: 简化版</span>
            <span>运行时间: 2h 15m</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TopBar
