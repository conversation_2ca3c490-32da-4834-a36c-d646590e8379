import React, { useState, useRef, useEffect } from 'react';
import { FaBell, FaUserCog, FaCog, FaQuestionCircle, FaChevronDown } from 'react-icons/fa';

const InteractionDemo = () => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);
  
  // 创建refs用于检测点击外部
  const notificationsRef = useRef(null);
  const userMenuRef = useRef(null);
  const settingsMenuRef = useRef(null);

  // 点击外部关闭浮窗的逻辑
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查通知浮窗
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setShowNotifications(false);
      }
      
      // 检查用户菜单浮窗
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false);
      }
      
      // 检查设置菜单浮窗
      if (settingsMenuRef.current && !settingsMenuRef.current.contains(event.target)) {
        setShowSettingsMenu(false);
      }
    };

    // 只有当任何浮窗打开时才添加事件监听器
    if (showNotifications || showUserMenu || showSettingsMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showNotifications, showUserMenu, showSettingsMenu]);

  const notifications = [
    { id: 1, title: '新的交易机会', message: 'DOGE 价格上涨 12.4%', time: '2分钟前', type: 'success' },
    { id: 2, title: '策略执行完成', message: '策略 #007 已成功执行', time: '5分钟前', type: 'success' },
    { id: 3, title: '风险提醒', message: '市场波动较大，建议调整仓位', time: '10分钟前', type: 'warning' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            菜单交互逻辑演示
          </h1>
          <p className="text-white/70">
            演示新的交互逻辑：点击图标打开浮窗，点击页面任意位置（除浮窗内部）关闭浮窗
          </p>
        </div>

        {/* 模拟顶部菜单栏 */}
        <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">模拟顶部菜单栏</h2>
          
          <div className="flex items-center justify-between bg-gray-800/50 rounded-lg p-4">
            {/* 左侧：Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="text-white font-semibold">MemeX</span>
            </div>

            {/* 右侧：菜单图标 */}
            <div className="flex items-center space-x-4">
              
              {/* 通知铃铛 */}
              <div className="relative" ref={notificationsRef}>
                <button
                  onClick={() => setShowNotifications(true)}
                  className="relative p-2 rounded-lg hover:bg-gray-700 transition-colors text-gray-400 hover:text-white"
                >
                  <FaBell className="text-lg" />
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    3
                  </span>
                </button>

                {/* 通知浮窗 */}
                {showNotifications && (
                  <div className="absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50">
                    <div className="p-4 border-b border-gray-700">
                      <h3 className="text-lg font-semibold text-white">通知中心</h3>
                    </div>
                    <div className="max-h-64 overflow-y-auto">
                      {notifications.map((notification) => (
                        <div key={notification.id} className="p-4 border-b border-gray-700 hover:bg-gray-700/50 transition-colors">
                          <div className="flex items-start space-x-3">
                            <div className={`w-2 h-2 rounded-full mt-2 ${
                              notification.type === 'success' ? 'bg-green-500' : 
                              notification.type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                            }`}></div>
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                              <p className="text-sm text-gray-400 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-500 mt-2">{notification.time}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-4 border-t border-gray-700">
                      <button className="w-full text-center text-sm text-indigo-400 hover:text-indigo-300 transition-colors">
                        查看所有通知
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 设置菜单 */}
              <div className="relative" ref={settingsMenuRef}>
                <button
                  onClick={() => setShowSettingsMenu(true)}
                  className="p-2 rounded-lg hover:bg-gray-700 transition-colors text-gray-400 hover:text-white"
                >
                  <FaCog className="text-lg" />
                </button>

                {/* 设置浮窗 */}
                {showSettingsMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50">
                    <div className="p-2">
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors">
                        系统设置
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors">
                        主题设置
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors">
                        语言设置
                      </button>
                      <hr className="my-2 border-gray-700" />
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors">
                        帮助中心
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* 用户头像和菜单 */}
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setShowUserMenu(true)}
                  className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <img 
                    src="https://i.pravatar.cc/32?img=8" 
                    alt="User Avatar" 
                    className="w-8 h-8 rounded-full border-2 border-indigo-500"
                  />
                  <div className="text-left">
                    <p className="text-sm font-medium text-white">John Doe</p>
                    <p className="text-xs text-gray-400">PRO 会员</p>
                  </div>
                  <FaChevronDown className="text-gray-400 text-sm" />
                </button>

                {/* 用户下拉菜单 */}
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-700 rounded-xl shadow-xl z-50">
                    <div className="p-4 border-b border-gray-700">
                      <div className="flex items-center space-x-3">
                        <img 
                          src="https://i.pravatar.cc/40?img=8" 
                          alt="User Avatar" 
                          className="w-10 h-10 rounded-full border-2 border-indigo-500"
                        />
                        <div>
                          <p className="text-sm font-medium text-white">John Doe</p>
                          <p className="text-xs text-gray-400"><EMAIL></p>
                        </div>
                      </div>
                    </div>
                    <div className="p-2">
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors flex items-center space-x-2">
                        <FaUserCog className="text-sm" />
                        <span>账户管理</span>
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors flex items-center space-x-2">
                        <FaCog className="text-sm" />
                        <span>系统设置</span>
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm text-white hover:bg-gray-700 rounded-lg transition-colors flex items-center space-x-2">
                        <FaQuestionCircle className="text-sm" />
                        <span>帮助中心</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 交互说明 */}
        <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
          <h2 className="text-xl font-semibold text-white mb-4">交互逻辑说明</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">修改前（旧逻辑）</h3>
              <div className="space-y-2 text-white/70">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  <span>点击图标切换浮窗显示/隐藏</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  <span>再次点击同一图标关闭浮窗</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                  <span>点击其他区域浮窗仍然保持打开</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">修改后（新逻辑）</h3>
              <div className="space-y-2 text-white/70">
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>点击图标打开浮窗</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>点击浮窗内部不会关闭</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>点击页面任意其他位置关闭浮窗</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="text-blue-400 font-semibold mb-2">💡 试试看</h4>
            <p className="text-blue-300 text-sm">
              点击上方的通知铃铛、设置齿轮或用户头像来打开浮窗，然后点击页面的其他地方来关闭它们。
              注意浮窗内部的点击不会关闭浮窗。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InteractionDemo;
