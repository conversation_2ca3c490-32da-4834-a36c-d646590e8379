import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  Clock, ZoomIn, ZoomOut, Edit, Save, X, Plus,
  RotateCcw, Filter, Star, AlertTriangle, <PERSON>rk<PERSON>, Brain
} from 'lucide-react'

const TimelineView = ({ events = [], predictions = [], onEventUpdate, onEventCreate }) => {
  // 时间轴状态
  const [zoomLevel, setZoomLevel] = useState(3) // 0: 年, 1: 月, 2: 周, 3: 天, 4: 小时, 5: 分钟
  const [currentTime, setCurrentTime] = useState(new Date())
  const [viewportStart, setViewportStart] = useState(new Date(Date.now() - 24 * 60 * 60 * 1000))
  const [viewportEnd, setViewportEnd] = useState(new Date(Date.now() + 24 * 60 * 60 * 1000))
  
  // 编辑状态
  const [editingEvent, setEditingEvent] = useState(null)
  const [showEventForm, setShowEventForm] = useState(false)
  const [selectedEvent, setSelectedEvent] = useState(null)
  
  // 过滤状态
  const [filters, setFilters] = useState({
    showPredictions: true,
    showEvents: true,
    confidenceFilter: 'ALL', // ALL, HIGH, MEDIUM, LOW
    categoryFilter: 'ALL'
  })

  const timelineRef = useRef(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState(null)
  const [isWheelZooming, setIsWheelZooming] = useState(false)
  const [isTouchZooming, setIsTouchZooming] = useState(false)
  const [touchState, setTouchState] = useState({
    initialDistance: 0,
    initialScale: 1,
    centerPoint: null,
    lastScale: 1
  })

  // 时间轴配置
  const zoomConfigs = [
    { level: 0, unit: 'year', step: 1, format: 'YYYY', label: '年视图', maxRange: 10 },
    { level: 1, unit: 'month', step: 1, format: 'YYYY-MM', label: '月视图', maxRange: 24 },
    { level: 2, unit: 'week', step: 1, format: 'MM-DD', label: '周视图', maxRange: 12 },
    { level: 3, unit: 'day', step: 1, format: 'MM-DD', label: '日视图', maxRange: 30 },
    { level: 4, unit: 'hour', step: 1, format: 'HH:mm', label: '小时视图', maxRange: 48 },
    { level: 5, unit: 'minute', step: 5, format: 'HH:mm', label: '分钟视图', maxRange: 120 }
  ]

  const currentConfig = zoomConfigs[zoomLevel]

  // 检测设备类型和触控板支持
  const [deviceInfo, setDeviceInfo] = useState({
    isMac: false,
    hasTouch: false,
    supportsGestures: false
  })

  useEffect(() => {
    // 使用userAgent检测Mac而不是废弃的platform属性
    const isMac = navigator.userAgent.toUpperCase().indexOf('MAC') >= 0
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const supportsGestures = 'ongesturestart' in window

    setDeviceInfo({
      isMac,
      hasTouch,
      supportsGestures
    })
  }, [])

  // 格式化时间显示
  const formatTimeLabel = (date, config) => {
    const options = {
      year: config.format.includes('YYYY') ? 'numeric' : undefined,
      month: config.format.includes('MM') ? '2-digit' : undefined,
      day: config.format.includes('DD') ? '2-digit' : undefined,
      hour: config.format.includes('HH') ? '2-digit' : undefined,
      minute: config.format.includes('mm') ? '2-digit' : undefined
    }
    return date.toLocaleString('zh-CN', options)
  }

  // 生成时间刻度
  const generateTimeMarks = useCallback(() => {
    const marks = []
    const config = currentConfig
    const start = new Date(viewportStart)
    const end = new Date(viewportEnd)
    
    // 根据缩放级别调整起始时间到合适的边界
    switch (config.unit) {
      case 'year':
        start.setMonth(0, 1)
        start.setHours(0, 0, 0, 0)
        break
      case 'month':
        start.setDate(1)
        start.setHours(0, 0, 0, 0)
        break
      case 'week':
        const dayOfWeek = start.getDay()
        start.setDate(start.getDate() - dayOfWeek)
        start.setHours(0, 0, 0, 0)
        break
      case 'day':
        start.setHours(0, 0, 0, 0)
        break
      case 'hour':
        start.setMinutes(0, 0, 0)
        break
      case 'minute':
        start.setSeconds(0, 0)
        break
    }

    let current = new Date(start)
    while (current <= end) {
      marks.push({
        time: new Date(current),
        label: formatTimeLabel(current, config),
        isNow: Math.abs(current - currentTime) < (config.unit === 'minute' ? 60000 : 
                                                  config.unit === 'hour' ? 3600000 : 
                                                  config.unit === 'day' ? 86400000 : 0)
      })

      // 增加时间间隔
      switch (config.unit) {
        case 'year':
          current.setFullYear(current.getFullYear() + config.step)
          break
        case 'month':
          current.setMonth(current.getMonth() + config.step)
          break
        case 'week':
          current.setDate(current.getDate() + 7 * config.step)
          break
        case 'day':
          current.setDate(current.getDate() + config.step)
          break
        case 'hour':
          current.setHours(current.getHours() + config.step)
          break
        case 'minute':
          current.setMinutes(current.getMinutes() + config.step)
          break
      }
    }

    return marks
  }, [viewportStart, viewportEnd, currentConfig, currentTime])

  // 计算事件在时间轴上的位置
  const getEventPosition = (eventTime) => {
    const totalDuration = viewportEnd - viewportStart
    const eventOffset = new Date(eventTime) - viewportStart
    return Math.max(0, Math.min(100, (eventOffset / totalDuration) * 100))
  }

  // 缩放控制 - 支持指定中心点的缩放
  const zoomIn = (centerPoint = null) => {
    if (zoomLevel < zoomConfigs.length - 1) {
      setZoomLevel(prev => prev + 1)
      // 缩小视口范围
      const center = centerPoint || new Date((viewportStart.getTime() + viewportEnd.getTime()) / 2)
      const newRange = (viewportEnd - viewportStart) / 3
      setViewportStart(new Date(center.getTime() - newRange / 2))
      setViewportEnd(new Date(center.getTime() + newRange / 2))
    }
  }

  const zoomOut = (centerPoint = null) => {
    if (zoomLevel > 0) {
      setZoomLevel(prev => prev - 1)
      // 扩大视口范围
      const center = centerPoint || new Date((viewportStart.getTime() + viewportEnd.getTime()) / 2)
      const newRange = (viewportEnd - viewportStart) * 3
      const maxFutureDate = new Date()
      maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

      const newStart = new Date(center.getTime() - newRange / 2)
      const newEnd = new Date(Math.min(center.getTime() + newRange / 2, maxFutureDate.getTime()))

      setViewportStart(newStart)
      setViewportEnd(newEnd)
    }
  }

  // 鼠标滚轮缩放处理
  const handleWheel = useCallback((e) => {
    e.preventDefault()

    // 防止过于频繁的缩放操作
    if (isWheelZooming) return

    setIsWheelZooming(true)

    // 计算鼠标在时间轴上的位置对应的时间点
    const timelineRect = timelineRef.current?.getBoundingClientRect()
    if (!timelineRect) {
      setIsWheelZooming(false)
      return
    }

    const mouseX = e.clientX - timelineRect.left
    const mousePercentage = Math.max(0, Math.min(1, mouseX / timelineRect.width))
    const totalDuration = viewportEnd - viewportStart
    const mouseTime = new Date(viewportStart.getTime() + totalDuration * mousePercentage)

    // 检测是否按住Ctrl键进行精细缩放
    const isFineTuning = e.ctrlKey || e.metaKey

    // 根据滚轮方向和敏感度进行缩放
    if (e.deltaY < 0) {
      // 向上滚动 - 放大
      if (isFineTuning) {
        // 精细缩放：调整视口范围而不改变缩放级别
        const currentRange = viewportEnd - viewportStart
        const newRange = currentRange * 0.9 // 缩小10%
        const centerOffset = (mouseTime.getTime() - viewportStart.getTime()) / currentRange

        const newStart = new Date(mouseTime.getTime() - newRange * centerOffset)
        const newEnd = new Date(mouseTime.getTime() + newRange * (1 - centerOffset))

        setViewportStart(newStart)
        setViewportEnd(newEnd)
      } else {
        zoomIn(mouseTime)
      }
    } else {
      // 向下滚动 - 缩小
      if (isFineTuning) {
        // 精细缩放：调整视口范围而不改变缩放级别
        const currentRange = viewportEnd - viewportStart
        const newRange = currentRange * 1.1 // 扩大10%
        const centerOffset = (mouseTime.getTime() - viewportStart.getTime()) / currentRange

        const maxFutureDate = new Date()
        maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

        const newStart = new Date(mouseTime.getTime() - newRange * centerOffset)
        const newEnd = new Date(Math.min(mouseTime.getTime() + newRange * (1 - centerOffset), maxFutureDate.getTime()))

        setViewportStart(newStart)
        setViewportEnd(newEnd)
      } else {
        zoomOut(mouseTime)
      }
    }

    // 防抖处理
    setTimeout(() => {
      setIsWheelZooming(false)
    }, isFineTuning ? 50 : 100) // 精细缩放时更快的响应
  }, [zoomLevel, viewportStart, viewportEnd, isWheelZooming, zoomIn, zoomOut])





  // 计算两点之间的距离
  const getDistance = useCallback((touch1, touch2) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // 计算两点的中心点
  const getCenter = useCallback((touch1, touch2) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2
    }
  }, [])

  // 自动调整缩放级别
  const autoAdjustZoomLevel = useCallback((range) => {
    const rangeInMs = range

    // 根据时间范围自动选择合适的缩放级别
    if (rangeInMs <= 2 * 60 * 60 * 1000) { // 2小时以内
      setZoomLevel(5) // 分钟视图
    } else if (rangeInMs <= 48 * 60 * 60 * 1000) { // 48小时以内
      setZoomLevel(4) // 小时视图
    } else if (rangeInMs <= 30 * 24 * 60 * 60 * 1000) { // 30天以内
      setZoomLevel(3) // 日视图
    } else if (rangeInMs <= 12 * 7 * 24 * 60 * 60 * 1000) { // 12周以内
      setZoomLevel(2) // 周视图
    } else if (rangeInMs <= 24 * 30 * 24 * 60 * 60 * 1000) { // 24个月以内
      setZoomLevel(1) // 月视图
    } else {
      setZoomLevel(0) // 年视图
    }
  }, [])

  // 执行触控缩放
  const performTouchZoom = useCallback((direction, centerTime, intensity) => {
    try {
      if (!centerTime || !direction || typeof intensity !== 'number' || intensity <= 0) {
        return
      }

      const currentRange = viewportEnd - viewportStart
      if (currentRange <= 0) return

      // 限制缩放强度，防止过度缩放
      const clampedIntensity = Math.max(0.01, Math.min(1, intensity))
      const zoomFactor = 1 + (clampedIntensity * 2) // 调整缩放强度

      let newRange
      if (direction === 'in') {
        newRange = currentRange / zoomFactor
      } else {
        newRange = currentRange * zoomFactor
      }

      // 限制缩放范围
      const minRange = 60 * 1000 // 最小1分钟
      const maxRange = 10 * 365 * 24 * 60 * 60 * 1000 // 最大10年
      newRange = Math.max(minRange, Math.min(maxRange, newRange))

      // 检查新范围是否有效
      if (newRange === currentRange) return

      const centerOffset = (centerTime.getTime() - viewportStart.getTime()) / currentRange
      const clampedOffset = Math.max(0, Math.min(1, centerOffset))

      const newStart = new Date(centerTime.getTime() - newRange * clampedOffset)
      const newEnd = new Date(centerTime.getTime() + newRange * (1 - clampedOffset))

      // 确保不超过未来2年的限制
      const maxFutureDate = new Date()
      maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 2)

      if (newEnd <= maxFutureDate && newStart < newEnd) {
        setViewportStart(newStart)
        setViewportEnd(newEnd)

        // 根据新的时间范围自动调整缩放级别
        autoAdjustZoomLevel(newRange)
      }
    } catch (error) {
      console.warn('Touch zoom error:', error)
    }
  }, [viewportStart, viewportEnd, autoAdjustZoomLevel])

  // 触摸开始处理
  const handleTouchStart = useCallback((e) => {
    try {
      if (e.touches.length === 2) {
        e.preventDefault()
        setIsTouchZooming(true)

        const touch1 = e.touches[0]
        const touch2 = e.touches[1]

        if (!touch1 || !touch2) return

        const distance = getDistance(touch1, touch2)
        const center = getCenter(touch1, touch2)

        // 计算中心点对应的时间
        const timelineRect = timelineRef.current?.getBoundingClientRect()
        if (timelineRect && distance > 0) {
          const centerX = center.x - timelineRect.left
          const centerPercentage = Math.max(0, Math.min(1, centerX / timelineRect.width))
          const totalDuration = viewportEnd - viewportStart
          const centerTime = new Date(viewportStart.getTime() + totalDuration * centerPercentage)

          setTouchState({
            initialDistance: distance,
            initialScale: 1,
            centerPoint: centerTime,
            lastScale: 1
          })
        }
      }
    } catch (error) {
      console.warn('Touch start error:', error)
      setIsTouchZooming(false)
    }
  }, [viewportStart, viewportEnd, getDistance, getCenter])

  // 触摸移动处理
  const handleTouchMove = useCallback((e) => {
    try {
      if (e.touches.length === 2 && isTouchZooming && touchState.centerPoint) {
        e.preventDefault()

        const touch1 = e.touches[0]
        const touch2 = e.touches[1]

        if (!touch1 || !touch2) return

        const currentDistance = getDistance(touch1, touch2)

        if (touchState.initialDistance > 0 && currentDistance > 0) {
          const scale = currentDistance / touchState.initialDistance
          const deltaScale = scale - touchState.lastScale

          // 设置缩放敏感度
          const sensitivity = 0.5
          const adjustedDelta = deltaScale * sensitivity

          if (Math.abs(adjustedDelta) > 0.05 && Math.abs(adjustedDelta) < 2) { // 防止过于敏感或异常的缩放
            if (adjustedDelta > 0) {
              // 双指分开 - 放大
              performTouchZoom('in', touchState.centerPoint, Math.abs(adjustedDelta))
            } else {
              // 双指合拢 - 缩小
              performTouchZoom('out', touchState.centerPoint, Math.abs(adjustedDelta))
            }

            setTouchState(prev => ({
              ...prev,
              lastScale: scale
            }))
          }
        }
      }
    } catch (error) {
      console.warn('Touch move error:', error)
    }
  }, [isTouchZooming, touchState, performTouchZoom, getDistance])

  // 触摸结束处理
  const handleTouchEnd = useCallback((e) => {
    try {
      if (e.touches.length < 2) {
        setIsTouchZooming(false)
        setTouchState({
          initialDistance: 0,
          initialScale: 1,
          centerPoint: null,
          lastScale: 1
        })
      }
    } catch (error) {
      console.warn('Touch end error:', error)
      // 确保状态被重置
      setIsTouchZooming(false)
      setTouchState({
        initialDistance: 0,
        initialScale: 1,
        centerPoint: null,
        lastScale: 1
      })
    }
  }, [])

  // 重置到当前时间
  const resetToNow = () => {
    const now = new Date()
    setCurrentTime(now)
    
    // 根据缩放级别设置合适的视口
    const ranges = {
      0: 365 * 24 * 60 * 60 * 1000, // 年: 1年
      1: 30 * 24 * 60 * 60 * 1000,  // 月: 30天
      2: 7 * 24 * 60 * 60 * 1000,   // 周: 7天
      3: 24 * 60 * 60 * 1000,       // 日: 24小时
      4: 6 * 60 * 60 * 1000,        // 小时: 6小时
      5: 2 * 60 * 60 * 1000         // 分钟: 2小时
    }
    
    const range = ranges[zoomLevel]
    setViewportStart(new Date(now.getTime() - range / 2))
    setViewportEnd(new Date(now.getTime() + range / 2))
  }

  // 拖拽平移
  const handleMouseDown = (e) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX, time: viewportStart.getTime() })
  }

  const handleMouseMove = useCallback((e) => {
    if (!isDragging || !dragStart) return

    const deltaX = e.clientX - dragStart.x
    const timelineWidth = timelineRef.current?.offsetWidth || 1000
    const totalDuration = viewportEnd - viewportStart
    const timeDelta = (deltaX / timelineWidth) * totalDuration

    const newStart = new Date(dragStart.time - timeDelta)
    const newEnd = new Date(newStart.getTime() + totalDuration)

    setViewportStart(newStart)
    setViewportEnd(newEnd)
  }, [isDragging, dragStart, viewportEnd, viewportStart])

  const handleMouseUp = () => {
    setIsDragging(false)
    setDragStart(null)
  }

  // 过滤事件
  const filteredEvents = events.filter(event => {
    if (!filters.showEvents) return false
    if (filters.categoryFilter !== 'ALL' && event.category !== filters.categoryFilter) return false
    return true
  })

  const filteredPredictions = predictions.filter(prediction => {
    if (!filters.showPredictions) return false
    if (filters.confidenceFilter !== 'ALL' && prediction.confidence !== filters.confidenceFilter) return false
    return true
  })

  // 事件编辑
  const startEditEvent = (event) => {
    setEditingEvent({ ...event })
    setSelectedEvent(event)
  }

  const saveEventEdit = () => {
    if (editingEvent && onEventUpdate) {
      onEventUpdate(editingEvent)
    }
    setEditingEvent(null)
    setSelectedEvent(null)
  }

  const cancelEventEdit = () => {
    setEditingEvent(null)
    setSelectedEvent(null)
  }

  // 获取事件优先级颜色
  const getEventPriorityColor = (event, prediction) => {
    if (prediction) {
      if (prediction.confidence === 'HIGH') return 'border-red-500 bg-red-500/20'
      if (prediction.confidence === 'MEDIUM') return 'border-yellow-500 bg-yellow-500/20'
      return 'border-green-500 bg-green-500/20'
    }
    
    if (event.importance === 'HIGH') return 'border-purple-500 bg-purple-500/20'
    if (event.importance === 'MEDIUM') return 'border-blue-500 bg-blue-500/20'
    return 'border-gray-500 bg-gray-500/20'
  }

  // 监听鼠标事件
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove])

  // 监听滚轮事件
  useEffect(() => {
    const timelineElement = timelineRef.current
    if (timelineElement) {
      timelineElement.addEventListener('wheel', handleWheel, { passive: false })
      return () => {
        timelineElement.removeEventListener('wheel', handleWheel)
      }
    }
  }, [handleWheel])

  // 监听触摸事件
  useEffect(() => {
    const timelineElement = timelineRef.current
    if (timelineElement) {
      timelineElement.addEventListener('touchstart', handleTouchStart, { passive: false })
      timelineElement.addEventListener('touchmove', handleTouchMove, { passive: false })
      timelineElement.addEventListener('touchend', handleTouchEnd, { passive: false })

      return () => {
        timelineElement.removeEventListener('touchstart', handleTouchStart)
        timelineElement.removeEventListener('touchmove', handleTouchMove)
        timelineElement.removeEventListener('touchend', handleTouchEnd)
      }
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  // 监听触控板手势事件 (Safari/WebKit)
  useEffect(() => {
    const timelineElement = timelineRef.current
    if (timelineElement) {
      const handleGestureStart = (e) => {
        try {
          e.preventDefault()
          setIsTouchZooming(true)

          // 计算手势中心点对应的时间
          const timelineRect = timelineElement.getBoundingClientRect()
          if (!timelineRect) return

          const centerX = e.clientX - timelineRect.left
          const centerPercentage = Math.max(0, Math.min(1, centerX / timelineRect.width))
          const totalDuration = viewportEnd - viewportStart
          const centerTime = new Date(viewportStart.getTime() + totalDuration * centerPercentage)

          setTouchState({
            initialDistance: 0,
            initialScale: e.scale || 1,
            centerPoint: centerTime,
            lastScale: e.scale || 1
          })
        } catch (error) {
          console.warn('Gesture start error:', error)
          setIsTouchZooming(false)
        }
      }

      const handleGestureChange = (e) => {
        try {
          e.preventDefault()
          if (isTouchZooming && touchState.centerPoint) {
            const scale = e.scale || 1

            // 验证scale值的有效性
            if (!isFinite(scale) || scale <= 0) return

            const deltaScale = scale - touchState.lastScale

            // 设置手势敏感度
            const sensitivity = 0.3
            const adjustedDelta = deltaScale * sensitivity

            if (Math.abs(adjustedDelta) > 0.02 && Math.abs(adjustedDelta) < 1) {
              if (adjustedDelta > 0) {
                // 双指分开 - 放大
                performTouchZoom('in', touchState.centerPoint, Math.abs(adjustedDelta))
              } else {
                // 双指合拢 - 缩小
                performTouchZoom('out', touchState.centerPoint, Math.abs(adjustedDelta))
              }

              setTouchState(prev => ({
                ...prev,
                lastScale: scale
              }))
            }
          }
        } catch (error) {
          console.warn('Gesture change error:', error)
        }
      }

      const handleGestureEnd = (e) => {
        e.preventDefault()
        setIsTouchZooming(false)
        setTouchState({
          initialDistance: 0,
          initialScale: 1,
          centerPoint: null,
          lastScale: 1
        })
      }

      // 添加WebKit手势事件监听器
      timelineElement.addEventListener('gesturestart', handleGestureStart, { passive: false })
      timelineElement.addEventListener('gesturechange', handleGestureChange, { passive: false })
      timelineElement.addEventListener('gestureend', handleGestureEnd, { passive: false })

      return () => {
        timelineElement.removeEventListener('gesturestart', handleGestureStart)
        timelineElement.removeEventListener('gesturechange', handleGestureChange)
        timelineElement.removeEventListener('gestureend', handleGestureEnd)
      }
    }
  }, [viewportStart, viewportEnd, isTouchZooming, touchState, performTouchZoom])

  // 监听现代浏览器的Pointer Events (更好的触控板支持)
  useEffect(() => {
    const timelineElement = timelineRef.current
    if (timelineElement && window.PointerEvent) {
      let pointers = new Map()
      let initialDistance = 0
      let initialCenter = null

      const handlePointerDown = (e) => {
        pointers.set(e.pointerId, e)

        if (pointers.size === 2) {
          const [p1, p2] = Array.from(pointers.values())
          initialDistance = getDistance(p1, p2)
          initialCenter = getCenter(p1, p2)

          // 计算中心点对应的时间
          const timelineRect = timelineElement.getBoundingClientRect()
          const centerX = initialCenter.x - timelineRect.left
          const centerPercentage = Math.max(0, Math.min(1, centerX / timelineRect.width))
          const totalDuration = viewportEnd - viewportStart
          const centerTime = new Date(viewportStart.getTime() + totalDuration * centerPercentage)

          setIsTouchZooming(true)
          setTouchState({
            initialDistance,
            initialScale: 1,
            centerPoint: centerTime,
            lastScale: 1
          })
        }
      }

      const handlePointerMove = (e) => {
        if (pointers.has(e.pointerId)) {
          pointers.set(e.pointerId, e)

          if (pointers.size === 2 && isTouchZooming) {
            const [p1, p2] = Array.from(pointers.values())
            const currentDistance = getDistance(p1, p2)

            if (initialDistance > 0) {
              const scale = currentDistance / initialDistance
              const deltaScale = scale - touchState.lastScale

              const sensitivity = 0.4
              const adjustedDelta = deltaScale * sensitivity

              if (Math.abs(adjustedDelta) > 0.03) {
                if (adjustedDelta > 0) {
                  performTouchZoom('in', touchState.centerPoint, Math.abs(adjustedDelta))
                } else {
                  performTouchZoom('out', touchState.centerPoint, Math.abs(adjustedDelta))
                }

                setTouchState(prev => ({
                  ...prev,
                  lastScale: scale
                }))
              }
            }
          }
        }
      }

      const handlePointerUp = (e) => {
        pointers.delete(e.pointerId)

        if (pointers.size < 2) {
          setIsTouchZooming(false)
          setTouchState({
            initialDistance: 0,
            initialScale: 1,
            centerPoint: null,
            lastScale: 1
          })
          initialDistance = 0
          initialCenter = null
        }
      }

      timelineElement.addEventListener('pointerdown', handlePointerDown)
      timelineElement.addEventListener('pointermove', handlePointerMove)
      timelineElement.addEventListener('pointerup', handlePointerUp)
      timelineElement.addEventListener('pointercancel', handlePointerUp)

      return () => {
        timelineElement.removeEventListener('pointerdown', handlePointerDown)
        timelineElement.removeEventListener('pointermove', handlePointerMove)
        timelineElement.removeEventListener('pointerup', handlePointerUp)
        timelineElement.removeEventListener('pointercancel', handlePointerUp)
      }
    }
  }, [viewportStart, viewportEnd, isTouchZooming, touchState, performTouchZoom, getDistance, getCenter])

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e) => {
      // 只在时间轴获得焦点时响应键盘事件
      if (!timelineRef.current?.contains(document.activeElement)) return

      switch (e.key) {
        case '+':
        case '=':
          e.preventDefault()
          zoomIn()
          break
        case '-':
        case '_':
          e.preventDefault()
          zoomOut()
          break
        case '0':
          e.preventDefault()
          resetToNow()
          break
        case 'ArrowLeft':
          e.preventDefault()
          // 向左平移
          const leftShift = (viewportEnd - viewportStart) * 0.1
          setViewportStart(new Date(viewportStart.getTime() - leftShift))
          setViewportEnd(new Date(viewportEnd.getTime() - leftShift))
          break
        case 'ArrowRight':
          e.preventDefault()
          // 向右平移
          const rightShift = (viewportEnd - viewportStart) * 0.1
          setViewportStart(new Date(viewportStart.getTime() + rightShift))
          setViewportEnd(new Date(viewportEnd.getTime() + rightShift))
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [viewportStart, viewportEnd, zoomLevel, zoomIn, zoomOut, resetToNow])

  // 实时更新当前时间
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000) // 每分钟更新一次

    return () => clearInterval(timer)
  }, [])

  const timeMarks = generateTimeMarks()

  return (
    <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6">
      {/* 时间轴控制栏 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
            <Clock className="w-5 h-5 text-blue-400" />
            <span>智能时间轴</span>
          </h3>
          <div className="text-sm text-white/60">
            {currentConfig.label} • {formatTimeLabel(viewportStart, currentConfig)} - {formatTimeLabel(viewportEnd, currentConfig)}
          </div>
          <div className="text-xs text-white/40 mt-1">
            {deviceInfo.isMac ? (
              <>💡 滚轮缩放 • Cmd+滚轮精细缩放 • 双指开合缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
            ) : deviceInfo.hasTouch ? (
              <>💡 滚轮缩放 • Ctrl+滚轮精细缩放 • 双指触控缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
            ) : (
              <>💡 滚轮缩放 • Ctrl+滚轮精细缩放 • 拖拽平移 • +/- 键缩放 • 0 键回到现在 • ←→ 键平移</>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* 缩放控制 */}
          <div className="flex items-center space-x-1 bg-white/5 rounded-lg p-1">
            <button
              onClick={() => zoomOut()}
              disabled={zoomLevel === 0}
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              title="缩小 (或使用滚轮向下)"
            >
              <ZoomOut className="w-4 h-4" />
            </button>

            {/* 缩放级别指示器 */}
            <div className="px-3 py-1 text-xs text-white/70 min-w-20 text-center relative">
              <div className="font-medium">{currentConfig.label}</div>
              <div className="flex items-center justify-center mt-1 space-x-1">
                {zoomConfigs.map((_, index) => (
                  <div
                    key={index}
                    className={`w-1 h-1 rounded-full transition-all duration-200 ${
                      index === zoomLevel
                        ? 'bg-blue-400 scale-150'
                        : index < zoomLevel
                          ? 'bg-white/50'
                          : 'bg-white/20'
                    }`}
                  />
                ))}
              </div>
            </div>

            <button
              onClick={() => zoomIn()}
              disabled={zoomLevel === zoomConfigs.length - 1}
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              title="放大 (或使用滚轮向上)"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>

          {/* 导航控制 */}
          <button
            onClick={resetToNow}
            className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded transition-all duration-200"
            title="回到现在"
          >
            <RotateCcw className="w-4 h-4" />
          </button>

          {/* 触控板支持指示器 */}
          {(deviceInfo.supportsGestures || deviceInfo.hasTouch) && (
            <div className="flex items-center space-x-1 text-xs text-white/50">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>{deviceInfo.isMac ? '触控板' : '触控'}</span>
            </div>
          )}

          {/* 添加事件 */}
          <button
            onClick={() => setShowEventForm(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span className="text-sm">添加事件</span>
          </button>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="flex items-center space-x-4 mb-6 p-3 bg-white/5 rounded-lg">
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-white/70" />
          <span className="text-sm text-white/70">过滤:</span>
        </div>
        
        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={filters.showEvents}
            onChange={(e) => setFilters(prev => ({ ...prev, showEvents: e.target.checked }))}
            className="w-4 h-4 text-blue-600 rounded"
          />
          <span className="text-sm text-white">事件</span>
        </label>

        <label className="flex items-center space-x-2 cursor-pointer">
          <input
            type="checkbox"
            checked={filters.showPredictions}
            onChange={(e) => setFilters(prev => ({ ...prev, showPredictions: e.target.checked }))}
            className="w-4 h-4 text-blue-600 rounded"
          />
          <span className="text-sm text-white">预测</span>
        </label>

        <select
          value={filters.confidenceFilter}
          onChange={(e) => setFilters(prev => ({ ...prev, confidenceFilter: e.target.value }))}
          className="bg-white/10 border border-white/20 rounded px-2 py-1 text-sm text-white"
        >
          <option value="ALL">所有置信度</option>
          <option value="HIGH">高置信度</option>
          <option value="MEDIUM">中等置信度</option>
          <option value="LOW">低置信度</option>
        </select>
      </div>

      {/* 时间轴主体 */}
      <div className="relative">
        {/* 时间轴容器 */}
        <div
          ref={timelineRef}
          className="relative h-96 bg-gradient-to-r from-white/5 to-white/10 rounded-lg overflow-hidden cursor-grab active:cursor-grabbing focus:outline-none focus:ring-2 focus:ring-blue-500/50"
          onMouseDown={handleMouseDown}
          style={{ userSelect: 'none' }}
          tabIndex={0}
          title="拖拽平移 • 滚轮缩放 • Ctrl+滚轮精细缩放 • 双指开合缩放 • 键盘快捷键: +/- 缩放, 0 回到现在, ←→ 平移"
        >
          {/* 时间刻度线 */}
          <div className="absolute top-0 left-0 right-0 h-8 border-b border-white/20">
            {timeMarks.map((mark, index) => (
              <div
                key={index}
                className="absolute top-0 h-full flex flex-col items-center"
                style={{ left: `${getEventPosition(mark.time)}%` }}
              >
                <div className={`w-px h-4 ${mark.isNow ? 'bg-red-400' : 'bg-white/30'}`}></div>
                <div className={`text-xs mt-1 ${mark.isNow ? 'text-red-400 font-bold' : 'text-white/70'}`}>
                  {mark.label}
                </div>
              </div>
            ))}
          </div>

          {/* 当前时间指示线 */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-red-400 z-20 shadow-lg"
            style={{ left: `${getEventPosition(currentTime)}%` }}
          >
            <div className="absolute -top-1 -left-2 w-4 h-4 bg-red-400 rounded-full shadow-lg animate-pulse"></div>
            <div className="absolute top-6 -left-8 text-xs text-red-400 font-bold whitespace-nowrap">
              现在
            </div>
          </div>

          {/* 事件轨道 */}
          <div className="absolute top-12 left-0 right-0 bottom-0">
            {/* 预测事件轨道 */}
            {filters.showPredictions && (
              <div className="relative h-20 border-b border-white/10 mb-2">
                <div className="absolute left-2 top-2 text-xs text-purple-400 font-medium">
                  AI预测
                </div>
                {filteredPredictions.map((prediction, index) => {
                  const event = events.find(e => e.id === prediction.event_id)
                  if (!event) return null

                  const position = getEventPosition(prediction.predicted_time)
                  const isVisible = position >= 0 && position <= 100

                  if (!isVisible) return null

                  return (
                    <div
                      key={`prediction-${index}`}
                      className="absolute top-6 transform -translate-x-1/2 cursor-pointer group"
                      style={{ left: `${position}%` }}
                      onClick={() => setSelectedEvent({ ...event, prediction })}
                    >
                      {/* 预测事件点 */}
                      <div className={`w-4 h-4 rounded-full border-2 ${getEventPriorityColor(event, prediction)}
                        shadow-lg group-hover:scale-125 transition-all duration-200 relative`}>
                        <div className="absolute inset-0 rounded-full animate-ping opacity-30"
                             style={{ backgroundColor: prediction.confidence === 'HIGH' ? '#ef4444' :
                                                      prediction.confidence === 'MEDIUM' ? '#f59e0b' : '#10b981' }}>
                        </div>
                      </div>

                      {/* 预测标签 */}
                      <div className="absolute top-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100
                        transition-all duration-200 bg-black/90 text-white text-xs p-2 rounded whitespace-nowrap z-30 border border-white/20">
                        <div className="font-medium">{event.title}</div>
                        <div className="text-white/70">
                          概率: {(prediction.hotspot_prob * 100).toFixed(1)}%
                        </div>
                        <div className="text-white/70">
                          置信度: {prediction.confidence}
                        </div>
                      </div>

                      {/* 连接线 */}
                      <div className="absolute top-4 left-1/2 w-px h-8 bg-white/20 transform -translate-x-1/2"></div>
                    </div>
                  )
                })}
              </div>
            )}

            {/* 实际事件轨道 */}
            {filters.showEvents && (
              <div className="relative h-20 border-b border-white/10 mb-2">
                <div className="absolute left-2 top-2 text-xs text-blue-400 font-medium">
                  实际事件
                </div>
                {filteredEvents.map((event, index) => {
                  const position = getEventPosition(event.event_time)
                  const isVisible = position >= 0 && position <= 100
                  const prediction = predictions.find(p => p.event_id === event.id)

                  if (!isVisible) return null

                  return (
                    <div
                      key={`event-${index}`}
                      className="absolute top-6 transform -translate-x-1/2 cursor-pointer group"
                      style={{ left: `${position}%` }}
                      onClick={() => startEditEvent(event)}
                    >
                      {/* 事件点 */}
                      <div className={`w-5 h-5 rounded-full border-2 ${getEventPriorityColor(event, prediction)}
                        shadow-lg group-hover:scale-125 transition-all duration-200 flex items-center justify-center`}>
                        {event.importance === 'HIGH' && <Star className="w-2 h-2 text-white" />}
                        {prediction?.confidence === 'HIGH' && <AlertTriangle className="w-2 h-2 text-white" />}
                      </div>

                      {/* 事件标签 */}
                      <div className="absolute top-7 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100
                        transition-all duration-200 bg-black/90 text-white text-xs p-2 rounded whitespace-nowrap z-30 border border-white/20">
                        <div className="font-medium">{event.title}</div>
                        <div className="text-white/70">{event.description}</div>
                        <div className="text-white/70">
                          {new Date(event.event_time).toLocaleString('zh-CN')}
                        </div>
                        {event.key_person && (
                          <div className="text-blue-400">关键人物: {event.key_person}</div>
                        )}
                      </div>

                      {/* 连接线 */}
                      <div className="absolute top-5 left-1/2 w-px h-8 bg-white/20 transform -translate-x-1/2"></div>
                    </div>
                  )
                })}
              </div>
            )}

            {/* 年度大事件轨道 (仅在年视图显示) */}
            {zoomLevel === 0 && (
              <div className="relative h-20">
                <div className="absolute left-2 top-2 text-xs text-yellow-400 font-medium">
                  年度大事件
                </div>
                {/* 这里可以添加年度重要事件的显示逻辑 */}
                <div className="absolute top-6 left-1/4 transform -translate-x-1/2">
                  <div className="w-6 h-6 rounded-full bg-yellow-500/20 border-2 border-yellow-500 flex items-center justify-center">
                    <Sparkles className="w-3 h-3 text-yellow-400" />
                  </div>
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 text-xs text-yellow-400 whitespace-nowrap">
                    重大科技突破
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 拖拽提示 */}
          {isDragging && (
            <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-500/50 rounded-lg flex items-center justify-center">
              <div className="text-blue-400 font-medium">拖拽平移时间轴</div>
            </div>
          )}

          {/* 滚轮缩放提示 */}
          {isWheelZooming && (
            <div className="absolute inset-0 bg-purple-500/10 border-2 border-purple-500/50 rounded-lg flex items-center justify-center">
              <div className="text-purple-400 font-medium flex items-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-purple-400 border-t-transparent rounded-full"></div>
                <span>滚轮缩放中...</span>
              </div>
            </div>
          )}

          {/* 触控板缩放提示 */}
          {isTouchZooming && (
            <div className="absolute inset-0 bg-green-500/10 border-2 border-green-500/50 rounded-lg flex items-center justify-center">
              <div className="text-green-400 font-medium flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
                <span>触控板缩放中...</span>
              </div>
            </div>
          )}
        </div>

        {/* 时间轴统计信息 */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-white/5 rounded-lg p-3 text-center">
            <div className="text-white/70 text-xs">总事件</div>
            <div className="text-white text-lg font-bold">{filteredEvents.length}</div>
          </div>
          <div className="bg-white/5 rounded-lg p-3 text-center">
            <div className="text-white/70 text-xs">AI预测</div>
            <div className="text-purple-400 text-lg font-bold">{filteredPredictions.length}</div>
          </div>
          <div className="bg-white/5 rounded-lg p-3 text-center">
            <div className="text-white/70 text-xs">高置信度</div>
            <div className="text-red-400 text-lg font-bold">
              {filteredPredictions.filter(p => p.confidence === 'HIGH').length}
            </div>
          </div>
          <div className="bg-white/5 rounded-lg p-3 text-center">
            <div className="text-white/70 text-xs">时间范围</div>
            <div className="text-blue-400 text-lg font-bold">{currentConfig.label}</div>
          </div>
        </div>
      </div>

      {/* 事件编辑模态框 */}
      {editingEvent && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md mx-4 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">编辑事件</h3>
              <button
                onClick={cancelEventEdit}
                className="text-white/50 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">事件标题</label>
                <input
                  type="text"
                  value={editingEvent.title || ''}
                  onChange={(e) => setEditingEvent(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none"
                  placeholder="输入事件标题..."
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">事件描述</label>
                <textarea
                  value={editingEvent.description || ''}
                  onChange={(e) => setEditingEvent(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none h-20 resize-none"
                  placeholder="描述事件详情..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">事件时间</label>
                  <input
                    type="datetime-local"
                    value={editingEvent.event_time ? new Date(editingEvent.event_time).toISOString().slice(0, 16) : ''}
                    onChange={(e) => setEditingEvent(prev => ({ ...prev, event_time: e.target.value }))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">重要性</label>
                  <select
                    value={editingEvent.importance || 'MEDIUM'}
                    onChange={(e) => setEditingEvent(prev => ({ ...prev, importance: e.target.value }))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                  >
                    <option value="LOW">低</option>
                    <option value="MEDIUM">中</option>
                    <option value="HIGH">高</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">关键人物</label>
                <input
                  type="text"
                  value={editingEvent.key_person || ''}
                  onChange={(e) => setEditingEvent(prev => ({ ...prev, key_person: e.target.value }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none"
                  placeholder="输入关键人物..."
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">事件类型</label>
                <select
                  value={editingEvent.event_type || 'GENERAL'}
                  onChange={(e) => setEditingEvent(prev => ({ ...prev, event_type: e.target.value }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                >
                  <option value="POLITICAL">政治</option>
                  <option value="TECHNOLOGY">科技</option>
                  <option value="ENTERTAINMENT">娱乐</option>
                  <option value="SPORTS">体育</option>
                  <option value="ECONOMIC">经济</option>
                  <option value="GENERAL">一般</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={cancelEventEdit}
                  className="flex-1 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 font-medium py-2 px-4 rounded-lg transition-all duration-200"
                >
                  取消
                </button>
                <button
                  onClick={saveEventEdit}
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <Save className="w-4 h-4" />
                  <span>保存</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 事件创建模态框 */}
      {showEventForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-lg p-6 w-full max-w-md mx-4 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">添加新事件</h3>
              <button
                onClick={() => setShowEventForm(false)}
                className="text-white/50 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form
              onSubmit={(e) => {
                e.preventDefault()
                const formData = new FormData(e.target)
                const newEvent = {
                  id: `event_${Date.now()}`,
                  title: formData.get('title'),
                  description: formData.get('description'),
                  event_time: formData.get('event_time'),
                  event_type: formData.get('event_type'),
                  key_person: formData.get('key_person'),
                  importance: formData.get('importance'),
                  created_at: new Date().toISOString()
                }

                if (onEventCreate) {
                  onEventCreate(newEvent)
                }
                setShowEventForm(false)
              }}
              className="space-y-4"
            >
              <div>
                <label className="block text-white/70 text-sm mb-2">事件标题</label>
                <input
                  name="title"
                  type="text"
                  required
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none"
                  placeholder="输入事件标题..."
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">事件描述</label>
                <textarea
                  name="description"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none h-20 resize-none"
                  placeholder="描述事件详情..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">事件时间</label>
                  <input
                    name="event_time"
                    type="datetime-local"
                    required
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">重要性</label>
                  <select
                    name="importance"
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                  >
                    <option value="LOW">低</option>
                    <option value="MEDIUM">中</option>
                    <option value="HIGH">高</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">关键人物</label>
                <input
                  name="key_person"
                  type="text"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/50 focus:border-blue-500 focus:outline-none"
                  placeholder="输入关键人物..."
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">事件类型</label>
                <select
                  name="event_type"
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
                >
                  <option value="POLITICAL">政治</option>
                  <option value="TECHNOLOGY">科技</option>
                  <option value="ENTERTAINMENT">娱乐</option>
                  <option value="SPORTS">体育</option>
                  <option value="ECONOMIC">经济</option>
                  <option value="GENERAL">一般</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowEventForm(false)}
                  className="flex-1 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 font-medium py-2 px-4 rounded-lg transition-all duration-200"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>创建</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 事件详情侧边栏 */}
      {selectedEvent && !editingEvent && (
        <div className="fixed right-0 top-0 bottom-0 w-80 bg-gray-900/95 backdrop-blur-md border-l border-white/20 z-40 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">事件详情</h3>
              <button
                onClick={() => setSelectedEvent(null)}
                className="text-white/50 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="text-white font-medium text-lg">{selectedEvent.title}</h4>
                <p className="text-white/70 text-sm mt-1">{selectedEvent.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-white/50">时间:</span>
                  <div className="text-white font-medium">
                    {new Date(selectedEvent.event_time).toLocaleString('zh-CN')}
                  </div>
                </div>
                <div>
                  <span className="text-white/50">重要性:</span>
                  <div className={`font-medium ${
                    selectedEvent.importance === 'HIGH' ? 'text-red-400' :
                    selectedEvent.importance === 'MEDIUM' ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    {selectedEvent.importance === 'HIGH' ? '高' :
                     selectedEvent.importance === 'MEDIUM' ? '中' : '低'}
                  </div>
                </div>
                <div>
                  <span className="text-white/50">类型:</span>
                  <div className="text-white font-medium">{selectedEvent.event_type}</div>
                </div>
                {selectedEvent.key_person && (
                  <div>
                    <span className="text-white/50">关键人物:</span>
                    <div className="text-blue-400 font-medium">{selectedEvent.key_person}</div>
                  </div>
                )}
              </div>

              {selectedEvent.prediction && (
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <h5 className="text-white font-medium mb-3 flex items-center space-x-2">
                    <Brain className="w-4 h-4 text-purple-400" />
                    <span>AI预测分析</span>
                  </h5>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-white/70 text-sm">热点概率:</span>
                      <span className={`font-bold ${
                        selectedEvent.prediction.hotspot_prob >= 0.8 ? 'text-red-400' :
                        selectedEvent.prediction.hotspot_prob >= 0.6 ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {(selectedEvent.prediction.hotspot_prob * 100).toFixed(1)}%
                      </span>
                    </div>

                    <div className="flex justify-between items-center">
                      <span className="text-white/70 text-sm">置信度:</span>
                      <span className={`px-2 py-1 rounded text-xs border ${
                        selectedEvent.prediction.confidence === 'HIGH' ? 'text-red-400 bg-red-500/20 border-red-500/30' :
                        selectedEvent.prediction.confidence === 'MEDIUM' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                        'text-green-400 bg-green-500/20 border-green-500/30'
                      }`}>
                        {selectedEvent.prediction.confidence}
                      </span>
                    </div>

                    {selectedEvent.prediction.factors && (
                      <div className="space-y-2">
                        <div className="text-white/70 text-sm">预测因子:</div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span className="text-white/60">时间序列:</span>
                            <span className="text-blue-400">
                              {(selectedEvent.prediction.factors.temporal_score * 100).toFixed(0)}%
                            </span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-white/60">影响力:</span>
                            <span className="text-green-400">
                              {(selectedEvent.prediction.factors.influence_score * 100).toFixed(0)}%
                            </span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span className="text-white/60">情感分析:</span>
                            <span className="text-purple-400">
                              {(selectedEvent.prediction.factors.sentiment_score * 100).toFixed(0)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex space-x-2 pt-4">
                <button
                  onClick={() => startEditEvent(selectedEvent)}
                  className="flex-1 bg-white/10 hover:bg-white/20 text-white border border-white/30 hover:border-white/50 font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2"
                >
                  <Edit className="w-4 h-4" />
                  <span>编辑</span>
                </button>
                {selectedEvent.prediction?.strategy_ready && (
                  <button className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2">
                    <Sparkles className="w-4 h-4" />
                    <span>生成策略</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TimelineView
