import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      // 你可以自定义降级后的 UI 并渲染
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div style={{
          padding: '20px',
          backgroundColor: '#1a202c',
          color: 'white',
          minHeight: '100vh',
          fontFamily: 'monospace'
        }}>
          <div style={{
            backgroundColor: '#742a2a',
            padding: '20px',
            borderRadius: '8px',
            marginBottom: '20px',
            border: '2px solid #fc8181'
          }}>
            <h1 style={{ color: '#fc8181', fontSize: '24px', marginBottom: '15px' }}>
              🚨 页面渲染错误
            </h1>
            <p style={{ marginBottom: '15px' }}>
              页面在渲染过程中发生了错误，这通常是由于组件代码问题导致的。
            </p>
            
            {this.state.error && (
              <div style={{ marginBottom: '15px' }}>
                <h3 style={{ color: '#fbb6ce', marginBottom: '10px' }}>错误信息:</h3>
                <pre style={{
                  backgroundColor: '#2d1b1b',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  fontSize: '12px',
                  color: '#fed7d7'
                }}>
                  {this.state.error.toString()}
                </pre>
              </div>
            )}

            {this.state.errorInfo && (
              <div style={{ marginBottom: '15px' }}>
                <h3 style={{ color: '#fbb6ce', marginBottom: '10px' }}>组件堆栈:</h3>
                <pre style={{
                  backgroundColor: '#2d1b1b',
                  padding: '10px',
                  borderRadius: '4px',
                  overflow: 'auto',
                  fontSize: '12px',
                  color: '#fed7d7',
                  maxHeight: '200px'
                }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}

            <div style={{ marginTop: '20px' }}>
              <button
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: '#4299e1',
                  color: 'white',
                  padding: '10px 20px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                刷新页面
              </button>
              <button
                onClick={() => window.location.href = '/'}
                style={{
                  backgroundColor: '#48bb78',
                  color: 'white',
                  padding: '10px 20px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                返回首页
              </button>
              <button
                onClick={() => window.location.href = '/minimal-test'}
                style={{
                  backgroundColor: '#ed8936',
                  color: 'white',
                  padding: '10px 20px',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                测试页面
              </button>
            </div>
          </div>

          <div style={{
            backgroundColor: '#2d3748',
            padding: '20px',
            borderRadius: '8px'
          }}>
            <h2 style={{ color: '#68d391', marginBottom: '15px' }}>🔧 调试建议</h2>
            <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
              <li>检查浏览器控制台是否有更详细的错误信息</li>
              <li>确认所有组件的导入路径是否正确</li>
              <li>检查是否有未定义的变量或函数调用</li>
              <li>验证组件的props类型是否匹配</li>
              <li>查看是否有无限循环或递归调用</li>
            </ul>
          </div>

          <div style={{
            backgroundColor: '#2a4365',
            padding: '20px',
            borderRadius: '8px',
            marginTop: '20px'
          }}>
            <h2 style={{ color: '#63b3ed', marginBottom: '15px' }}>📊 环境信息</h2>
            <div style={{ fontSize: '12px' }}>
              <p><strong>时间:</strong> {new Date().toLocaleString()}</p>
              <p><strong>路径:</strong> {window.location.pathname}</p>
              <p><strong>用户代理:</strong> {navigator.userAgent}</p>
              <p><strong>React版本:</strong> {React.version}</p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
