import React, { useState, useEffect, useRef } from 'react'
import { SystemStatus, TrendIndicator, Badge } from './StatusIndicators'

const Header = ({ onToggleSidebar, connectionStatus, isMobile, sidebarOpen }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [notifications, setNotifications] = useState(3)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [showNotifications, setShowNotifications] = useState(false)

  // 创建ref用于检测点击外部
  const notificationsRef = useRef(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // 点击外部关闭通知浮窗的逻辑
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setShowNotifications(false)
      }
    }

    // 只有当通知浮窗打开时才添加事件监听器
    if (showNotifications) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showNotifications])

  return (
    <header className="header">
      <div style={{ display: 'flex', alignItems: 'center', gap: '24px', width: '100%' }}>
        {/* 移动端菜单按钮 */}
        <button
          className="mobile-menu-btn hidden-desktop btn btn-outline btn-sm"
          onClick={onToggleSidebar}
          aria-label={sidebarOpen ? "关闭菜单" : "打开菜单"}
          style={{
            display: isMobile ? 'flex' : 'none',
            alignItems: 'center',
            justifyContent: 'center',
            minWidth: '40px',
            height: '40px'
          }}
        >
          <i className={`fas ${sidebarOpen ? 'fa-times' : 'fa-bars'}`}></i>
        </button>

        <div className="search-bar">
          <i className="fas fa-search" style={{ color: 'var(--text-tertiary)', marginRight: '8px' }}></i>
          <input
            type="text"
            placeholder={isMobile ? "搜索..." : "搜索代币、策略或数据..."}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* 快速统计 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
          fontSize: '14px'
        }}>
          <TrendIndicator trend="up" value="****%" label="市场" />
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            color: 'var(--text-tertiary)'
          }}>
            <i className="fas fa-clock"></i>
            <span>{currentTime.toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
      
      <div className="header-actions">
        {/* 连接状态指示器 */}
        <SystemStatus
          status={connectionStatus === 'connected' ? 'online' : 'offline'}
        />

        {/* 快速操作按钮 */}
        <button className="btn btn-outline btn-sm" title="刷新数据">
          <i className="fas fa-sync-alt"></i>
        </button>

        {/* 通知中心 */}
        <div
          className="notification interactive-element"
          style={{ position: 'relative' }}
          ref={notificationsRef}
          onClick={() => setShowNotifications(true)}
        >
          <i className="fas fa-bell"></i>
          {notifications > 0 && (
            <Badge
              variant="danger"
              size="sm"
              count={notifications}
              style={{
                position: 'absolute',
                top: '-6px',
                right: '-6px'
              }}
            />
          )}
        </div>

        {/* 用户头像 */}
        <div className="user-avatar" style={{ position: 'relative' }}>
          <i className="fas fa-user"></i>
          <div style={{
            position: 'absolute',
            bottom: '-2px',
            right: '-2px',
            width: '12px',
            height: '12px',
            background: 'var(--success)',
            borderRadius: '50%',
            border: '2px solid var(--bg-secondary)'
          }}></div>
        </div>

        {/* 移动端菜单按钮 */}
        <button
          className="btn btn-outline btn-sm md:hidden"
          onClick={onToggleSidebar}
          aria-label="切换菜单"
        >
          <i className="fas fa-bars"></i>
        </button>
      </div>
    </header>
  )
}

export default Header
