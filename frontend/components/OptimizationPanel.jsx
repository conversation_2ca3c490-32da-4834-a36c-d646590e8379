import React, { useState, useEffect } from 'react'
import {
  Setting<PERSON>,
  TrendingUp,
  Shield,
  Zap,
  Target,
  BarChart3,
  Sliders,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Droplets,
  LogOut
} from 'lucide-react'

const OptimizationPanel = () => {
  const [sensitivityLevel, setSensitivityLevel] = useState('balanced')
  const [optimizationResults, setOptimizationResults] = useState(null)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [showCustomParams, setShowCustomParams] = useState(false)
  const [parameters, setParameters] = useState({
    hotspot_threshold: 0.75,
    exit_threshold: 0.75,
    stop_loss_multiplier: 1.0,
    batch_size_multiplier: 1.0
  })

  // 自定义参数状态
  const [customParams, setCustomParams] = useState({
    // 舆情监测参数
    hotspot: {
      min_virality: 0.85,
      cultural_fit_threshold: 0.75,
      response_timeout: 300,
      min_volume_twitter: 1000,
      min_volume_reddit: 500,
      min_volume_tiktok: 100000
    },
    // 策略生成参数
    strategy: {
      supply_min: 1000000000,
      supply_max: 50000000000,
      airdrop_ratio: 0.50,
      liquidity_ratio: 0.30,
      marketing_ratio: 0.10,
      team_ratio: 0.10,
      burn_rate_base: 0.05
    },
    // 流动性控制参数
    liquidity: {
      min_wallets: 20,
      max_wallets: 100,
      initial_funding: 0.5,
      slippage_tolerance: 0.005,
      whale_alert_threshold: 0.05,
      tvl_ratio_min: 0.5
    },
    // 退出策略参数
    exit: {
      signal_threshold: 0.75,
      rsi_weight: 0.25,
      sentiment_weight: 0.20,
      volume_weight: 0.15,
      whale_weight: 0.10,
      volatility_weight: 0.30,
      stop_loss_default: 0.15,
      batch_size_high_vol: 0.08,
      batch_size_medium_vol: 0.10,
      batch_size_low_vol: 0.12
    }
  })

  // 灵敏度配置
  const sensitivityConfigs = {
    conservative: {
      name: '保守策略',
      description: '低风险，稳定收益',
      color: 'text-green-400',
      bgColor: 'bg-green-500/20',
      borderColor: 'border-green-500/30'
    },
    balanced: {
      name: '平衡策略',
      description: '中等风险，均衡收益',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20',
      borderColor: 'border-blue-500/30'
    },
    aggressive: {
      name: '激进策略',
      description: '高风险，高收益潜力',
      color: 'text-red-400',
      bgColor: 'bg-red-500/20',
      borderColor: 'border-red-500/30'
    }
  }

  // 获取市场灵敏度配置
  const fetchSensitivityConfig = async (level) => {
    try {
      const response = await fetch(`http://localhost:8001/optimization/market-sensitivity/${level}`)
      const data = await response.json()
      if (data.status === 'success') {
        setParameters(data.configuration)
      }
    } catch (error) {
      console.error('获取灵敏度配置失败:', error)
    }
  }

  // 执行参数优化
  const runOptimization = async () => {
    setIsOptimizing(true)
    
    try {
      // 模拟热点数据
      const hotspotData = {
        keyword: 'AI',
        category: 'technology',
        score: 0.85,
        trend: 'rising',
        regions: ['US', 'EU', 'ASIA']
      }

      // 分析热点参数优化
      const hotspotResponse = await fetch('http://localhost:8001/optimization/analyze-hotspot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(hotspotData)
      })
      const hotspotOptimization = await hotspotResponse.json()

      // 优化策略参数
      const strategyResponse = await fetch('http://localhost:8001/optimization/strategy-parameters', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(hotspotData)
      })
      const strategyOptimization = await strategyResponse.json()

      setOptimizationResults({
        hotspot: hotspotOptimization,
        strategy: strategyOptimization,
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error('参数优化失败:', error)
    } finally {
      setIsOptimizing(false)
    }
  }

  // 处理灵敏度变化
  const handleSensitivityChange = (level) => {
    setSensitivityLevel(level)
    fetchSensitivityConfig(level)
  }

  // 处理自定义参数变化
  const handleCustomParamChange = (category, param, value) => {
    setCustomParams(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [param]: parseFloat(value) || value
      }
    }))
  }

  // 重置参数到默认值
  const resetToDefaults = () => {
    setCustomParams({
      hotspot: {
        min_virality: 0.85,
        cultural_fit_threshold: 0.75,
        response_timeout: 300,
        min_volume_twitter: 1000,
        min_volume_reddit: 500,
        min_volume_tiktok: 100000
      },
      strategy: {
        supply_min: 1000000000,
        supply_max: 50000000000,
        airdrop_ratio: 0.50,
        liquidity_ratio: 0.30,
        marketing_ratio: 0.10,
        team_ratio: 0.10,
        burn_rate_base: 0.05
      },
      liquidity: {
        min_wallets: 20,
        max_wallets: 100,
        initial_funding: 0.5,
        slippage_tolerance: 0.005,
        whale_alert_threshold: 0.05,
        tvl_ratio_min: 0.5
      },
      exit: {
        signal_threshold: 0.75,
        rsi_weight: 0.25,
        sentiment_weight: 0.20,
        volume_weight: 0.15,
        whale_weight: 0.10,
        volatility_weight: 0.30,
        stop_loss_default: 0.15,
        batch_size_high_vol: 0.08,
        batch_size_medium_vol: 0.10,
        batch_size_low_vol: 0.12
      }
    })
  }

  // 应用自定义参数
  const applyCustomParams = async () => {
    try {
      const response = await fetch('http://localhost:8001/optimization/validate-parameters', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customParams)
      })
      const data = await response.json()

      if (data.status === 'success') {
        setCustomParams(data.validated_params)

        // 保存配置
        await fetch('http://localhost:8001/optimization/custom-parameters/save', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data.validated_params)
        })

        alert('自定义参数已应用并验证！')
      }
    } catch (error) {
      console.error('应用参数失败:', error)
      alert('应用参数失败，请检查参数值')
    }
  }

  // 加载参数预设
  const loadPreset = async (presetType) => {
    try {
      const response = await fetch('http://localhost:8001/optimization/custom-parameters/presets')
      const data = await response.json()

      if (data.status === 'success' && data.presets[presetType]) {
        const presetParams = data.presets[presetType].params

        // 合并预设参数到当前参数
        setCustomParams(prev => ({
          ...prev,
          ...presetParams
        }))

        alert(`已加载${data.presets[presetType].name}`)
      }
    } catch (error) {
      console.error('加载预设失败:', error)
    }
  }

  useEffect(() => {
    fetchSensitivityConfig(sensitivityLevel)
  }, [])

  return (
    <div className="space-y-6">
      {/* 参数优化控制面板 */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-white flex items-center space-x-2">
            <Sliders className="w-6 h-6 text-purple-400" />
            <span>参数优化控制中心</span>
          </h2>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowCustomParams(!showCustomParams)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>自定义参数</span>
            </button>

            <button
              onClick={runOptimization}
              disabled={isOptimizing}
              className="btn-primary flex items-center space-x-2"
            >
              {isOptimizing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Zap className="w-4 h-4" />
              )}
              <span>{isOptimizing ? '优化中...' : '执行优化'}</span>
            </button>
          </div>
        </div>

        {/* 市场灵敏度控制 */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-white mb-4">市场灵敏度设置</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {Object.entries(sensitivityConfigs).map(([level, config]) => (
              <button
                key={level}
                onClick={() => handleSensitivityChange(level)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  sensitivityLevel === level 
                    ? `${config.borderColor} ${config.bgColor}` 
                    : 'border-white/20 bg-white/5 hover:border-white/40'
                }`}
              >
                <div className={`text-lg font-medium ${
                  sensitivityLevel === level ? config.color : 'text-white'
                }`}>
                  {config.name}
                </div>
                <div className="text-white/60 text-sm mt-1">
                  {config.description}
                </div>
                {sensitivityLevel === level && (
                  <CheckCircle className={`w-5 h-5 ${config.color} mt-2 mx-auto`} />
                )}
              </button>
            ))}
          </div>
        </div>

        {/* 当前参数显示 */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white/5 rounded-lg p-4 text-center">
            <div className="text-white/60 text-sm">热点阈值</div>
            <div className="text-blue-400 text-xl font-bold">
              {(parameters.hotspot_threshold * 100).toFixed(0)}%
            </div>
          </div>
          <div className="bg-white/5 rounded-lg p-4 text-center">
            <div className="text-white/60 text-sm">退出阈值</div>
            <div className="text-green-400 text-xl font-bold">
              {(parameters.exit_threshold * 100).toFixed(0)}%
            </div>
          </div>
          <div className="bg-white/5 rounded-lg p-4 text-center">
            <div className="text-white/60 text-sm">止损倍数</div>
            <div className="text-red-400 text-xl font-bold">
              {parameters.stop_loss_multiplier}x
            </div>
          </div>
          <div className="bg-white/5 rounded-lg p-4 text-center">
            <div className="text-white/60 text-sm">批量倍数</div>
            <div className="text-purple-400 text-xl font-bold">
              {parameters.batch_size_multiplier}x
            </div>
          </div>
        </div>
      </div>

      {/* 优化结果显示 */}
      {optimizationResults && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 热点优化结果 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              <span>热点参数优化</span>
            </h3>

            <div className="space-y-4">
              <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/30">
                <h4 className="text-green-300 font-medium mb-2">优化建议</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">响应紧急度</span>
                    <span className="text-green-400">
                      {optimizationResults.hotspot.recommendations?.response_urgency || 'normal'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">病毒性阈值</span>
                    <span className="text-green-400">
                      {((optimizationResults.hotspot.recommendations?.virality_threshold || 0.75) * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-3">
                <h5 className="text-white font-medium mb-2">来源权重优化</h5>
                <div className="space-y-1 text-sm">
                  {Object.entries(optimizationResults.hotspot.recommendations?.source_weights || {}).map(([source, weight]) => (
                    <div key={source} className="flex justify-between">
                      <span className="text-white/70 capitalize">{source}</span>
                      <span className="text-blue-400">{(weight * 100).toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 策略优化结果 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Target className="w-5 h-5 text-purple-400" />
              <span>策略参数优化</span>
            </h3>

            <div className="space-y-4">
              <div className="bg-purple-500/10 rounded-lg p-4 border border-purple-500/30">
                <h4 className="text-purple-300 font-medium mb-2">分配优化</h4>
                <div className="space-y-2 text-sm">
                  {Object.entries(optimizationResults.strategy.allocation_optimization || {}).map(([type, ratio]) => (
                    <div key={type} className="flex justify-between">
                      <span className="text-white/70 capitalize">{type}</span>
                      <span className="text-purple-400">{(ratio * 100).toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-3">
                <h5 className="text-white font-medium mb-2">其他优化参数</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">燃烧率</span>
                    <span className="text-yellow-400">
                      {((optimizationResults.strategy.burn_rate || 0.05) * 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">对冲预算</span>
                    <span className="text-cyan-400">
                      {((optimizationResults.strategy.hedging_params?.budget_allocation || 0.05) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 性能预期 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <BarChart3 className="w-5 h-5 text-blue-400" />
          <span>优化效果预期</span>
        </h3>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400 mb-1">+40%</div>
            <div className="text-white/60 text-sm">热点响应速度</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400 mb-1">+35%</div>
            <div className="text-white/60 text-sm">代币成功率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400 mb-1">+30%</div>
            <div className="text-white/60 text-sm">退出收益率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-400 mb-1">-50%</div>
            <div className="text-white/60 text-sm">极端风险</div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-blue-500/10 rounded-lg border border-blue-500/30">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-5 h-5 text-blue-400 mt-0.5" />
            <div>
              <div className="text-blue-400 font-medium text-sm">优化提示</div>
              <div className="text-white/70 text-xs mt-1">
                参数优化基于历史数据和机器学习模型，实际效果可能因市场条件而异。建议定期重新优化参数以适应市场变化。
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 自定义参数面板 */}
      {showCustomParams && (
        <div className="card p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
              <Settings className="w-5 h-5 text-purple-400" />
              <span>自定义参数配置</span>
            </h3>

            <div className="flex items-center space-x-3">
              <select
                onChange={(e) => e.target.value && loadPreset(e.target.value)}
                className="input-field text-sm"
                defaultValue=""
              >
                <option value="">选择预设配置</option>
                <option value="conservative">保守配置</option>
                <option value="balanced">平衡配置</option>
                <option value="aggressive">激进配置</option>
              </select>

              <button
                onClick={resetToDefaults}
                className="btn-secondary text-sm"
              >
                重置默认值
              </button>
              <button
                onClick={applyCustomParams}
                className="btn-primary text-sm"
              >
                应用参数
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 舆情监测参数 */}
            <div className="space-y-4">
              <h4 className="text-white font-medium flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span>舆情监测参数</span>
              </h4>

              <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/30 space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-white/70 text-sm mb-1">最低病毒性</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.hotspot.min_virality}
                      onChange={(e) => handleCustomParamChange('hotspot', 'min_virality', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">文化适配度</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.hotspot.cultural_fit_threshold}
                      onChange={(e) => handleCustomParamChange('hotspot', 'cultural_fit_threshold', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">响应超时(秒)</label>
                    <input
                      type="number"
                      min="60"
                      max="600"
                      value={customParams.hotspot.response_timeout}
                      onChange={(e) => handleCustomParamChange('hotspot', 'response_timeout', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">Twitter最低量</label>
                    <input
                      type="number"
                      min="100"
                      value={customParams.hotspot.min_volume_twitter}
                      onChange={(e) => handleCustomParamChange('hotspot', 'min_volume_twitter', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 策略生成参数 */}
            <div className="space-y-4">
              <h4 className="text-white font-medium flex items-center space-x-2">
                <Target className="w-4 h-4 text-purple-400" />
                <span>策略生成参数</span>
              </h4>

              <div className="bg-purple-500/10 rounded-lg p-4 border border-purple-500/30 space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-white/70 text-sm mb-1">最小供应量</label>
                    <input
                      type="number"
                      min="1000000"
                      value={customParams.strategy.supply_min}
                      onChange={(e) => handleCustomParamChange('strategy', 'supply_min', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">最大供应量</label>
                    <input
                      type="number"
                      min="1000000"
                      value={customParams.strategy.supply_max}
                      onChange={(e) => handleCustomParamChange('strategy', 'supply_max', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">空投比例</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.strategy.airdrop_ratio}
                      onChange={(e) => handleCustomParamChange('strategy', 'airdrop_ratio', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">流动性比例</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.strategy.liquidity_ratio}
                      onChange={(e) => handleCustomParamChange('strategy', 'liquidity_ratio', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {/* 流动性控制参数 */}
            <div className="space-y-4">
              <h4 className="text-white font-medium flex items-center space-x-2">
                <Droplets className="w-4 h-4 text-cyan-400" />
                <span>流动性控制参数</span>
              </h4>

              <div className="bg-cyan-500/10 rounded-lg p-4 border border-cyan-500/30 space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-white/70 text-sm mb-1">最小钱包数</label>
                    <input
                      type="number"
                      min="10"
                      max="50"
                      value={customParams.liquidity.min_wallets}
                      onChange={(e) => handleCustomParamChange('liquidity', 'min_wallets', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">最大钱包数</label>
                    <input
                      type="number"
                      min="50"
                      max="200"
                      value={customParams.liquidity.max_wallets}
                      onChange={(e) => handleCustomParamChange('liquidity', 'max_wallets', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">初始资金</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0.1"
                      max="10"
                      value={customParams.liquidity.initial_funding}
                      onChange={(e) => handleCustomParamChange('liquidity', 'initial_funding', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">滑点容忍度</label>
                    <input
                      type="number"
                      step="0.001"
                      min="0.001"
                      max="0.05"
                      value={customParams.liquidity.slippage_tolerance}
                      onChange={(e) => handleCustomParamChange('liquidity', 'slippage_tolerance', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 退出策略参数 */}
            <div className="space-y-4">
              <h4 className="text-white font-medium flex items-center space-x-2">
                <LogOut className="w-4 h-4 text-red-400" />
                <span>退出策略参数</span>
              </h4>

              <div className="bg-red-500/10 rounded-lg p-4 border border-red-500/30 space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-white/70 text-sm mb-1">信号阈值</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0.5"
                      max="1"
                      value={customParams.exit.signal_threshold}
                      onChange={(e) => handleCustomParamChange('exit', 'signal_threshold', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">RSI权重</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.exit.rsi_weight}
                      onChange={(e) => handleCustomParamChange('exit', 'rsi_weight', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">情绪权重</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      max="1"
                      value={customParams.exit.sentiment_weight}
                      onChange={(e) => handleCustomParamChange('exit', 'sentiment_weight', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                  <div>
                    <label className="block text-white/70 text-sm mb-1">默认止损</label>
                    <input
                      type="number"
                      step="0.01"
                      min="0.05"
                      max="0.5"
                      value={customParams.exit.stop_loss_default}
                      onChange={(e) => handleCustomParamChange('exit', 'stop_loss_default', e.target.value)}
                      className="input-field w-full text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 参数验证提示 */}
          <div className="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-500/30">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="w-5 h-5 text-blue-400 mt-0.5" />
              <div>
                <div className="text-blue-400 font-medium text-sm">参数配置提示</div>
                <div className="text-white/70 text-xs mt-1">
                  • 修改参数后请点击"应用参数"进行验证和保存<br/>
                  • 系统会自动验证参数的合理性和边界值<br/>
                  • 建议在测试环境中验证参数效果后再应用到生产环境<br/>
                  • 可以随时点击"重置默认值"恢复到推荐配置
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default OptimizationPanel
