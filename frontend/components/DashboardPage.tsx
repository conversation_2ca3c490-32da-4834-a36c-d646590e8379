import React, { useState, useEffect } from 'react';
import { useDashboardStore } from '../store/dashboardStore';
import Sidebar from './layout/Sidebar';
import Header from './layout/Header';
import WelcomeBanner from './dashboard/WelcomeBanner';
import { StatCardsGrid } from './dashboard/StatCard';
import MarketChart from './dashboard/MarketChart';
import HotCoinsList from './dashboard/HotCoinsList';
import { FeatureCardsGrid } from './dashboard/FeatureCard';

const DashboardPage: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  // 从Zustand store获取数据
  const {
    user,
    market,
    stats,
    hotCoins,
    features,
    chartData,
    isLoading,
    lastUpdated
  } = useDashboardStore();

  // 响应式处理
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      const tablet = window.innerWidth < 1200;
      
      setIsMobile(mobile);
      
      if (mobile) {
        setSidebarCollapsed(true);
        setSidebarOpen(false);
      } else {
        setSidebarCollapsed(tablet);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // 切换侧边栏
  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  // 处理统计卡片点击
  const handleStatCardClick = (cardTitle: string) => {
    console.log('点击统计卡片:', cardTitle);
    // 这里可以添加导航逻辑
  };

  // 处理代币点击
  const handleCoinClick = (coin: any) => {
    console.log('点击代币:', coin);
    // 这里可以添加代币详情页面导航
  };

  // 注意：已移除getMainContentStyle函数，现在使用flex布局

  return (
    <div className="h-full w-full flex flex-col bg-gray-900">
      {/* 主布局容器 */}
      <div className="flex h-full overflow-hidden">
        {/* 侧边栏 */}
        <Sidebar
          collapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          isMobile={isMobile}
          isOpen={sidebarOpen}
        />

        {/* 主内容区 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 顶部导航 */}
          <Header
            onToggleSidebar={toggleSidebar}
            sidebarCollapsed={sidebarCollapsed}
            isMobile={isMobile}
          />

          {/* 主要内容 - 可滚动区域 */}
          <main className="flex-1 overflow-y-auto p-4 md:p-6 space-y-6 md:space-y-8">
          {/* 欢迎横幅 */}
          <WelcomeBanner 
            user={user} 
            market={market}
            className="animate-fade-in"
          />

          {/* 统计卡片 */}
          <section className="animate-fade-in delay-100">
            <StatCardsGrid 
              cards={stats}
              loading={isLoading}
              onCardClick={handleStatCardClick}
            />
          </section>

          {/* 图表和热门代币 */}
          <section className="flex flex-col lg:flex-row gap-6 md:gap-8 animate-fade-in delay-200">
            {/* 市场图表 */}
            <div className="w-full lg:w-2/3 min-h-[400px]">
              <MarketChart
                data={chartData}
                loading={isLoading}
                height={400}
                className="h-full"
              />
            </div>

            {/* 热门代币列表 */}
            <div className="w-full lg:w-1/3 min-h-[400px]">
              <HotCoinsList
                coins={hotCoins}
                loading={isLoading}
                onCoinClick={handleCoinClick}
                className="h-full"
              />
            </div>
          </section>

          {/* 功能卡片 */}
          <section className="animate-fade-in delay-300">
            <div className="mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">核心功能</h2>
              <p className="text-gray-400">快速访问MemeMaster AI的主要功能模块</p>
            </div>
            <FeatureCardsGrid 
              cards={features}
              loading={isLoading}
            />
          </section>

          {/* 底部信息 */}
          <footer className="pt-8 border-t border-gray-700 animate-fade-in delay-400">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm text-gray-400">
              <div className="mb-4 md:mb-0">
                <p>© 2023 MemeMaster AI. 智能Meme币交易系统</p>
                <p className="mt-1">最后更新: {new Date(lastUpdated).toLocaleString('zh-CN')}</p>
              </div>
              
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>系统运行正常</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>实时数据同步</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span>AI引擎活跃</span>
                </div>
              </div>
            </div>
          </footer>
          </main>
        </div>
      </div>



      {/* 全局样式 */}
      <style jsx global>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fade-in {
          animation: fade-in 0.6s ease-out forwards;
        }

        .delay-100 {
          animation-delay: 0.1s;
        }

        .delay-200 {
          animation-delay: 0.2s;
        }

        .delay-300 {
          animation-delay: 0.3s;
        }

        .delay-400 {
          animation-delay: 0.4s;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
          width: 6px;
        }

        ::-webkit-scrollbar-track {
          background: rgba(31, 41, 55, 0.5);
        }

        ::-webkit-scrollbar-thumb {
          background: rgba(99, 102, 241, 0.5);
          border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
          background: rgba(99, 102, 241, 0.7);
        }

        /* 全屏布局修复 */
        html, body, #root {
          height: 100%;
          width: 100%;
          margin: 0;
          padding: 0;
        }

        /* 防止body滚动 */
        body {
          overflow: hidden;
        }

        /* 平滑滚动 */
        html {
          scroll-behavior: smooth;
        }

        /* 主内容区滚动控制 */
        .main-content {
          overflow-y: auto;
          overflow-x: hidden;
        }

        /* 选择文本样式 */
        ::selection {
          background: rgba(99, 102, 241, 0.3);
          color: white;
        }
      `}</style>
    </div>
  );
};

export default DashboardPage;
