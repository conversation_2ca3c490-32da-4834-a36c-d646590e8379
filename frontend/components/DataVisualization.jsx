import React, { useEffect, useRef } from 'react'

// 简单的线图组件
export const LineChart = ({ 
  data = [], 
  width = 400, 
  height = 200, 
  color = 'var(--primary)',
  gradient = true,
  animated = true
}) => {
  const canvasRef = useRef(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !data.length) return

    const ctx = canvas.getContext('2d')
    const dpr = window.devicePixelRatio || 1
    
    // 设置高分辨率
    canvas.width = width * dpr
    canvas.height = height * dpr
    canvas.style.width = width + 'px'
    canvas.style.height = height + 'px'
    ctx.scale(dpr, dpr)

    // 清除画布
    ctx.clearRect(0, 0, width, height)

    if (data.length < 2) return

    // 计算数据范围
    const values = data.map(d => d.value)
    const minValue = Math.min(...values)
    const maxValue = Math.max(...values)
    const range = maxValue - minValue || 1

    // 绘制网格
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.lineWidth = 1
    
    // 水平网格线
    for (let i = 0; i <= 4; i++) {
      const y = (height / 4) * i
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(width, y)
      ctx.stroke()
    }

    // 垂直网格线
    for (let i = 0; i <= 6; i++) {
      const x = (width / 6) * i
      ctx.beginPath()
      ctx.moveTo(x, 0)
      ctx.lineTo(x, height)
      ctx.stroke()
    }

    // 绘制渐变填充
    if (gradient) {
      const gradientFill = ctx.createLinearGradient(0, 0, 0, height)
      gradientFill.addColorStop(0, color.replace('var(--primary)', '#4338ca') + '40')
      gradientFill.addColorStop(1, color.replace('var(--primary)', '#4338ca') + '00')

      ctx.beginPath()
      data.forEach((point, index) => {
        const x = (width / (data.length - 1)) * index
        const y = height - ((point.value - minValue) / range) * height
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.lineTo(width, height)
      ctx.lineTo(0, height)
      ctx.closePath()
      ctx.fillStyle = gradientFill
      ctx.fill()
    }

    // 绘制线条
    ctx.strokeStyle = color.replace('var(--primary)', '#4338ca')
    ctx.lineWidth = 3
    ctx.lineCap = 'round'
    ctx.lineJoin = 'round'

    ctx.beginPath()
    data.forEach((point, index) => {
      const x = (width / (data.length - 1)) * index
      const y = height - ((point.value - minValue) / range) * height
      
      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    })
    ctx.stroke()

    // 绘制数据点
    data.forEach((point, index) => {
      const x = (width / (data.length - 1)) * index
      const y = height - ((point.value - minValue) / range) * height
      
      // 外圈
      ctx.beginPath()
      ctx.arc(x, y, 6, 0, 2 * Math.PI)
      ctx.fillStyle = color.replace('var(--primary)', '#4338ca')
      ctx.fill()
      
      // 内圈
      ctx.beginPath()
      ctx.arc(x, y, 3, 0, 2 * Math.PI)
      ctx.fillStyle = '#ffffff'
      ctx.fill()
    })

  }, [data, width, height, color, gradient])

  return (
    <div style={{ position: 'relative', width, height }}>
      <canvas 
        ref={canvasRef}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: 'var(--radius-md)'
        }}
      />
    </div>
  )
}

// 环形进度图
export const CircularProgress = ({ 
  value = 0, 
  max = 100, 
  size = 120, 
  strokeWidth = 8,
  color = 'var(--primary)',
  backgroundColor = 'var(--bg-tertiary)',
  showValue = true,
  label = ''
}) => {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100)
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (percentage / 100) * circumference

  return (
    <div 
      style={{
        position: 'relative',
        width: size,
        height: size,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <svg
        width={size}
        height={size}
        style={{ transform: 'rotate(-90deg)' }}
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color.replace('var(--primary)', '#4338ca')}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          style={{
            transition: 'stroke-dashoffset 1s ease-in-out',
            filter: 'drop-shadow(0 0 6px rgba(67, 56, 202, 0.4))'
          }}
        />
      </svg>
      
      {/* 中心内容 */}
      <div 
        style={{
          position: 'absolute',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          textAlign: 'center'
        }}
      >
        {showValue && (
          <div 
            style={{
              fontSize: `${size * 0.15}px`,
              fontWeight: 'var(--font-bold)',
              color: 'var(--text-primary)',
              fontFamily: 'var(--font-mono)'
            }}
          >
            {percentage.toFixed(1)}%
          </div>
        )}
        {label && (
          <div 
            style={{
              fontSize: `${size * 0.08}px`,
              color: 'var(--text-tertiary)',
              marginTop: '2px'
            }}
          >
            {label}
          </div>
        )}
      </div>
    </div>
  )
}

// 迷你图表
export const MiniChart = ({ 
  data = [], 
  type = 'line', 
  width = 100, 
  height = 40,
  color = 'var(--primary)'
}) => {
  const canvasRef = useRef(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !data.length) return

    const ctx = canvas.getContext('2d')
    const dpr = window.devicePixelRatio || 1
    
    canvas.width = width * dpr
    canvas.height = height * dpr
    canvas.style.width = width + 'px'
    canvas.style.height = height + 'px'
    ctx.scale(dpr, dpr)

    ctx.clearRect(0, 0, width, height)

    const values = data.map(d => typeof d === 'number' ? d : d.value)
    const minValue = Math.min(...values)
    const maxValue = Math.max(...values)
    const range = maxValue - minValue || 1

    if (type === 'line') {
      ctx.strokeStyle = color.replace('var(--primary)', '#4338ca')
      ctx.lineWidth = 2
      ctx.lineCap = 'round'
      ctx.lineJoin = 'round'

      ctx.beginPath()
      values.forEach((value, index) => {
        const x = (width / (values.length - 1)) * index
        const y = height - ((value - minValue) / range) * height
        
        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })
      ctx.stroke()
    } else if (type === 'bar') {
      const barWidth = width / values.length * 0.8
      const barSpacing = width / values.length * 0.2

      values.forEach((value, index) => {
        const x = index * (width / values.length) + barSpacing / 2
        const barHeight = ((value - minValue) / range) * height
        const y = height - barHeight

        ctx.fillStyle = color.replace('var(--primary)', '#4338ca')
        ctx.fillRect(x, y, barWidth, barHeight)
      })
    }

  }, [data, type, width, height, color])

  return (
    <canvas 
      ref={canvasRef}
      style={{
        width: '100%',
        height: '100%',
        borderRadius: 'var(--radius-sm)'
      }}
    />
  )
}

// 实时数据流
export const RealTimeStream = ({ 
  data = [], 
  maxPoints = 50,
  updateInterval = 1000,
  color = 'var(--success)'
}) => {
  const [streamData, setStreamData] = React.useState(data)

  useEffect(() => {
    const interval = setInterval(() => {
      setStreamData(prev => {
        const newData = [...prev]
        newData.push({
          timestamp: Date.now(),
          value: Math.random() * 100
        })
        
        if (newData.length > maxPoints) {
          newData.shift()
        }
        
        return newData
      })
    }, updateInterval)

    return () => clearInterval(interval)
  }, [maxPoints, updateInterval])

  return (
    <div style={{ width: '100%', height: '200px' }}>
      <LineChart 
        data={streamData}
        width={400}
        height={200}
        color={color}
        gradient={true}
        animated={true}
      />
    </div>
  )
}
