import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

const NavigationTest = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const testRoutes = [
    { path: '/', label: '主页' },
    { path: '/hotspot', label: '趋势热点' },
    { path: '/strategy', label: '策略引擎' },
    { path: '/deploy', label: '一键部署' },
    { path: '/liquidity', label: '流动性管理' },
    { path: '/exit', label: '动态退出' },
    { path: '/automation', label: '自动化工具' },
    { path: '/account', label: '账户管理' },
    { path: '/settings', label: '系统设置' },
    { path: '/help', label: '帮助中心' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
          <h1 className="text-2xl font-bold text-white mb-6">导航测试页面</h1>
          
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-white mb-3">当前路径</h2>
            <div className="bg-gray-800 rounded-lg p-4">
              <code className="text-green-400">{location.pathname}</code>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-lg font-semibold text-white mb-3">测试导航</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {testRoutes.map((route) => (
                <button
                  key={route.path}
                  onClick={() => navigate(route.path)}
                  className={`p-3 rounded-lg transition-colors ${
                    location.pathname === route.path
                      ? 'bg-indigo-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {route.label}
                </button>
              ))}
            </div>
          </div>

          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <h3 className="text-blue-400 font-semibold mb-2">测试说明</h3>
            <ul className="text-blue-300 text-sm space-y-1">
              <li>• 点击上方按钮测试路由跳转</li>
              <li>• 当前路径会在上方显示</li>
              <li>• 活跃路径按钮会高亮显示</li>
              <li>• 如果某个路径无法跳转，说明路由配置有问题</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationTest;
