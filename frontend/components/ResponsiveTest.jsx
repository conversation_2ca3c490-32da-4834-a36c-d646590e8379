import React, { useState, useEffect } from 'react'

const ResponsiveTest = () => {
  const [screenInfo, setScreenInfo] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
    deviceType: 'desktop'
  })

  useEffect(() => {
    const updateScreenInfo = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      let deviceType = 'desktop'
      if (width < 480) {
        deviceType = 'mobile-small'
      } else if (width < 768) {
        deviceType = 'mobile'
      } else if (width < 1200) {
        deviceType = 'tablet'
      } else if (width < 1600) {
        deviceType = 'desktop'
      } else {
        deviceType = 'desktop-large'
      }

      setScreenInfo({ width, height, deviceType })
    }

    updateScreenInfo()
    window.addEventListener('resize', updateScreenInfo)
    
    return () => window.removeEventListener('resize', updateScreenInfo)
  }, [])

  const getDeviceIcon = (type) => {
    switch (type) {
      case 'mobile-small':
        return 'fas fa-mobile-alt'
      case 'mobile':
        return 'fas fa-mobile-alt'
      case 'tablet':
        return 'fas fa-tablet-alt'
      case 'desktop':
        return 'fas fa-desktop'
      case 'desktop-large':
        return 'fas fa-tv'
      default:
        return 'fas fa-desktop'
    }
  }

  const getDeviceColor = (type) => {
    switch (type) {
      case 'mobile-small':
        return 'var(--danger)'
      case 'mobile':
        return 'var(--warning)'
      case 'tablet':
        return 'var(--info)'
      case 'desktop':
        return 'var(--success)'
      case 'desktop-large':
        return 'var(--primary)'
      default:
        return 'var(--text-tertiary)'
    }
  }

  const breakpoints = [
    { name: '移动端(小)', range: '< 480px', active: screenInfo.width < 480 },
    { name: '移动端', range: '480px - 767px', active: screenInfo.width >= 480 && screenInfo.width < 768 },
    { name: '平板端', range: '768px - 1199px', active: screenInfo.width >= 768 && screenInfo.width < 1200 },
    { name: '桌面端', range: '1200px - 1599px', active: screenInfo.width >= 1200 && screenInfo.width < 1600 },
    { name: '大屏幕', range: '≥ 1600px', active: screenInfo.width >= 1600 }
  ]

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      background: 'var(--card-bg)',
      backdropFilter: 'blur(10px)',
      border: '1px solid var(--border-color)',
      borderRadius: 'var(--radius-lg)',
      padding: 'var(--space-4)',
      minWidth: '280px',
      zIndex: 1000,
      fontSize: 'var(--text-sm)'
    }}>
      {/* 标题 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: 'var(--space-2)',
        marginBottom: 'var(--space-3)',
        paddingBottom: 'var(--space-2)',
        borderBottom: '1px solid var(--border-color)'
      }}>
        <i 
          className={getDeviceIcon(screenInfo.deviceType)}
          style={{ color: getDeviceColor(screenInfo.deviceType) }}
        ></i>
        <span style={{ fontWeight: 'var(--font-semibold)', color: 'var(--text-primary)' }}>
          响应式测试
        </span>
      </div>

      {/* 屏幕信息 */}
      <div style={{ marginBottom: 'var(--space-3)' }}>
        <div style={{ color: 'var(--text-secondary)', marginBottom: 'var(--space-1)' }}>
          屏幕尺寸: <span style={{ color: 'var(--text-primary)', fontFamily: 'var(--font-mono)' }}>
            {screenInfo.width} × {screenInfo.height}
          </span>
        </div>
        <div style={{ color: 'var(--text-secondary)' }}>
          设备类型: <span style={{ 
            color: getDeviceColor(screenInfo.deviceType),
            fontWeight: 'var(--font-semibold)'
          }}>
            {screenInfo.deviceType}
          </span>
        </div>
      </div>

      {/* 断点信息 */}
      <div>
        <div style={{ 
          color: 'var(--text-secondary)', 
          marginBottom: 'var(--space-2)',
          fontSize: 'var(--text-xs)'
        }}>
          断点状态:
        </div>
        {breakpoints.map((bp, index) => (
          <div 
            key={index}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 'var(--space-1) 0',
              color: bp.active ? 'var(--success)' : 'var(--text-tertiary)',
              fontSize: 'var(--text-xs)'
            }}
          >
            <span>{bp.name}</span>
            <span style={{ fontFamily: 'var(--font-mono)' }}>{bp.range}</span>
            {bp.active && (
              <i className="fas fa-check" style={{ color: 'var(--success)', marginLeft: 'var(--space-2)' }}></i>
            )}
          </div>
        ))}
      </div>

      {/* 测试功能 */}
      <div style={{
        marginTop: 'var(--space-3)',
        paddingTop: 'var(--space-3)',
        borderTop: '1px solid var(--border-color)'
      }}>
        <div style={{ 
          display: 'flex', 
          gap: 'var(--space-2)',
          flexWrap: 'wrap'
        }}>
          <button 
            className="btn btn-outline btn-sm"
            onClick={() => window.dispatchEvent(new Event('resize'))}
            style={{ fontSize: 'var(--text-xs)' }}
          >
            <i className="fas fa-sync-alt"></i>
            刷新
          </button>
          <button 
            className="btn btn-outline btn-sm"
            onClick={() => console.log('Screen Info:', screenInfo)}
            style={{ fontSize: 'var(--text-xs)' }}
          >
            <i className="fas fa-code"></i>
            日志
          </button>
        </div>
      </div>
    </div>
  )
}

// 响应式网格测试组件
export const ResponsiveGridTest = () => {
  return (
    <div className="container-responsive" style={{ marginTop: 'var(--space-8)' }}>
      <h3 style={{ marginBottom: 'var(--space-6)', color: 'var(--text-primary)' }}>
        响应式网格测试
      </h3>
      
      <div className="grid-responsive">
        {Array.from({ length: 8 }).map((_, index) => (
          <div 
            key={index}
            style={{
              background: 'var(--card-bg)',
              padding: 'var(--space-4)',
              borderRadius: 'var(--radius-lg)',
              border: '1px solid var(--border-color)',
              textAlign: 'center',
              color: 'var(--text-secondary)'
            }}
          >
            网格项 {index + 1}
          </div>
        ))}
      </div>
    </div>
  )
}

// 响应式文本测试组件
export const ResponsiveTextTest = () => {
  return (
    <div className="container-responsive" style={{ marginTop: 'var(--space-8)' }}>
      <h3 style={{ marginBottom: 'var(--space-6)', color: 'var(--text-primary)' }}>
        响应式文本测试
      </h3>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--space-4)' }}>
        <p className="text-responsive-xl">超大文本 - 响应式调整</p>
        <p className="text-responsive-lg">大文本 - 响应式调整</p>
        <p className="text-responsive-base">基础文本 - 响应式调整</p>
        <p className="text-responsive-sm">小文本 - 响应式调整</p>
      </div>
    </div>
  )
}

export default ResponsiveTest
