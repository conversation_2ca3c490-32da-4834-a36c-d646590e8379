import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Settings, Plus, Play, Pause, Square, Edit, Trash2,
  TrendingUp, DollarSign, Shield, Clock, Target,
  BarChart3, Activity, AlertTriangle, CheckCircle, Sliders,
  Zap, Database, Monitor, Search, Download, Eye, FileText,
  Brain, XCircle, Sparkles
} from 'lucide-react';
import OptimizationPanel from './OptimizationPanel';

interface Strategy {
  id: number;
  name: string;
  symbol?: string;
  description: string;
  status: 'active' | 'paused' | 'stopped' | 'draft' | 'pending' | 'deploying';
  category?: string;
  type: string;
  profit: number;
  risk: 'low' | 'medium' | 'high';
  createdAt: string;
  lastRun: string;
  deployedChains: string[];
  totalInvested: number;
  currentValue: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  tags: string[];
  performance: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
  // 新增字段用于策略仓库功能
  score?: number;
  estimated_roi?: number;
  viral_potential?: number;
  sentiment?: number;
  hotspot_source?: string;
  tokenomics?: {
    total_supply: number;
    allocation: {
      airdrop: number;
      liquidity: number;
      marketing: number;
      team: number;
    };
    burn_rate: number;
  };
  deployment_config?: {
    platforms: string[];
    chains: string[];
    gas_optimized: boolean;
  };
  risk_level?: string;
  compliance_status?: string;
  // 新增监控数据字段
  monitoringData?: {
    currentPrice: number;
    priceChange24h: number;
    marketCap: number;
    marketCapChange24h: number;
    holders: number;
    holdersChange24h: number;
    liquidity: number;
    liquidityChange24h: number;
    volume24h: number;
    volumeChange24h: number;
    transactions24h: number;
    avgTransactionSize: number;
    topHolderPercentage: number;
    liquidityRatio: number;
    priceImpact1k: number;
    priceImpact10k: number;
    lastUpdate: string;
  };
}

const StrategyManagerFixed = () => {
  const navigate = useNavigate();

  // 本地状态管理
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [deployedStrategies, setDeployedStrategies] = useState<Strategy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('repository');
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);

  // 新增状态管理
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState({
    status: 'all',
    category: 'all',
    score: 'all'
  });
  const [selectedStrategies, setSelectedStrategies] = useState<number[]>([]);
  const [isDeploying, setIsDeploying] = useState(false);
  const [deploymentProgress, setDeploymentProgress] = useState(0);
  const [showStrategyDetails, setShowStrategyDetails] = useState(false);

  // 策略仓库模拟数据（未部署策略）
  const mockRepositoryStrategies: Strategy[] = [
    {
      id: 1,
      name: 'TRUMPWIN',
      symbol: 'TWIN',
      description: 'Trump 2024 Victory Token - 基于政治热点的高潜力策略',
      status: 'pending',
      category: 'politics',
      type: 'politics',
      score: 92,
      estimated_roi: 850,
      viral_potential: 0.89,
      sentiment: 0.94,
      risk_level: 'medium',
      compliance_status: 'verified',
      hotspot_source: 'Trump 2024 Election Campaign',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      profit: 0,
      risk: 'medium',
      lastRun: '',
      deployedChains: [],
      totalInvested: 0,
      currentValue: 0,
      winRate: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      tags: ['政治', '热点', '高潜力'],
      performance: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      tokenomics: {
        total_supply: 1000000000,
        burn_rate: 0.02,
        allocation: {
          airdrop: 15,
          liquidity: 40,
          marketing: 25,
          team: 20
        }
      },
      deployment_config: {
        platforms: ['pump.fun', 'raydium'],
        chains: ['solana'],
        gas_optimized: true
      }
    },
    {
      id: 2,
      name: 'AIDOG',
      symbol: 'AIDOG',
      description: 'AI Dog Token - 结合AI和Meme文化的创新代币',
      status: 'draft',
      category: 'tech',
      type: 'tech',
      score: 78,
      estimated_roi: 420,
      viral_potential: 0.76,
      sentiment: 0.68,
      risk_level: 'medium',
      compliance_status: 'pending',
      hotspot_source: 'AI Technology Trend',
      createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      profit: 0,
      risk: 'medium',
      lastRun: '',
      deployedChains: [],
      totalInvested: 0,
      currentValue: 0,
      winRate: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      tags: ['AI', '科技', 'Meme'],
      performance: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      tokenomics: {
        total_supply: 500000000,
        burn_rate: 0.01,
        allocation: {
          airdrop: 20,
          liquidity: 35,
          marketing: 30,
          team: 15
        }
      },
      deployment_config: {
        platforms: ['pump.fun'],
        chains: ['solana'],
        gas_optimized: true
      }
    },
    {
      id: 3,
      name: 'MOONCAT',
      symbol: 'MCAT',
      description: 'Moon Cat - 基于猫咪文化的社区驱动代币',
      status: 'pending',
      category: 'culture',
      type: 'culture',
      score: 85,
      estimated_roi: 650,
      viral_potential: 0.82,
      sentiment: 0.79,
      risk_level: 'low',
      compliance_status: 'verified',
      hotspot_source: 'Cat Meme Viral Trend',
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      profit: 0,
      risk: 'low',
      lastRun: '',
      deployedChains: [],
      totalInvested: 0,
      currentValue: 0,
      winRate: 0,
      maxDrawdown: 0,
      sharpeRatio: 0,
      tags: ['猫咪', '社区', '病毒传播'],
      performance: { daily: 0, weekly: 0, monthly: 0, yearly: 0 },
      tokenomics: {
        total_supply: 2000000000,
        burn_rate: 0.03,
        allocation: {
          airdrop: 25,
          liquidity: 30,
          marketing: 35,
          team: 10
        }
      },
      deployment_config: {
        platforms: ['pump.fun', 'raydium'],
        chains: ['solana'],
        gas_optimized: true
      }
    }
  ];

  // 已部署策略模拟数据（包含完整监控指标）
  const mockDeployedStrategies: Strategy[] = [
    {
      id: 101,
      name: "DOGE 趋势跟踪策略",
      symbol: "DOGE",
      description: "基于技术分析的狗狗币趋势跟踪策略，适合中长期投资",
      status: "active",
      type: "trend_following",
      profit: 12.5,
      risk: "medium",
      createdAt: "2023-12-15",
      lastRun: "2023-12-19 10:30",
      deployedChains: ["ethereum", "bsc"],
      totalInvested: 5000,
      currentValue: 5625,
      winRate: 68,
      maxDrawdown: -8.2,
      sharpeRatio: 1.45,
      tags: ["趋势跟踪", "技术分析", "中风险"],
      performance: {
        daily: 2.1,
        weekly: 8.7,
        monthly: 12.5,
        yearly: 45.2
      },
      // 新增详细监控指标
      monitoringData: {
        currentPrice: 0.0847,
        priceChange24h: 5.2,
        marketCap: 12450000,
        marketCapChange24h: 3.8,
        holders: 15420,
        holdersChange24h: 127,
        liquidity: 2850000,
        liquidityChange24h: -2.1,
        volume24h: 8750000,
        volumeChange24h: 15.6,
        transactions24h: 2847,
        avgTransactionSize: 3074,
        topHolderPercentage: 12.5,
        liquidityRatio: 0.23,
        priceImpact1k: 0.15,
        priceImpact10k: 1.42,
        lastUpdate: new Date().toISOString()
      }
    },
    {
      id: 102,
      name: "PEPE 套利策略",
      symbol: "PEPE",
      description: "跨交易所PEPE代币套利策略，利用价差获取稳定收益",
      status: "active",
      type: "arbitrage",
      profit: 8.7,
      risk: "low",
      createdAt: "2023-12-12",
      lastRun: "2023-12-19 09:15",
      deployedChains: ["ethereum", "polygon"],
      totalInvested: 8000,
      currentValue: 8696,
      winRate: 82,
      maxDrawdown: -3.1,
      sharpeRatio: 2.15,
      tags: ["套利", "低风险", "稳定收益"],
      performance: {
        daily: 0.3,
        weekly: 2.1,
        monthly: 8.7,
        yearly: 28.4
      },
      monitoringData: {
        currentPrice: 0.000001247,
        priceChange24h: -1.8,
        marketCap: 524000000,
        marketCapChange24h: -0.9,
        holders: 89420,
        holdersChange24h: -45,
        liquidity: 15200000,
        liquidityChange24h: 4.2,
        volume24h: 45600000,
        volumeChange24h: -8.3,
        transactions24h: 15847,
        avgTransactionSize: 2876,
        topHolderPercentage: 8.2,
        liquidityRatio: 0.029,
        priceImpact1k: 0.08,
        priceImpact10k: 0.75,
        lastUpdate: new Date().toISOString()
      }
    },
    {
      id: 103,
      name: "SHIB 波段交易策略",
      symbol: "SHIB",
      description: "利用SHIB价格波动进行短期波段交易的自动化策略",
      status: "paused",
      type: "swing_trading",
      profit: -2.3,
      risk: "high",
      createdAt: "2023-12-10",
      lastRun: "2023-12-18 15:45",
      deployedChains: ["ethereum"],
      totalInvested: 3000,
      currentValue: 2931,
      winRate: 45,
      maxDrawdown: -15.6,
      sharpeRatio: 0.78,
      tags: ["波段交易", "高频", "高风险"],
      performance: {
        daily: -0.8,
        weekly: -2.3,
        monthly: -2.3,
        yearly: -8.9
      },
      monitoringData: {
        currentPrice: 0.00000847,
        priceChange24h: -12.4,
        marketCap: 4980000,
        marketCapChange24h: -11.8,
        holders: 1247000,
        holdersChange24h: -2847,
        liquidity: 890000,
        liquidityChange24h: -18.5,
        volume24h: 12400000,
        volumeChange24h: 28.7,
        transactions24h: 45820,
        avgTransactionSize: 270,
        topHolderPercentage: 15.8,
        liquidityRatio: 0.18,
        priceImpact1k: 0.45,
        priceImpact10k: 4.2,
        lastUpdate: new Date().toISOString()
      }
    }
  ];

  // 策略统计数据
  const [stats, setStats] = useState({
    totalStrategies: 0,
    activeStrategies: 0,
    totalProfit: 0,
    avgWinRate: 0,
    totalInvested: 0,
    currentValue: 0
  });

  // 筛选策略
  const filteredStrategies = strategies.filter(strategy => {
    const matchesSearch = !searchQuery ||
      strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (strategy.symbol && strategy.symbol.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (strategy.hotspot_source && strategy.hotspot_source.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = filterOptions.status === 'all' || strategy.status === filterOptions.status;
    const matchesCategory = filterOptions.category === 'all' || strategy.category === filterOptions.category;
    const matchesScore = filterOptions.score === 'all' ||
                        (filterOptions.score === 'high' && (strategy.score || 0) >= 85) ||
                        (filterOptions.score === 'medium' && (strategy.score || 0) >= 70 && (strategy.score || 0) < 85) ||
                        (filterOptions.score === 'low' && (strategy.score || 0) < 70);

    return matchesSearch && matchesStatus && matchesCategory && matchesScore;
  });

  // 事件处理函数
  const handleStrategySelect = (strategyId: number) => {
    setSelectedStrategies(prev =>
      prev.includes(strategyId)
        ? prev.filter(id => id !== strategyId)
        : [...prev, strategyId]
    );
  };

  const handleSelectAll = () => {
    if (selectedStrategies.length === filteredStrategies.length) {
      setSelectedStrategies([]);
    } else {
      setSelectedStrategies(filteredStrategies.map(s => s.id));
    }
  };

  const handleViewStrategy = (strategy: Strategy) => {
    setSelectedStrategy(strategy);
    setShowStrategyDetails(true);
  };

  const deployStrategy = async (strategy: Strategy) => {
    setIsDeploying(true);
    setDeploymentProgress(0);

    // 模拟部署进度
    const interval = setInterval(() => {
      setDeploymentProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsDeploying(false);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  const handleBatchDeploy = () => {
    if (selectedStrategies.length > 0) {
      const strategy = strategies.find(s => selectedStrategies.includes(s.id));
      if (strategy) {
        deployStrategy(strategy);
      }
    }
  };

  // 初始化数据
  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setStrategies(mockRepositoryStrategies);
      setDeployedStrategies(mockDeployedStrategies);

      // 计算统计数据（基于已部署策略）
      const totalStrategies = mockDeployedStrategies.length;
      const activeStrategies = mockDeployedStrategies.filter(s => s.status === 'active').length;
      const totalProfit = mockDeployedStrategies.reduce((sum, s) => sum + s.profit, 0);
      const avgWinRate = mockDeployedStrategies.reduce((sum, s) => sum + s.winRate, 0) / totalStrategies;
      const totalInvested = mockDeployedStrategies.reduce((sum, s) => sum + s.totalInvested, 0);
      const currentValue = mockDeployedStrategies.reduce((sum, s) => sum + s.currentValue, 0);

      setStats({
        totalStrategies,
        activeStrategies,
        totalProfit,
        avgWinRate,
        totalInvested,
        currentValue
      });

      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'paused': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'stopped': return 'text-red-400 bg-red-500/10 border-red-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="w-4 h-4 text-green-400" />;
      case 'paused': return <Pause className="w-4 h-4 text-yellow-400" />;
      case 'stopped': return <Square className="w-4 h-4 text-red-400" />;
      default: return <Pause className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 flex flex-col">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-500/10 text-blue-400 rounded-xl">
              <Target className="text-2xl" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">策略引擎中心</h1>
              <p className="text-gray-400 text-lg">智能交易策略 • 安全发币操作 • 多链部署 • 自动化审计</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-green-500/10 text-green-400 px-4 py-2 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>系统运行正常</span>
            </div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-2">
              <span className="text-gray-400">成功率: </span>
              <span className="text-green-400 font-bold">99.2%</span>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 mb-8 bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 p-1 rounded-xl shadow-lg">
          {[
            { id: 'repository', name: '策略仓库', icon: Database, description: '未部署策略管理' },
            { id: 'monitoring', name: '策略监控', icon: Monitor, description: '已部署策略监控' },
            { id: 'deployment', name: '一键部署', icon: Zap, description: '快速部署工作流' },
            { id: 'security', name: '安全审计', icon: Shield, description: '智能合约安全检查' },
            { id: 'optimization', name: '参数优化', icon: Sliders, description: '智能参数优化控制' },
            { id: 'analytics', name: '数据分析', icon: BarChart3, description: '策略表现分析' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex flex-col items-center justify-center space-y-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
              }`}
              title={tab.description}
            >
              <tab.icon className="w-5 h-5" />
              <span className="text-sm">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* 策略仓库标签页内容 */}
        {activeTab === 'repository' && (
          <div className="min-h-[800px]">
            <div className="space-y-8">
              {/* 策略仓库统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">策略仓库</p>
                      <p className="text-3xl font-bold text-white mt-1">{strategies.length}</p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <Database className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">待部署</p>
                      <p className="text-3xl font-bold text-white mt-1">{strategies.filter(s => s.status === 'pending').length}</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Zap className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">平均评分</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        {(strategies.reduce((sum, s) => sum + (s.score || 0), 0) / strategies.length).toFixed(0)}
                      </p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <Target className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-400 text-sm font-medium">预期ROI</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        {(strategies.reduce((sum, s) => sum + (s.estimated_roi || 0), 0) / strategies.length).toFixed(0)}%
                      </p>
                    </div>
                    <div className="p-3 bg-orange-500/20 rounded-xl">
                      <TrendingUp className="w-8 h-8 text-orange-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 筛选和搜索栏 */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-bold text-white">策略仓库</h2>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedStrategies.length === filteredStrategies.length && filteredStrategies.length > 0}
                        onChange={handleSelectAll}
                        className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-400">
                        已选择 {selectedStrategies.length} 个策略
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    {/* 搜索框 */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="搜索策略名称、符号或热点来源..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
                      />
                    </div>

                    {/* 筛选按钮 */}
                    <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                      <select
                        value={filterOptions.status}
                        onChange={(e) => setFilterOptions(prev => ({ ...prev, status: e.target.value }))}
                        className="bg-transparent text-gray-300 text-sm focus:outline-none"
                      >
                        <option value="all">全部状态</option>
                        <option value="draft">草稿</option>
                        <option value="pending">待部署</option>
                        <option value="deploying">部署中</option>
                      </select>
                    </div>

                    <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                      <select
                        value={filterOptions.category}
                        onChange={(e) => setFilterOptions(prev => ({ ...prev, category: e.target.value }))}
                        className="bg-transparent text-gray-300 text-sm focus:outline-none"
                      >
                        <option value="all">全部类别</option>
                        <option value="politics">政治</option>
                        <option value="tech">科技</option>
                        <option value="culture">文化</option>
                        <option value="finance">金融</option>
                      </select>
                    </div>

                    {/* 批量操作按钮 */}
                    {selectedStrategies.length > 0 && (
                      <button
                        onClick={handleBatchDeploy}
                        disabled={isDeploying}
                        className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors disabled:opacity-50"
                      >
                        <Zap className="w-4 h-4" />
                        <span>批量部署 ({selectedStrategies.length})</span>
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* 策略列表 - 卡片网格布局 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {isLoading ? (
                  <div className="col-span-full flex items-center justify-center py-20">
                    <div className="text-center">
                      <div className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-gray-400">正在加载策略数据...</p>
                    </div>
                  </div>
                ) : (
                  filteredStrategies.map((strategy) => (
                    <div key={strategy.id} className="group bg-gradient-to-br from-gray-800/60 to-gray-900/60 border border-gray-700/50 rounded-2xl p-6 hover:border-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300">
                      {/* 策略头部信息 */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedStrategies.includes(strategy.id)}
                            onChange={() => handleStrategySelect(strategy.id)}
                            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />

                          <div className={`relative p-2 rounded-xl ${
                            (strategy.score || 0) >= 85 ? 'bg-red-500/20 text-red-400' :
                            (strategy.score || 0) >= 70 ? 'bg-orange-500/20 text-orange-400' :
                            'bg-green-500/20 text-green-400'
                          }`}>
                            <Zap className="w-5 h-5" />
                            {(strategy.score || 0) >= 85 && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                            )}
                          </div>

                          <div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded-lg text-xs font-bold border ${
                                strategy.status === 'pending' ? 'text-blue-400 bg-blue-500/10 border-blue-500/30' :
                                strategy.status === 'draft' ? 'text-gray-400 bg-gray-500/10 border-gray-500/30' :
                                strategy.status === 'deploying' ? 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30' :
                                'text-green-400 bg-green-500/10 border-green-500/30'
                              }`}>
                                {strategy.status === 'pending' ? '待部署' :
                                 strategy.status === 'draft' ? '草稿' :
                                 strategy.status === 'deploying' ? '部署中' : '已完成'}
                              </span>
                              <span className="text-sm font-medium text-purple-400">
                                评分: {strategy.score || 0}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              {strategy.hotspot_source || '未知来源'}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-gray-700/30 text-gray-400 text-xs rounded">
                            {strategy.category || strategy.type}
                          </span>
                        </div>
                      </div>

                      {/* 策略内容 */}
                      <div className="mb-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-lg font-bold text-white group-hover:text-blue-400 transition-colors">
                            {strategy.name}
                          </h3>
                          {strategy.symbol && (
                            <span className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded font-mono">
                              {strategy.symbol}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                          {strategy.description}
                        </p>
                      </div>

                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-green-400">
                            <TrendingUp className="w-4 h-4" />
                            <span className="text-sm font-medium">{strategy.estimated_roi || 0}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">预期ROI</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-purple-400">
                            <Brain className="w-4 h-4" />
                            <span className="text-sm font-medium">{((strategy.viral_potential || 0) * 100).toFixed(0)}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">病毒潜力</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-orange-400">
                            <Activity className="w-4 h-4" />
                            <span className="text-sm font-medium">{((strategy.sentiment || 0) * 100).toFixed(0)}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">情绪指数</p>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            创建: {new Date(strategy.createdAt).toLocaleDateString()}
                          </span>
                          {strategy.compliance_status && (
                            <span className={`px-2 py-1 rounded text-xs ${
                              strategy.compliance_status === 'verified' ? 'bg-green-500/20 text-green-400' :
                              strategy.compliance_status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-red-500/20 text-red-400'
                            }`}>
                              {strategy.compliance_status === 'verified' ? '已验证' :
                               strategy.compliance_status === 'pending' ? '审核中' : '未通过'}
                            </span>
                          )}
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewStrategy(strategy)}
                            className="flex items-center space-x-1 px-3 py-1.5 bg-gray-500/20 text-gray-400 rounded-lg hover:bg-gray-500/30 transition-colors text-xs font-medium"
                          >
                            <Eye className="w-3 h-3" />
                            <span>详情</span>
                          </button>

                          {strategy.status !== 'deploying' && (
                            <button
                              onClick={() => deployStrategy(strategy)}
                              disabled={isDeploying}
                              className="flex items-center space-x-1 px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors text-xs font-medium disabled:opacity-50"
                            >
                              <Zap className="w-3 h-3" />
                              <span>部署</span>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* 空状态 */}
              {!isLoading && strategies.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white mb-2">暂无策略数据</h3>
                  <p className="text-gray-400 text-sm">还没有创建任何策略，点击上方按钮开始创建</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 策略监控标签页内容 */}
        {activeTab === 'monitoring' && (
          <div className="min-h-[800px]">
            <div className="space-y-8">
              {/* 监控统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">活跃策略</p>
                      <p className="text-3xl font-bold text-white mt-1">{stats.activeStrategies}</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Activity className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">总市值</p>
                      <p className="text-3xl font-bold text-white mt-1">${(stats.currentValue / 1000).toFixed(1)}K</p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <DollarSign className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">总收益率</p>
                      <p className={`text-3xl font-bold mt-1 ${stats.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {stats.totalProfit >= 0 ? '+' : ''}{stats.totalProfit.toFixed(1)}%
                      </p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <TrendingUp className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-400 text-sm font-medium">平均胜率</p>
                      <p className="text-3xl font-bold text-white mt-1">{stats.avgWinRate.toFixed(0)}%</p>
                    </div>
                    <div className="p-3 bg-orange-500/20 rounded-xl">
                      <Target className="w-8 h-8 text-orange-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 已部署策略列表 */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                <h2 className="text-xl font-bold text-white mb-6">已部署策略监控</h2>

                <div className="space-y-6">
                  {deployedStrategies.map((strategy) => (
                    <div key={strategy.id} className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 border border-gray-700/50 rounded-2xl p-6 hover:border-blue-500/30 transition-all duration-300">
                      {/* 策略头部 */}
                      <div className="flex items-start justify-between mb-6">
                        <div className="flex items-center space-x-4">
                          <div className={`p-3 rounded-xl ${
                            strategy.status === 'active' ? 'bg-green-500/20 text-green-400' :
                            strategy.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-red-500/20 text-red-400'
                          }`}>
                            {getStatusIcon(strategy.status)}
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-white">{strategy.name}</h3>
                            <div className="flex items-center space-x-3 mt-1">
                              <span className="text-lg text-gray-300 font-mono">{strategy.symbol}</span>
                              <span className={`px-2 py-1 rounded text-xs ${getStatusColor(strategy.status)}`}>
                                {strategy.status === 'active' ? '运行中' :
                                 strategy.status === 'paused' ? '已暂停' : '已停止'}
                              </span>
                              <span className="text-xs text-gray-500">
                                最后更新: {strategy.monitoringData ? new Date(strategy.monitoringData.lastUpdate).toLocaleTimeString() : '未知'}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`text-2xl font-bold ${strategy.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            {strategy.profit >= 0 ? '+' : ''}{strategy.profit}%
                          </p>
                          <p className="text-sm text-gray-400">总收益率</p>
                        </div>
                      </div>

                      {strategy.monitoringData && (
                        <>
                          {/* 价格和市值信息 */}
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-sm text-gray-400">当前价格</p>
                                <DollarSign className="w-4 h-4 text-blue-400" />
                              </div>
                              <p className="text-lg font-bold text-white">${strategy.monitoringData.currentPrice.toFixed(8)}</p>
                              <p className={`text-sm ${strategy.monitoringData.priceChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {strategy.monitoringData.priceChange24h >= 0 ? '+' : ''}{strategy.monitoringData.priceChange24h.toFixed(2)}% (24h)
                              </p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-sm text-gray-400">市值</p>
                                <TrendingUp className="w-4 h-4 text-purple-400" />
                              </div>
                              <p className="text-lg font-bold text-white">${(strategy.monitoringData.marketCap / 1000000).toFixed(2)}M</p>
                              <p className={`text-sm ${strategy.monitoringData.marketCapChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {strategy.monitoringData.marketCapChange24h >= 0 ? '+' : ''}{strategy.monitoringData.marketCapChange24h.toFixed(2)}% (24h)
                              </p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-sm text-gray-400">持币人数</p>
                                <Target className="w-4 h-4 text-green-400" />
                              </div>
                              <p className="text-lg font-bold text-white">{strategy.monitoringData.holders.toLocaleString()}</p>
                              <p className={`text-sm ${strategy.monitoringData.holdersChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {strategy.monitoringData.holdersChange24h >= 0 ? '+' : ''}{strategy.monitoringData.holdersChange24h} (24h)
                              </p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-sm text-gray-400">流动性</p>
                                <Activity className="w-4 h-4 text-cyan-400" />
                              </div>
                              <p className="text-lg font-bold text-white">${(strategy.monitoringData.liquidity / 1000000).toFixed(2)}M</p>
                              <p className={`text-sm ${strategy.monitoringData.liquidityChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {strategy.monitoringData.liquidityChange24h >= 0 ? '+' : ''}{strategy.monitoringData.liquidityChange24h.toFixed(2)}% (24h)
                              </p>
                            </div>
                          </div>

                          {/* 交易数据 */}
                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <p className="text-sm text-gray-400 mb-1">24h交易量</p>
                              <p className="text-lg font-bold text-white">${(strategy.monitoringData.volume24h / 1000000).toFixed(2)}M</p>
                              <p className={`text-xs ${strategy.monitoringData.volumeChange24h >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                {strategy.monitoringData.volumeChange24h >= 0 ? '+' : ''}{strategy.monitoringData.volumeChange24h.toFixed(1)}%
                              </p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <p className="text-sm text-gray-400 mb-1">24h交易次数</p>
                              <p className="text-lg font-bold text-white">{strategy.monitoringData.transactions24h.toLocaleString()}</p>
                              <p className="text-xs text-gray-400">平均: ${strategy.monitoringData.avgTransactionSize.toLocaleString()}</p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <p className="text-sm text-gray-400 mb-1">大户占比</p>
                              <p className="text-lg font-bold text-white">{strategy.monitoringData.topHolderPercentage.toFixed(1)}%</p>
                              <p className="text-xs text-gray-400">流动性比率: {(strategy.monitoringData.liquidityRatio * 100).toFixed(1)}%</p>
                            </div>

                            <div className="bg-gray-700/30 rounded-xl p-4">
                              <p className="text-sm text-gray-400 mb-1">价格影响</p>
                              <p className="text-lg font-bold text-white">{strategy.monitoringData.priceImpact1k.toFixed(2)}%</p>
                              <p className="text-xs text-gray-400">1K USD / 10K: {strategy.monitoringData.priceImpact10k.toFixed(2)}%</p>
                            </div>
                          </div>
                        </>
                      )}

                      {/* 策略表现指标 */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div className="bg-gray-700/30 rounded-xl p-4">
                          <p className="text-sm text-gray-400 mb-1">胜率</p>
                          <p className="text-lg font-bold text-purple-400">{strategy.winRate}%</p>
                        </div>
                        <div className="bg-gray-700/30 rounded-xl p-4">
                          <p className="text-sm text-gray-400 mb-1">夏普比率</p>
                          <p className="text-lg font-bold text-blue-400">{strategy.sharpeRatio}</p>
                        </div>
                        <div className="bg-gray-700/30 rounded-xl p-4">
                          <p className="text-sm text-gray-400 mb-1">最大回撤</p>
                          <p className="text-lg font-bold text-red-400">{strategy.maxDrawdown}%</p>
                        </div>
                        <div className="bg-gray-700/30 rounded-xl p-4">
                          <p className="text-sm text-gray-400 mb-1">当前价值</p>
                          <p className="text-lg font-bold text-white">${strategy.currentValue.toLocaleString()}</p>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-3 pt-4 border-t border-gray-700/50">
                        <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors">
                          <BarChart3 className="w-4 h-4" />
                          <span>详细监控</span>
                        </button>
                        <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-500/20 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors">
                          <Plus className="w-4 h-4" />
                          <span>添加流动性</span>
                        </button>
                        <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500/20 text-orange-400 rounded-lg hover:bg-orange-500/30 transition-colors">
                          <Target className="w-4 h-4" />
                          <span>启动退出</span>
                        </button>
                        {strategy.status === 'active' ? (
                          <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-yellow-500/20 text-yellow-400 rounded-lg hover:bg-yellow-500/30 transition-colors">
                            <Pause className="w-4 h-4" />
                            <span>暂停</span>
                          </button>
                        ) : (
                          <button className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-green-500/20 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors">
                            <Play className="w-4 h-4" />
                            <span>启动</span>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 其他标签页内容 */}
        {(activeTab === 'deployment' || activeTab === 'security' || activeTab === 'analytics') && (
          <div className="min-h-[800px]">
            <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-12 text-center">
              <div className="w-16 h-16 text-gray-400 mx-auto mb-4 flex items-center justify-center">
                {activeTab === 'deployment' && <Zap className="w-16 h-16" />}
                {activeTab === 'security' && <Shield className="w-16 h-16" />}
                {activeTab === 'analytics' && <BarChart3 className="w-16 h-16" />}
              </div>
              <h3 className="text-lg font-medium text-white mb-2">
                {activeTab === 'deployment' && '一键部署'}
                {activeTab === 'security' && '安全审计'}
                {activeTab === 'analytics' && '数据分析'}
              </h3>
              <p className="text-gray-400">
                {activeTab === 'deployment' && '快速部署工作流功能正在开发中...'}
                {activeTab === 'security' && '智能合约安全检查功能正在开发中...'}
                {activeTab === 'analytics' && '策略表现分析功能正在开发中...'}
              </p>
            </div>
          </div>
        )}

        {/* 参数优化标签页内容 */}
        {activeTab === 'optimization' && (
          <div className="min-h-[800px] space-y-6">
            <OptimizationPanel />
          </div>
        )}

        {/* 策略详情模态框 */}
        {showStrategyDetails && selectedStrategy && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* 模态框头部 */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className={`p-3 rounded-xl ${
                    (selectedStrategy.score || 0) >= 85 ? 'bg-red-500/20 text-red-400' :
                    (selectedStrategy.score || 0) >= 70 ? 'bg-orange-500/20 text-orange-400' :
                    'bg-green-500/20 text-green-400'
                  }`}>
                    <Zap className="w-6 h-6" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white">{selectedStrategy.name}</h2>
                    <p className="text-gray-400">{selectedStrategy.symbol}</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowStrategyDetails(false)}
                  className="p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-colors"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              {/* 策略详情内容 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 基础信息 */}
                <div className="space-y-4">
                  <div className="bg-gray-700/30 rounded-xl p-4">
                    <h3 className="text-lg font-bold text-white mb-3">基础信息</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-400">策略名称:</span>
                        <span className="text-white">{selectedStrategy.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">代币符号:</span>
                        <span className="text-white">{selectedStrategy.symbol}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">类别:</span>
                        <span className="text-white">{selectedStrategy.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">状态:</span>
                        <span className={`${
                          selectedStrategy.status === 'pending' ? 'text-blue-400' :
                          selectedStrategy.status === 'draft' ? 'text-gray-400' :
                          'text-green-400'
                        }`}>
                          {selectedStrategy.status === 'pending' ? '待部署' :
                           selectedStrategy.status === 'draft' ? '草稿' : '已完成'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">热点来源:</span>
                        <span className="text-white">{selectedStrategy.hotspot_source}</span>
                      </div>
                    </div>
                  </div>

                  {/* 关键指标 */}
                  <div className="bg-gray-700/30 rounded-xl p-4">
                    <h3 className="text-lg font-bold text-white mb-3">关键指标</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-400">{selectedStrategy.score}</p>
                        <p className="text-xs text-gray-400">综合评分</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-400">{selectedStrategy.estimated_roi}%</p>
                        <p className="text-xs text-gray-400">预期ROI</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-400">{((selectedStrategy.viral_potential || 0) * 100).toFixed(0)}%</p>
                        <p className="text-xs text-gray-400">病毒潜力</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-400">{((selectedStrategy.sentiment || 0) * 100).toFixed(0)}%</p>
                        <p className="text-xs text-gray-400">情绪指数</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 经济模型和部署配置 */}
                <div className="space-y-4">
                  {selectedStrategy.tokenomics && (
                    <div className="bg-gray-700/30 rounded-xl p-4">
                      <h3 className="text-lg font-bold text-white mb-3">经济模型</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">总供应量:</span>
                          <span className="text-white">{selectedStrategy.tokenomics.total_supply.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">燃烧率:</span>
                          <span className="text-white">{(selectedStrategy.tokenomics.burn_rate * 100).toFixed(1)}%</span>
                        </div>
                        <div className="mt-3">
                          <p className="text-gray-400 mb-2">代币分配:</p>
                          <div className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">空投:</span>
                              <span className="text-white">{selectedStrategy.tokenomics.allocation.airdrop}%</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">流动性:</span>
                              <span className="text-white">{selectedStrategy.tokenomics.allocation.liquidity}%</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">营销:</span>
                              <span className="text-white">{selectedStrategy.tokenomics.allocation.marketing}%</span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-400">团队:</span>
                              <span className="text-white">{selectedStrategy.tokenomics.allocation.team}%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedStrategy.deployment_config && (
                    <div className="bg-gray-700/30 rounded-xl p-4">
                      <h3 className="text-lg font-bold text-white mb-3">部署配置</h3>
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-400">平台:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedStrategy.deployment_config.platforms.map((platform, index) => (
                              <span key={index} className="px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">
                                {platform}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-400">链:</span>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedStrategy.deployment_config.chains.map((chain, index) => (
                              <span key={index} className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded">
                                {chain}
                              </span>
                            ))}
                          </div>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Gas优化:</span>
                          <span className={selectedStrategy.deployment_config.gas_optimized ? 'text-green-400' : 'text-red-400'}>
                            {selectedStrategy.deployment_config.gas_optimized ? '已启用' : '未启用'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-end space-x-4 mt-6 pt-6 border-t border-gray-700">
                <button
                  onClick={() => setShowStrategyDetails(false)}
                  className="px-4 py-2 bg-gray-500/20 text-gray-400 rounded-lg hover:bg-gray-500/30 transition-colors"
                >
                  关闭
                </button>
                {selectedStrategy.status !== 'deploying' && (
                  <button
                    onClick={() => {
                      deployStrategy(selectedStrategy);
                      setShowStrategyDetails(false);
                    }}
                    disabled={isDeploying}
                    className="px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors disabled:opacity-50"
                  >
                    部署策略
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 部署进度模态框 */}
        {isDeploying && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 rounded-2xl p-8 max-w-md w-full mx-4">
              <div className="text-center">
                <div className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                <h3 className="text-lg font-bold text-white mb-2">正在部署策略</h3>
                <p className="text-gray-400 mb-4">请稍候，策略正在部署中...</p>
                <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${deploymentProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-400">{deploymentProgress}% 完成</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StrategyManagerFixed;
