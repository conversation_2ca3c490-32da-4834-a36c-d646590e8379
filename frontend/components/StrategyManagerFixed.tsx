import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Settings, Plus, Play, Pause, Square, Edit, Trash2,
  TrendingUp, DollarSign, Shield, Clock, Target,
  BarChart3, Activity, AlertTriangle, CheckCircle, Sliders
} from 'lucide-react';
import OptimizationPanel from './OptimizationPanel';

interface Strategy {
  id: number;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'stopped';
  type: string;
  profit: number;
  risk: 'low' | 'medium' | 'high';
  createdAt: string;
  lastRun: string;
  deployedChains: string[];
  totalInvested: number;
  currentValue: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  tags: string[];
  performance: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
}

const StrategyManagerFixed = () => {
  const navigate = useNavigate();

  // 本地状态管理
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('strategies');
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null);

  // 模拟策略数据
  const mockStrategies: Strategy[] = [
    {
      id: 1,
      name: "DOGE 趋势跟踪策略",
      description: "基于技术分析的狗狗币趋势跟踪策略，适合中长期投资",
      status: "active",
      type: "trend_following",
      profit: 12.5,
      risk: "medium",
      createdAt: "2023-12-15",
      lastRun: "2023-12-19 10:30",
      deployedChains: ["ethereum", "bsc"],
      totalInvested: 5000,
      currentValue: 5625,
      winRate: 68,
      maxDrawdown: -8.2,
      sharpeRatio: 1.45,
      tags: ["趋势跟踪", "技术分析", "中风险"],
      performance: {
        daily: 2.1,
        weekly: 8.7,
        monthly: 12.5,
        yearly: 45.2
      }
    },
    {
      id: 2,
      name: "SHIB 波段交易策略",
      description: "利用SHIB价格波动进行短期波段交易的自动化策略",
      status: "paused",
      type: "swing_trading",
      profit: -2.3,
      risk: "high",
      createdAt: "2023-12-10",
      lastRun: "2023-12-18 15:45",
      deployedChains: ["ethereum"],
      totalInvested: 3000,
      currentValue: 2931,
      winRate: 45,
      maxDrawdown: -15.6,
      sharpeRatio: 0.78,
      tags: ["波段交易", "高频", "高风险"],
      performance: {
        daily: -0.8,
        weekly: -2.3,
        monthly: -2.3,
        yearly: -8.9
      }
    },
    {
      id: 3,
      name: "PEPE 套利策略",
      description: "跨交易所PEPE代币套利策略，利用价差获取稳定收益",
      status: "active",
      type: "arbitrage",
      profit: 8.7,
      risk: "low",
      createdAt: "2023-12-12",
      lastRun: "2023-12-19 09:15",
      deployedChains: ["ethereum", "polygon"],
      totalInvested: 8000,
      currentValue: 8696,
      winRate: 82,
      maxDrawdown: -3.1,
      sharpeRatio: 2.15,
      tags: ["套利", "低风险", "稳定收益"],
      performance: {
        daily: 0.3,
        weekly: 2.1,
        monthly: 8.7,
        yearly: 28.4
      }
    },
    {
      id: 4,
      name: "多币种组合策略",
      description: "分散投资多种Meme币的组合策略，降低单币种风险",
      status: "stopped",
      type: "portfolio",
      profit: 15.8,
      risk: "medium",
      createdAt: "2023-11-28",
      lastRun: "2023-12-17 14:20",
      deployedChains: ["ethereum", "bsc", "polygon"],
      totalInvested: 12000,
      currentValue: 13896,
      winRate: 74,
      maxDrawdown: -6.8,
      sharpeRatio: 1.89,
      tags: ["组合投资", "分散风险", "多链"],
      performance: {
        daily: 0.7,
        weekly: 3.2,
        monthly: 15.8,
        yearly: 52.1
      }
    }
  ];

  // 策略统计数据
  const [stats, setStats] = useState({
    totalStrategies: 0,
    activeStrategies: 0,
    totalProfit: 0,
    avgWinRate: 0,
    totalInvested: 0,
    currentValue: 0
  });

  // 初始化数据
  useEffect(() => {
    setIsLoading(true);
    setTimeout(() => {
      setStrategies(mockStrategies);
      
      // 计算统计数据
      const totalStrategies = mockStrategies.length;
      const activeStrategies = mockStrategies.filter(s => s.status === 'active').length;
      const totalProfit = mockStrategies.reduce((sum, s) => sum + s.profit, 0);
      const avgWinRate = mockStrategies.reduce((sum, s) => sum + s.winRate, 0) / totalStrategies;
      const totalInvested = mockStrategies.reduce((sum, s) => sum + s.totalInvested, 0);
      const currentValue = mockStrategies.reduce((sum, s) => sum + s.currentValue, 0);

      setStats({
        totalStrategies,
        activeStrategies,
        totalProfit,
        avgWinRate,
        totalInvested,
        currentValue
      });

      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/10 border-green-500/30';
      case 'paused': return 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30';
      case 'stopped': return 'text-red-400 bg-red-500/10 border-red-500/30';
      default: return 'text-gray-400 bg-gray-500/10 border-gray-500/30';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'high': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="w-4 h-4 text-green-400" />;
      case 'paused': return <Pause className="w-4 h-4 text-yellow-400" />;
      case 'stopped': return <Square className="w-4 h-4 text-red-400" />;
      default: return <Pause className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 flex flex-col">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-500/10 text-blue-400 rounded-xl">
              <Target className="text-2xl" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">策略引擎中心</h1>
              <p className="text-gray-400 text-lg">智能交易策略 • 安全发币操作 • 多链部署 • 自动化审计</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-green-500/10 text-green-400 px-4 py-2 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>系统运行正常</span>
            </div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-2">
              <span className="text-gray-400">成功率: </span>
              <span className="text-green-400 font-bold">99.2%</span>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 mb-8 bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 p-1 rounded-xl shadow-lg">
          <button
            onClick={() => setActiveTab('strategies')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'strategies'
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
            }`}
          >
            <Target className="w-5 h-5" />
            <span>策略管理</span>
          </button>
          <button
            onClick={() => setActiveTab('optimization')}
            className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
              activeTab === 'optimization'
                ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
            }`}
          >
            <Sliders className="w-5 h-5" />
            <span>参数优化</span>
          </button>
        </div>

        {/* 策略管理标签页内容 */}
        {activeTab === 'strategies' && (
          <div className="min-h-[800px]">
            <div className="space-y-8">
              {/* 策略统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">总策略数</p>
                      <p className="text-3xl font-bold text-white mt-1">{stats.totalStrategies}</p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <BarChart3 className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">活跃策略</p>
                      <p className="text-3xl font-bold text-white mt-1">{stats.activeStrategies}</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Activity className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">总收益率</p>
                      <p className={`text-3xl font-bold mt-1 ${stats.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {stats.totalProfit >= 0 ? '+' : ''}{stats.totalProfit.toFixed(1)}%
                      </p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <TrendingUp className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-400 text-sm font-medium">平均胜率</p>
                      <p className="text-3xl font-bold text-white mt-1">{stats.avgWinRate.toFixed(0)}%</p>
                    </div>
                    <div className="p-3 bg-orange-500/20 rounded-xl">
                      <Target className="w-8 h-8 text-orange-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 筛选和搜索栏 */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-bold text-white">策略筛选</h2>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                      <button className="px-3 py-1 bg-blue-500/20 text-blue-400 rounded border border-blue-500/30 text-sm">
                        全部
                      </button>
                      <button className="px-3 py-1 bg-gray-700/50 text-gray-300 rounded hover:bg-gray-600/50 transition-colors text-sm">
                        活跃
                      </button>
                      <button className="px-3 py-1 bg-gray-700/50 text-gray-300 rounded hover:bg-gray-600/50 transition-colors text-sm">
                        暂停
                      </button>
                    </div>

                    <button className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors">
                      <Plus className="w-4 h-4" />
                      <span>新建策略</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 策略列表 - 卡片网格布局 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {isLoading ? (
                  <div className="col-span-full flex items-center justify-center py-20">
                    <div className="text-center">
                      <div className="w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-gray-400">正在加载策略数据...</p>
                    </div>
                  </div>
                ) : (
                  strategies.map((strategy) => (
                    <div key={strategy.id} className="group bg-gradient-to-br from-gray-800/60 to-gray-900/60 border border-gray-700/50 rounded-2xl p-6 hover:border-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300">
                      {/* 策略头部信息 */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`relative p-2 rounded-xl ${
                            strategy.status === 'active' ? 'bg-green-500/20 text-green-400' :
                            strategy.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-red-500/20 text-red-400'
                          }`}>
                            {getStatusIcon(strategy.status)}
                            {strategy.status === 'active' && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                            )}
                          </div>

                          <div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded-lg text-xs font-bold border ${getStatusColor(strategy.status)}`}>
                                {strategy.status === 'active' ? '运行中' :
                                 strategy.status === 'paused' ? '已暂停' : '已停止'}
                              </span>
                              <span className={`text-sm font-medium ${getRiskColor(strategy.risk)}`}>
                                {strategy.risk === 'low' ? '低风险' : strategy.risk === 'medium' ? '中风险' : '高风险'}
                              </span>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              创建: {strategy.createdAt}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <span className="px-2 py-1 bg-gray-700/30 text-gray-400 text-xs rounded">
                            {strategy.type}
                          </span>
                        </div>
                      </div>

                      {/* 策略内容 */}
                      <div className="mb-4">
                        <h3 className="text-lg font-bold text-white mb-2 line-clamp-2 group-hover:text-blue-400 transition-colors">
                          {strategy.name}
                        </h3>
                        <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                          {strategy.description}
                        </p>
                      </div>

                      {/* 统计信息 */}
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className={`flex items-center justify-center space-x-1 ${strategy.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                            <TrendingUp className="w-4 h-4" />
                            <span className="text-sm font-medium">{strategy.profit >= 0 ? '+' : ''}{strategy.profit}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">收益率</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-purple-400">
                            <Target className="w-4 h-4" />
                            <span className="text-sm font-medium">{strategy.winRate}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">胜率</p>
                        </div>

                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 text-blue-400">
                            <DollarSign className="w-4 h-4" />
                            <span className="text-sm font-medium">${(strategy.currentValue / 1000).toFixed(1)}K</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">当前价值</p>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-500">
                            最后运行: {strategy.lastRun}
                          </span>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button className="flex items-center space-x-1 px-3 py-1.5 bg-gray-500/20 text-gray-400 rounded-lg hover:bg-gray-500/30 transition-colors text-xs font-medium">
                            <Edit className="w-3 h-3" />
                            <span>编辑</span>
                          </button>

                          <button className="flex items-center space-x-1 px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors text-xs font-medium">
                            <BarChart3 className="w-3 h-3" />
                            <span>分析</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* 空状态 */}
              {!isLoading && strategies.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white mb-2">暂无策略数据</h3>
                  <p className="text-gray-400 text-sm">还没有创建任何策略，点击上方按钮开始创建</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 参数优化标签页内容 */}
        {activeTab === 'optimization' && (
          <div className="min-h-[800px] space-y-6">
            <OptimizationPanel />
          </div>
        )}
      </div>
    </div>
  );
};

export default StrategyManagerFixed;
