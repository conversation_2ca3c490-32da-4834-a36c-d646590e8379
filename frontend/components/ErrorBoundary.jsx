import React from 'react'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // 你同样可以将错误日志上报给服务器
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({
      error: error,
      errorInfo: errorInfo
    })
  }

  render() {
    if (this.state.hasError) {
      // 你可以自定义降级后的 UI 并渲染
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative flex items-center justify-center">
          <div className="max-w-2xl mx-auto p-8">
            <div className="bg-red-500/10 backdrop-blur-md border border-red-500/20 rounded-xl shadow-lg p-8">
              <div className="text-center">
                <div className="text-6xl mb-4">🚨</div>
                <h1 className="text-2xl font-bold text-red-400 mb-4">应用出现错误</h1>
                <p className="text-white/70 mb-6">
                  MemeMaster AI 遇到了一个错误，正在尝试恢复...
                </p>
                
                {/* 错误详情 */}
                <div className="bg-black/30 rounded-lg p-4 mb-6 text-left">
                  <h3 className="text-lg font-semibold text-white mb-2">错误详情：</h3>
                  <div className="text-red-300 text-sm font-mono">
                    {this.state.error && this.state.error.toString()}
                  </div>
                  {this.state.errorInfo && (
                    <div className="mt-2 text-white/60 text-xs">
                      <details>
                        <summary className="cursor-pointer">查看堆栈信息</summary>
                        <pre className="mt-2 whitespace-pre-wrap">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30 px-6 py-3 rounded-lg font-medium transition-all duration-200"
                  >
                    🔄 刷新页面
                  </button>
                  
                  <button
                    onClick={() => {
                      this.setState({ hasError: false, error: null, errorInfo: null })
                    }}
                    className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 px-6 py-3 rounded-lg font-medium transition-all duration-200"
                  >
                    🔧 尝试恢复
                  </button>
                  
                  <button
                    onClick={() => window.location.href = '/simple'}
                    className="bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30 px-6 py-3 rounded-lg font-medium transition-all duration-200"
                  >
                    🛡️ 安全模式
                  </button>
                </div>

                {/* 建议 */}
                <div className="mt-6 text-white/60 text-sm">
                  <p>💡 如果问题持续存在，请尝试：</p>
                  <ul className="mt-2 text-left list-disc list-inside space-y-1">
                    <li>清除浏览器缓存</li>
                    <li>检查网络连接</li>
                    <li>使用安全模式</li>
                    <li>联系技术支持</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
