import React from 'react';
import { 
  FaBrain, 
  FaCogs, 
  FaCrown, 
  FaWallet,
  FaFire,
  FaRobot,
  FaBolt,
  FaChartPie,
  FaChartLine,
  FaArrowUp,
  FaArrowDown,
  FaMinus,
  FaEye,
  FaHeart,
  FaShare,
  FaFilter,
  FaSearch,
  FaPlus,
  FaPlay,
  FaPause,
  FaStop,
  FaEdit,
  FaTrash,
  FaCog,
  FaUser,
  FaBell,
  FaLock,
  FaPalette,
  FaGlobe,
  FaHome,
  FaExclamationTriangle,
  FaBars,
  FaTimes,
  FaChevronDown,
  FaSignOutAlt
} from 'react-icons/fa';

const IconTestPage: React.FC = () => {
  const iconTests = [
    { name: 'FaBrain', component: FaBrain, category: 'Dashboard' },
    { name: 'FaCogs', component: FaCogs, category: 'Dashboard' },
    { name: '<PERSON>a<PERSON>rown', component: FaCrown, category: 'Dashboard' },
    { name: 'FaWallet', component: FaWallet, category: 'Dashboard' },
    { name: 'Fa<PERSON><PERSON>', component: FaFire, category: 'Hotspot' },
    { name: 'FaRobot', component: FaRobot, category: 'Dashboard' },
    { name: 'FaBolt', component: FaBolt, category: 'Dashboard' },
    { name: 'FaChartPie', component: FaChartPie, category: 'Dashboard' },
    { name: 'FaChartLine', component: FaChartLine, category: 'Charts' },
    { name: 'FaArrowUp', component: FaArrowUp, category: 'Indicators' },
    { name: 'FaArrowDown', component: FaArrowDown, category: 'Indicators' },
    { name: 'FaMinus', component: FaMinus, category: 'Indicators' },
    { name: 'FaEye', component: FaEye, category: 'Actions' },
    { name: 'FaHeart', component: FaHeart, category: 'Actions' },
    { name: 'FaShare', component: FaShare, category: 'Actions' },
    { name: 'FaFilter', component: FaFilter, category: 'Actions' },
    { name: 'FaSearch', component: FaSearch, category: 'Actions' },
    { name: 'FaPlus', component: FaPlus, category: 'Strategy' },
    { name: 'FaPlay', component: FaPlay, category: 'Strategy' },
    { name: 'FaPause', component: FaPause, category: 'Strategy' },
    { name: 'FaStop', component: FaStop, category: 'Strategy' },
    { name: 'FaEdit', component: FaEdit, category: 'Strategy' },
    { name: 'FaTrash', component: FaTrash, category: 'Strategy' },
    { name: 'FaCog', component: FaCog, category: 'Settings' },
    { name: 'FaUser', component: FaUser, category: 'Settings' },
    { name: 'FaBell', component: FaBell, category: 'Settings' },
    { name: 'FaLock', component: FaLock, category: 'Settings' },
    { name: 'FaPalette', component: FaPalette, category: 'Settings' },
    { name: 'FaGlobe', component: FaGlobe, category: 'Settings' },
    { name: 'FaHome', component: FaHome, category: 'Navigation' },
    { name: 'FaExclamationTriangle', component: FaExclamationTriangle, category: 'Alerts' },
    { name: 'FaBars', component: FaBars, category: 'Navigation' },
    { name: 'FaTimes', component: FaTimes, category: 'Navigation' },
    { name: 'FaChevronDown', component: FaChevronDown, category: 'Navigation' },
    { name: 'FaSignOutAlt', component: FaSignOutAlt, category: 'Navigation' }
  ];

  const categories = [...new Set(iconTests.map(icon => icon.category))];

  return (
    <div className="p-8 bg-gray-900 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-4xl font-bold text-white mb-8">🔍 React Icons 测试页面</h1>
        
        <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 mb-8">
          <h2 className="text-2xl font-bold text-white mb-4">测试结果</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-4">
              <h3 className="text-green-400 font-semibold mb-2">✅ 成功加载</h3>
              <p className="text-white text-2xl font-bold">{iconTests.length}</p>
              <p className="text-gray-400 text-sm">个图标组件</p>
            </div>
            
            <div className="bg-blue-500/10 border border-blue-500/30 rounded-xl p-4">
              <h3 className="text-blue-400 font-semibold mb-2">📦 分类数量</h3>
              <p className="text-white text-2xl font-bold">{categories.length}</p>
              <p className="text-gray-400 text-sm">个功能分类</p>
            </div>
            
            <div className="bg-purple-500/10 border border-purple-500/30 rounded-xl p-4">
              <h3 className="text-purple-400 font-semibold mb-2">🎯 测试状态</h3>
              <p className="text-green-400 text-xl font-bold">全部通过</p>
              <p className="text-gray-400 text-sm">无导入错误</p>
            </div>
          </div>
        </div>

        {categories.map(category => (
          <div key={category} className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
              <span className="w-4 h-4 bg-indigo-500 rounded-full mr-3"></span>
              {category} 分类
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
              {iconTests
                .filter(icon => icon.category === category)
                .map(({ name, component: IconComponent }) => (
                  <div 
                    key={name}
                    className="bg-gray-800/50 border border-gray-700 rounded-xl p-4 hover:border-indigo-500/50 transition-colors group"
                  >
                    <div className="text-center">
                      <div className="w-12 h-12 bg-indigo-500/10 rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:bg-indigo-500/20 transition-colors">
                        <IconComponent className="text-2xl text-indigo-400" />
                      </div>
                      <p className="text-xs text-gray-400 font-mono break-all">
                        {name}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        ))}

        <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6 mt-8">
          <h2 className="text-xl font-bold text-white mb-4">🔧 修复记录</h2>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              <div>
                <p className="text-white font-medium">修复前错误:</p>
                <code className="text-red-400 text-sm bg-gray-900 px-2 py-1 rounded">
                  FaShield is not exported from 'react-icons/fa'
                </code>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <p className="text-white font-medium">修复方案:</p>
                <code className="text-green-400 text-sm bg-gray-900 px-2 py-1 rounded">
                  将 FaShield 替换为 FaLock
                </code>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <p className="text-white font-medium">清除缓存:</p>
                <code className="text-blue-400 text-sm bg-gray-900 px-2 py-1 rounded">
                  rm -rf node_modules/.vite && npm run frontend:dev
                </code>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-indigo-500/10 border border-indigo-500/30 rounded-2xl p-6 mt-8">
          <h2 className="text-xl font-bold text-indigo-400 mb-4">📋 使用建议</h2>
          <ul className="space-y-2 text-gray-300">
            <li>• 所有图标已验证可正常导入和使用</li>
            <li>• 建议使用 react-icons 官方文档确认图标名称</li>
            <li>• 如需添加新图标，请先在此页面测试</li>
            <li>• 图标导入错误会导致整个页面无法渲染</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default IconTestPage;
