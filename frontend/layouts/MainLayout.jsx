import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import Sidebar from '../components/Sidebar'
import Header from '../components/Header'
import { fetchSystemStatus } from '../store/slices/systemSlice'

const MainLayout = ({ children }) => {
  const dispatch = useDispatch()
  const { connectionStatus } = useSelector(state => state.system || {})
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)

      // 在移动端自动折叠侧边栏
      if (mobile) {
        setSidebarCollapsed(true)
        setSidebarOpen(false)
      } else {
        setSidebarCollapsed(window.innerWidth < 1200)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // 处理侧边栏切换
  const handleSidebarToggle = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen)
    } else {
      setSidebarCollapsed(!sidebarCollapsed)
    }
  }

  // 处理移动端遮罩点击
  const handleOverlayClick = () => {
    if (isMobile) {
      setSidebarOpen(false)
    }
  }

  useEffect(() => {
    // 安全地调用Redux actions
    try {
      // 定期获取系统状态
      const statusInterval = setInterval(() => {
        dispatch(fetchSystemStatus()).catch(error => {
          console.warn('Failed to fetch system status:', error)
        })
      }, 5000)

      // 清理函数
      return () => {
        clearInterval(statusInterval)
      }
    } catch (error) {
      console.warn('MainLayout useEffect error:', error)
    }
  }, [dispatch])

  return (
    <div className="container">
      {/* 移动端遮罩 */}
      {isMobile && (
        <div
          className={`sidebar-overlay ${sidebarOpen ? 'active' : ''}`}
          onClick={handleOverlayClick}
        />
      )}

      <Sidebar
        collapsed={sidebarCollapsed}
        open={sidebarOpen}
        isMobile={isMobile}
        onToggle={handleSidebarToggle}
        connectionStatus={connectionStatus}
      />

      <main className="main-content">
        <Header
          onToggleSidebar={handleSidebarToggle}
          connectionStatus={connectionStatus}
          isMobile={isMobile}
          sidebarOpen={sidebarOpen}
        />
        <div className="dashboard-content">
          {children}
        </div>
      </main>
    </div>
  )
}

export default MainLayout
