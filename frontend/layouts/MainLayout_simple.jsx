import React from 'react'
import Sidebar from '../components/Sidebar_simple'
import TopBar from '../components/TopBar_simple'

const MainLayout = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative">
      <div className="flex relative z-10">
        {/* 侧边栏 */}
        <Sidebar />
        
        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col relative z-10">
          {/* 顶部状态栏 */}
          <TopBar />
          
          {/* 页面内容 */}
          <main className="flex-1 p-6 overflow-auto relative z-20">
            <div className="max-w-7xl mx-auto relative z-30">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

export default MainLayout
