import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'

// 简化的测试组件
function SimpleApp() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative">
      <div className="flex relative z-10">
        {/* 简化的侧边栏 */}
        <div className="w-64 bg-black/20 backdrop-blur-md border-r border-white/10 h-screen flex flex-col relative z-30">
          <div className="p-6 border-b border-white/10">
            <h1 className="text-xl font-bold text-white">MemeMaster AI</h1>
            <p className="text-xs text-white/60">测试版本</p>
          </div>
          <nav className="flex-1 p-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-3 px-4 py-3 rounded-lg bg-white/20 text-white">
                <span>🏠</span>
                <span className="font-medium">仪表盘</span>
              </div>
              <div className="flex items-center space-x-3 px-4 py-3 rounded-lg text-white/70 hover:bg-white/10">
                <span>🔥</span>
                <span className="font-medium">趋势热点</span>
              </div>
              <div className="flex items-center space-x-3 px-4 py-3 rounded-lg text-white/70 hover:bg-white/10">
                <span>🛡️</span>
                <span className="font-medium">策略引擎</span>
              </div>
            </div>
          </nav>
        </div>
        
        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col relative z-10">
          {/* 顶部状态栏 */}
          <div className="bg-black/20 backdrop-blur-md border-b border-white/10 px-6 py-4 relative z-30">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
                  <span className="text-sm font-medium text-green-400">系统运行正常</span>
                </div>
              </div>
              <div className="text-white/70 text-sm">
                {new Date().toLocaleTimeString('zh-CN')}
              </div>
            </div>
          </div>
          
          {/* 页面内容 */}
          <main className="flex-1 p-6 overflow-auto relative z-20">
            <div className="max-w-7xl mx-auto relative z-30">
              <div className="space-y-6">
                {/* 页面标题 */}
                <div>
                  <h1 className="text-3xl font-bold text-white">🎉 React应用测试成功！</h1>
                  <p className="text-white/60 mt-1">MemeMaster AI 简化版本正常运行</p>
                </div>

                {/* 测试卡片 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {[
                    { title: '组件渲染', value: '✅ 正常', color: 'text-green-400', bg: 'bg-green-500/20' },
                    { title: 'CSS样式', value: '✅ 正常', color: 'text-blue-400', bg: 'bg-blue-500/20' },
                    { title: 'JavaScript', value: '✅ 正常', color: 'text-purple-400', bg: 'bg-purple-500/20' },
                    { title: '响应式设计', value: '✅ 正常', color: 'text-cyan-400', bg: 'bg-cyan-500/20' }
                  ].map((item, index) => (
                    <div key={index} className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6 relative z-20">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white/60 text-sm">{item.title}</p>
                          <p className={`text-2xl font-bold mt-1 ${item.color}`}>{item.value}</p>
                        </div>
                        <div className={`p-3 rounded-lg ${item.bg}`}>
                          <div className={`w-6 h-6 ${item.color}`}>✓</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 功能测试 */}
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6 relative z-20">
                  <h3 className="text-lg font-semibold text-white mb-4">🧪 功能测试</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-white font-medium mb-2">React Hooks</h4>
                      <TestHooks />
                    </div>
                    <div>
                      <h4 className="text-white font-medium mb-2">事件处理</h4>
                      <TestEvents />
                    </div>
                  </div>
                </div>

                {/* 诊断信息 */}
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6 relative z-20">
                  <h3 className="text-lg font-semibold text-white mb-4">📋 诊断信息</h3>
                  <div className="space-y-2 text-sm">
                    <div className="text-green-400">✅ React 18 正常工作</div>
                    <div className="text-green-400">✅ Tailwind CSS 样式正常</div>
                    <div className="text-green-400">✅ 组件渲染正常</div>
                    <div className="text-green-400">✅ 事件处理正常</div>
                    <div className="text-green-400">✅ 状态管理正常</div>
                    <div className="text-blue-400">ℹ️ 这是简化版本，如果这个能正常工作，说明基础React环境没问题</div>
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}

// 测试Hooks的组件
function TestHooks() {
  const [count, setCount] = React.useState(0)
  const [time, setTime] = React.useState(new Date())

  React.useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="space-y-2">
      <div className="text-white/70">计数器: {count}</div>
      <div className="text-white/70">时间: {time.toLocaleTimeString()}</div>
      <button 
        onClick={() => setCount(c => c + 1)}
        className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
      >
        增加计数
      </button>
    </div>
  )
}

// 测试事件处理的组件
function TestEvents() {
  const [message, setMessage] = React.useState('点击按钮测试')

  const handleClick = () => {
    setMessage(`点击时间: ${new Date().toLocaleTimeString()}`)
  }

  return (
    <div className="space-y-2">
      <div className="text-white/70">{message}</div>
      <button 
        onClick={handleClick}
        className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm"
      >
        测试点击
      </button>
    </div>
  )
}

// 渲染应用
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <SimpleApp />
  </React.StrictMode>
)
