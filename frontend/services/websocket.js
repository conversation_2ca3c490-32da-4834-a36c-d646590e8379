import { io } from 'socket.io-client'

class WebSocketService {
  constructor() {
    this.socket = null
    this.listeners = new Map()
  }

  connect() {
    if (this.socket?.connected) {
      return
    }

    this.socket = io('ws://localhost:8000', {
      transports: ['websocket'],
      autoConnect: true,
    })

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.emit('connection', { status: 'connected' })
    })

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected')
      this.emit('connection', { status: 'disconnected' })
    })

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error)
      this.emit('error', error)
    })

    // 监听系统状态更新
    this.socket.on('system_status', (data) => {
      this.emit('system_status', data)
    })

    // 监听热点更新
    this.socket.on('hotspot_update', (data) => {
      this.emit('hotspot_update', data)
    })

    // 监听策略更新
    this.socket.on('strategy_update', (data) => {
      this.emit('strategy_update', data)
    })

    // 监听钱包状态更新
    this.socket.on('wallet_update', (data) => {
      this.emit('wallet_update', data)
    })

    // 监听流动性更新
    this.socket.on('liquidity_update', (data) => {
      this.emit('liquidity_update', data)
    })

    // 监听退出信号更新
    this.socket.on('exit_signal', (data) => {
      this.emit('exit_signal', data)
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('WebSocket listener error:', error)
        }
      })
    }
  }

  send(event, data) {
    if (this.socket?.connected) {
      this.socket.emit(event, data)
    }
  }
}

// 创建单例实例
const websocketService = new WebSocketService()

export default websocketService
