import axios from 'axios'

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:8001',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// 系统 API
export const systemAPI = {
  getStatus: () => api.get('/health'),
  getInfo: () => api.get('/system/info'),
}

// 热点监控 API
export const hotspotAPI = {
  getHotspots: () => api.get('/hotspot/current'),
  getAdvancedAnalytics: () => api.get('/hotspot/analytics'),
  updateSettings: (settings) => api.post('/hotspot/settings', settings),
  updateMLSettings: (settings) => api.post('/hotspot/ml-settings', settings),
  toggleDarkwebMonitoring: () => api.post('/hotspot/darkweb/toggle'),
  updateLanguageSettings: (languageSettings) => api.post('/hotspot/languages', languageSettings),
  getDistribution: () => api.get('/hotspot/distribution'),
  getDarkwebSignals: () => api.get('/hotspot/darkweb/signals'),
  getVideoAnalysis: () => api.get('/hotspot/video/analysis'),
  getRegulatoryAlerts: () => api.get('/hotspot/regulatory/alerts'),
}

// 策略管理 API
export const strategyAPI = {
  // 策略仓库相关
  getRepository: () => api.get('/strategy/repository'),
  getDeployedStrategies: () => api.get('/strategy/deployed'),
  getStrategies: () => api.get('/strategy/list'), // 兼容旧接口

  // 策略CRUD操作
  createStrategy: (data) => api.post('/strategy/create', data),
  getStrategyDetails: (id) => api.get(`/strategy/${id}`),
  updateStrategy: (id, data) => api.put(`/strategy/${id}`, data),
  deleteStrategy: (id) => api.delete(`/strategy/${id}`),

  // 策略部署相关
  deployStrategy: (deploymentData) => api.post('/strategy/deploy', deploymentData),
  batchDeployStrategies: (deploymentData) => api.post('/strategy/batch-deploy', deploymentData),
  deployToken: (tokenData) => api.post('/strategy/deploy-token', tokenData),

  // 策略监控相关
  addMonitoring: (id, config) => api.post(`/strategy/${id}/monitor`, config),
  pauseStrategy: (id) => api.post(`/strategy/${id}/pause`),
  resumeStrategy: (id) => api.post(`/strategy/${id}/resume`),

  // 安全和优化相关
  runSecurityAudit: (tokenData) => api.post('/strategy/security-audit', tokenData),
  optimizeGas: (tokenData) => api.post('/strategy/optimize-gas', tokenData),
  generateContract: (tokenData) => api.post('/strategy/generate-contract', tokenData),
}

// 钱包管理 API
export const walletAPI = {
  getWallets: () => api.get('/wallet/list'),
  createWallets: (count) => api.post('/wallet/create', { count }),
  getBalance: (address) => api.get(`/wallet/${address}/balance`),
  // 新增批量生成钱包
  generateWallets: (config) => api.post('/wallet/generate-batch', config),
  // 随机资金分配
  distributeRandomFunds: (config) => api.post('/wallet/distribute-funds', config),
  // 启用租金回收
  enableRentRecovery: () => api.post('/wallet/rent-recovery/enable'),
  // 代理管理
  rotateProxy: () => api.post('/wallet/proxy/rotate'),
  getProxyStatus: () => api.get('/wallet/proxy/status'),
  // 防封禁监控
  getAntiBanMetrics: () => api.get('/wallet/anti-ban/metrics'),
  updateAntiBanSettings: (settings) => api.post('/wallet/anti-ban/settings', settings),
}

// 流动性控制 API
export const liquidityAPI = {
  getData: () => api.get('/liquidity/data'),
  updateSettings: (settings) => api.post('/liquidity/settings', settings),
  // MEV防护
  enableMEVProtection: () => api.post('/liquidity/mev/enable'),
  getMEVStats: () => api.get('/liquidity/mev/stats'),
  configureFlashbots: (config) => api.post('/liquidity/mev/flashbots', config),
  // 鲸鱼监控
  getWhaleActivity: () => api.get('/liquidity/whale/activity'),
  enableWhaleMonitoring: () => api.post('/liquidity/whale/enable'),
  getNansenData: () => api.get('/liquidity/whale/nansen'),
  // TVL自动补充
  triggerAutoRefill: () => api.post('/liquidity/tvl/auto-refill'),
  getTVLHistory: () => api.get('/liquidity/tvl/history'),
  // 滑点优化
  getSlippageOptimization: () => api.get('/liquidity/slippage/optimization'),
  configure1inch: (config) => api.post('/liquidity/aggregator/1inch', config),
  configureParaswap: (config) => api.post('/liquidity/aggregator/paraswap', config),
  splitLargeOrder: (orderData) => api.post('/liquidity/order/split', orderData),
}

// 部署平台 API
export const deploymentAPI = {
  // Pump.fun部署
  deployToPumpFun: (config) => api.post('/deploy/pumpfun', config),
  getPumpFunStrategy: () => api.get('/deploy/pumpfun/strategy'),

  // RAYDIUM部署
  deployToRaydium: (config) => api.post('/deploy/raydium', config),
  getRaydiumStrategy: () => api.get('/deploy/raydium/strategy'),

  // 平台对比
  getPlatformComparison: () => api.get('/deploy/comparison'),
  getRecommendations: (projectType) => api.get(`/deploy/recommendations?type=${projectType}`),
}

// 业务流程 API
export const businessFlowAPI = {
  // 触发热点警报
  triggerHotspotAlert: (hotspotData) => api.post('/business-flow/hotspot-alert', hotspotData),

  // AI策略生成
  generateAIStrategy: (hotspotData) => api.post('/business-flow/generate-strategy', hotspotData),

  // 部署Meme币
  deployMemeToken: (config) => api.post('/business-flow/deploy-meme', config),

  // 初始化流动性控制
  initializeLiquidityControl: (tokenData) => api.post('/business-flow/init-liquidity', tokenData),

  // 创建退出策略
  createExitStrategy: (tokenData) => api.post('/business-flow/create-exit-strategy', tokenData),

  // 获取业务流程状态
  getFlowStatus: (flowId) => api.get(`/business-flow/status/${flowId}`),

  // 获取已部署代币列表
  getDeployedTokens: () => api.get('/business-flow/deployed-tokens'),

  // 更新代币状态
  updateTokenStatus: (tokenId, updates) => api.put(`/business-flow/tokens/${tokenId}`, updates),

  // 执行退出策略
  executeExitStrategy: (tokenId, strategyConfig) => api.post(`/business-flow/tokens/${tokenId}/exit`, strategyConfig),
}

// 退出系统 API
export const exitAPI = {
  getSignals: () => api.get('/exit/signals'),
  getMultiIndicators: () => api.get('/exit/multi-indicators'),
  retrainXGBoost: () => api.post('/exit/xgboost/retrain'),
  emergencyExit: (params) => api.post('/exit/emergency', params),
  activateHedging: (params) => api.post('/exit/hedging/activate', params),
  updateSettings: (settings) => api.post('/exit/settings', settings),
  getBatches: () => api.get('/exit/batches'),
  getHedgingPositions: () => api.get('/exit/hedging/positions'),
}

export default api
