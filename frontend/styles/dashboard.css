/* MemeMaster AI Dashboard Styles */

/* 字体导入 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 全局样式 */
.font-inter {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.7);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
  }
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(71, 85, 105, 0.5);
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-online {
  background-color: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-warning {
  background-color: #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.status-error {
  background-color: #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.status-offline {
  background-color: #6b7280;
  box-shadow: 0 0 10px rgba(107, 114, 128, 0.5);
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
}

.btn-secondary {
  background: rgba(71, 85, 105, 0.5);
  border: 1px solid rgba(71, 85, 105, 0.7);
  color: #e2e8f0;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(71, 85, 105, 0.7);
  border-color: rgba(99, 102, 241, 0.5);
  color: white;
}

.btn-outline {
  background: transparent;
  border: 1px solid rgba(71, 85, 105, 0.5);
  color: #94a3b8;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background: rgba(71, 85, 105, 0.3);
  border-color: rgba(99, 102, 241, 0.5);
  color: white;
}

/* 图标样式 */
.icon-ai {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.icon-trade {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.icon-sub {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.icon-wallet {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* 数据趋势 */
.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-neutral {
  color: #6b7280;
}

/* 代币变化样式 */
.change-up {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
}

.change-down {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 600;
}

/* 侧边栏样式 */
.sidebar-nav {
  transition: all 0.3s ease;
}

.sidebar-nav:hover {
  background: rgba(71, 85, 105, 0.3);
  transform: translateX(2px);
}

.sidebar-nav.active {
  background: rgba(99, 102, 241, 0.2);
  border-left: 3px solid #6366f1;
  color: #a5b4fc;
}

/* 搜索框样式 */
.search-input {
  background: rgba(30, 41, 59, 0.6);
  border: 1px solid rgba(71, 85, 105, 0.5);
  transition: all 0.3s ease;
}

.search-input:focus {
  background: rgba(30, 41, 59, 0.8);
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 通知样式 */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 图表容器 */
.chart-container {
  position: relative;
  height: 320px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .welcome-banner {
    flex-direction: column;
    text-align: center;
  }
  
  .welcome-banner .btn-primary {
    margin-top: 1rem;
    width: 100%;
  }
}

@media (max-width: 640px) {
  .header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .search-bar {
    width: 100%;
  }
  
  .timeframe-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .timeframe-buttons button {
    flex: 1;
    min-width: 60px;
  }
}

/* 加载动画 */
.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-spinner {
  border: 2px solid rgba(99, 102, 241, 0.3);
  border-radius: 50%;
  border-top: 2px solid #6366f1;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

/* 自定义滚动区域 */
.custom-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.5) rgba(30, 41, 59, 0.3);
}

/* 焦点样式 */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
