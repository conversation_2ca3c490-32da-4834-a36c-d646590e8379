/* MemeMaster AI 增强滚动条样式 */
/* 基于fullscreen浏览器右侧边框滚动条效果 */

/* ===== 全局滚动条样式 ===== */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.8);
  border-radius: 6px;
  border: 1px solid rgba(30, 41, 59, 0.5);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 50%,
    rgba(99, 102, 241, 0.8) 100%
  );
  border-radius: 6px;
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 1) 0%,
    rgba(139, 92, 246, 1) 50%,
    rgba(99, 102, 241, 1) 100%
  );
  border-color: rgba(99, 102, 241, 0.6);
  box-shadow: 
    0 4px 8px rgba(99, 102, 241, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: scaleY(1.05);
}

::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, 
    rgba(79, 70, 229, 1) 0%,
    rgba(124, 58, 237, 1) 50%,
    rgba(79, 70, 229, 1) 100%
  );
  box-shadow: 
    0 2px 4px rgba(79, 70, 229, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transform: scaleY(0.95);
}

/* 滚动条角落 */
::-webkit-scrollbar-corner {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(30, 41, 59, 0.5);
}

/* ===== Firefox 滚动条样式 ===== */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(99, 102, 241, 0.8) rgba(15, 23, 42, 0.8);
}

/* ===== 增强滚动条容器类 ===== */
.enhanced-scrollbar {
  overflow-y: auto;
  overflow-x: hidden;
}

.enhanced-scrollbar::-webkit-scrollbar {
  width: 14px;
}

.enhanced-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.9);
  border-radius: 7px;
  border: 2px solid rgba(30, 41, 59, 0.6);
  box-shadow: 
    inset 0 0 8px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.enhanced-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.9) 0%,
    rgba(139, 92, 246, 0.9) 25%,
    rgba(168, 85, 247, 0.9) 50%,
    rgba(139, 92, 246, 0.9) 75%,
    rgba(99, 102, 241, 0.9) 100%
  );
  border-radius: 7px;
  border: 2px solid rgba(99, 102, 241, 0.4);
  box-shadow: 
    0 3px 6px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

.enhanced-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 1) 0%,
    rgba(139, 92, 246, 1) 25%,
    rgba(168, 85, 247, 1) 50%,
    rgba(139, 92, 246, 1) 75%,
    rgba(99, 102, 241, 1) 100%
  );
  border-color: rgba(99, 102, 241, 0.7);
  box-shadow: 
    0 5px 10px rgba(99, 102, 241, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1),
    0 0 15px rgba(99, 102, 241, 0.3);
  transform: scaleY(1.1) scaleX(1.1);
}

/* ===== 细滚动条样式 ===== */
.thin-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.4);
  border-radius: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.6) 0%,
    rgba(139, 92, 246, 0.6) 100%
  );
  border-radius: 4px;
  border: 1px solid rgba(99, 102, 241, 0.2);
  transition: all 0.2s ease;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 100%
  );
  border-color: rgba(99, 102, 241, 0.4);
}

/* ===== 隐藏滚动条但保持功能 ===== */
.hidden-scrollbar {
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* ===== 动画滚动条 ===== */
.animated-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, 
    rgba(99, 102, 241, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 25%,
    rgba(168, 85, 247, 0.8) 50%,
    rgba(139, 92, 246, 0.8) 75%,
    rgba(99, 102, 241, 0.8) 100%
  );
  background-size: 200% 200%;
  animation: scrollbar-gradient 3s ease infinite;
}

@keyframes scrollbar-gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ===== 发光滚动条效果 ===== */
.glow-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.8) 0%,
    rgba(139, 92, 246, 0.8) 100%
  );
  border-radius: 6px;
  box-shadow: 
    0 0 10px rgba(99, 102, 241, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: scrollbar-glow 2s ease-in-out infinite alternate;
}

@keyframes scrollbar-glow {
  from {
    box-shadow: 
      0 0 10px rgba(99, 102, 241, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  to {
    box-shadow: 
      0 0 20px rgba(99, 102, 241, 0.8),
      0 0 30px rgba(139, 92, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

/* ===== 响应式滚动条 ===== */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  .enhanced-scrollbar::-webkit-scrollbar {
    width: 10px;
  }
}

@media (max-width: 480px) {
  ::-webkit-scrollbar {
    width: 6px;
  }
  
  .enhanced-scrollbar::-webkit-scrollbar {
    width: 8px;
  }
}

/* ===== 特殊容器滚动条 ===== */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 3px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.4);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.6);
}

.content-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.content-scrollbar::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.6);
  border-radius: 5px;
  border: 1px solid rgba(30, 41, 59, 0.4);
}

.content-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.7) 0%,
    rgba(139, 92, 246, 0.7) 100%
  );
  border-radius: 5px;
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.content-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, 
    rgba(99, 102, 241, 0.9) 0%,
    rgba(139, 92, 246, 0.9) 100%
  );
  box-shadow: 0 3px 6px rgba(99, 102, 241, 0.3);
}
