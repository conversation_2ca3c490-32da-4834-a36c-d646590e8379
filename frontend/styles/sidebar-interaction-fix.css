/* 侧边栏交互问题修复样式 */
/* 解决点击菜单时出现白色边框的问题 */

/* ===== 全局按钮和链接focus修复 ===== */
button,
a,
[role="button"],
.nav-item,
.menu-item {
    /* 移除默认的focus outline */
    outline: none !important;
    
    /* 移除可能的白色边框 */
    border: none !important;
    
    /* 确保没有box-shadow导致的边框效果 */
    box-shadow: none !important;
}

/* 为活跃状态的菜单项提供正确的样式 */
button:focus,
a:focus,
.nav-item:focus,
.menu-item:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* ===== 侧边栏特定修复 ===== */

/* TypeScript版侧边栏 (Sidebar.tsx) */
.sidebar button {
    outline: none !important;
    border: none !important;
}

.sidebar button:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.sidebar button:active {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* 简化版侧边栏 (Sidebar_simple.jsx) */
.sidebar-simple button {
    outline: none !important;
    border: none !important;
}

.sidebar-simple button:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

/* 基础版侧边栏 (Sidebar.jsx) NavLink修复 */
.nav-item {
    outline: none !important;
    border: none !important;
}

.nav-item:focus {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
}

.nav-item.active {
    outline: none !important;
    border: none !important;
    /* 保持背景渐变，但移除边框 */
    background: linear-gradient(135deg,
        rgba(67, 56, 202, 0.2) 0%,
        rgba(99, 102, 241, 0.15) 100%) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* ===== React Router NavLink修复 ===== */
.nav-item[aria-current="page"] {
    outline: none !important;
    border: none !important;
}

/* ===== 通用交互元素修复 ===== */

/* 移除所有可能导致白色边框的样式 */
*:focus,
*:active,
*:focus-visible {
    outline: none !important;
    border-color: transparent !important;
}

/* 特殊情况：确保没有webkit特定的outline */
*::-webkit-focus-ring-color {
    outline-color: transparent !important;
}

/* ===== 可访问性保持 ===== */

/* 只在键盘导航时显示focus指示器 */
.js-focus-visible :focus:not(.focus-visible) {
    outline: none !important;
}

.focus-visible {
    outline: 2px solid rgba(99, 102, 241, 0.5) !important;
    outline-offset: 2px !important;
}

/* ===== 特定组件修复 ===== */

/* 修复可能的Tailwind CSS冲突 */
.focus\:outline-none:focus {
    outline: none !important;
}

.focus\:ring-0:focus {
    box-shadow: none !important;
}

/* 修复可能的Material-UI或其他UI库冲突 */
.MuiButton-root:focus,
.MuiIconButton-root:focus,
.MuiListItem-root:focus {
    outline: none !important;
}

/* ===== 动画和过渡修复 ===== */

/* 确保过渡动画不会产生边框效果 */
button,
a,
.nav-item {
    transition: background-color 0.3s ease,
                color 0.3s ease,
                transform 0.3s ease,
                box-shadow 0.3s ease !important;
    
    /* 明确排除border的过渡 */
    transition-property: background-color, color, transform, box-shadow !important;
}

/* ===== 浏览器特定修复 ===== */

/* Chrome/Safari */
button:focus,
a:focus {
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
}

/* Firefox */
button::-moz-focus-inner,
a::-moz-focus-inner {
    border: 0 !important;
    outline: none !important;
}

/* Edge */
button::-ms-focus-inner,
a::-ms-focus-inner {
    border: 0 !important;
    outline: none !important;
}

/* ===== 调试辅助 ===== */

/* 开发环境下可以启用这个来调试focus问题 */
/*
.debug-focus *:focus {
    outline: 2px dashed red !important;
    outline-offset: 2px !important;
}
*/

/* ===== 移动端特定修复 ===== */

/* 移动端触摸高亮修复 */
@media (max-width: 768px) {
    button,
    a,
    .nav-item {
        -webkit-tap-highlight-color: transparent !important;
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        user-select: none !important;
    }
}

/* ===== 高对比度模式支持 ===== */

/* 在高对比度模式下提供可见的focus指示器 */
@media (prefers-contrast: high) {
    button:focus-visible,
    a:focus-visible,
    .nav-item:focus-visible {
        outline: 2px solid currentColor !important;
        outline-offset: 2px !important;
    }
}

/* ===== 减少动画模式支持 ===== */

/* 为偏好减少动画的用户移除过渡效果 */
@media (prefers-reduced-motion: reduce) {
    button,
    a,
    .nav-item {
        transition: none !important;
    }
}
