/* 策略管理模块专用样式 */

/* 布局优化 */
.strategy-manager-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.strategy-content-area {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* 卡片网格布局优化 */
.strategy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  align-items: stretch;
}

/* 策略卡片动画 */
.strategy-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.strategy-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.strategy-card.selected {
  border-color: #3B82F6;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator.active::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: #10B981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.deploying::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: #3B82F6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
}

/* 评分颜色 */
.score-high {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.1);
}

.score-medium {
  color: #F59E0B;
  background: rgba(245, 158, 11, 0.1);
}

.score-low {
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
}

/* 类别标签 */
.category-politics {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.category-finance {
  color: #10B981;
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.category-technology {
  color: #3B82F6;
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.category-subculture {
  color: #8B5CF6;
  background: rgba(139, 92, 246, 0.2);
  border: 1px solid rgba(139, 92, 246, 0.3);
}

/* 监控数据动画 */
.monitoring-data {
  animation: fadeInUp 0.5s ease-out;
}

.price-chart-bar {
  transition: height 0.3s ease;
}

.price-chart-bar:hover {
  opacity: 0.8;
}

/* 模态框动画 */
.modal-overlay {
  animation: fadeIn 0.3s ease-out;
}

.modal-content {
  animation: slideInUp 0.3s ease-out;
}

/* 批量操作栏 */
.batch-actions {
  position: sticky;
  top: 0;
  z-index: 10;
  background: rgba(17, 24, 39, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 筛选器样式 */
.filter-active {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3B82F6;
  color: #3B82F6;
}

/* 卡片内容区域高度管理 */
.card-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-metrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 80px;
}

.card-actions {
  margin-top: auto;
  padding-top: 1rem;
}

/* 统计卡片高度一致性 */
.stats-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 140px;
}

.stats-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .strategy-grid {
    grid-template-columns: 1fr;
  }

  .monitoring-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .strategy-card {
    margin-bottom: 1rem;
    min-height: auto;
  }

  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .batch-actions {
    padding: 0.75rem;
  }

  .strategy-content-area {
    padding: 1rem;
  }
}

@media (max-width: 1024px) {
  .strategy-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 工具提示 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 成功/错误状态 */
.status-success {
  color: #10B981;
  background: rgba(16, 185, 129, 0.1);
}

.status-error {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.1);
}

.status-warning {
  color: #F59E0B;
  background: rgba(245, 158, 11, 0.1);
}

/* 数据可视化增强 */
.metric-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.metric-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.metric-value {
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 1.2;
}

.metric-change {
  font-size: 0.75rem;
  font-weight: 500;
}

.metric-change.positive {
  color: #10B981;
}

.metric-change.negative {
  color: #EF4444;
}

/* 搜索框增强 */
.search-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
