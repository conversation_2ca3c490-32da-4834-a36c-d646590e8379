/* MemeMaster AI 专业动画系统 */

/* ===== 页面加载动画 ===== */
.fade-in {
  animation: fadeIn var(--duration-slow) var(--ease-out);
}

.slide-in-up {
  animation: slideInUp var(--duration-normal) var(--ease-out);
}

.slide-in-right {
  animation: slideInRight var(--duration-normal) var(--ease-out);
}

.scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== 数据动画 ===== */
.counter-animation {
  animation: countUp 1s var(--ease-out);
}

@keyframes countUp {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.chart-animate {
  animation: chartReveal 1s var(--ease-out);
}

@keyframes chartReveal {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== 加载状态 ===== */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top: 2px solid var(--accent-blue);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: var(--space-1);
}

.loading-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-blue);
  border-radius: var(--radius-full);
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* ===== 数据流动效果 ===== */
.data-stream {
  position: relative;
  overflow: hidden;
}

.data-stream::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    transparent
  );
  animation: dataFlow 3s infinite;
}

@keyframes dataFlow {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* ===== 脉冲效果 ===== */
.pulse-blue {
  animation: pulseBlue 2s infinite;
}

.pulse-green {
  animation: pulseGreen 2s infinite;
}

.pulse-red {
  animation: pulseRed 2s infinite;
}

@keyframes pulseBlue {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
}

@keyframes pulseGreen {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
}

@keyframes pulseRed {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
}

/* ===== 悬浮效果 ===== */
.hover-lift {
  transition: all var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.hover-glow {
  transition: all var(--duration-normal) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: var(--glow-blue);
}

.hover-scale {
  transition: transform var(--duration-fast) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* ===== 数据更新动画 ===== */
.data-update {
  animation: dataUpdate 0.5s var(--ease-out);
}

@keyframes dataUpdate {
  0% {
    background: rgba(59, 130, 246, 0.2);
    transform: scale(1.02);
  }
  100% {
    background: transparent;
    transform: scale(1);
  }
}

/* ===== 进度动画 ===== */
.progress-animated .progress-fill {
  animation: progressFill 2s var(--ease-out);
}

@keyframes progressFill {
  from { width: 0; }
}

/* ===== 通知动画 ===== */
.notification-enter {
  animation: notificationEnter var(--duration-normal) var(--ease-out);
}

.notification-exit {
  animation: notificationExit var(--duration-normal) var(--ease-in);
}

@keyframes notificationEnter {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notificationExit {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* ===== 模态框动画 ===== */
.modal-backdrop {
  animation: backdropFadeIn var(--duration-normal) var(--ease-out);
}

.modal-content {
  animation: modalSlideIn var(--duration-normal) var(--ease-out);
}

@keyframes backdropFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ===== 图表动画 ===== */
.chart-line {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: drawLine 2s var(--ease-out) forwards;
}

.chart-bar {
  animation: growBar 1s var(--ease-out) forwards;
  transform-origin: bottom;
}

.chart-pie {
  animation: rotatePie 1s var(--ease-out) forwards;
  transform-origin: center;
}

@keyframes drawLine {
  to { stroke-dashoffset: 0; }
}

@keyframes growBar {
  from { transform: scaleY(0); }
  to { transform: scaleY(1); }
}

@keyframes rotatePie {
  from { transform: rotate(-90deg); }
  to { transform: rotate(0deg); }
}

/* ===== 交互反馈 ===== */
.click-ripple {
  position: relative;
  overflow: hidden;
}

.click-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  to {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

/* ===== 性能优化 ===== */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ===== 减少动画偏好 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
