/* 侧边栏文字方向紧急修复 */
/* 强制所有侧边栏文字水平显示 */

/* 全局重置 */
.sidebar,
.sidebar *,
.nav-item,
.nav-item *,
.logo,
.logo *,
.user-info,
.user-info * {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    text-align: left !important;
    transform: none !important;
    rotate: none !important;
    display: block !important;
}

/* 确保flex布局正确 */
.sidebar {
    display: flex !important;
    flex-direction: column !important;
}

.logo {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
}

.nav-item {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
}

.nav-item span {
    display: inline-block !important;
    white-space: nowrap !important;
}

.logo h1 {
    display: inline-block !important;
    white-space: nowrap !important;
    font-size: 26px !important;
    font-weight: 800 !important;
    background: linear-gradient(to right, #6366f1, #8b5cf6) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

/* 机器人图标样式 */
.logo .fa-robot {
    font-size: 28px !important;
    color: #6366f1 !important;
    margin-right: 12px !important;
}

/* 移除任何可能的旋转变换 */
.sidebar * {
    transform: none !important;
    rotate: none !important;
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
}

/* 确保文字不会垂直显示 */
span, h1, h2, h3, h4, h5, h6, p, div, a, button {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 修复line-clamp导致的文字垂直显示问题 */
.line-clamp-2,
.line-clamp-3,
*[class*="line-clamp"] {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 修复webkit-box-orient导致的问题 */
*[style*="-webkit-box-orient: vertical"] {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
}
