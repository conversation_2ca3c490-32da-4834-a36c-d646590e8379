/* MemeMaster AI 专业数据分析视觉系统 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 主色调 - 深蓝科技风 */
  --primary-900: #0F172A;
  --primary-800: #1E293B;
  --primary-700: #334155;
  --primary-600: #475569;
  --primary-500: #64748B;
  --primary-400: #94A3B8;
  --primary-300: #CBD5E1;
  --primary-200: #E2E8F0;
  --primary-100: #F1F5F9;

  /* 功能色 - 数据可视化 */
  --accent-blue: #3B82F6;
  --accent-cyan: #06B6D4;
  --accent-indigo: #6366F1;
  --accent-purple: #8B5CF6;

  /* 状态色系 */
  --success-500: #10B981;
  --success-400: #34D399;
  --success-300: #6EE7B7;
  
  --warning-500: #F59E0B;
  --warning-400: #FBBF24;
  --warning-300: #FCD34D;
  
  --danger-500: #EF4444;
  --danger-400: #F87171;
  --danger-300: #FCA5A5;
  
  --info-500: #3B82F6;
  --info-400: #60A5FA;
  --info-300: #93C5FD;

  /* 文本颜色 */
  --text-primary: #FFFFFF;
  --text-secondary: #E2E8F0;
  --text-tertiary: #94A3B8;
  --text-disabled: #64748B;
  --text-inverse: #0F172A;

  /* 背景颜色 */
  --bg-primary: #0F172A;
  --bg-secondary: #1E293B;
  --bg-tertiary: #334155;
  --bg-overlay: rgba(15, 23, 42, 0.95);
  --bg-glass: rgba(30, 41, 59, 0.6);

  /* 字体系统 */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
  --font-display: 'Poppins', 'Inter', sans-serif;

  /* 字体尺寸 */
  --text-4xl: 2.25rem;
  --text-3xl: 1.875rem;
  --text-2xl: 1.5rem;
  --text-xl: 1.25rem;
  --text-lg: 1.125rem;
  --text-base: 1rem;
  --text-sm: 0.875rem;
  --text-xs: 0.75rem;
  --text-data-lg: 2rem;
  --text-data-md: 1.5rem;
  --text-data-sm: 1.25rem;

  /* 字重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-black: 900;

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* 圆角系统 */
  --radius-none: 0;
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* 发光效果 */
  --glow-blue: 0 0 20px rgba(59, 130, 246, 0.3);
  --glow-green: 0 0 20px rgba(16, 185, 129, 0.3);
  --glow-red: 0 0 20px rgba(239, 68, 68, 0.3);
  --glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);

  /* 渐变 */
  --gradient-blue: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  --gradient-green: linear-gradient(135deg, #10B981 0%, #047857 100%);
  --gradient-purple: linear-gradient(135deg, #8B5CF6 0%, #5B21B6 100%);
  --gradient-red: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* 图表色板 */
  --chart-1: #3B82F6;
  --chart-2: #10B981;
  --chart-3: #F59E0B;
  --chart-4: #EF4444;
  --chart-5: #8B5CF6;
  --chart-6: #06B6D4;
  --chart-7: #84CC16;
  --chart-8: #F97316;

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* 动画缓动 */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 基础重置 ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
}

/* ===== 工具类 ===== */

/* 文本样式 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }

.text-success { color: var(--success-400); }
.text-warning { color: var(--warning-400); }
.text-danger { color: var(--danger-400); }
.text-info { color: var(--info-400); }

/* 字体尺寸 */
.text-4xl { font-size: var(--text-4xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-xl { font-size: var(--text-xl); }
.text-lg { font-size: var(--text-lg); }
.text-base { font-size: var(--text-base); }
.text-sm { font-size: var(--text-sm); }
.text-xs { font-size: var(--text-xs); }

/* 字重 */
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-black { font-weight: var(--font-black); }

/* 字体族 */
.font-mono { font-family: var(--font-mono); }
.font-display { font-family: var(--font-display); }

/* 背景 */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-glass { background: var(--bg-glass); backdrop-filter: blur(10px); }

/* 渐变背景 */
.bg-gradient-blue { background: var(--gradient-blue); }
.bg-gradient-green { background: var(--gradient-green); }
.bg-gradient-purple { background: var(--gradient-purple); }
.bg-gradient-red { background: var(--gradient-red); }

/* 圆角 */
.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* 发光效果 */
.glow-blue { box-shadow: var(--glow-blue); }
.glow-green { box-shadow: var(--glow-green); }
.glow-red { box-shadow: var(--glow-red); }
.glow-purple { box-shadow: var(--glow-purple); }

/* 动画 */
.transition-fast { transition: all var(--duration-fast) var(--ease-out); }
.transition-normal { transition: all var(--duration-normal) var(--ease-out); }
.transition-slow { transition: all var(--duration-slow) var(--ease-out); }

/* 间距 */
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* 边框 */
.border { border: 1px solid rgba(255, 255, 255, 0.1); }
.border-primary { border-color: var(--primary-600); }
.border-success { border-color: var(--success-400); }
.border-warning { border-color: var(--warning-400); }
.border-danger { border-color: var(--danger-400); }

/* 透明度 */
.opacity-10 { opacity: 0.1; }
.opacity-20 { opacity: 0.2; }
.opacity-30 { opacity: 0.3; }
.opacity-50 { opacity: 0.5; }
.opacity-70 { opacity: 0.7; }
.opacity-90 { opacity: 0.9; }

/* ===== 专业组件样式 ===== */

/* 数据卡片 */
.data-card {
  background: var(--bg-glass);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-normal) var(--ease-out);
}

.data-card:hover {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl), var(--glow-blue);
}

/* 数据指标 */
.metric-value {
  font-family: var(--font-mono);
  font-size: var(--text-data-lg);
  font-weight: var(--font-bold);
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.metric-label {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-change {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.metric-change.positive { color: var(--success-400); }
.metric-change.negative { color: var(--danger-400); }
.metric-change.neutral { color: var(--text-tertiary); }

/* 专业按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--gradient-blue);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--glow-blue);
}

.btn-secondary {
  background: transparent;
  color: var(--accent-blue);
  border: 1px solid var(--accent-blue);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  box-shadow: var(--glow-blue);
}

.btn-success {
  background: var(--gradient-green);
  color: var(--text-primary);
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--glow-green);
}

.btn-danger {
  background: var(--gradient-red);
  color: var(--text-primary);
}

.btn-danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--glow-red);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
}

/* 专业表格 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.data-table th {
  background: var(--bg-tertiary);
  padding: var(--space-4);
  text-align: left;
  font-weight: var(--font-semibold);
  font-size: var(--text-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table td {
  padding: var(--space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  font-size: var(--text-sm);
}

.data-table tr:hover {
  background: rgba(255, 255, 255, 0.02);
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* 数据单元格 */
.table-cell-number {
  font-family: var(--font-mono);
  font-weight: var(--font-medium);
  text-align: right;
}

.table-cell-status {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.status-active {
  background: rgba(16, 185, 129, 0.2);
  color: var(--success-400);
}

.status-inactive {
  background: rgba(107, 114, 128, 0.2);
  color: var(--text-tertiary);
}

.status-warning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--warning-400);
}

.status-error {
  background: rgba(239, 68, 68, 0.2);
  color: var(--danger-400);
}

/* 输入框 */
.input-field {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--bg-tertiary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: var(--text-sm);
  transition: all var(--duration-fast) var(--ease-out);
}

.input-field:focus {
  outline: none;
  border-color: var(--accent-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-field::placeholder {
  color: var(--text-disabled);
}

/* 选择框 */
.select-field {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--space-10);
}

/* 进度条 */
.progress-bar {
  width: 100%;
  height: var(--space-2);
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-blue);
  border-radius: var(--radius-full);
  transition: width var(--duration-normal) var(--ease-out);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
