/* 页面标题文字方向修复 */
/* 专门修复策略管理页面和其他页面标题垂直显示的问题 */

/* 强制所有页面标题水平显示 */
.strategy-manager-container h1,
.strategy-manager-container h2,
.strategy-manager-container h3,
.hotspot-monitor-container h1,
.hotspot-monitor-container h2,
.hotspot-monitor-container h3,
.main-content h1,
.main-content h2,
.main-content h3,
.container h1,
.container h2,
.container h3 {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: block !important;
    white-space: normal !important;
    word-break: normal !important;
    overflow-wrap: normal !important;
}

/* 特别针对策略管理中心标题 */
.text-4xl,
.text-3xl,
.text-2xl {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

/* 修复渐变文字标题 */
.bg-gradient-to-r {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

.bg-clip-text {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: inline-block !important;
}

/* 修复可能的flex容器问题 */
.flex.items-center h1,
.flex.items-center h2,
.flex.items-center h3 {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    flex-shrink: 0 !important;
}

/* 确保标题容器不会导致文字垂直 */
.flex.items-center.space-x-4 {
    flex-direction: row !important;
    align-items: center !important;
}

.flex.items-center.space-x-4 > div {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 修复可能的CSS Grid问题 */
.grid h1,
.grid h2,
.grid h3 {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 强制修复所有可能的文字方向问题 */
* {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 特别强制修复页面标题 */
h1, h2, h3, h4, h5, h6 {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: block !important;
    white-space: normal !important;
    word-break: normal !important;
    overflow-wrap: normal !important;
    text-align: left !important;
}

/* 强制修复Tailwind CSS类 */
.text-4xl, .text-3xl, .text-2xl, .text-xl, .text-lg {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

/* 强制修复渐变文字 */
.bg-gradient-to-r, .bg-clip-text, .text-transparent {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
    display: inline-block !important;
}

/* 强制修复flex容器 */
.flex, .flex * {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* 但保持侧边栏的特殊设置 */
.sidebar,
.sidebar * {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}
