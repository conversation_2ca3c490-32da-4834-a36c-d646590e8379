/* MemeMaster AI 专业视觉系统 - 优化版 */

/* 文字方向修复 - 确保所有文字都是水平显示 */
.sidebar,
.sidebar *,
.nav-item,
.nav-item *,
.logo,
.logo * {
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    direction: ltr !important;
}

/* ===== CSS 变量定义 ===== */
:root {
    /* 主色调 - 深邃专业蓝紫 */
    --primary: #4338ca;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --primary-gradient: linear-gradient(135deg, #4338ca 0%, #6366f1 50%, #8b5cf6 100%);
    --primary-glow: rgba(67, 56, 202, 0.4);

    /* 功能色彩 - 金融级精准配色 */
    --secondary: #059669;
    --secondary-light: #10b981;
    --danger: #dc2626;
    --danger-light: #ef4444;
    --warning: #d97706;
    --warning-light: #f59e0b;
    --success: #059669;
    --success-light: #10b981;
    --info: #0284c7;
    --info-light: #0ea5e9;
    
    /* 背景色系 - 深度层次感 */
    --bg-primary: #0a0f1c;
    --bg-secondary: #111827;
    --bg-tertiary: #1f2937;
    --bg-quaternary: #374151;
    --card-bg: rgba(17, 24, 39, 0.8);
    --card-bg-hover: rgba(31, 41, 55, 0.9);
    --sidebar-bg: linear-gradient(180deg, #111827 0%, #0a0f1c 100%);
    --header-bg: rgba(10, 15, 28, 0.7);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    
    /* 文本颜色 - 高对比度可读性 */
    --text-primary: #ffffff;
    --text-secondary: #e5e7eb;
    --text-tertiary: #9ca3af;
    --text-quaternary: #6b7280;
    --text-muted: rgba(255, 255, 255, 0.6);
    --text-inverse: #111827;
    
    /* 边框和分割线 - 精细层次 */
    --border-color: rgba(75, 85, 99, 0.3);
    --border-hover: var(--primary);
    --border-focus: var(--primary-light);
    --border-success: var(--success);
    --border-danger: var(--danger);
    --border-warning: var(--warning);
    
    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* 字体尺寸 */
    --text-xs: 12px;
    --text-sm: 13px;
    --text-base: 14px;
    --text-md: 15px;
    --text-lg: 16px;
    --text-xl: 18px;
    --text-2xl: 24px;
    --text-3xl: 32px;
    
    /* 字重 */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* 间距系统 */
    --space-1: 4px;
    --space-2: 8px;
    --space-3: 12px;
    --space-4: 16px;
    --space-5: 20px;
    --space-6: 24px;
    --space-8: 32px;
    --space-10: 40px;
    --space-12: 48px;
    --space-16: 64px;
    
    /* 圆角系统 */
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* 阴影系统 - 深度立体感 */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25), 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 24px 48px rgba(0, 0, 0, 0.3), 0 12px 24px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    
    /* 动画时长 */
    --duration-fast: 0.15s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
    
    /* 动画缓动 */
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== 基础重置 ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 移除所有元素的默认focus outline */
*:focus {
    outline: none;
}

/* 为可访问性添加自定义focus样式 */
button:focus-visible,
a:focus-visible,
[tabindex]:focus-visible {
    outline: 2px solid rgba(99, 102, 241, 0.5);
    outline-offset: 2px;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 布局系统 ===== */
.container {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* ===== 侧边栏样式 ===== */
.sidebar {
    width: 260px;
    background: var(--sidebar-bg);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    z-index: 10;
    position: relative;
    /* 确保文字方向正确 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        rgba(67, 56, 202, 0.05) 0%,
        rgba(17, 24, 39, 0.8) 50%,
        rgba(10, 15, 28, 0.9) 100%);
    z-index: -1;
}

.sidebar.collapsed {
    width: 80px;
}

.logo {
    display: flex;
    align-items: center;
    padding: var(--space-6) var(--space-6) var(--space-8);
    border-bottom: 1px solid var(--glass-border);
    margin-bottom: var(--space-6);
    position: relative;
    /* 确保文字方向正确 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    flex-direction: row !important;
}

.logo::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

.mobile-close-btn {
    position: absolute;
    top: 50%;
    right: var(--space-4);
    transform: translateY(-50%);
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.mobile-close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    transform: translateY(-50%) scale(1.1);
}

.mobile-menu-btn {
    display: none;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    background: var(--glass-bg);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.mobile-menu-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

/* 机器人图标样式 */
.fa-robot:before {
    content: "\f544";
}

.logo i {
    font-size: 28px;
    color: var(--primary);
    margin-right: 10px;
    transition: all var(--duration-normal) var(--ease-out);
    filter: drop-shadow(0 0 8px var(--primary-glow));
}

.sidebar.collapsed .logo i {
    margin-right: 0;
}

.logo h1 {
    font-size: 26px;
    font-weight: 800;
    background: linear-gradient(to right, #6366f1, #8b5cf6);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    transition: opacity var(--duration-normal) var(--ease-out);
    /* 确保文字方向正确 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

.sidebar.collapsed .logo h1 {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.nav-item {
    padding: var(--space-4) var(--space-6);
    margin: var(--space-1) var(--space-4);
    display: flex;
    align-items: center;
    color: var(--text-tertiary);
    text-decoration: none;
    font-size: var(--text-base);
    font-weight: var(--font-medium);
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
    /* 确保文字方向正确 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    flex-direction: row !important;
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-gradient);
    transform: scaleY(0);
    transition: transform var(--duration-normal) var(--ease-out);
}

.nav-item:hover {
    background: var(--glass-bg);
    color: var(--text-secondary);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.nav-item:hover::before {
    transform: scaleY(1);
}

.nav-item.active {
    background: linear-gradient(135deg,
        rgba(67, 56, 202, 0.2) 0%,
        rgba(99, 102, 241, 0.15) 100%);
    color: var(--text-primary);
    box-shadow: var(--shadow-lg);
}

.nav-item.active::before {
    transform: scaleY(1);
}

.nav-item i {
    width: 24px;
    margin-right: var(--space-3);
    font-size: 18px;
    transition: margin var(--duration-normal) var(--ease-out);
}

.sidebar.collapsed .nav-item i {
    margin-right: 0;
}

.nav-item span {
    transition: opacity var(--duration-normal) var(--ease-out);
    /* 确保文字方向正确 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
    display: inline-block !important;
    white-space: nowrap !important;
}

.sidebar.collapsed .nav-item span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .nav-item {
    justify-content: center;
    padding: var(--space-4) 0;
}

.divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--space-4) var(--space-5);
}

.sidebar.collapsed .divider {
    margin: var(--space-4) var(--space-3);
}

.user-info {
    padding: var(--space-5);
    margin-top: auto;
    border-top: 1px solid var(--border-color);
}

.user-info .plan {
    background: var(--primary);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: 20px;
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    display: inline-block;
    margin-bottom: var(--space-3);
}

.sidebar.collapsed .user-info .plan,
.sidebar.collapsed .user-info .info {
    display: none;
}

/* ===== 主内容区样式 ===== */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.header {
    background: rgba(17, 24, 39, 0.7);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    padding: var(--space-5) var(--space-8);
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-lg);
    z-index: 50;
    position: sticky;
    top: 0;
    transition: all 0.3s ease-in-out;
}

/* 滚动时的半透明效果 */
.header.scrolled {
    background: rgba(10, 15, 28, 0.85);
    backdrop-filter: blur(16px) saturate(200%);
    -webkit-backdrop-filter: blur(16px) saturate(200%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.search-bar {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-5);
    display: flex;
    align-items: center;
    width: 320px;
    border: 1px solid var(--glass-border);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
}

.search-bar::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--radius-xl);
    padding: 1px;
    background: var(--primary-gradient);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.search-bar:focus-within {
    border-color: transparent;
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

.search-bar:focus-within::before {
    opacity: 1;
}

.search-bar input {
    background: transparent;
    border: none;
    color: var(--text-primary);
    padding: var(--space-1) var(--space-3);
    width: 100%;
    outline: none;
    font-size: var(--text-base);
}

.search-bar input::placeholder {
    color: var(--text-tertiary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-5);
}

.notification,
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: all var(--duration-normal) var(--ease-out);
}

.notification:hover,
.user-avatar:hover {
    background: var(--primary);
    transform: scale(1.05);
}

.notification::after {
    content: "3";
    position: absolute;
    top: -3px;
    right: -3px;
    width: 18px;
    height: 18px;
    background: var(--danger);
    color: white;
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-semibold);
}

.dashboard-content {
    flex: 1;
    padding: var(--space-6);
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
    overflow-y: auto;
}

/* ===== 欢迎横幅 ===== */
.welcome-banner {
    background: linear-gradient(135deg,
        var(--primary) 0%,
        var(--primary-light) 50%,
        var(--primary-light) 100%);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.welcome-banner::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite reverse;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.welcome-text {
    position: relative;
    z-index: 2;
}

.welcome-text h2 {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-3);
    color: white;
}

.welcome-text p {
    max-width: 600px;
    opacity: 0.9;
    color: white;
    font-size: var(--text-lg);
}

.welcome-actions {
    position: relative;
    z-index: 2;
}

/* ===== 统计卡片 ===== */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-5);
}

.stat-card {
    background: var(--card-bg);
    backdrop-filter: blur(15px);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid var(--glass-border);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: all var(--duration-normal) var(--ease-out);
}

.stat-card::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: var(--radius-xl);
    padding: 1px;
    background: var(--primary-gradient);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    background: var(--card-bg-hover);
    box-shadow: var(--shadow-2xl);
}

.stat-card:hover::before {
    opacity: 1;
    height: 6px;
}

.stat-card:hover::after {
    opacity: 0.6;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: transform var(--duration-normal) var(--ease-out);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

.icon-ai {
    background: rgba(99, 102, 241, 0.2);
    color: var(--primary);
}

.icon-trade {
    background: rgba(16, 185, 129, 0.2);
    color: var(--secondary);
}

.icon-sub {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning);
}

.icon-wallet {
    background: rgba(239, 68, 68, 0.2);
    color: var(--danger);
}

.stat-value {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--space-1);
    color: var(--text-primary);
    font-family: var(--font-mono);
}

.stat-title {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

.stat-trend {
    display: flex;
    align-items: center;
    font-size: var(--text-sm);
    margin-top: var(--space-3);
    font-weight: var(--font-medium);
}

.trend-up {
    color: var(--secondary);
}

.trend-down {
    color: var(--danger);
}

.trend-neutral {
    color: var(--text-tertiary);
}

/* ===== 图表容器 ===== */
.charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-5);
}

.chart-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid var(--border-color);
    transition: all var(--duration-normal) var(--ease-out);
}

.chart-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-lg);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-5);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-title i {
    color: var(--primary);
}

.card-actions {
    display: flex;
    gap: var(--space-2);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* ===== 按钮系统 ===== */
.btn {
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    border: none;
    font-size: var(--text-base);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--duration-normal) var(--ease-out);
}

.btn:active::before {
    width: 200px;
    height: 200px;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-slow) var(--ease-out);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.btn-primary:hover:not(:disabled)::before {
    left: 100%;
}

.btn-primary:active {
    transform: translateY(0) scale(0.98);
}

.btn-secondary {
    background: var(--secondary);
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #0d9488;
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.btn-outline:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--primary);
    color: var(--text-primary);
}

.btn-sm {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-lg);
}

/* ===== 热门代币列表 ===== */
.hot-coins {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.hot-coin {
    display: flex;
    align-items: center;
    padding: var(--space-3);
    background: rgba(30, 41, 59, 0.4);
    border-radius: var(--radius-lg);
    transition: all var(--duration-normal) var(--ease-out);
    border: 1px solid transparent;
}

.hot-coin:hover {
    background: rgba(99, 102, 241, 0.1);
    transform: translateX(5px);
    border-color: var(--primary);
}

.coin-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--space-4);
    font-weight: var(--font-bold);
    color: white;
    font-size: var(--text-sm);
}

.coin-info {
    flex: 1;
}

.coin-name {
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-1);
    color: var(--text-primary);
    font-size: var(--text-base);
}

.coin-symbol {
    font-size: var(--text-sm);
    color: var(--text-tertiary);
}

.coin-change {
    font-weight: var(--font-semibold);
    font-family: var(--font-mono);
}

.change-up {
    color: var(--secondary);
}

.change-down {
    color: var(--danger);
}

/* ===== 功能卡片 ===== */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-5);
}

.feature-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: var(--space-5);
    border: 1px solid var(--border-color);
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary);
    box-shadow: var(--shadow-lg);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-bottom: var(--space-4);
    background: rgba(99, 102, 241, 0.2);
    color: var(--primary);
    transition: all var(--duration-normal) var(--ease-out);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
    background: var(--primary);
    color: white;
}

.feature-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    margin-bottom: var(--space-3);
    color: var(--text-primary);
}

.feature-desc {
    color: var(--text-muted);
    margin-bottom: var(--space-4);
    font-size: var(--text-base);
    line-height: 1.6;
}

/* ===== 增强动画效果 ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        filter: blur(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px) rotateY(-10deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(40px) rotateY(10deg);
    }
    to {
        opacity: 1;
        transform: translateX(0) rotateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8) rotate(-5deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) skewX(-15deg); }
    100% { transform: translateX(200%) skewX(-15deg); }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--primary-glow);
    }
    50% {
        box-shadow: 0 0 20px var(--primary-glow), 0 0 30px var(--primary-glow);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s var(--ease-out);
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s var(--ease-out);
}

.animate-slide-in-right {
    animation: slideInRight 0.6s var(--ease-out);
}

.animate-scale-in {
    animation: scaleIn 0.6s var(--ease-out);
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* 延迟动画 */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* ===== 增强工具类 ===== */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.bg-gradient {
    background: var(--primary-gradient);
    background-size: 200% 200%;
    animation: gradientShift 4s ease infinite;
}

.glass-effect {
    background: var(--card-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    position: relative;
}

.glass-effect::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.2),
        rgba(255, 255, 255, 0.05));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.hover-lift {
    transition: all var(--duration-normal) var(--ease-out);
    transform-style: preserve-3d;
}

.hover-lift:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow: var(--shadow-2xl);
}

.hover-glow {
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
}

.hover-glow:hover {
    box-shadow: 0 0 30px var(--primary-glow);
    transform: scale(1.02);
}

.hover-scale {
    transition: transform var(--duration-fast) var(--ease-out);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.interactive-element {
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.interactive-element::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all var(--duration-normal) var(--ease-out);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

.interactive-element:active::after {
    width: 200px;
    height: 200px;
}

.loading-shimmer {
    position: relative;
    overflow: hidden;
}

.loading-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: shimmer 2s infinite;
}

/* ===== 增强响应式设计 ===== */

/* 超大屏幕 (>1600px) */
@media (min-width: 1600px) {
    .dashboard-content {
        max-width: 1400px;
        margin: 0 auto;
    }

    .stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-8);
    }

    .charts-container {
        grid-template-columns: 2.5fr 1fr;
        gap: var(--space-8);
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-8);
    }
}

/* 大屏幕 (1200px-1600px) */
@media (min-width: 1200px) and (max-width: 1599px) {
    .stats-container {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-6);
    }

    .charts-container {
        grid-template-columns: 2fr 1fr;
        gap: var(--space-6);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }
}

/* 中等屏幕 (768px-1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
    .sidebar {
        width: 80px;
        transition: width var(--duration-normal) var(--ease-out);
    }

    .sidebar.expanded {
        width: 260px;
    }

    .sidebar:not(.expanded) .logo h1,
    .sidebar:not(.expanded) .nav-item span,
    .sidebar:not(.expanded) .user-info .plan,
    .sidebar:not(.expanded) .user-info .info {
        opacity: 0;
        width: 0;
        overflow: hidden;
        transition: all var(--duration-normal) var(--ease-out);
    }

    .sidebar:not(.expanded) .logo {
        justify-content: center;
        padding: var(--space-5) var(--space-2);
    }

    .sidebar:not(.expanded) .nav-item {
        justify-content: center;
        padding: var(--space-4) var(--space-2);
        margin: var(--space-1) var(--space-2);
    }

    .sidebar:not(.expanded) .nav-item i {
        margin-right: 0;
    }

    .sidebar:not(.expanded) .divider {
        margin: var(--space-4) var(--space-2);
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-5);
    }

    .charts-container {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-5);
    }

    .header {
        padding: var(--space-4) var(--space-6);
    }

    .search-bar {
        width: 250px;
    }
}

/* 小屏幕 (480px-767px) */
@media (min-width: 480px) and (max-width: 767px) {
    .sidebar {
        position: fixed;
        left: -260px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left var(--duration-normal) var(--ease-out);
        box-shadow: var(--shadow-2xl);
    }

    .sidebar.open {
        left: 0;
    }

    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all var(--duration-normal) var(--ease-out);
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .main-content {
        width: 100%;
        margin-left: 0;
    }

    .header {
        padding: var(--space-4) var(--space-5);
        flex-wrap: wrap;
        gap: var(--space-3);
    }

    .search-bar {
        width: 200px;
        order: 2;
    }

    .header-actions {
        order: 1;
        gap: var(--space-3);
    }

    .mobile-menu-btn {
        display: flex !important;
        order: 0;
    }

    .dashboard-content {
        padding: var(--space-5);
        gap: var(--space-5);
    }

    .welcome-banner {
        flex-direction: column;
        text-align: center;
        gap: var(--space-5);
        padding: var(--space-6);
    }

    .welcome-text h2 {
        font-size: var(--text-xl);
    }

    .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }

    .stat-card {
        padding: var(--space-4);
    }

    .stat-value {
        font-size: var(--text-2xl);
    }

    .charts-container {
        grid-template-columns: 1fr;
        gap: var(--space-5);
    }

    .chart-container {
        height: 250px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .feature-card {
        padding: var(--space-4);
    }

    .hot-coin {
        padding: var(--space-3);
        flex-wrap: wrap;
    }

    .coin-info {
        min-width: 120px;
    }
}

/* 移动端 (320px-479px) */
@media (max-width: 479px) {
    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        width: 100%;
        height: 100vh;
        z-index: 1000;
        transition: left var(--duration-normal) var(--ease-out);
    }

    .sidebar.open {
        left: 0;
    }

    .main-content {
        width: 100%;
        margin-left: 0;
    }

    .header {
        padding: var(--space-3) var(--space-4);
        flex-direction: column;
        gap: var(--space-3);
        position: sticky;
        top: 0;
        z-index: 100;
    }

    .header-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .mobile-menu-btn {
        display: flex !important;
    }

    .search-bar {
        width: 100%;
        margin-top: var(--space-2);
    }

    .header-actions {
        gap: var(--space-2);
    }

    .dashboard-content {
        padding: var(--space-4);
        gap: var(--space-4);
    }

    .welcome-banner {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
        padding: var(--space-5);
    }

    .welcome-text h2 {
        font-size: var(--text-lg);
        line-height: 1.3;
    }

    .welcome-text p {
        font-size: var(--text-sm);
        line-height: 1.4;
    }

    .stats-container {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    .stat-card {
        padding: var(--space-4);
        text-align: center;
    }

    .stat-header {
        justify-content: center;
        margin-bottom: var(--space-3);
    }

    .stat-value {
        font-size: var(--text-xl);
        margin-bottom: var(--space-2);
    }

    .charts-container {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .chart-container {
        height: 200px;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }

    .card-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-3);
    }

    .feature-card {
        padding: var(--space-4);
        text-align: center;
    }

    .feature-title {
        font-size: var(--text-lg);
    }

    .feature-desc {
        font-size: var(--text-sm);
        line-height: 1.4;
    }

    .btn {
        width: 100%;
        justify-content: center;
        padding: var(--space-3) var(--space-4);
    }

    .hot-coins {
        gap: var(--space-2);
    }

    .hot-coin {
        padding: var(--space-3);
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .coin-icon {
        align-self: center;
    }

    .coin-info {
        text-align: center;
    }
}

/* ===== 响应式工具类 ===== */

/* 显示/隐藏工具类 */
.hidden { display: none !important; }
.block { display: block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

/* 桌面端显示 */
@media (min-width: 768px) {
    .hidden-desktop { display: none !important; }
    .block-desktop { display: block !important; }
    .flex-desktop { display: flex !important; }
}

/* 移动端显示 */
@media (max-width: 767px) {
    .hidden-mobile { display: none !important; }
    .block-mobile { display: block !important; }
    .flex-mobile { display: flex !important; }
}

/* 平板端显示 */
@media (min-width: 768px) and (max-width: 1199px) {
    .hidden-tablet { display: none !important; }
    .block-tablet { display: block !important; }
    .flex-tablet { display: flex !important; }
}

/* 响应式间距 */
@media (max-width: 767px) {
    .p-responsive { padding: var(--space-4) !important; }
    .px-responsive { padding-left: var(--space-4) !important; padding-right: var(--space-4) !important; }
    .py-responsive { padding-top: var(--space-4) !important; padding-bottom: var(--space-4) !important; }
    .m-responsive { margin: var(--space-4) !important; }
    .mx-responsive { margin-left: var(--space-4) !important; margin-right: var(--space-4) !important; }
    .my-responsive { margin-top: var(--space-4) !important; margin-bottom: var(--space-4) !important; }
    .gap-responsive { gap: var(--space-4) !important; }
}

/* 响应式文字大小 */
@media (max-width: 767px) {
    .text-responsive-sm { font-size: var(--text-sm) !important; }
    .text-responsive-base { font-size: var(--text-base) !important; }
    .text-responsive-lg { font-size: var(--text-lg) !important; }
    .text-responsive-xl { font-size: var(--text-xl) !important; }
}

/* 响应式容器 */
.container-responsive {
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

@media (min-width: 640px) {
    .container-responsive {
        max-width: 640px;
        padding: 0 var(--space-6);
    }
}

@media (min-width: 768px) {
    .container-responsive {
        max-width: 768px;
        padding: 0 var(--space-8);
    }
}

@media (min-width: 1024px) {
    .container-responsive {
        max-width: 1024px;
    }
}

@media (min-width: 1280px) {
    .container-responsive {
        max-width: 1280px;
    }
}

@media (min-width: 1536px) {
    .container-responsive {
        max-width: 1536px;
    }
}

/* 响应式网格 */
.grid-responsive {
    display: grid;
    gap: var(--space-4);
    grid-template-columns: 1fr;
}

@media (min-width: 640px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-5);
    }
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-6);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* 响应式Flex */
.flex-responsive {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

@media (min-width: 768px) {
    .flex-responsive {
        flex-direction: row;
        gap: var(--space-6);
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
        transform: none;
    }

    .interactive-element {
        min-height: 44px;
        min-width: 44px;
    }

    .btn {
        min-height: 44px;
        padding: var(--space-3) var(--space-5);
    }

    .nav-item {
        min-height: 48px;
        padding: var(--space-4) var(--space-5);
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo i,
    .stat-icon i,
    .feature-icon i {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .header-actions,
    .btn,
    .interactive-element {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .dashboard-content {
        padding: 0 !important;
    }

    .stat-card,
    .chart-card,
    .feature-card {
        break-inside: avoid;
        border: 1px solid #000 !important;
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .welcome-banner {
        background: white !important;
        color: black !important;
        border: 2px solid #000 !important;
    }
}

/* ===== 深色主题优化 ===== */
.dark-theme {
    --card-bg: rgba(30, 41, 59, 0.8);
    --border-color: #475569;
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
    :root {
        --border-color: #64748b;
        --text-tertiary: #cbd5e1;
    }
}

/* ===== 减少动画偏好 ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .sidebar,
    .header-actions,
    .btn {
        display: none;
    }

    .main-content {
        margin-left: 0;
    }

    .dashboard-content {
        padding: 0;
    }

    .stat-card,
    .chart-card,
    .feature-card {
        break-inside: avoid;
        border: 1px solid #000;
        background: white;
        color: black;
    }
}

/* 输入框样式 */
.input-field {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid rgba(74, 85, 104, 0.6);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: white;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    width: 100%;
}

.input-field:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-field::placeholder {
    color: rgba(156, 163, 175, 0.7);
}

/* 文本截断样式 - 修复文字垂直显示问题 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    /* 强制文字方向为水平 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    /* 强制文字方向为水平 */
    writing-mode: horizontal-tb !important;
    text-orientation: mixed !important;
}

/* 热点卡片动画 */
.hotspot-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hotspot-card:hover {
    transform: translateY(-4px);
}

/* 脉冲动画 */
@keyframes pulse-glow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}
