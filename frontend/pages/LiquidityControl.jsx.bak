import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area, BarChart, Bar } from 'recharts'
import {
  Droplets,
  TrendingUp,
  TrendingDown,
  Settings,
  Shield,
  AlertTriangle,
  DollarSign,
  Zap,
  Eye,
  Activity,
  Target,
  RefreshCw,
  CheckCircle,
  Clock
} from 'lucide-react'
// 创建模拟的action函数
const fetchLiquidityData = () => ({ type: 'FETCH_LIQUIDITY_DATA' })
const updateSettings = (payload) => ({ type: 'UPDATE_LIQUIDITY_SETTINGS', payload })
const enableMEVProtection = () => ({ type: 'ENABLE_MEV_PROTECTION' })
const triggerAutoRefill = () => ({ type: 'TRIGGER_AUTO_REFILL' })
const fetchWhaleActivity = () => ({ type: 'FETCH_WHALE_ACTIVITY' })

const LiquidityControl = () => {
  const dispatch = useDispatch()
  const {
    currentTVL,
    tvlChange24h,
    optimalSlippage,
    settings,
    isLoading,
    mevProtectionStats,
    whaleActivity,
    autoRefillStatus,
    slippageOptimization
  } = useSelector(state => state?.liquidity || {
    currentTVL: 1200000,
    tvlChange24h: 5.2,
    optimalSlippage: 0.45,
    settings: {
      tvlAlertThreshold: 1000000,
      slippageTolerance: 1.0,
      autoRefillEnabled: true,
      mevProtectionLevel: 'medium'
    },
    isLoading: false,
    mevProtectionStats: {},
    whaleActivity: [],
    autoRefillStatus: {},
    slippageOptimization: {}
  })
  const [showSettings, setShowSettings] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  useEffect(() => {
    dispatch(fetchLiquidityData())
    
    // 每30秒更新一次流动性数据
    const interval = setInterval(() => {
      dispatch(fetchLiquidityData())
    }, 30000)

    return () => clearInterval(interval)
  }, [dispatch])

  const mockTVLHistory = [
    { time: '00:00', tvl: 800000 },
    { time: '04:00', tvl: 950000 },
    { time: '08:00', tvl: 1100000 },
    { time: '12:00', tvl: 1200000 },
    { time: '16:00', tvl: 1350000 },
    { time: '20:00', tvl: 1200000 },
    { time: '24:00', tvl: currentTVL || 1200000 }
  ]

  const handleSettingChange = (key, value) => {
    dispatch(updateSettings({ [key]: value }))
  }

  const formatCurrency = (amount) => {
    const safeAmount = amount || 0
    if (safeAmount >= 1000000) {
      return `$${(safeAmount / 1000000).toFixed(1)}M`
    }
    return `$${safeAmount.toLocaleString()}`
  }

  const getSlippageColor = () => {
    const safeSlippage = optimalSlippage || 0
    if (safeSlippage <= 0.5) return 'text-green-400'
    if (safeSlippage <= 1.0) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getTVLChangeColor = () => {
    return (tvlChange24h || 0) >= 0 ? 'text-green-400' : 'text-red-400'
  }

  const handleEnableMEVProtection = async () => {
    await dispatch(enableMEVProtection())
  }

  const handleTriggerAutoRefill = async () => {
    await dispatch(triggerAutoRefill())
  }

  const handleFetchWhaleActivity = async () => {
    await dispatch(fetchWhaleActivity())
  }

  const getMEVProtectionColor = (level) => {
    switch (level) {
      case 'high': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const [activeTab, setActiveTab] = useState('overview') // overview, mev, whale, optimization

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题和导航 */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">流动性管理（抗MEV版）</h1>
            <p className="text-white/60 mt-1">MEV防护 • 鲸鱼监控 • 滑点优化 • 自动补充</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>高级设置</span>
            </button>
            <button
              onClick={handleTriggerAutoRefill}
              className="btn-primary flex items-center space-x-2"
            >
              <Zap className="w-4 h-4" />
              <span>立即补充</span>
            </button>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
          {[
            { id: 'overview', label: '总览', icon: Activity },
            { id: 'mev', label: 'MEV防护', icon: Shield },
            { id: 'whale', label: '鲸鱼监控', icon: Eye },
            { id: 'optimization', label: '滑点优化', icon: Target }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 关键指标 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">当前TVL</p>
              <p className="text-2xl font-bold text-white mt-1">
                {formatCurrency(currentTVL || 1200000)}
              </p>
              <p className={`text-sm mt-1 flex items-center space-x-1 ${getTVLChangeColor()}`}>
                {(tvlChange24h || 0) >= 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                <span>{(tvlChange24h || 0) >= 0 ? '+' : ''}{(tvlChange24h || 0).toFixed(1)}%</span>
              </p>
            </div>
            <div className="p-3 rounded-lg bg-blue-500/20">
              <Droplets className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">最优滑点</p>
              <p className={`text-2xl font-bold mt-1 ${getSlippageColor()}`}>
                {(optimalSlippage || 0).toFixed(2)}%
              </p>
              <p className="text-white/50 text-sm mt-1">实时计算</p>
            </div>
            <div className="p-3 rounded-lg bg-green-500/20">
              <TrendingUp className="w-6 h-6 text-green-400" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">MEV保护</p>
              <p className="text-2xl font-bold text-white mt-1">
                {(settings?.mevProtectionLevel || 'medium') === 'high' ? '高' :
                 (settings?.mevProtectionLevel || 'medium') === 'medium' ? '中' : '低'}
              </p>
              <p className="text-green-400 text-sm mt-1">已启用</p>
            </div>
            <div className="p-3 rounded-lg bg-yellow-500/20">
              <Shield className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/60 text-sm">自动补充</p>
              <p className="text-2xl font-bold text-white mt-1">
                {(settings?.autoRefillEnabled || true) ? '开启' : '关闭'}
              </p>
              <p className={`text-sm mt-1 ${(settings?.autoRefillEnabled || true) ? 'text-green-400' : 'text-red-400'}`}>
                {(settings?.autoRefillEnabled || true) ? '自动管理' : '手动管理'}
              </p>
            </div>
            <div className="p-3 rounded-lg bg-purple-500/20">
              <DollarSign className="w-6 h-6 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* MEV防护和鲸鱼监控 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* MEV防护统计 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Shield className="w-5 h-5 text-blue-400" />
            <span>MEV防护统计</span>
          </h3>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <div className="text-white/60 text-sm">MEV损失减少</div>
              <div className="text-green-400 text-2xl font-bold">
                {mevProtectionStats?.lossReduction || 84.2}%
              </div>
              <div className="text-green-400 text-xs">↑ 2.1%</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <div className="text-white/60 text-sm">Flashbots使用率</div>
              <div className="text-blue-400 text-2xl font-bold">
                {mevProtectionStats?.flashbotsUsage || 76}%
              </div>
              <div className="text-blue-400 text-xs">活跃</div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">私有内存池</span>
              <span className="text-green-400 text-sm">
                {mevProtectionStats?.privateMempool ? '启用' : '禁用'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">交易随机化</span>
              <span className="text-blue-400 text-sm">
                {mevProtectionStats?.randomization ? '启用' : '禁用'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">前置交易检测</span>
              <span className="text-yellow-400 text-sm">
                {mevProtectionStats?.frontrunDetection || 0} 次拦截
              </span>
            </div>
          </div>

          <button
            onClick={handleEnableMEVProtection}
            className="btn-primary w-full mt-4 flex items-center justify-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>增强MEV防护</span>
          </button>
        </div>

        {/* 鲸鱼监控 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Eye className="w-5 h-5 text-purple-400" />
            <span>鲸鱼活动监控</span>
          </h3>

          <div className="space-y-4">
            {(whaleActivity || [
              { address: '0x1234...5678', action: '大额买入', amount: '$2.4M', impact: '+3.2%', time: '2分钟前' },
              { address: '0x9876...4321', action: '流动性撤出', amount: '$1.8M', impact: '-2.1%', time: '5分钟前' },
              { address: '0xabcd...efgh', action: '大额卖出', amount: '$3.1M', impact: '-4.5%', time: '8分钟前' }
            ]).map((activity, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-3 border border-white/10">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-mono text-sm">{activity.address}</span>
                  <span className="text-white/50 text-xs">{activity.time}</span>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <span className="text-white/70 text-sm">{activity.action}</span>
                    <div className="text-blue-400 font-bold">{activity.amount}</div>
                  </div>
                  <div className={`text-sm font-bold ${
                    activity.impact.startsWith('+') ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {activity.impact}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <button
            onClick={handleFetchWhaleActivity}
            className="btn-secondary w-full mt-4 flex items-center justify-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>刷新监控</span>
          </button>
        </div>
      </div>

      {/* 滑点优化和自动补充 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 滑点优化 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Target className="w-5 h-5 text-green-400" />
            <span>滑点优化</span>
          </h3>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <div className="text-white/60 text-sm">当前滑点</div>
              <div className={`text-2xl font-bold ${getSlippageColor()}`}>
                {(optimalSlippage || 0).toFixed(3)}%
              </div>
              <div className="text-green-400 text-xs">优化中</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4 text-center">
              <div className="text-white/60 text-sm">聚合器节省</div>
              <div className="text-purple-400 text-2xl font-bold">
                {slippageOptimization?.aggregatorSavings || 23.4}%
              </div>
              <div className="text-purple-400 text-xs">1inch+Paraswap</div>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">1inch聚合器</span>
              <span className="text-blue-400 text-sm">
                {slippageOptimization?.oneinchActive ? '活跃' : '待机'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">Paraswap聚合器</span>
              <span className="text-green-400 text-sm">
                {slippageOptimization?.paraswapActive ? '活跃' : '待机'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">订单拆分</span>
              <span className="text-yellow-400 text-sm">
                {slippageOptimization?.orderSplitting || 0} 个活跃
              </span>
            </div>
          </div>
        </div>

        {/* TVL自动补充 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Droplets className="w-5 h-5 text-blue-400" />
            <span>TVL自动补充</span>
          </h3>

          <div className="space-y-4">
            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70">补充状态</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  autoRefillStatus?.active ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                }`}>
                  {autoRefillStatus?.active ? '活跃' : '待机'}
                </span>
              </div>
              <div className="text-white text-sm">
                阈值: {formatCurrency(settings?.tvlAlertThreshold || 1000000)}
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70">上次补充</span>
                <span className="text-blue-400 text-sm">
                  {autoRefillStatus?.lastRefill || '2小时前'}
                </span>
              </div>
              <div className="text-white text-sm">
                金额: {formatCurrency(autoRefillStatus?.lastAmount || 500000)}
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70">预计下次补充</span>
                <span className="text-yellow-400 text-sm">
                  {autoRefillStatus?.nextRefill || '4小时后'}
                </span>
              </div>
              <div className="text-white text-sm">
                预计金额: {formatCurrency(autoRefillStatus?.nextAmount || 300000)}
              </div>
            </div>
          </div>

          <button
            onClick={handleTriggerAutoRefill}
            className="btn-success w-full mt-4 flex items-center justify-center space-x-2"
          >
            <Zap className="w-4 h-4" />
            <span>立即补充</span>
          </button>
        </div>
      </div>
        </div>
      )}

      {/* MEV防护标签页 */}
      {activeTab === 'mev' && (
        <div className="space-y-6">
          {/* MEV防护状态 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-400" />
                <span>Flashbots集成</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">RPC端点</span>
                  <span className="text-green-400">已连接</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Bundle成功率</span>
                  <span className="text-blue-400 font-bold">94.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均优先费</span>
                  <span className="text-yellow-400">3.2 Gwei</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">MEV损失减少</span>
                  <span className="text-green-400 font-bold">84.2%</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Clock className="w-5 h-5 text-purple-400" />
                <span>私有内存池</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">保护状态</span>
                  <span className="text-green-400">启用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">交易随机化</span>
                  <span className="text-blue-400">活跃</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">延迟范围</span>
                  <span className="text-yellow-400">2-8秒</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">前置交易拦截</span>
                  <span className="text-green-400">12次</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-yellow-400" />
                <span>MEV统计</span>
              </h3>
              <div className="space-y-4">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="text-green-400 text-sm font-medium">总节省金额</div>
                  <div className="text-green-400 text-xl font-bold">$127,450</div>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="text-blue-400 text-sm font-medium">拦截攻击</div>
                  <div className="text-blue-400 text-xl font-bold">89次</div>
                </div>
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                  <div className="text-purple-400 text-sm font-medium">保护效率</div>
                  <div className="text-purple-400 text-xl font-bold">96.8%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 鲸鱼监控标签页 */}
      {activeTab === 'whale' && (
        <div className="space-y-6">
          {/* 鲸鱼活动监控 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Eye className="w-5 h-5 text-purple-400" />
                <span>实时鲸鱼活动</span>
              </h3>
              <div className="space-y-4">
                {[
                  { address: '0x1234...5678', action: '大额买入', amount: '$2.4M', impact: '+3.2%', time: '2分钟前', risk: 'low' },
                  { address: '0x9876...4321', action: '流动性撤出', amount: '$1.8M', impact: '-2.1%', time: '5分钟前', risk: 'medium' },
                  { address: '0xabcd...efgh', action: '大额卖出', amount: '$3.1M', impact: '-4.5%', time: '8分钟前', risk: 'high' },
                  { address: '0xdef0...1234', action: '流动性添加', amount: '$5.2M', impact: '+6.8%', time: '12分钟前', risk: 'low' }
                ].map((activity, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-mono text-sm">{activity.address}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          activity.risk === 'high' ? 'bg-red-500/20 text-red-400' :
                          activity.risk === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {activity.risk === 'high' ? '高风险' : activity.risk === 'medium' ? '中风险' : '低风险'}
                        </span>
                        <span className="text-white/50 text-xs">{activity.time}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-white/70 text-sm">{activity.action}</span>
                        <div className="text-blue-400 font-bold">{activity.amount}</div>
                      </div>
                      <div className={`text-sm font-bold ${
                        activity.impact.startsWith('+') ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {activity.impact}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-400" />
                <span>Nansen数据</span>
              </h3>
              <div className="space-y-4">
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Smart Money流入</span>
                    <span className="text-green-400 font-bold">$8.2M</span>
                  </div>
                  <div className="text-green-400 text-sm">↑ 15.3% (24h)</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">鲸鱼交易数</span>
                    <span className="text-blue-400 font-bold">23</span>
                  </div>
                  <div className="text-blue-400 text-sm">过去1小时</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">监控地址</span>
                    <span className="text-purple-400 font-bold">247</span>
                  </div>
                  <div className="text-purple-400 text-sm">活跃监控中</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">预警触发</span>
                    <span className="text-yellow-400 font-bold">5</span>
                  </div>
                  <div className="text-yellow-400 text-sm">需要关注</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 滑点优化标签页 */}
      {activeTab === 'optimization' && (
        <div className="space-y-6">
          {/* 滑点优化 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-400" />
                <span>1inch聚合器</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">连接状态</span>
                  <span className="text-green-400">已连接</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">支持链</span>
                  <span className="text-blue-400">4个</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400 font-bold">96.8%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均节省</span>
                  <span className="text-purple-400 font-bold">18.5%</span>
                </div>
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="text-green-400 text-sm font-medium">今日节省</div>
                  <div className="text-green-400 text-xl font-bold">$12,450</div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Zap className="w-5 h-5 text-blue-400" />
                <span>Paraswap聚合器</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">连接状态</span>
                  <span className="text-green-400">已连接</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">支持链</span>
                  <span className="text-blue-400">5个</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400 font-bold">94.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均节省</span>
                  <span className="text-purple-400 font-bold">16.3%</span>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="text-blue-400 text-sm font-medium">今日节省</div>
                  <div className="text-blue-400 text-xl font-bold">$9,820</div>
                </div>
              </div>
            </div>
          </div>

          {/* 大额订单拆分 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Droplets className="w-5 h-5 text-purple-400" />
              <span>大额订单拆分算法</span>
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">活跃拆分订单</div>
                <div className="text-purple-400 text-2xl font-bold">7</div>
                <div className="text-purple-400 text-xs mt-1">实时执行中</div>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">平均拆分数</div>
                <div className="text-blue-400 text-2xl font-bold">4.2</div>
                <div className="text-blue-400 text-xs mt-1">每个大额订单</div>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">滑点减少</div>
                <div className="text-green-400 text-2xl font-bold">67%</div>
                <div className="text-green-400 text-xs mt-1">相比直接执行</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 设置面板 */}
      {showSettings && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-6">流动性设置</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 基础设置 */}
            <div>
              <h4 className="text-white font-medium mb-4">基础参数</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    TVL预警阈值: {formatCurrency(settings?.tvlAlertThreshold || 1000000)}
                  </label>
                  <input
                    type="range"
                    min="500000"
                    max="5000000"
                    step="100000"
                    value={settings?.tvlAlertThreshold || 1000000}
                    onChange={(e) => handleSettingChange('tvlAlertThreshold', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    滑点容忍度: {(settings?.slippageTolerance || 1.0).toFixed(1)}%
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="5"
                    step="0.1"
                    value={settings?.slippageTolerance || 1.0}
                    onChange={(e) => handleSettingChange('slippageTolerance', parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings?.autoRefillEnabled || true}
                    onChange={(e) => handleSettingChange('autoRefillEnabled', e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded"
                  />
                  <label className="text-white/70">自动补充流动性</label>
                </div>
              </div>
            </div>

            {/* MEV保护设置 */}
            <div>
              <h4 className="text-white font-medium mb-4">MEV保护</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">保护等级</label>
                  <select
                    value={settings?.mevProtectionLevel || 'medium'}
                    onChange={(e) => handleSettingChange('mevProtectionLevel', e.target.value)}
                    className="input-field w-full"
                  >
                    <option value="low">低 - 基础保护</option>
                    <option value="medium">中 - 标准保护</option>
                    <option value="high">高 - 最大保护</option>
                  </select>
                </div>

                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="flex items-start space-x-2">
                    <Shield className="w-4 h-4 text-blue-400 mt-0.5" />
                    <div className="text-sm">
                      <p className="text-blue-300 font-medium">MEV保护说明</p>
                      <p className="text-white/70 mt-1">
                        {(settings?.mevProtectionLevel || 'medium') === 'high'
                          ? '最高级别保护，包含私有内存池、时间延迟和随机化策略'
                          : (settings?.mevProtectionLevel || 'medium') === 'medium'
                          ? '标准保护，包含基础反MEV策略和监控'
                          : '基础保护，仅包含基本的MEV检测'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* TVL趋势图 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">TVL趋势 (24小时)</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockTVLHistory}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
              <XAxis dataKey="time" stroke="rgba(255,255,255,0.7)" />
              <YAxis 
                stroke="rgba(255,255,255,0.7)"
                tickFormatter={(value) => formatCurrency(value)}
              />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(0,0,0,0.8)', 
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px'
                }}
                formatter={(value) => [formatCurrency(value), 'TVL']}
              />
              <Line 
                type="monotone" 
                dataKey="tvl" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 流动性池状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4">活跃流动性池</h3>
          <div className="space-y-3">
            {[
              { pair: 'MEME/ETH', tvl: 450000, apy: 12.5, volume24h: 89000 },
              { pair: 'VIRAL/USDC', tvl: 320000, apy: 8.3, volume24h: 67000 },
              { pair: 'TREND/SOL', tvl: 280000, apy: 15.2, volume24h: 45000 }
            ].map((pool, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white font-medium">{pool.pair}</span>
                  <span className="text-green-400 text-sm">{pool.apy}% APY</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-white/60">TVL: </span>
                    <span className="text-white">{formatCurrency(pool.tvl)}</span>
                  </div>
                  <div>
                    <span className="text-white/60">24h量: </span>
                    <span className="text-white">{formatCurrency(pool.volume24h)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4">风险监控</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-white text-sm">流动性充足</span>
              </div>
              <span className="text-green-400 text-sm">正常</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span className="text-white text-sm">滑点监控</span>
              </div>
              <span className="text-yellow-400 text-sm">注意</span>
            </div>

            <div className="flex items-center justify-between p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span className="text-white text-sm">MEV保护</span>
              </div>
              <span className="text-blue-400 text-sm">活跃</span>
            </div>
          </div>

          {currentTVL < settings.tvlAlertThreshold && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-400" />
                <span className="text-red-400 text-sm font-medium">TVL预警</span>
              </div>
              <p className="text-white/70 text-xs mt-1">
                当前TVL低于预警阈值，建议增加流动性
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default LiquidityControl
