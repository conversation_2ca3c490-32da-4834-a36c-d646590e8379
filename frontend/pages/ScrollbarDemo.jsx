import React from 'react';

const ScrollbarDemo = () => {
  // 生成大量内容用于演示滚动
  const generateContent = (count) => {
    return Array.from({ length: count }, (_, i) => (
      <div key={i} className="p-4 mb-4 bg-white/5 backdrop-blur-md rounded-lg border border-white/10">
        <h3 className="text-lg font-semibold text-white mb-2">
          内容项目 #{i + 1}
        </h3>
        <p className="text-white/70">
          这是一个演示内容项目，用于展示不同的滚动条效果。每个项目都包含一些文本内容，
          让您可以看到滚动条在实际使用中的表现。滚动条的设计采用了现代化的渐变效果，
          并且具有平滑的悬停动画。
        </p>
        <div className="mt-3 flex space-x-2">
          <span className="px-2 py-1 bg-indigo-500/20 text-indigo-300 rounded text-sm">
            标签 {i + 1}
          </span>
          <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded text-sm">
            演示
          </span>
        </div>
      </div>
    ));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            增强滚动条效果演示
          </h1>
          <p className="text-white/70">
            展示不同类型的自定义滚动条效果，基于fullscreen浏览器右侧边框滚动条设计
          </p>
        </div>

        {/* 演示区域网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          
          {/* 增强滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-indigo-500 rounded-full mr-3"></span>
              增强滚动条
            </h2>
            <div className="h-80 enhanced-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：渐变背景、悬停效果、缩放动画
            </p>
          </div>

          {/* 细滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
              细滚动条
            </h2>
            <div className="h-80 thin-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：纤细设计、适合侧边栏使用
            </p>
          </div>

          {/* 动画滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-purple-500 rounded-full mr-3"></span>
              动画滚动条
            </h2>
            <div className="h-80 animated-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：渐变动画、流动效果
            </p>
          </div>

          {/* 发光滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></span>
              发光滚动条
            </h2>
            <div className="h-80 glow-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：发光效果、呼吸动画
            </p>
          </div>

          {/* 内容滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
              内容滚动条
            </h2>
            <div className="h-80 content-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：适合主内容区域、平衡的视觉效果
            </p>
          </div>

          {/* 侧边栏滚动条 */}
          <div className="bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <span className="w-3 h-3 bg-red-500 rounded-full mr-3"></span>
              侧边栏滚动条
            </h2>
            <div className="h-80 sidebar-scrollbar overflow-y-auto pr-2">
              {generateContent(15)}
            </div>
            <p className="text-white/50 text-sm mt-3">
              特点：极简设计、不干扰导航
            </p>
          </div>
        </div>

        {/* 全宽演示区域 */}
        <div className="mt-8 bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
          <h2 className="text-2xl font-semibold text-white mb-4 flex items-center">
            <span className="w-4 h-4 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full mr-3"></span>
            全宽增强滚动条演示
          </h2>
          <div className="h-96 enhanced-scrollbar overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {generateContent(30)}
            </div>
          </div>
          <div className="mt-4 p-4 bg-white/5 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">技术特点</h3>
            <ul className="text-white/70 space-y-1">
              <li>• 基于 -webkit-scrollbar 的自定义样式</li>
              <li>• 支持 Firefox 的 scrollbar-width 和 scrollbar-color</li>
              <li>• 渐变背景和边框效果</li>
              <li>• 平滑的悬停和激活状态动画</li>
              <li>• 响应式设计，适配不同屏幕尺寸</li>
              <li>• 与 MemeX 品牌色彩系统完美融合</li>
            </ul>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-8 bg-white/5 backdrop-blur-md rounded-xl border border-white/10 p-6">
          <h2 className="text-2xl font-semibold text-white mb-4">使用说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">CSS 类名</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-indigo-300">.enhanced-scrollbar</code>
                  <span className="text-white/70">增强效果</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-green-300">.thin-scrollbar</code>
                  <span className="text-white/70">细滚动条</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-purple-300">.animated-scrollbar</code>
                  <span className="text-white/70">动画效果</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-yellow-300">.glow-scrollbar</code>
                  <span className="text-white/70">发光效果</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-blue-300">.content-scrollbar</code>
                  <span className="text-white/70">内容区域</span>
                </div>
                <div className="flex justify-between items-center p-2 bg-white/5 rounded">
                  <code className="text-red-300">.sidebar-scrollbar</code>
                  <span className="text-white/70">侧边栏</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">应用场景</h3>
              <div className="space-y-3 text-white/70">
                <div className="p-3 bg-white/5 rounded">
                  <strong className="text-white">主内容区域：</strong> 使用 enhanced-scrollbar 或 content-scrollbar
                </div>
                <div className="p-3 bg-white/5 rounded">
                  <strong className="text-white">侧边栏导航：</strong> 使用 sidebar-scrollbar 或 thin-scrollbar
                </div>
                <div className="p-3 bg-white/5 rounded">
                  <strong className="text-white">特殊效果：</strong> 使用 animated-scrollbar 或 glow-scrollbar
                </div>
                <div className="p-3 bg-white/5 rounded">
                  <strong className="text-white">隐藏滚动条：</strong> 使用 hidden-scrollbar 类
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScrollbarDemo;
