import React, { useState, useEffect } from 'react'
import ResponsiveTest, { ResponsiveGridTest, ResponsiveTextTest } from '../components/ResponsiveTest'

const ResponsiveTestPage = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  })

  const [deviceType, setDeviceType] = useState('')
  const [testResults, setTestResults] = useState({
    sidebar: 'unknown',
    grid: 'unknown',
    text: 'unknown',
    spacing: 'unknown'
  })

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      const height = window.innerHeight
      
      setScreenSize({ width, height })
      
      // 判断设备类型
      if (width < 480) {
        setDeviceType('移动端 (小屏)')
        setTestResults({
          sidebar: '全屏覆盖',
          grid: '单列布局',
          text: '小字体',
          spacing: '紧凑间距'
        })
      } else if (width < 768) {
        setDeviceType('移动端 (大屏)')
        setTestResults({
          sidebar: '滑动显示',
          grid: '双列布局',
          text: '中等字体',
          spacing: '适中间距'
        })
      } else if (width < 1200) {
        setDeviceType('平板端')
        setTestResults({
          sidebar: '可折叠',
          grid: '三列布局',
          text: '标准字体',
          spacing: '标准间距'
        })
      } else if (width < 1600) {
        setDeviceType('桌面端')
        setTestResults({
          sidebar: '完整显示',
          grid: '四列布局',
          text: '大字体',
          spacing: '宽松间距'
        })
      } else {
        setDeviceType('大屏桌面端')
        setTestResults({
          sidebar: '完整显示',
          grid: '四列布局',
          text: '超大字体',
          spacing: '最大间距'
        })
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const testCases = [
    {
      title: '侧边栏适配',
      description: '测试侧边栏在不同屏幕尺寸下的显示方式',
      result: testResults.sidebar,
      icon: 'fas fa-bars',
      color: 'var(--primary)'
    },
    {
      title: '网格布局',
      description: '测试网格系统的响应式列数变化',
      result: testResults.grid,
      icon: 'fas fa-th',
      color: 'var(--success)'
    },
    {
      title: '文字缩放',
      description: '测试文字大小的响应式调整',
      result: testResults.text,
      icon: 'fas fa-font',
      color: 'var(--warning)'
    },
    {
      title: '间距调整',
      description: '测试间距在不同设备上的适配',
      result: testResults.spacing,
      icon: 'fas fa-arrows-alt',
      color: 'var(--info)'
    }
  ]

  return (
    <div className="animate-fade-in">
      {/* 页面标题 */}
      <div className="welcome-banner">
        <div className="welcome-text">
          <h2 className="text-responsive-xl">📱 响应式设计测试中心</h2>
          <p className="text-responsive-base">
            实时测试和验证MemeMaster AI的响应式设计适配性，确保在所有设备上都有最佳体验
          </p>
          <div className="flex-responsive" style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            marginTop: '12px',
            fontSize: '14px',
            opacity: 0.9,
            flexWrap: 'wrap'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
              <div style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor: '#10b981',
                animation: 'pulse 2s infinite'
              }}></div>
              <span>实时检测</span>
            </div>
            <div>当前设备: <span style={{ color: '#6366f1' }}>{deviceType}</span></div>
            <div>分辨率: <span style={{ color: '#f59e0b' }}>{screenSize.width}×{screenSize.height}</span></div>
          </div>
        </div>
        <div className="welcome-actions">
          <button className="btn btn-primary btn-lg" onClick={() => window.location.reload()}>
            <i className="fas fa-sync-alt"></i>
            <span className="hidden-mobile">刷新测试</span>
            <span className="block-mobile">刷新</span>
          </button>
        </div>
      </div>

      {/* 实时屏幕信息 */}
      <div className="stats-container">
        <div className="stat-card animate-slide-in-left delay-100">
          <div className="stat-header">
            <div className="stat-icon icon-primary">
              <i className="fas fa-desktop"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--primary)',
              background: 'rgba(67, 56, 202, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(67, 56, 202, 0.2)'
            }}>
              宽度
            </div>
          </div>
          <div className="stat-value">{screenSize.width}px</div>
          <div className="stat-title">屏幕宽度</div>
          <div className="stat-trend trend-neutral">
            <i className="fas fa-arrows-alt-h"></i>
            <span>水平分辨率</span>
          </div>
        </div>

        <div className="stat-card animate-slide-in-left delay-200">
          <div className="stat-header">
            <div className="stat-icon icon-success">
              <i className="fas fa-mobile-alt"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--success)',
              background: 'rgba(16, 185, 129, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(16, 185, 129, 0.2)'
            }}>
              高度
            </div>
          </div>
          <div className="stat-value">{screenSize.height}px</div>
          <div className="stat-title">屏幕高度</div>
          <div className="stat-trend trend-neutral">
            <i className="fas fa-arrows-alt-v"></i>
            <span>垂直分辨率</span>
          </div>
        </div>

        <div className="stat-card animate-slide-in-left delay-300">
          <div className="stat-header">
            <div className="stat-icon icon-warning">
              <i className="fas fa-tv"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--warning)',
              background: 'rgba(245, 158, 11, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(245, 158, 11, 0.2)'
            }}>
              检测中
            </div>
          </div>
          <div className="stat-value text-responsive-base" style={{ fontSize: '18px' }}>{deviceType}</div>
          <div className="stat-title">设备类型</div>
          <div className="stat-trend trend-up">
            <i className="fas fa-check-circle"></i>
            <span>自动识别</span>
          </div>
        </div>

        <div className="stat-card animate-slide-in-left delay-400">
          <div className="stat-header">
            <div className="stat-icon icon-info">
              <i className="fas fa-expand-arrows-alt"></i>
            </div>
            <div style={{
              fontSize: '12px',
              color: 'var(--info)',
              background: 'rgba(2, 132, 199, 0.1)',
              padding: '4px 8px',
              borderRadius: '12px',
              border: '1px solid rgba(2, 132, 199, 0.2)'
            }}>
              比例
            </div>
          </div>
          <div className="stat-value">{(screenSize.width / screenSize.height).toFixed(2)}</div>
          <div className="stat-title">宽高比</div>
          <div className="stat-trend trend-neutral">
            <i className="fas fa-calculator"></i>
            <span>屏幕比例</span>
          </div>
        </div>
      </div>

      {/* 响应式测试结果 */}
      <div className="features-grid">
        {testCases.map((test, index) => (
          <div
            key={test.title}
            className={`feature-card hover-lift interactive-element animate-fade-in delay-${500 + index * 100}`}
            style={{ animationDelay: `${0.5 + index * 0.1}s` }}
          >
            <div className="feature-icon" style={{ color: test.color }}>
              <i className={test.icon}></i>
            </div>
            <div className="feature-title">{test.title}</div>
            <div className="feature-desc">{test.description}</div>
            <div style={{
              marginTop: 'var(--space-4)',
              padding: 'var(--space-3)',
              background: 'rgba(67, 56, 202, 0.1)',
              borderRadius: 'var(--radius-md)',
              border: '1px solid rgba(67, 56, 202, 0.2)',
              textAlign: 'center',
              color: test.color,
              fontWeight: 'var(--font-semibold)'
            }}>
              {test.result}
            </div>

            {/* 状态指示器 */}
            <div style={{
              position: 'absolute',
              top: '16px',
              right: '16px',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: test.color,
              animation: 'pulse 2s infinite'
            }}></div>
          </div>
        ))}
      </div>

      {/* 响应式网格测试 */}
      <ResponsiveGridTest />

      {/* 响应式文本测试 */}
      <ResponsiveTextTest />

      {/* 浮动测试工具 */}
      <ResponsiveTest />
    </div>
  )
}

export default ResponsiveTestPage
