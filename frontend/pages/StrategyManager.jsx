import React, { useState, useEffect } from 'react'
import {
  Zap, Plus, Settings, Database, Monitor, Shield, BarChart3,
  Search, CheckCircle, Download, Eye, Activity, Target,
  FileText, DollarSign, Sparkles, Brain, XCircle, Sliders
} from 'lucide-react'
import OptimizationPanel from '../components/OptimizationPanel.jsx'

const StrategyManager = () => {
  // State management
  const [activeTab, setActiveTab] = useState('repository')
  const [searchQuery, setSearchQuery] = useState('')
  const [filterOptions, setFilterOptions] = useState({
    status: 'all',
    category: 'all',
    score: 'all'
  })
  const [selectedStrategies, setSelectedStrategies] = useState([])
  const [isDeploying, setIsDeploying] = useState(false)
  const [deploymentProgress, setDeploymentProgress] = useState(0)
  const [showStrategyDetails, setShowStrategyDetails] = useState(false)
  const [selectedStrategy, setSelectedStrategy] = useState(null)
  const [showStrategyModal, setShowStrategyModal] = useState(false)
  const [generatedStrategy, setGeneratedStrategy] = useState(null)
  const [showDuplicateCheck, setShowDuplicateCheck] = useState(false)
  const [duplicateCheckResult, setDuplicateCheckResult] = useState(null)
  const [selectedStrategyForCheck, setSelectedStrategyForCheck] = useState(null)
  const [isCheckingDuplicates, setIsCheckingDuplicates] = useState(false)

  // Mock data for strategies
  const [strategies] = useState([
    {
      id: 1,
      name: 'TRUMPWIN',
      symbol: 'TWIN',
      status: 'pending',
      category: 'politics',
      score: 92,
      estimated_roi: 850,
      viral_potential: 0.89,
      sentiment: 0.94,
      risk_level: 'medium',
      compliance_status: 'verified',
      hotspot_source: 'Trump 2024 Election Campaign',
      created: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      tokenomics: {
        total_supply: 1000000000,
        burn_rate: 0.02,
        allocation: {
          airdrop: 15,
          liquidity: 40,
          marketing: 25,
          team: 20
        }
      },
      deployment_config: {
        platforms: ['pump.fun', 'raydium'],
        chains: ['solana'],
        gas_optimized: true
      }
    }
  ])

  // Filter strategies based on search and filters
  const filteredStrategies = strategies.filter(strategy => {
    const matchesSearch = strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         strategy.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         strategy.hotspot_source.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = filterOptions.status === 'all' || strategy.status === filterOptions.status
    const matchesCategory = filterOptions.category === 'all' || strategy.category === filterOptions.category
    const matchesScore = filterOptions.score === 'all' || 
                        (filterOptions.score === 'high' && strategy.score >= 85) ||
                        (filterOptions.score === 'medium' && strategy.score >= 70 && strategy.score < 85) ||
                        (filterOptions.score === 'low' && strategy.score < 70)
    
    return matchesSearch && matchesStatus && matchesCategory && matchesScore
  })

  // Event handlers
  const handleStrategySelect = (strategyId) => {
    setSelectedStrategies(prev => 
      prev.includes(strategyId) 
        ? prev.filter(id => id !== strategyId)
        : [...prev, strategyId]
    )
  }

  const handleSelectAll = () => {
    if (selectedStrategies.length === filteredStrategies.length) {
      setSelectedStrategies([])
    } else {
      setSelectedStrategies(filteredStrategies.map(s => s.id))
    }
  }

  const handleViewStrategy = (strategy) => {
    setSelectedStrategy(strategy)
    setShowStrategyDetails(true)
  }

  const deployStrategy = async (strategy) => {
    setIsDeploying(true)
    setDeploymentProgress(0)
    
    // Simulate deployment progress
    const interval = setInterval(() => {
      setDeploymentProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsDeploying(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const handleBatchDeploy = () => {
    if (selectedStrategies.length > 0) {
      deployStrategy(strategies.find(s => selectedStrategies.includes(s.id)))
    }
  }

  const handleExportStrategies = () => {
    const selectedData = strategies.filter(s => selectedStrategies.includes(s.id))
    console.log('Exporting strategies:', selectedData)
  }

  const checkTokenDuplicates = async (strategy) => {
    setIsCheckingDuplicates(true)
    setSelectedStrategyForCheck(strategy)
    
    // Simulate API call
    setTimeout(() => {
      setDuplicateCheckResult({
        token_name: strategy.name,
        risk_level: 'LOW',
        risk_score: 25,
        checked_at: new Date().toISOString(),
        platform_results: [
          {
            platform: 'Pump.fun',
            exists: false
          },
          {
            platform: 'Raydium',
            exists: false
          }
        ]
      })
      setShowDuplicateCheck(true)
      setIsCheckingDuplicates(false)
    }, 2000)
  }

  const getRiskLevelColor = (level) => {
    switch(level) {
      case 'HIGH': return 'border-red-500/50 bg-red-500/10'
      case 'MEDIUM': return 'border-yellow-500/50 bg-yellow-500/10'
      default: return 'border-green-500/50 bg-green-500/10'
    }
  }

  const getRiskBadgeColor = (level) => {
    switch(level) {
      case 'HIGH': return 'bg-red-500/20 text-red-400'
      case 'MEDIUM': return 'bg-yellow-500/20 text-yellow-400'
      default: return 'bg-green-500/20 text-green-400'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 flex flex-col">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-500/10 text-blue-400 rounded-xl">
              <Zap className="text-2xl" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">策略引擎中心</h1>
              <p className="text-gray-400 text-lg">智能交易策略 • 安全发币操作 • 多链部署 • 自动化审计</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-green-500/10 text-green-400 px-4 py-2 rounded-xl">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>系统运行正常</span>
            </div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-2">
              <span className="text-gray-400">成功率: </span>
              <span className="text-green-400 font-bold">99.2%</span>
            </div>
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl px-4 py-2">
              <span className="text-gray-400">Gas节省: </span>
              <span className="text-blue-400 font-bold">42.8%</span>
            </div>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 mb-8 bg-gray-800/40 backdrop-blur-sm border border-gray-700/50 p-1 rounded-xl shadow-lg">
          {[
            { id: 'repository', name: '策略仓库', icon: Database, description: '未部署策略管理' },
            { id: 'monitoring', name: '策略监控', icon: Monitor, description: '已部署策略监控' },
            { id: 'deployment', name: '一键部署', icon: Zap, description: '快速部署工作流' },
            { id: 'security', name: '安全审计', icon: Shield, description: '智能合约安全检查' },
            { id: 'parameters', name: '参数优化', icon: Sliders, description: '智能参数优化控制' },
            { id: 'optimization', name: 'Gas优化', icon: Zap, description: 'Gas费用优化' },
            { id: 'analytics', name: '数据分析', icon: BarChart3, description: '策略表现分析' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab.id
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-[1.02]'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700/50 hover:scale-[1.01]'
              }`}
            >
              <tab.icon className="w-5 h-5" />
              <span className="hidden lg:inline">{tab.name}</span>
            </button>
          ))}
        </div>

        {/* 策略仓库标签页内容 */}
        {activeTab === 'repository' && (
          <div className="min-h-[800px]">
            <div className="space-y-8">
              {/* 策略统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">待部署策略</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        {strategies.filter(s => s.status === 'pending').length}
                      </p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <Database className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">平均ROI</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        +{Math.round(strategies.reduce((sum, s) => sum + s.estimated_roi, 0) / strategies.length)}%
                      </p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Target className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">病毒潜力</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        {Math.round(strategies.reduce((sum, s) => sum + s.viral_potential, 0) / strategies.length * 100)}%
                      </p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <Sparkles className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-400 text-sm font-medium">平均热度</p>
                      <p className="text-3xl font-bold text-white mt-1">
                        {Math.round(strategies.reduce((sum, s) => sum + s.score, 0) / strategies.length)}
                      </p>
                    </div>
                    <div className="p-3 bg-orange-500/20 rounded-xl">
                      <Activity className="w-8 h-8 text-orange-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 筛选和搜索栏 */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                  <div className="flex items-center space-x-4">
                    <h2 className="text-xl font-bold text-white">策略筛选</h2>
                    <div className="flex items-center space-x-2">
                      <Search className="w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="搜索策略名称、符号或热点来源..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm w-64"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                      <select
                        value={filterOptions.status}
                        onChange={(e) => setFilterOptions(prev => ({ ...prev, status: e.target.value }))}
                        className="bg-transparent text-white text-sm border-none outline-none"
                      >
                        <option value="all">所有状态</option>
                        <option value="draft">草稿</option>
                        <option value="pending">待部署</option>
                        <option value="deploying">部署中</option>
                      </select>
                    </div>

                    <div className="flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2">
                      <select
                        value={filterOptions.category}
                        onChange={(e) => setFilterOptions(prev => ({ ...prev, category: e.target.value }))}
                        className="bg-transparent text-white text-sm border-none outline-none"
                      >
                        <option value="all">所有类别</option>
                        <option value="politics">政治</option>
                        <option value="finance">金融</option>
                        <option value="technology">科技</option>
                        <option value="subculture">亚文化</option>
                      </select>
                    </div>

                    <button className="flex items-center space-x-2 bg-blue-500/20 text-blue-400 px-4 py-2 rounded-lg hover:bg-blue-500/30 transition-colors">
                      <Plus className="w-4 h-4" />
                      <span>新建策略</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 策略列表 - 卡片网格布局 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredStrategies.map((strategy) => (
                  <div key={strategy.id} className="group bg-gradient-to-br from-gray-800/60 to-gray-900/60 border border-gray-700/50 rounded-2xl p-6 hover:border-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300">
                    {/* 策略头部信息 */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`relative p-2 rounded-xl ${
                          strategy.score >= 85 ? 'bg-red-500/20 text-red-400' :
                          strategy.score >= 70 ? 'bg-orange-500/20 text-orange-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          <Zap className="w-5 h-5" />
                          {strategy.score >= 85 && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                          )}
                        </div>

                        <div>
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-lg text-xs font-bold ${
                              strategy.score >= 85 ? 'text-red-400 bg-red-500/10 border border-red-500/20' :
                              strategy.score >= 70 ? 'text-orange-400 bg-orange-500/10 border border-orange-500/20' :
                              'text-green-400 bg-green-500/10 border border-green-500/20'
                            }`}>
                              {strategy.score}
                            </span>
                            <span className="text-2xl">
                              {strategy.status === 'pending' ? '⏳' : strategy.status === 'deploying' ? '🚀' : '📝'}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(strategy.created).toLocaleString()}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-lg text-xs font-medium ${
                          strategy.category === 'politics' ? 'bg-red-500/10 text-red-400' :
                          strategy.category === 'technology' ? 'bg-blue-500/10 text-blue-400' :
                          strategy.category === 'finance' ? 'bg-green-500/10 text-green-400' :
                          'bg-purple-500/10 text-purple-400'
                        }`}>
                          {strategy.category}
                        </span>
                      </div>
                    </div>

                    {/* 策略内容 */}
                    <div className="mb-4">
                      <h3 className="text-lg font-bold text-white mb-2 line-clamp-2 group-hover:text-blue-400 transition-colors">
                        {strategy.name} ({strategy.symbol})
                      </h3>
                      <p className="text-gray-400 text-sm line-clamp-3 leading-relaxed">
                        {strategy.hotspot_source}
                      </p>
                    </div>

                    {/* 统计信息 */}
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-green-400">
                          <Target className="w-4 h-4" />
                          <span className="text-sm font-medium">+{strategy.estimated_roi}%</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">预期ROI</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-purple-400">
                          <Sparkles className="w-4 h-4" />
                          <span className="text-sm font-medium">{(strategy.viral_potential * 100).toFixed(0)}%</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">病毒潜力</p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center space-x-1 text-blue-400">
                          <Brain className="w-4 h-4" />
                          <span className="text-sm font-medium">{(strategy.sentiment * 100).toFixed(0)}%</span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">情绪指数</p>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          strategy.status === 'draft' ? 'bg-gray-700/30 text-gray-400' :
                          strategy.status === 'pending' ? 'bg-yellow-700/30 text-yellow-400' :
                          strategy.status === 'deploying' ? 'bg-blue-700/30 text-blue-400' :
                          'bg-green-700/30 text-green-400'
                        }`}>
                          {strategy.status}
                        </span>
                        <span className="px-2 py-1 bg-gray-700/30 text-gray-400 text-xs rounded">
                          {strategy.risk_level}
                        </span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleViewStrategy(strategy)}
                          className="flex items-center space-x-1 px-3 py-1.5 bg-gray-500/20 text-gray-400 rounded-lg hover:bg-gray-500/30 transition-colors text-xs font-medium"
                        >
                          <Eye className="w-3 h-3" />
                          <span>详情</span>
                        </button>

                        <button
                          onClick={() => deployStrategy(strategy)}
                          className="flex items-center space-x-1 px-3 py-1.5 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors text-xs font-medium"
                        >
                          <Zap className="w-3 h-3" />
                          <span>部署</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 空状态 */}
              {filteredStrategies.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Database className="w-8 h-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-medium text-white mb-2">暂无策略数据</h3>
                  <p className="text-gray-400 text-sm">当前筛选条件下没有找到相关策略，请尝试调整筛选条件</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 策略监控标签页内容 */}
        {activeTab === 'monitoring' && (
          <div className="min-h-[800px]">
            <div className="space-y-8">
              {/* 监控统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">活跃策略</p>
                      <p className="text-3xl font-bold text-white mt-1">12</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Activity className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">总收益</p>
                      <p className="text-3xl font-bold text-white mt-1">+$24,580</p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <Target className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">成功率</p>
                      <p className="text-3xl font-bold text-white mt-1">89.5%</p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <Shield className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-yellow-400 text-sm font-medium">风险评级</p>
                      <p className="text-3xl font-bold text-white mt-1">中等</p>
                    </div>
                    <div className="p-3 bg-yellow-500/20 rounded-xl">
                      <BarChart3 className="w-8 h-8 text-yellow-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 监控面板 */}
              <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-6">
                <h3 className="text-xl font-bold text-white mb-4">实时监控面板</h3>
                <div className="text-center py-12">
                  <Monitor className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-400">监控面板正在开发中...</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 参数优化标签页内容 */}
        {activeTab === 'parameters' && (
          <div className="min-h-[800px] space-y-6">
            <OptimizationPanel />
          </div>
        )}

        {/* 其他标签页内容 */}
        {activeTab !== 'repository' && activeTab !== 'monitoring' && activeTab !== 'parameters' && (
          <div className="min-h-[800px]">
            <div className="bg-gray-800/50 border border-gray-700 rounded-2xl p-12 text-center">
              <div className="w-16 h-16 text-gray-400 mx-auto mb-4 flex items-center justify-center">
                {activeTab === 'deployment' && <Zap className="w-16 h-16" />}
                {activeTab === 'security' && <Shield className="w-16 h-16" />}
                {activeTab === 'optimization' && <Zap className="w-16 h-16" />}
                {activeTab === 'analytics' && <BarChart3 className="w-16 h-16" />}
              </div>
              <h3 className="text-lg font-medium text-white mb-2">
                {activeTab === 'deployment' && '一键部署'}
                {activeTab === 'security' && '安全审计'}
                {activeTab === 'optimization' && 'Gas优化'}
                {activeTab === 'analytics' && '数据分析'}
              </h3>
              <p className="text-gray-400">此功能正在开发中，敬请期待...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default StrategyManager
