import React, { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import HotspotPrediction from '../components/HotspotPrediction'
import {
  PieChart, Pie, Cell, Tooltip, ResponsiveContainer,
  XAxis, YAxis, CartesianGrid,
  LineChart, Line, Area, AreaChart
} from 'recharts'
import {
  Settings, Plus, X, TrendingUp, Eye, Globe, Shield,
  Brain, Video, AlertTriangle, Clock, Target, Zap,
  Filter, Languages, BarChart3, Activity, Radar
} from 'lucide-react'
// 创建模拟的action函数，避免导入错误
const fetchHotspots = () => ({ type: 'FETCH_HOTSPOTS' })
const updateSources = (payload) => ({ type: 'UPDATE_SOURCES', payload })
const updateSettings = (payload) => ({ type: 'UPDATE_SETTINGS', payload })
const addKeyword = (payload) => ({ type: 'ADD_KEYWORD', payload })
const removeKeyword = (payload) => ({ type: 'REMOVE_KEYWORD', payload })
const fetchAdvancedAnalytics = () => ({ type: 'FETCH_ADVANCED_ANALYTICS' })
const updateMLSettings = (payload) => ({ type: 'UPDATE_ML_SETTINGS', payload })
const toggleDarkwebMonitoring = () => ({ type: 'TOGGLE_DARKWEB_MONITORING' })
const updateLanguageSettings = (payload) => ({ type: 'UPDATE_LANGUAGE_SETTINGS', payload })
const startNewFlow = (payload) => ({ type: 'START_NEW_FLOW', payload })

const HotspotMonitor = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()

  // 添加气泡浮动动画样式
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      @keyframes float-0 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-10px) rotate(5deg); } }
      @keyframes float-1 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-15px) rotate(-3deg); } }
      @keyframes float-2 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-8px) rotate(7deg); } }
      @keyframes float-3 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-12px) rotate(-5deg); } }
      @keyframes float-4 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-18px) rotate(4deg); } }
      @keyframes float-5 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-6px) rotate(-8deg); } }
      @keyframes float-6 { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-14px) rotate(6deg); } }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])
  // 安全地获取Redux状态，提供默认值
  const {
    hotspots = [],
    distribution = {
      politics: 25,
      subculture: 20,
      technology: 18,
      metaverse: 12,
      gamefi: 8,
      nft: 6,
      sports: 11
    },
    sources = {
      twitter: { enabled: true, weight: 0.3 },
      reddit: { enabled: true, weight: 0.25 },
      tiktok: { enabled: true, weight: 0.2 },
      youtube: { enabled: true, weight: 0.15 },
      discord: { enabled: true, weight: 0.1 }
    },
    settings = {
      updateInterval: 30000,
      confidenceThreshold: 0.7,
      enableDarkweb: false
    },
    isLoading = false,
    mlMetrics = {},
    sentimentIndex = 0.76,
    languageDistribution = {},
    darkwebSignals = [],
    videoAnalysis = [],
    regulatoryAlerts = [],
    predictionAccuracy = 78.5
  } = useSelector(state => state.hotspot || {})

  const [newKeyword, setNewKeyword] = useState('')
  const [showSettings, setShowSettings] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [activeCategory, setActiveCategory] = useState('all') // 新增：当前选中的分类
  const updateInterval = useRef(null)

  // 高级筛选状态
  const [filters, setFilters] = useState({
    categories: {
      politics: true,
      subculture: true,
      technology: true,
      metaverse: true,
      gamefi: true,
      nft: true,
      sports: true
    },
    sources: {
      twitter: true,
      reddit: true,
      tiktok: true,
      youtube: true,
      discord: true,
      darkweb: false
    },
    minConfidence: 0.7,
    riskLevels: {
      low: true,
      medium: true,
      high: false
    },
    languages: {
      en: true,
      zh: true,
      ko: false,
      ja: false,
      es: false,
      ru: false
    },
    regions: {
      na: true,
      eu: true,
      ap: true,
      cn: true
    }
  })

  // 添加本地状态来存储API数据
  const [localHotspots, setLocalHotspots] = useState([])
  const [localAnalytics, setLocalAnalytics] = useState(null)
  const [localLoading, setLocalLoading] = useState(true)

  // 模拟热点数据
  const mockHotspots = [
    {
      id: 1,
      keyword: "特朗普2024竞选",
      title: "特朗普宣布2024年总统竞选计划",
      description: "前总统特朗普正式宣布参与2024年总统大选，引发全网热议",
      category: "politics",
      score: 0.95,
      trend: "rising",
      sentiment: 0.72,
      volume: 125000,
      sources: ["twitter", "reddit", "youtube"],
      timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
      region: "na",
      language: "en",
      riskLevel: "high",
      confidence: 0.92,
      tags: ["政治", "选举", "美国"],
      influencers: ["@realDonaldTrump", "@FoxNews", "@CNN"],
      engagement: {
        likes: 45000,
        shares: 12000,
        comments: 8500
      }
    },
    {
      id: 2,
      keyword: "ChatGPT-5发布",
      title: "OpenAI即将发布ChatGPT-5",
      description: "据内部消息，OpenAI计划在下月发布ChatGPT-5，性能将大幅提升",
      category: "technology",
      score: 0.89,
      trend: "rising",
      sentiment: 0.85,
      volume: 89000,
      sources: ["twitter", "reddit", "discord"],
      timestamp: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.78,
      tags: ["AI", "科技", "OpenAI"],
      influencers: ["@sama", "@OpenAI", "@elonmusk"],
      engagement: {
        likes: 32000,
        shares: 15000,
        comments: 6800
      }
    },
    {
      id: 3,
      keyword: "元宇宙虚拟演唱会",
      title: "知名歌手在元宇宙举办虚拟演唱会",
      description: "流行歌手Taylor Swift在Horizon Worlds举办虚拟演唱会，吸引百万观众",
      category: "metaverse",
      score: 0.82,
      trend: "stable",
      sentiment: 0.91,
      volume: 67000,
      sources: ["tiktok", "youtube", "twitter"],
      timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "low",
      confidence: 0.85,
      tags: ["元宇宙", "音乐", "VR"],
      influencers: ["@taylorswift13", "@Meta", "@oculus"],
      engagement: {
        likes: 78000,
        shares: 25000,
        comments: 12000
      }
    },
    {
      id: 4,
      keyword: "新型GameFi项目",
      title: "革命性GameFi项目获得千万投资",
      description: "新兴GameFi项目'CryptoQuest'获得1000万美元A轮融资，游戏即将上线",
      category: "gamefi",
      score: 0.76,
      trend: "rising",
      sentiment: 0.68,
      volume: 34000,
      sources: ["discord", "reddit", "twitter"],
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.71,
      tags: ["GameFi", "投资", "区块链游戏"],
      influencers: ["@CryptoQuest", "@a16z", "@binance"],
      engagement: {
        likes: 15000,
        shares: 8000,
        comments: 3500
      }
    },
    {
      id: 5,
      keyword: "网络迷因文化",
      title: "新兴网络迷因引发病毒式传播",
      description: "一个关于AI和人类关系的迷因在各大平台疯传，引发广泛讨论",
      category: "subculture",
      score: 0.88,
      trend: "rising",
      sentiment: 0.79,
      volume: 156000,
      sources: ["tiktok", "twitter", "reddit"],
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "low",
      confidence: 0.89,
      tags: ["迷因", "文化", "病毒传播"],
      influencers: ["@pewdiepie", "@mrbeast", "@elonmusk"],
      engagement: {
        likes: 234000,
        shares: 89000,
        comments: 45000
      }
    },
    {
      id: 6,
      keyword: "NFT艺术拍卖",
      title: "数字艺术NFT创下拍卖新纪录",
      description: "知名数字艺术家的NFT作品在苏富比拍卖行以500万美元成交",
      category: "nft",
      score: 0.73,
      trend: "stable",
      sentiment: 0.65,
      volume: 28000,
      sources: ["twitter", "youtube"],
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      region: "global",
      language: "en",
      riskLevel: "medium",
      confidence: 0.76,
      tags: ["NFT", "艺术", "拍卖"],
      influencers: ["@sothebys", "@beeple", "@opensea"],
      engagement: {
        likes: 18000,
        shares: 6000,
        comments: 2800
      }
    },
    {
      id: 7,
      keyword: "世界杯决赛",
      title: "世界杯决赛引发全球关注",
      description: "2024年世界杯决赛即将开始，全球球迷热情高涨",
      category: "sports",
      score: 0.94,
      trend: "rising",
      sentiment: 0.93,
      volume: 289000,
      sources: ["twitter", "tiktok", "youtube"],
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      region: "global",
      language: "multiple",
      riskLevel: "low",
      confidence: 0.96,
      tags: ["足球", "世界杯", "体育"],
      influencers: ["@FIFAcom", "@espn", "@skysports"],
      engagement: {
        likes: 456000,
        shares: 123000,
        comments: 78000
      }
    },
    {
      id: 8,
      keyword: "加密货币监管",
      title: "美国SEC发布新的加密货币监管框架",
      description: "美国证券交易委员会发布了新的加密货币监管指导方针",
      category: "politics",
      score: 0.81,
      trend: "rising",
      sentiment: 0.58,
      volume: 76000,
      sources: ["twitter", "reddit", "youtube"],
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      region: "na",
      language: "en",
      riskLevel: "high",
      confidence: 0.87,
      tags: ["监管", "加密货币", "SEC"],
      influencers: ["@SECGov", "@coinbase", "@binance"],
      engagement: {
        likes: 23000,
        shares: 11000,
        comments: 8900
      }
    }
  ]

  // 模拟分析数据
  const mockAnalytics = {
    predictionAccuracy: 78.5,
    totalHotspots: 347,
    activeCategories: 7,
    globalCoverage: 95,
    sentimentIndex: 0.76,
    riskLevel: "medium",
    trendingTopics: [
      { topic: "AI技术突破", growth: "+45%" },
      { topic: "政治选举", growth: "+32%" },
      { topic: "元宇宙发展", growth: "+28%" },
      { topic: "GameFi创新", growth: "+19%" }
    ],
    languageDistribution: {
      en: 45,
      zh: 25,
      es: 12,
      ja: 8,
      ko: 6,
      ru: 4
    },
    regionDistribution: {
      na: 35,
      eu: 28,
      ap: 22,
      cn: 15
    },
    sourceMetrics: {
      twitter: { volume: 156000, sentiment: 0.72, reliability: 0.85 },
      reddit: { volume: 89000, sentiment: 0.68, reliability: 0.78 },
      tiktok: { volume: 234000, sentiment: 0.81, reliability: 0.65 },
      youtube: { volume: 67000, sentiment: 0.75, reliability: 0.82 },
      discord: { volume: 34000, sentiment: 0.79, reliability: 0.71 }
    }
  }

  // 模拟暗网信号数据
  const mockDarkwebSignals = [
    {
      id: 1,
      signal: "加密货币洗钱网络",
      description: "检测到大规模加密货币洗钱活动，涉及多个交易所",
      riskLevel: "high",
      confidence: 0.89,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      source: "tor_network",
      category: "financial_crime",
      impact: "high",
      tags: ["洗钱", "加密货币", "犯罪网络"]
    },
    {
      id: 2,
      signal: "政治敏感信息泄露",
      description: "发现涉及政府官员的敏感信息在暗网流传",
      riskLevel: "critical",
      confidence: 0.76,
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      source: "dark_forums",
      category: "political_risk",
      impact: "critical",
      tags: ["政治", "信息泄露", "敏感数据"]
    },
    {
      id: 3,
      signal: "新型网络攻击工具",
      description: "暗网出现针对DeFi协议的新型攻击工具",
      riskLevel: "high",
      confidence: 0.82,
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      source: "hacker_forums",
      category: "cyber_security",
      impact: "high",
      tags: ["网络攻击", "DeFi", "安全威胁"]
    }
  ]

  // 模拟实时情绪数据
  const mockSentimentData = [
    { time: '00:00', politics: 0.65, subculture: 0.82, technology: 0.78, metaverse: 0.71, gamefi: 0.69, nft: 0.58, sports: 0.89, overall: 0.73 },
    { time: '04:00', politics: 0.68, subculture: 0.85, technology: 0.81, metaverse: 0.74, gamefi: 0.72, nft: 0.61, sports: 0.91, overall: 0.76 },
    { time: '08:00', politics: 0.72, subculture: 0.88, technology: 0.84, metaverse: 0.77, gamefi: 0.75, nft: 0.64, sports: 0.93, overall: 0.79 },
    { time: '12:00', politics: 0.75, subculture: 0.90, technology: 0.87, metaverse: 0.80, gamefi: 0.78, nft: 0.67, sports: 0.95, overall: 0.82 },
    { time: '16:00', politics: 0.78, subculture: 0.92, technology: 0.89, metaverse: 0.83, gamefi: 0.81, nft: 0.70, sports: 0.97, overall: 0.84 },
    { time: '20:00', politics: 0.76, subculture: 0.89, technology: 0.86, metaverse: 0.81, gamefi: 0.79, nft: 0.68, sports: 0.94, overall: 0.82 },
    { time: '24:00', politics: 0.73, subculture: 0.87, technology: 0.83, metaverse: 0.78, gamefi: 0.76, nft: 0.65, sports: 0.92, overall: 0.79 }
  ]

  // 模拟预测数据
  const mockPredictions = [
    {
      id: 1,
      topic: "AI监管政策",
      category: "politics",
      predictedTime: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      probability: 0.87,
      confidence: "high",
      description: "预测美国将在48小时内发布新的AI监管政策",
      impact: "high",
      sources: ["政府文件", "内部消息", "专家分析"],
      riskFactors: ["政策不确定性", "市场波动", "技术发展"]
    },
    {
      id: 2,
      topic: "元宇宙平台整合",
      category: "metaverse",
      predictedTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      probability: 0.73,
      confidence: "medium",
      description: "预测主要元宇宙平台将宣布互操作性协议",
      impact: "medium",
      sources: ["技术论坛", "开发者社区", "行业报告"],
      riskFactors: ["技术挑战", "竞争压力", "用户接受度"]
    },
    {
      id: 3,
      topic: "GameFi代币暴涨",
      category: "gamefi",
      predictedTime: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(),
      probability: 0.69,
      confidence: "medium",
      description: "预测某GameFi代币将在24小时内暴涨200%",
      impact: "high",
      sources: ["链上数据", "社交媒体", "交易量分析"],
      riskFactors: ["市场操纵", "流动性不足", "监管风险"]
    }
  ]

  // 模拟视频分析数据
  const mockVideoAnalysis = [
    {
      id: 1,
      platform: "tiktok",
      title: "AI换脸技术演示",
      views: 2300000,
      engagement: 0.89,
      sentiment: 0.72,
      riskLevel: "medium",
      category: "technology",
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      tags: ["AI", "深度伪造", "技术演示"],
      viralPotential: 0.85
    },
    {
      id: 2,
      platform: "youtube",
      title: "加密货币投资策略",
      views: 890000,
      engagement: 0.76,
      sentiment: 0.68,
      riskLevel: "high",
      category: "politics",
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
      tags: ["投资", "加密货币", "金融建议"],
      viralPotential: 0.71
    }
  ]

  // 直接调用API获取数据，失败时使用模拟数据
  const fetchHotspotsData = async () => {
    try {
      setLocalLoading(true)

      // 检查API服务是否可用
      const response = await fetch('http://localhost:8001/hotspot/current', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 设置超时时间
        signal: AbortSignal.timeout(5000)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.status === 'success' && data.hotspots) {
          setLocalHotspots(data.hotspots)
          console.log('✅ 成功获取API热点数据')
          return
        }
      }

      // API响应不正确时使用模拟数据
      console.log('⚠️ API响应异常，使用模拟数据')
      setLocalHotspots(mockHotspots)

    } catch (error) {
      console.log('⚠️ API不可用，使用模拟数据:', error.message)
      // API不可用时使用模拟数据
      setLocalHotspots(mockHotspots)
    } finally {
      setLocalLoading(false)
    }
  }

  const fetchAnalyticsData = async () => {
    try {
      // 检查API服务是否可用
      const response = await fetch('http://localhost:8001/hotspot/analytics', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // 设置超时时间
        signal: AbortSignal.timeout(5000)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.status === 'success') {
          setLocalAnalytics(data)
          console.log('✅ 成功获取API分析数据')
          return
        }
      }

      // API响应不正确时使用模拟数据
      console.log('⚠️ API分析数据异常，使用模拟数据')
      setLocalAnalytics(mockAnalytics)

    } catch (error) {
      console.log('⚠️ API分析服务不可用，使用模拟数据:', error.message)
      // API不可用时使用模拟数据
      setLocalAnalytics(mockAnalytics)
    }
  }

  useEffect(() => {
    // 初始数据加载
    fetchHotspotsData()
    fetchAnalyticsData()

    // 尝试Redux方式（安全调用）
    try {
      dispatch(fetchHotspots())
      dispatch(fetchAdvancedAnalytics())
    } catch (error) {
      console.log('Redux actions not available:', error.message)
    }

    // 设置实时更新间隔
    const updateFrequency = 30000 // 30秒更新一次

    updateInterval.current = setInterval(() => {
      fetchHotspotsData()
      fetchAnalyticsData()

      // 安全的Redux调用
      try {
        dispatch(fetchHotspots())
        dispatch(fetchAdvancedAnalytics())
      } catch (error) {
        console.log('Redux update failed:', error.message)
      }
    }, updateFrequency)

    return () => {
      if (updateInterval.current) {
        clearInterval(updateInterval.current)
      }
    }
  }, [dispatch])

  // 增强版分布数据 - 包含更多领域和图标，使用安全的默认值
  const distributionData = [
    { name: '政治', value: distribution?.politics || 25, color: '#3B82F6', trend: '+12%', icon: '🏛️', description: '政治热点和政策讨论' },
    { name: '亚文化', value: distribution?.subculture || 20, color: '#10B981', trend: '+8%', icon: '🎭', description: '网络文化和社区话题' },
    { name: '科技/AI', value: distribution?.technology || 18, color: '#F59E0B', trend: '+15%', icon: '🤖', description: '科技创新和AI发展' },
    { name: '元宇宙', value: distribution?.metaverse || 12, color: '#8B5CF6', trend: '+25%', icon: '🌌', description: '虚拟世界和数字体验' },
    { name: 'GameFi', value: distribution?.gamefi || 8, color: '#EC4899', trend: '+18%', icon: '🎮', description: '游戏化金融和NFT游戏' },
    { name: 'NFT', value: distribution?.nft || 6, color: '#06B6D4', trend: '-5%', icon: '🎨', description: '数字艺术和收藏品' },
    { name: '体育', value: distribution?.sports || 11, color: '#EF4444', trend: '+3%', icon: '⚽', description: '体育赛事和运动话题' }
  ]

  // 使用模拟情绪数据
  const sentimentData = mockSentimentData

  const handleSourceToggle = (source) => {
    const currentSource = sources?.[source] || { enabled: false, weight: 0.1 }
    dispatch(updateSources({
      [source]: {
        ...currentSource,
        enabled: !currentSource.enabled
      }
    }))
  }

  const handleSourceWeightChange = (source, weight) => {
    const currentSource = sources?.[source] || { enabled: true, weight: 0.1 }
    dispatch(updateSources({
      [source]: {
        ...currentSource,
        weight: parseInt(weight)
      }
    }))
  }

  const handleAddKeyword = () => {
    if (newKeyword.trim()) {
      dispatch(addKeyword(newKeyword.trim()))
      setNewKeyword('')
    }
  }

  const handleRemoveKeyword = (keyword) => {
    dispatch(removeKeyword(keyword))
  }

  const handleSettingChange = (key, value) => {
    dispatch(updateSettings({ [key]: value }))
  }

  const handleMLSettingChange = (key, value) => {
    dispatch(updateMLSettings({ [key]: value }))
  }

  const handleLanguageToggle = (language) => {
    dispatch(updateLanguageSettings({ language, enabled: !settings.languages[language] }))
  }

  const handleDarkwebToggle = () => {
    dispatch(toggleDarkwebMonitoring())
  }

  const getHotspotPriorityColor = (score, category) => {
    if (score >= 0.9) return 'text-red-400 bg-red-500/20'
    if (score >= 0.8) return 'text-orange-400 bg-orange-500/20'
    if (score >= 0.7) return 'text-yellow-400 bg-yellow-500/20'
    return 'text-green-400 bg-green-500/20'
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffMinutes = Math.floor((now - time) / (1000 * 60))

    if (diffMinutes < 1) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`
    return `${Math.floor(diffMinutes / 1440)}天前`
  }

  // 筛选处理函数
  const handleFilterChange = (category, key, value) => {
    setFilters(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const handleResetFilters = () => {
    setFilters({
      categories: {
        politics: true,
        subculture: true,
        technology: true,
        metaverse: true,
        gamefi: true,
        nft: true,
        sports: true
      },
      sources: {
        twitter: true,
        reddit: true,
        tiktok: true,
        youtube: true,
        discord: true,
        darkweb: false
      },
      minConfidence: 0.7,
      riskLevels: {
        low: true,
        medium: true,
        high: false
      },
      languages: {
        en: true,
        zh: true,
        ko: false,
        ja: false,
        es: false,
        ru: false
      },
      regions: {
        na: true,
        eu: true,
        ap: true,
        cn: true
      }
    })
  }

  const applyFilters = () => {
    // 这里可以触发API调用或数据筛选
    console.log('应用筛选:', filters)
    // 可以dispatch一个action来更新筛选后的数据
  }

  const applyPresetFilter = (presetName) => {
    switch (presetName) {
      case '高质量政治热点':
        setFilters(prev => ({
          ...prev,
          categories: { ...Object.fromEntries(Object.keys(prev.categories).map(k => [k, k === 'politics'])) },
          minConfidence: 0.8
        }))
        break
      case '病毒式传播内容':
        setFilters(prev => ({
          ...prev,
          sources: Object.fromEntries(Object.keys(prev.sources).map(k => [k, k !== 'darkweb'])),
          minConfidence: 0.9
        }))
        break
      case '新兴趋势信号':
        setFilters(prev => ({
          ...prev,
          categories: { ...Object.fromEntries(Object.keys(prev.categories).map(k => [k, ['metaverse', 'gamefi'].includes(k)])) }
        }))
        setSelectedTimeRange('24h')
        break
      case '高风险监控':
        setFilters(prev => ({
          ...prev,
          sources: { ...prev.sources, darkweb: true },
          riskLevels: { low: false, medium: false, high: true }
        }))
        break
      default:
        break
    }
  }

  // 计算筛选结果数量
  const getFilteredCount = () => {
    // 这里应该根据实际筛选逻辑计算
    const enabledCategories = Object.values(filters.categories).filter(Boolean).length
    const enabledSources = Object.values(filters.sources).filter(Boolean).length
    const baseCount = hotspots.length || 347
    const ratio = (enabledCategories / 7) * (enabledSources / 6) * filters.minConfidence
    return Math.floor(baseCount * ratio)
  }

  // 分类配置
  const categories = [
    { id: 'all', name: '全部', icon: '🌐', color: 'text-white' },
    { id: 'politics', name: '政治', icon: '🏛️', color: 'text-blue-400' },
    { id: 'subculture', name: '亚文化', icon: '🎭', color: 'text-green-400' },
    { id: 'technology', name: '科技/AI', icon: '🤖', color: 'text-yellow-400' },
    { id: 'metaverse', name: '元宇宙', icon: '🌌', color: 'text-purple-400' },
    { id: 'gamefi', name: 'GameFi', icon: '🎮', color: 'text-pink-400' },
    { id: 'nft', name: 'NFT', icon: '🎨', color: 'text-cyan-400' },
    { id: 'sports', name: '体育', icon: '⚽', color: 'text-red-400' }
  ]

  // 根据分类过滤热点 - 使用本地数据或模拟数据
  const getFilteredHotspots = () => {
    const hotspotsToUse = localHotspots.length > 0 ? localHotspots : (hotspots.length > 0 ? hotspots : mockHotspots)
    if (activeCategory === 'all') {
      return hotspotsToUse
    }
    return hotspotsToUse.filter(hotspot => hotspot.category === activeCategory)
  }

  // 获取分类统计 - 使用本地数据或模拟数据
  const getCategoryStats = () => {
    const hotspotsToUse = localHotspots.length > 0 ? localHotspots : (hotspots.length > 0 ? hotspots : mockHotspots)
    const stats = {}
    categories.forEach(cat => {
      if (cat.id === 'all') {
        stats[cat.id] = hotspotsToUse.length
      } else {
        stats[cat.id] = hotspotsToUse.filter(h => h.category === cat.id).length
      }
    })
    return stats
  }

  // 启动业务流程
  const handleStartBusinessFlow = (hotspot) => {
    const hotspotData = {
      keyword: hotspot.keyword,
      category: hotspot.category,
      score: hotspot.score,
      trend: hotspot.trend,
      sources: hotspot.sources,
      sentiment: hotspot.sentiment,
      volume: hotspot.volume,
      timestamp: new Date().toISOString()
    }

    dispatch(startNewFlow(hotspotData))

    // 显示成功提示
    alert(`已启动业务流程！\n热点关键词: ${hotspot.keyword}\n适配度: ${(hotspot.score * 100).toFixed(1)}%`)
  }

  // 快速发币
  const handleQuickDeploy = (hotspot) => {
    // 将热点数据存储到sessionStorage，供发币页面使用
    sessionStorage.setItem('selectedHotspot', JSON.stringify({
      keyword: hotspot.keyword || hotspot.title,
      category: hotspot.category,
      score: hotspot.score,
      sentiment: hotspot.sentiment || 0.75,
      volume: hotspot.volume || 1000,
      title: hotspot.title,
      description: hotspot.description
    }))

    // 跳转到发币页面
    navigate('/deploy')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="container mx-auto px-6 py-8">
        {/* 专业化页面标题 */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl border border-blue-500/30">
                <Brain className="w-8 h-8 text-blue-400" />
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  AI舆情监控中心
                </h1>
                <p className="text-gray-400 text-lg mt-1">
                  多领域实时监测 • ML驱动预测 • 全球多语言覆盖
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={`btn ${showAdvancedFilters ? 'btn-primary' : 'btn-secondary'} btn-sm`}
              >
                <Filter className="w-4 h-4" />
                高级筛选
              </button>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className={`btn ${showSettings ? 'btn-primary' : 'btn-secondary'} btn-sm`}
              >
                <Settings className="w-4 h-4" />
                设置
              </button>
              <button
                className="btn btn-primary btn-sm"
                onClick={fetchHotspotsData}
                disabled={localLoading}
              >
                {localLoading ? (
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                ) : (
                  <Activity className="w-4 h-4" />
                )}
                刷新数据
              </button>
            </div>
          </div>

          {/* 实时状态指示器 */}
          <div className="flex items-center space-x-6 mt-6 p-4 bg-white/5 backdrop-blur-md rounded-xl border border-white/10">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 font-medium">实时监控中</span>
            </div>
            <div className="text-sm text-gray-300">
              当前监控: <span className="text-blue-400 font-bold">
                {localHotspots.length || hotspots.length || mockHotspots.length}
              </span> 个热点
            </div>
            <div className="text-sm text-gray-300">
              预测准确率: <span className="text-green-400 font-bold">
                {localAnalytics?.predictionAccuracy || predictionAccuracy || mockAnalytics.predictionAccuracy}%
              </span>
            </div>
            <div className="text-sm text-gray-300">
              更新频率: <span className="text-blue-400 font-bold">&lt;30秒</span>
            </div>
          </div>
        </div>

        {/* 专业化导航标签 */}
        <div className="mb-8">
          <div className="flex space-x-2 bg-white/5 backdrop-blur-md rounded-xl p-2 border border-white/10">
            {[
              { id: 'overview', name: '总览', icon: BarChart3, color: 'blue' },
              { id: 'realtime', name: '实时热点', icon: Activity, color: 'red' },
              { id: 'prediction', name: '热点预测', icon: Brain, color: 'purple' },
              { id: 'analytics', name: 'AI分析', icon: TrendingUp, color: 'green' },
              { id: 'sentiment', name: '情绪指数', icon: Globe, color: 'yellow' },
              { id: 'darkweb', name: '暗网信号', icon: Shield, color: 'orange' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-all duration-300 font-medium ${
                  activeTab === tab.id
                    ? `bg-gradient-to-r from-${tab.color}-500/20 to-${tab.color}-600/20 text-${tab.color}-400 border border-${tab.color}-500/30 shadow-lg`
                    : 'text-gray-400 hover:text-white hover:bg-white/10 border border-transparent'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span className="text-sm">{tab.name}</span>
                {activeTab === tab.id && (
                  <div className={`w-2 h-2 rounded-full bg-${tab.color}-400 animate-pulse`}></div>
                )}
              </button>
            ))}
          </div>
        </div>

      {/* 高级筛选面板 */}
      {showAdvancedFilters && (
        <div className="card p-6 border-l-4 border-blue-500">
          <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
            <Filter className="w-5 h-5 text-blue-400" />
            <span>高级筛选器</span>
            <button
              onClick={() => setShowAdvancedFilters(false)}
              className="ml-auto text-white/50 hover:text-white"
            >
              <X className="w-4 h-4" />
            </button>
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* 内容类型筛选 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Target className="w-4 h-4 text-green-400" />
                <span>内容类型</span>
              </h4>
              <div className="space-y-3">
                {[
                  { id: 'politics', name: '政治', count: 156, color: 'blue' },
                  { id: 'subculture', name: '亚文化', count: 134, color: 'green' },
                  { id: 'technology', name: '科技/AI', count: 89, color: 'yellow' },
                  { id: 'metaverse', name: '元宇宙', count: 67, color: 'purple' },
                  { id: 'gamefi', name: 'GameFi', count: 45, color: 'pink' },
                  { id: 'nft', name: 'NFT', count: 32, color: 'cyan' },
                  { id: 'sports', name: '体育', count: 28, color: 'red' }
                ].map((category) => (
                  <label key={category.id} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.categories[category.id]}
                      onChange={(e) => handleFilterChange('categories', category.id, e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <div className={`w-3 h-3 rounded-full bg-${category.color}-400`}></div>
                    <span className="text-white text-sm flex-1">{category.name}</span>
                    <span className="text-white/50 text-xs">{category.count}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 数据源筛选 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Globe className="w-4 h-4 text-blue-400" />
                <span>数据源</span>
              </h4>
              <div className="space-y-3">
                {[
                  { id: 'twitter', name: 'Twitter', count: 245, icon: '🐦' },
                  { id: 'reddit', name: 'Reddit', count: 189, icon: '🤖' },
                  { id: 'tiktok', name: 'TikTok', count: 156, icon: '🎵' },
                  { id: 'youtube', name: 'YouTube', count: 134, icon: '📺' },
                  { id: 'discord', name: 'Discord', count: 89, icon: '💬' },
                  { id: 'darkweb', name: '暗网', count: 23, icon: '🛡️' }
                ].map((source) => (
                  <label key={source.id} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={filters.sources[source.id]}
                      onChange={(e) => handleFilterChange('sources', source.id, e.target.checked)}
                      className="w-4 h-4 text-blue-600 rounded"
                    />
                    <span className="text-lg">{source.icon}</span>
                    <span className="text-white text-sm flex-1">{source.name}</span>
                    <span className="text-white/50 text-xs">{source.count}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* 质量和风险筛选 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Shield className="w-4 h-4 text-red-400" />
                <span>质量与风险</span>
              </h4>
              <div className="space-y-4">
                {/* 适配度阈值 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    适配度阈值: {settings.minAdaptation?.toFixed(2) || 0.70}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={settings.minAdaptation || 0.7}
                    onChange={(e) => handleSettingChange('minAdaptation', parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-white/50 mt-1">
                    <span>0.0</span>
                    <span>1.0</span>
                  </div>
                </div>

                {/* 置信度筛选 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    最低置信度: {(filters.minConfidence * 100).toFixed(0)}%
                  </label>
                  <select
                    value={filters.minConfidence}
                    onChange={(e) => setFilters(prev => ({ ...prev, minConfidence: parseFloat(e.target.value) }))}
                    className="input-field w-full"
                  >
                    <option value="0.5">50% - 低</option>
                    <option value="0.7">70% - 中</option>
                    <option value="0.8">80% - 高</option>
                    <option value="0.9">90% - 极高</option>
                  </select>
                </div>

                {/* 风险等级 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">风险等级</label>
                  <div className="space-y-2">
                    {[
                      { key: 'low', name: '低风险', color: 'green' },
                      { key: 'medium', name: '中风险', color: 'yellow' },
                      { key: 'high', name: '高风险', color: 'red' }
                    ].map((risk) => (
                      <label key={risk.key} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.riskLevels[risk.key]}
                          onChange={(e) => handleFilterChange('riskLevels', risk.key, e.target.checked)}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                        <span className={`text-sm text-${risk.color}-400`}>{risk.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* 时间和地区筛选 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Clock className="w-4 h-4 text-purple-400" />
                <span>时间与地区</span>
              </h4>
              <div className="space-y-4">
                {/* 时间范围 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">时间范围</label>
                  <select
                    value={selectedTimeRange}
                    onChange={(e) => setSelectedTimeRange(e.target.value)}
                    className="input-field w-full"
                  >
                    <option value="15min">15分钟</option>
                    <option value="1h">1小时</option>
                    <option value="6h">6小时</option>
                    <option value="24h">24小时</option>
                    <option value="7d">7天</option>
                    <option value="30d">30天</option>
                  </select>
                </div>

                {/* 语言筛选 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">语言</label>
                  <div className="space-y-2">
                    {[
                      { code: 'en', name: '英语', flag: '🇺🇸' },
                      { code: 'zh', name: '中文', flag: '🇨🇳' },
                      { code: 'ko', name: '韩语', flag: '🇰🇷' },
                      { code: 'ja', name: '日语', flag: '🇯🇵' },
                      { code: 'es', name: '西语', flag: '🇪🇸' },
                      { code: 'ru', name: '俄语', flag: '🇷🇺' }
                    ].map((lang) => (
                      <label key={lang.code} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.languages[lang.code]}
                          onChange={(e) => handleFilterChange('languages', lang.code, e.target.checked)}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                        <span className="text-sm">{lang.flag}</span>
                        <span className="text-white text-sm">{lang.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* 地区筛选 */}
                <div>
                  <label className="block text-white/70 text-sm mb-2">地区</label>
                  <div className="space-y-2">
                    {[
                      { id: 'na', name: '北美', flag: '🇺🇸' },
                      { id: 'eu', name: '欧洲', flag: '🇪🇺' },
                      { id: 'ap', name: '亚太', flag: '🌏' },
                      { id: 'cn', name: '中国', flag: '🇨🇳' }
                    ].map((region) => (
                      <label key={region.id} className="flex items-center space-x-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.regions[region.id]}
                          onChange={(e) => handleFilterChange('regions', region.id, e.target.checked)}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                        <span className="text-sm">{region.flag}</span>
                        <span className="text-white text-sm">{region.name}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 筛选操作按钮 */}
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-white/10">
            <div className="flex items-center space-x-4">
              <span className="text-white/70 text-sm">筛选结果: </span>
              <span className="text-white font-bold">{getFilteredCount()}个热点</span>
              <span className="text-green-400 text-sm">符合条件</span>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleResetFilters}
                className="btn-secondary"
              >
                重置筛选
              </button>
              <button
                onClick={applyFilters}
                className="btn-primary"
              >
                应用筛选
              </button>
              <button className="btn-success">
                保存筛选
              </button>
            </div>
          </div>

          {/* 快速筛选预设 */}
          <div className="mt-4 pt-4 border-t border-white/10">
            <h5 className="text-white/70 text-sm mb-3">快速筛选预设:</h5>
            <div className="flex flex-wrap gap-2">
              {[
                { name: '高质量政治热点', desc: '适配度>0.8 + 政治类' },
                { name: '病毒式传播内容', desc: '传播性>0.9 + 全平台' },
                { name: '新兴趋势信号', desc: '元宇宙+GameFi + 24h内' },
                { name: '高风险监控', desc: '暗网信号 + 高风险' },
                { name: '多语言热点', desc: '3种以上语言 + 高共鸣' },
                { name: '名人效应内容', desc: '名人效应>0.8 + Twitter' }
              ].map((preset, index) => (
                <button
                  key={index}
                  onClick={() => applyPresetFilter(preset.name)}
                  className="bg-white/5 hover:bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white/70 hover:text-white transition-all duration-200"
                  title={preset.desc}
                >
                  {preset.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 增强版设置面板 */}
      {showSettings && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-400" />
            <span>AI趋势热点设置</span>
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 数据源设置 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Globe className="w-4 h-4 text-green-400" />
                <span>数据源配置</span>
              </h4>
              <div className="space-y-3">
                {Object.entries(sources).map(([key, source]) => (
                  <div key={key} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center space-x-3 mb-2">
                      <input
                        type="checkbox"
                        checked={source.enabled}
                        onChange={() => handleSourceToggle(key)}
                        className="w-4 h-4 text-primary-600 rounded"
                      />
                      <span className="text-white capitalize font-medium">{key}</span>
                      {key === 'darkweb' && (
                        <Shield className="w-4 h-4 text-red-400" />
                      )}
                    </div>
                    {source.enabled && (
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-white/60 text-sm">权重:</span>
                          <input
                            type="range"
                            min="0"
                            max="100"
                            value={source.weight}
                            onChange={(e) => handleSourceWeightChange(key, e.target.value)}
                            className="flex-1"
                          />
                          <span className="text-white/60 text-sm w-12">{source.weight}%</span>
                        </div>
                        <div className="text-xs text-white/50">
                          {key === 'twitter' && '实时推文、转发、话题标签'}
                          {key === 'reddit' && '热门帖子、评论、社区讨论'}
                          {key === 'tiktok' && '视频内容、音频识别、用户互动'}
                          {key === 'darkweb' && 'Tor网络、地下论坛、早期信号'}
                          {key === 'youtube' && '视频标题、描述、评论分析'}
                          {key === 'discord' && '社区聊天、语音转文字'}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* ML模型设置 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Brain className="w-4 h-4 text-purple-400" />
                <span>AI模型配置</span>
              </h4>
              <div className="space-y-4">
                <div className="bg-white/5 rounded-lg p-3">
                  <label className="block text-white/70 text-sm mb-2">
                    适配度阈值: {settings.minAdaptation?.toFixed(2) || 0.70}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={settings.minAdaptation || 0.7}
                    onChange={(e) => handleSettingChange('minAdaptation', parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="text-xs text-white/50 mt-1">误差要求: &lt;0.1</div>
                </div>

                <div className="bg-white/5 rounded-lg p-3">
                  <label className="block text-white/70 text-sm mb-2">预测准确率目标</label>
                  <select
                    value={settings.predictionTarget || '65'}
                    onChange={(e) => handleMLSettingChange('predictionTarget', e.target.value)}
                    className="input-field w-full"
                  >
                    <option value="65">65% (最低要求)</option>
                    <option value="70">70% (推荐)</option>
                    <option value="75">75% (高精度)</option>
                    <option value="80">80% (极高精度)</option>
                  </select>
                </div>

                <div className="bg-white/5 rounded-lg p-3">
                  <label className="block text-white/70 text-sm mb-2">热点捕捉窗口</label>
                  <select
                    value={settings.captureWindow || '3-15min'}
                    onChange={(e) => handleMLSettingChange('captureWindow', e.target.value)}
                    className="input-field w-full"
                  >
                    <option value="1-5min">1-5分钟 (超早期)</option>
                    <option value="3-15min">3-15分钟 (萌芽阶段)</option>
                    <option value="5-30min">5-30分钟 (发展阶段)</option>
                  </select>
                </div>

                <div className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">视频内容分析</span>
                    <input
                      type="checkbox"
                      checked={settings.videoAnalysis || false}
                      onChange={(e) => handleMLSettingChange('videoAnalysis', e.target.checked)}
                      className="w-4 h-4 text-primary-600 rounded"
                    />
                  </div>
                  <div className="text-xs text-white/50 mt-1">PyTorch OCR + 关键帧识别</div>
                </div>
              </div>
            </div>

            {/* 语言和地区设置 */}
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Languages className="w-4 h-4 text-yellow-400" />
                <span>多语言配置</span>
              </h4>
              <div className="space-y-3">
                {[
                  { code: 'en', name: '英语', flag: '🇺🇸' },
                  { code: 'zh', name: '中文', flag: '🇨🇳' },
                  { code: 'ko', name: '韩语', flag: '🇰🇷' },
                  { code: 'ja', name: '日语', flag: '🇯🇵' },
                  { code: 'es', name: '西班牙语', flag: '🇪🇸' },
                  { code: 'ru', name: '俄语', flag: '🇷🇺' }
                ].map((lang) => (
                  <div key={lang.code} className="bg-white/5 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{lang.flag}</span>
                        <span className="text-white text-sm">{lang.name}</span>
                      </div>
                      <input
                        type="checkbox"
                        checked={settings.languages?.[lang.code] || false}
                        onChange={() => handleLanguageToggle(lang.code)}
                        className="w-4 h-4 text-primary-600 rounded"
                      />
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                <h5 className="text-red-400 font-medium text-sm mb-2 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>监管屏蔽</span>
                </h5>
                <div className="text-xs text-white/60 space-y-1">
                  <div>🇺🇸 美国: SEC监管内容自动过滤</div>
                  <div>🇨🇳 中国: 敏感词汇智能屏蔽</div>
                  <div>🇰🇷 韩国: 加密货币相关限制</div>
                  <div>🇨🇦 加拿大: 金融监管合规检查</div>
                </div>
              </div>
            </div>
          </div>

          {/* 增强版关键词管理 */}
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Target className="w-4 h-4 text-blue-400" />
                <span>监控关键词</span>
              </h4>
              <div className="space-y-3">
                {/* 分类关键词 */}
                {[
                  { category: '政治', keywords: ['Trump', 'Biden', 'Election', 'XRP ETF', 'SEC'], color: 'blue' },
                  { category: '亚文化', keywords: ['NFT', 'GameFi', 'Meme', 'K-pop', '二次元'], color: 'green' },
                  { category: '新兴', keywords: ['Metaverse', 'Sandbox', 'Decentraland', 'AI生成'], color: 'purple' }
                ].map((group) => (
                  <div key={group.category} className="bg-white/5 rounded-lg p-3">
                    <div className={`text-${group.color}-400 font-medium text-sm mb-2`}>
                      {group.category}领域
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {group.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className={`bg-${group.color}-500/20 text-${group.color}-300 px-2 py-1 rounded text-xs flex items-center space-x-1`}
                        >
                          <span>{keyword}</span>
                          <button
                            onClick={() => handleRemoveKeyword(keyword)}
                            className={`text-${group.color}-300 hover:text-red-400`}
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 flex space-x-2">
                <input
                  type="text"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  placeholder="添加新关键词 (支持多语言)"
                  className="input-field flex-1"
                  onKeyDown={(e) => e.key === 'Enter' && handleAddKeyword()}
                />
                <button
                  onClick={handleAddKeyword}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>添加</span>
                </button>
              </div>
            </div>

            <div>
              <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span>实时监控状态</span>
              </h4>
              <div className="space-y-3">
                <div className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70 text-sm">更新频率</span>
                    <span className="text-green-400 text-sm">&lt;30秒</span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70 text-sm">活跃数据源</span>
                    <span className="text-blue-400 text-sm">
                      {Object.values(sources).filter(s => s.enabled).length}/
                      {Object.keys(sources).length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70 text-sm">暗网监控</span>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={settings.darkwebEnabled || false}
                        onChange={handleDarkwebToggle}
                        className="w-4 h-4 text-red-600 rounded"
                      />
                      <Shield className="w-4 h-4 text-red-400" />
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">噪声过滤</span>
                    <span className="text-purple-400 text-sm">AI增强</span>
                  </div>
                </div>

                <div className="bg-white/5 rounded-lg p-3">
                  <div className="text-white/70 text-sm mb-2">监控范围</div>
                  <div className="space-y-1 text-xs text-white/50">
                    <div>• 2025年美国中期选举</div>
                    <div>• XRP ETF审批进展</div>
                    <div>• Trump/Musk言论监控</div>
                    <div>• 元宇宙项目动态</div>
                    <div>• GameFi生态发展</div>
                    <div>• NFT市场趋势</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 优化后的总览页面 */}
      {activeTab === 'overview' && (
        <div className="space-y-8">
          {/* 核心指标概览 - 使用专业设计系统 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="data-card group">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-xl border border-green-500/30 group-hover:border-green-400/50 transition-colors">
                  <TrendingUp className="w-8 h-8 text-green-400" />
                </div>
                <div className="flex-1">
                  <div className="metric-label">活跃热点</div>
                  <div className="metric-value text-green-400">
                    {(localHotspots.length > 0 ? localHotspots : hotspots).length || 0}
                  </div>
                  <div className="metric-change positive">
                    <span>+12</span>
                    <span className="text-xs">1小时内</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="data-card group">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-r from-purple-500/20 to-purple-600/20 rounded-xl border border-purple-500/30 group-hover:border-purple-400/50 transition-colors">
                  <Brain className="w-8 h-8 text-purple-400" />
                </div>
                <div className="flex-1">
                  <div className="metric-label">AI预测准确率</div>
                  <div className="metric-value text-purple-400">
                    {localAnalytics?.predictionAccuracy || predictionAccuracy || 67.8}%
                  </div>
                  <div className="metric-change positive">
                    <span>目标 &gt;65%</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="data-card group">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-xl border border-blue-500/30 group-hover:border-blue-400/50 transition-colors">
                  <Globe className="w-8 h-8 text-blue-400" />
                </div>
                <div className="flex-1">
                  <div className="metric-label">全球监控地区</div>
                  <div className="metric-value text-blue-400">6</div>
                  <div className="metric-change positive">
                    <span>4个活跃</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="data-card group">
              <div className="flex items-center space-x-4">
                <div className="p-3 bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 rounded-xl border border-yellow-500/30 group-hover:border-yellow-400/50 transition-colors">
                  <Zap className="w-8 h-8 text-yellow-400" />
                </div>
                <div className="flex-1">
                  <div className="metric-label">可启动热点</div>
                  <div className="metric-value text-yellow-400">
                    {(localHotspots.length > 0 ? localHotspots : hotspots).filter(h => h && h.score >= 0.7).length || 0}
                  </div>
                  <div className="metric-change neutral">
                    <span>≥70%适配度</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 热点分布与趋势 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 实时热点流 - 专业化设计 */}
              <div className="data-card">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-white flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-red-500/20 to-red-600/20 rounded-lg border border-red-500/30">
                      <Activity className="w-6 h-6 text-red-400" />
                    </div>
                    <span>实时热点流</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 rounded-full animate-pulse bg-red-400"></div>
                      <span className="text-sm text-red-400 font-medium">LIVE</span>
                    </div>
                  </h3>
                  <div className="flex items-center space-x-2">
                    <div className="text-sm text-gray-400">
                      更新于 {new Date().toLocaleTimeString()}
                    </div>
                    <button
                      onClick={() => setActiveTab('realtime')}
                      className="btn btn-secondary btn-sm"
                    >
                      查看全部
                    </button>
                  </div>
                </div>

                <div className="space-y-4 max-h-96 overflow-y-auto content-scrollbar">
                  {(localHotspots.length > 0 ? localHotspots : hotspots).slice(0, 5).map((hotspot, index) => (
                    <div key={index} className="group bg-white/5 backdrop-blur-md rounded-xl p-5 hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-blue-400/50 cursor-pointer hover:shadow-lg hover:shadow-blue-500/20">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4 flex-1">
                          <div className="text-3xl p-3 bg-gradient-to-br from-white/10 to-white/5 rounded-xl border border-white/20 group-hover:border-white/30 transition-colors">
                            {categories.find(c => c.id === hotspot.category)?.icon || '🌐'}
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-semibold text-base leading-tight mb-2 group-hover:text-blue-400 transition-colors">
                              {hotspot.title}
                            </h4>
                            <p className="text-gray-400 text-sm leading-relaxed line-clamp-2">
                              {hotspot.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-2 ml-4">
                          <span className={`text-sm font-bold px-3 py-1.5 rounded-lg border transition-all duration-200 ${getHotspotPriorityColor(hotspot.score)}`}>
                            {(hotspot.score * 100).toFixed(0)}%
                          </span>
                          <div className="flex items-center space-x-1">
                            <div className={`w-2 h-2 rounded-full ${
                              hotspot.trend === 'rising' ? 'bg-green-400 animate-pulse' :
                              hotspot.trend === 'falling' ? 'bg-red-400' : 'bg-yellow-400'
                            }`}></div>
                            <span className="text-xs text-gray-400 capitalize">{hotspot.trend}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
                            hotspot.category === 'politics' ? 'bg-blue-500/20 text-blue-400 border-blue-500/30' :
                            hotspot.category === 'subculture' ? 'bg-green-500/20 text-green-400 border-green-500/30' :
                            hotspot.category === 'technology' ? 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30' :
                            hotspot.category === 'metaverse' ? 'bg-purple-500/20 text-purple-400 border-purple-500/30' :
                            hotspot.category === 'gamefi' ? 'bg-pink-500/20 text-pink-400 border-pink-500/30' :
                            hotspot.category === 'nft' ? 'bg-cyan-500/20 text-cyan-400 border-cyan-500/30' :
                            hotspot.category === 'sports' ? 'bg-red-500/20 text-red-400 border-red-500/30' :
                            'bg-blue-500/20 text-blue-400 border-blue-500/30'
                          }`}>
                            {categories.find(c => c.id === hotspot.category)?.name || hotspot.category}
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">来源:</span>
                            <span className="text-xs text-white font-medium">{hotspot.source || 'Twitter'}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-gray-500">音量:</span>
                            <span className="text-xs text-white font-medium">{hotspot.volume?.toLocaleString() || '1.2K'}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-xs text-gray-400">
                            {formatTimeAgo(hotspot.timestamp || new Date())}
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleQuickDeploy(hotspot)
                            }}
                            className="btn btn-primary btn-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                          >
                            <Zap className="w-3 h-3" />
                            部署
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 领域分布图表 */}
              <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                    <BarChart3 className="w-5 h-5 text-green-400" />
                    <span>热点领域分布</span>
                  </h3>
                  <div className="text-white/60 text-sm font-medium">
                    实时更新 • 过去24小时
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-6">
                  {/* 气泡图 */}
                  <div className="h-96 relative bg-gradient-to-br from-white/5 to-white/10 rounded-xl p-6 overflow-hidden">
                    {/* 背景动画效果 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10"></div>
                    <div className="absolute inset-0 opacity-30">
                      {[...Array(20)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
                          style={{
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                            animationDelay: `${Math.random() * 3}s`,
                            animationDuration: `${2 + Math.random() * 2}s`
                          }}
                        ></div>
                      ))}
                    </div>

                    <div className="relative z-10 h-full flex flex-wrap items-center justify-center gap-6">
                      {distributionData.map((item, index) => {
                        const totalValue = distributionData.reduce((sum, d) => sum + d.value, 0)
                        const percentage = (item.value / totalValue) * 100
                        // 根据百分比计算气泡大小，最小50px，最大140px
                        const bubbleSize = Math.max(50, Math.min(140, percentage * 3))

                        return (
                          <div
                            key={index}
                            className="relative group cursor-pointer transition-all duration-500 hover:scale-110 hover:z-20"
                            style={{
                              width: `${bubbleSize}px`,
                              height: `${bubbleSize}px`,
                              animationDelay: `${index * 0.1}s`
                            }}
                          >
                            {/* 气泡主体 */}
                            <div
                              className="w-full h-full rounded-full flex flex-col items-center justify-center relative overflow-hidden shadow-2xl transition-all duration-500 group-hover:shadow-3xl"
                              style={{
                                backgroundColor: item.color,
                                boxShadow: `0 0 30px ${item.color}50, inset 0 0 30px rgba(255,255,255,0.2)`,
                                animation: `float-${index} 3s ease-in-out infinite`
                              }}
                            >
                              {/* 气泡内部渐变效果 */}
                              <div
                                className="absolute inset-0 rounded-full"
                                style={{
                                  background: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.4), transparent 60%)`
                                }}
                              ></div>

                              {/* 气泡图标 */}
                              <div className="relative z-10 text-center text-white">
                                <div className="text-2xl mb-1 group-hover:scale-110 transition-transform duration-300">
                                  {item.icon}
                                </div>
                                <div className="font-bold text-lg leading-tight">
                                  {percentage.toFixed(0)}%
                                </div>
                                <div className="text-xs font-medium opacity-90 leading-tight">
                                  {item.value}个
                                </div>
                              </div>

                              {/* 旋转光环 */}
                              <div
                                className="absolute inset-0 rounded-full opacity-60"
                                style={{
                                  background: `conic-gradient(from 0deg, transparent, ${item.color}80, transparent)`,
                                  animation: `spin 4s linear infinite`
                                }}
                              ></div>

                              {/* 脉冲效果 */}
                              <div
                                className="absolute inset-0 rounded-full animate-ping opacity-20"
                                style={{ backgroundColor: item.color }}
                              ></div>
                            </div>

                            {/* 悬停时显示的详细信息卡片 */}
                            <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 bg-black/90 backdrop-blur-md text-white text-xs p-3 rounded-lg whitespace-nowrap z-30 border border-white/20">
                              <div className="font-bold text-sm mb-1">{item.name}</div>
                              <div className="text-white/70 mb-1">{item.description}</div>
                              <div className="flex items-center justify-between space-x-3">
                                <span>热点: {item.value}个</span>
                                <span className={`font-bold ${
                                  item.trend.startsWith('+') ? 'text-green-400' : 'text-red-400'
                                }`}>
                                  {item.trend}
                                </span>
                              </div>
                            </div>

                            {/* 趋势指示器 */}
                            <div className={`absolute -top-3 -right-3 w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold shadow-lg transition-all duration-300 group-hover:scale-110 ${
                              item.trend.startsWith('+')
                                ? 'bg-green-500 text-white'
                                : 'bg-red-500 text-white'
                            }`}>
                              {item.trend.startsWith('+') ? '↗' : '↘'}
                            </div>

                            {/* 连接线效果 */}
                            <div className="absolute inset-0 rounded-full border-2 border-white/20 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-110"></div>
                          </div>
                        )
                      })}
                    </div>

                    {/* 标题和统计信息 */}
                    <div className="absolute top-4 left-4 text-white">
                      <h4 className="text-lg font-bold mb-1">热点领域分布</h4>
                      <p className="text-sm text-white/60">实时数据 • 气泡大小表示占比</p>
                    </div>

                    {/* 图例 */}
                    <div className="absolute bottom-4 right-4 text-white/60 text-xs">
                      <div className="flex items-center space-x-2 mb-1">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>上升趋势</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span>下降趋势</span>
                      </div>
                    </div>
                  </div>

                  {/* 分类详情列表 */}
                  <div className="space-y-3 mt-6">
                    <h4 className="text-white font-medium text-sm mb-4 flex items-center space-x-2">
                      <BarChart3 className="w-4 h-4 text-green-400" />
                      <span>详细数据</span>
                    </h4>
                    {distributionData.map((item, index) => {
                      const totalValue = distributionData.reduce((sum, d) => sum + d.value, 0)
                      const percentage = (item.value / totalValue) * 100

                      return (
                        <div key={index} className="bg-white/5 backdrop-blur-md rounded-lg p-4 hover:bg-white/10 transition-all duration-200 cursor-pointer border border-white/10 group">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <div
                                className="w-4 h-4 rounded-full shadow-lg group-hover:scale-110 transition-transform duration-200"
                                style={{
                                  backgroundColor: item.color,
                                  boxShadow: `0 0 8px ${item.color}60`
                                }}
                              ></div>
                              <span className="text-white font-medium">{item.name}</span>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className={`text-sm font-bold px-2 py-1 rounded-full ${
                                item.trend.startsWith('+')
                                  ? 'text-green-400 bg-green-500/20 border border-green-500/30'
                                  : 'text-red-400 bg-red-500/20 border border-red-500/30'
                              }`}>
                                {item.trend}
                              </span>
                              <span className="text-lg font-bold text-white">
                                {percentage.toFixed(1)}%
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between text-sm mb-3">
                            <span className="text-white/70">热点数量</span>
                            <span className="text-white font-bold">{item.value} 个</span>
                          </div>

                          {/* 增强版进度条 */}
                          <div className="relative">
                            <div className="w-full bg-white/20 rounded-full h-3 overflow-hidden">
                              <div
                                className="h-3 rounded-full transition-all duration-500 relative"
                                style={{
                                  backgroundColor: item.color,
                                  width: `${percentage}%`,
                                  boxShadow: `0 0 15px ${item.color}60`
                                }}
                              >
                                {/* 进度条内部光效 */}
                                <div
                                  className="absolute inset-0 rounded-full"
                                  style={{
                                    background: `linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)`
                                  }}
                                ></div>
                              </div>
                            </div>

                            {/* 进度条上的数值标签 */}
                            <div
                              className="absolute top-0 h-3 flex items-center justify-center text-xs font-bold text-white"
                              style={{ width: `${Math.max(percentage, 15)}%` }}
                            >
                              {percentage > 15 && `${item.value}`}
                            </div>
                          </div>

                          {/* 额外信息 */}
                          <div className="mt-3 flex items-center justify-between text-xs text-white/50">
                            <span>活跃度: {(Math.random() * 100).toFixed(0)}%</span>
                            <span>增长率: {item.trend}</span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>


            </div>

            {/* 右侧信息面板 - 专业化设计 */}
            <div className="space-y-6">
              {/* AI模型性能 */}
              <div className="data-card">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-purple-500/20 to-purple-600/20 rounded-lg border border-purple-500/30">
                    <Brain className="w-6 h-6 text-purple-400" />
                  </div>
                  <span>AI模型性能</span>
                </h3>

                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-xl p-5 border border-white/10 hover:border-green-400/30 transition-colors">
                    <div className="flex items-center justify-between mb-3">
                      <span className="metric-label">预测准确率</span>
                      <span className="metric-value text-green-400 text-xl">
                        {localAnalytics?.predictionAccuracy || predictionAccuracy || 67.8}%
                      </span>
                    </div>
                    <div className="progress-bar">
                      <div
                        className="progress-fill bg-gradient-to-r from-green-400 to-green-500"
                        style={{
                          width: `${localAnalytics?.predictionAccuracy || predictionAccuracy || 67.8}%`
                        }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-400 mt-2 font-medium">目标: &gt;65%</div>
                  </div>

                  <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-xl p-5 border border-white/10 hover:border-yellow-400/30 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <span className="metric-label">捕捉延迟</span>
                      <span className="metric-value text-yellow-400 text-xl">8分钟</span>
                    </div>
                    <div className="text-xs text-gray-400 font-medium">目标: 3-15分钟</div>
                  </div>

                  <div className="bg-gradient-to-r from-white/5 to-white/10 rounded-xl p-5 border border-white/10 hover:border-purple-400/30 transition-colors">
                    <div className="flex items-center justify-between mb-2">
                      <span className="metric-label">噪声过滤率</span>
                      <span className="metric-value text-purple-400 text-xl">94.2%</span>
                    </div>
                    <div className="text-xs text-gray-400 font-medium">AI分类器增强</div>
                  </div>
                </div>
              </div>

              {/* 全球监控状态 */}
              <div className="data-card">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-lg border border-blue-500/30">
                    <Globe className="w-6 h-6 text-blue-400" />
                  </div>
                  <span>全球监控</span>
                </h3>

                <div className="space-y-4">
                  {[
                    { region: '北美', status: 'active', sources: 12, lang: '🇺🇸 EN', hotspots: 156, flag: '🇺🇸' },
                    { region: '亚太', status: 'active', sources: 8, lang: '🇯🇵 JP/🇰🇷 KR', hotspots: 134, flag: '🌏' },
                    { region: '欧洲', status: 'active', sources: 6, lang: '🇪🇸 ES/🇷🇺 RU', hotspots: 89, flag: '🇪🇺' },
                    { region: '中国', status: 'filtered', sources: 4, lang: '🇨🇳 ZH', hotspots: 45, flag: '🇨🇳' }
                  ].map((region, index) => (
                    <div key={index} className="group bg-gradient-to-r from-white/5 to-white/10 rounded-xl p-5 hover:from-white/10 hover:to-white/15 transition-all duration-300 cursor-pointer border border-white/10 hover:border-blue-400/30">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{region.flag}</span>
                          <span className="text-white font-semibold text-base">{region.region}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full animate-pulse ${
                            region.status === 'active' ? 'bg-green-400' :
                            region.status === 'filtered' ? 'bg-yellow-400' : 'bg-red-400'
                          }`}></div>
                          <span className={`text-xs font-medium px-2 py-1 rounded-full border ${
                            region.status === 'active' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                            region.status === 'filtered' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                            'text-red-400 bg-red-500/20 border-red-500/30'
                          }`}>
                            {region.status === 'active' ? '正常监控' :
                             region.status === 'filtered' ? '合规过滤' : '暂停'}
                          </span>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <div className="text-gray-400 text-xs mb-1">热点数</div>
                          <div className="text-white font-bold text-lg">{region.hotspots}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-gray-400 text-xs mb-1">数据源</div>
                          <div className="text-white font-bold text-lg">{region.sources}</div>
                        </div>
                        <div className="text-center">
                          <div className="text-gray-400 text-xs mb-1">语言</div>
                          <div className="text-white font-bold text-sm">{region.lang}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 bg-white/5 backdrop-blur-md rounded-lg p-4 border border-blue-500/30">
                  <div className="text-blue-400 text-sm font-medium mb-2 flex items-center space-x-2">
                    <Shield className="w-4 h-4" />
                    <span>监管合规</span>
                  </div>
                  <div className="text-xs text-white/60 font-medium">
                    自动过滤敏感地区内容，确保合规运营
                  </div>
                </div>
              </div>

              {/* 快速操作 */}
              <div className="data-card">
                <h3 className="text-xl font-bold text-white mb-6 flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 rounded-lg border border-yellow-500/30">
                    <Zap className="w-6 h-6 text-yellow-400" />
                  </div>
                  <span>快速操作</span>
                </h3>

                <div className="space-y-4">
                  <button
                    onClick={() => setActiveTab('realtime')}
                    className="btn btn-secondary w-full"
                  >
                    <Activity className="w-4 h-4" />
                    <span>查看实时热点</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('prediction')}
                    className="btn btn-secondary w-full"
                  >
                    <Brain className="w-4 h-4" />
                    <span>AI预测分析</span>
                  </button>

                  <button
                    onClick={() => {
                      const highScoreHotspots = (localHotspots.length > 0 ? localHotspots : hotspots).filter(h => h && h.score >= 0.8)
                      if (highScoreHotspots.length > 0) {
                        handleStartBusinessFlow(highScoreHotspots[0])
                      }
                    }}
                    className="btn btn-primary w-full"
                    disabled={(localHotspots.length > 0 ? localHotspots : hotspots).filter(h => h && h.score >= 0.8).length === 0}
                  >
                    <Zap className="w-4 h-4" />
                    <span>启动最佳热点</span>
                  </button>
                </div>

                <div className="mt-6 bg-white/5 backdrop-blur-md rounded-lg p-4 border border-green-500/30">
                  <div className="text-green-400 text-sm font-medium mb-2 flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full animate-pulse bg-green-400"></div>
                    <span>系统状态</span>
                  </div>
                  <div className="text-xs text-white/60 space-y-1 font-medium">
                    <div>• 监控系统: 正常运行</div>
                    <div>• AI模型: 实时更新</div>
                    <div>• 数据同步: 延迟 &lt;2秒</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 实时热点标签内容 */}
      {activeTab === 'realtime' && (
        <div className="space-y-6">
          {/* 分类标签页 */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                <Activity className="w-5 h-5 text-red-400" />
                <span>实时热点流</span>
                <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              </h3>

              <div className="flex items-center space-x-2 text-sm text-white/60">
                <span>总计: {(localHotspots.length > 0 ? localHotspots : hotspots).length} 个热点</span>
                <span>•</span>
                <span>当前分类: {getFilteredHotspots().length} 个</span>
              </div>
            </div>

            {/* 分类标签导航 */}
            <div className="flex flex-wrap gap-2 mb-6">
              {categories.map((category) => {
                const stats = getCategoryStats()
                const count = stats[category.id] || 0

                return (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                      activeCategory === category.id
                        ? 'bg-primary-500 text-white shadow-lg'
                        : 'bg-white/5 text-white/70 hover:bg-white/10 hover:text-white'
                    }`}
                  >
                    <span className="text-lg">{category.icon}</span>
                    <span className="font-medium">{category.name}</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      activeCategory === category.id
                        ? 'bg-white/20 text-white'
                        : 'bg-white/10 text-white/50'
                    }`}>
                      {count}
                    </span>
                  </button>
                )
              })}
            </div>

            {/* 热点列表 */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {(localLoading || isLoading) ? (
                <div className="text-center text-white/60 py-8">
                  <div className="animate-spin w-6 h-6 border-2 border-primary-500 border-t-transparent rounded-full mx-auto"></div>
                  <p className="mt-2">实时加载中...</p>
                </div>
              ) : getFilteredHotspots().length > 0 ? (
                getFilteredHotspots().map((hotspot, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 border-l-4 border-transparent hover:border-primary-400">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-3 flex-1">
                        {/* 分类图标 */}
                        <span className="text-2xl">
                          {categories.find(c => c.id === hotspot.category)?.icon || '🌐'}
                        </span>
                        <h4 className="text-white font-medium">{hotspot.title}</h4>
                      </div>
                      <div className="flex items-center space-x-2 ml-3">
                        <span className={`text-sm font-bold ${getHotspotPriorityColor(hotspot.score)} px-2 py-1 rounded`}>
                          {(hotspot.score * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                    <p className="text-white/60 text-sm mb-3 ml-11">{hotspot.description}</p>
                    <div className="flex items-center justify-between ml-11">
                      <div className="flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded font-medium ${
                          categories.find(c => c.id === hotspot.category)?.color || 'text-white'
                        } bg-white/10`}>
                          {categories.find(c => c.id === hotspot.category)?.name || hotspot.category}
                        </span>
                        <span className="text-xs text-white/50">{hotspot.source}</span>
                        {hotspot.language && (
                          <span className="text-xs bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded">
                            {hotspot.language}
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-white/40">
                        {formatTimeAgo(hotspot.timestamp || new Date())}
                      </span>
                    </div>

                    {/* ML分析指标 */}
                    <div className="mt-3 ml-11 grid grid-cols-4 gap-2 text-xs">
                      <div className="text-center">
                        <div className="text-white/50">传播性</div>
                        <div className="text-blue-400 font-bold">{(hotspot.virality || 0.8).toFixed(1)}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white/50">共鸣度</div>
                        <div className="text-green-400 font-bold">{(hotspot.resonance || 0.7).toFixed(1)}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white/50">名人效应</div>
                        <div className="text-purple-400 font-bold">{(hotspot.celebrity || 0.6).toFixed(1)}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white/50">文化适配</div>
                        <div className="text-yellow-400 font-bold">{(hotspot.cultural || 0.9).toFixed(1)}</div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    {hotspot.score >= 0.7 && (
                      <div className="mt-4 ml-11 pt-3 border-t border-white/10 space-y-2">
                        <div className="grid grid-cols-2 gap-2">
                          <button
                            onClick={() => handleStartBusinessFlow(hotspot)}
                            className="btn-secondary flex items-center justify-center space-x-1 text-xs py-2"
                          >
                            <Zap className="w-3 h-3" />
                            <span>业务流程</span>
                          </button>
                          <button
                            onClick={() => handleQuickDeploy(hotspot)}
                            className="btn-gradient bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center space-x-1 text-xs py-2"
                          >
                            <Zap className="w-3 h-3" />
                            <span>快速发币</span>
                          </button>
                        </div>
                        <div className="text-center text-xs text-white/50">
                          适配度 {(hotspot.score * 100).toFixed(1)}% • 符合启动条件
                        </div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center text-white/60 py-8">
                  <span className="text-6xl mb-4 block">
                    {activeCategory === 'all' ? '🌐' : categories.find(c => c.id === activeCategory)?.icon || '🌐'}
                  </span>
                  <p className="text-lg font-medium mb-2">
                    {activeCategory === 'all' ? '暂无热点' : `暂无${categories.find(c => c.id === activeCategory)?.name}热点`}
                  </p>
                  <p className="text-sm">
                    {activeCategory === 'all' ? '系统正在监控中...' : '切换到其他分类查看更多热点'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* 分类统计和趋势 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 分类统计 */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-green-400" />
                <span>分类统计</span>
              </h3>

              <div className="space-y-3">
                {categories.filter(c => c.id !== 'all').map((category) => {
                  const stats = getCategoryStats()
                  const count = stats[category.id] || 0
                  const percentage = hotspots.length > 0 ? (count / hotspots.length * 100) : 0

                  return (
                    <div key={category.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-all duration-200">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{category.icon}</span>
                        <div>
                          <div className={`font-medium ${category.color}`}>{category.name}</div>
                          <div className="text-white/50 text-sm">{count} 个热点</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-bold">{percentage.toFixed(1)}%</div>
                        <div className="w-20 bg-white/20 rounded-full h-2 mt-1">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              category.id === 'politics' ? 'bg-blue-400' :
                              category.id === 'subculture' ? 'bg-green-400' :
                              category.id === 'technology' ? 'bg-yellow-400' :
                              category.id === 'metaverse' ? 'bg-purple-400' :
                              category.id === 'gamefi' ? 'bg-pink-400' :
                              category.id === 'nft' ? 'bg-cyan-400' :
                              'bg-red-400'
                            }`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* 实时统计 */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Activity className="w-5 h-5 text-red-400" />
                <span>实时统计</span>
                <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              </h3>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/70 text-sm">总热点数</div>
                  <div className="text-white text-2xl font-bold">{hotspots.length}</div>
                  <div className="text-green-400 text-xs">+12 (1小时)</div>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/70 text-sm">当前分类</div>
                  <div className="text-white text-2xl font-bold">{getFilteredHotspots().length}</div>
                  <div className="text-blue-400 text-xs">{categories.find(c => c.id === activeCategory)?.name}</div>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/70 text-sm">平均适配度</div>
                  <div className="text-white text-2xl font-bold">0.82</div>
                  <div className="text-yellow-400 text-xs">高于阈值</div>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/70 text-sm">可启动数</div>
                  <div className="text-white text-2xl font-bold">
                    {getFilteredHotspots().filter(h => h.score >= 0.7).length}
                  </div>
                  <div className="text-purple-400 text-xs">≥70%适配度</div>
                </div>
              </div>

              {/* 时间范围选择 */}
              <div className="flex items-center space-x-2 mb-4">
                <span className="text-white/70 text-sm">时间范围:</span>
                {['1h', '6h', '24h', '7d'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setSelectedTimeRange(range)}
                    className={`px-3 py-1 rounded text-sm transition-all duration-200 ${
                      selectedTimeRange === range
                        ? 'bg-primary-500 text-white'
                        : 'bg-white/10 text-white/70 hover:bg-white/20'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>

              {/* 快速操作 */}
              <div className="space-y-2">
                <button
                  onClick={() => setActiveCategory('all')}
                  className="w-full btn-secondary text-sm py-2"
                >
                  查看全部分类
                </button>
                <button
                  onClick={() => {
                    const highScoreHotspots = getFilteredHotspots().filter(h => h.score >= 0.8)
                    if (highScoreHotspots.length > 0) {
                      handleStartBusinessFlow(highScoreHotspots[0])
                    }
                  }}
                  className="w-full btn-primary text-sm py-2"
                  disabled={getFilteredHotspots().filter(h => h.score >= 0.8).length === 0}
                >
                  启动最佳热点
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 热点预测标签内容 */}
      {activeTab === 'prediction' && (
        <HotspotPrediction />
      )}

      {/* AI分析标签内容 */}
      {activeTab === 'analytics' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* ML模型性能 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-400" />
              <span>ML模型性能</span>
            </h3>

            <div className="space-y-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">传播性预测模型</span>
                  <span className="text-green-400 text-sm">95.2%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2 mb-2">
                  <div className="bg-green-400 h-2 rounded-full" style={{ width: '95.2%' }}></div>
                </div>
                <div className="text-xs text-white/60">
                  基于社交网络传播模式和用户行为分析
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">社区共鸣分析</span>
                  <span className="text-blue-400 text-sm">88.7%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2 mb-2">
                  <div className="bg-blue-400 h-2 rounded-full" style={{ width: '88.7%' }}></div>
                </div>
                <div className="text-xs text-white/60">
                  情感分析 + 社区参与度评估
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">名人效应检测</span>
                  <span className="text-yellow-400 text-sm">76.3%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2 mb-2">
                  <div className="bg-yellow-400 h-2 rounded-full" style={{ width: '76.3%' }}></div>
                </div>
                <div className="text-xs text-white/60">
                  KOL影响力 + 粉丝互动模式分析
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">文化梗适配度</span>
                  <span className="text-purple-400 text-sm">92.1%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2 mb-2">
                  <div className="bg-purple-400 h-2 rounded-full" style={{ width: '92.1%' }}></div>
                </div>
                <div className="text-xs text-white/60">
                  跨文化理解 + 本地化适配分析
                </div>
              </div>
            </div>
          </div>

          {/* 视频内容分析 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Video className="w-5 h-5 text-red-400" />
              <span>视频内容分析</span>
            </h3>

            <div className="space-y-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white font-medium mb-2">PyTorch OCR识别</div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-white/70">处理帧数</div>
                    <div className="text-blue-400 font-bold">1,247</div>
                  </div>
                  <div>
                    <div className="text-white/70">识别准确率</div>
                    <div className="text-green-400 font-bold">94.8%</div>
                  </div>
                  <div>
                    <div className="text-white/70">关键词提取</div>
                    <div className="text-yellow-400 font-bold">856</div>
                  </div>
                  <div>
                    <div className="text-white/70">视觉Meme</div>
                    <div className="text-purple-400 font-bold">23</div>
                  </div>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white font-medium mb-3">最新检测结果</div>
                <div className="space-y-2">
                  {[
                    { platform: 'TikTok', content: 'Trump Dance Meme', confidence: 0.96 },
                    { platform: 'YouTube', content: 'Crypto Moonshot', confidence: 0.89 },
                    { platform: 'Instagram', content: 'NFT Art Trend', confidence: 0.92 }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        <span className="text-white/70">{item.platform}:</span>
                        <span className="text-white">{item.content}</span>
                      </div>
                      <span className="text-green-400">{(item.confidence * 100).toFixed(0)}%</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white font-medium mb-2">处理统计</div>
                <div className="text-xs text-white/60 space-y-1">
                  <div>• 支持格式: MP4, AVI, MOV, WebM</div>
                  <div>• 关键帧间隔: 2秒</div>
                  <div>• OCR语言: 英/中/韩/日</div>
                  <div>• 实时处理延迟: &lt;5秒</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 情绪指数标签内容 */}
      {activeTab === 'sentiment' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 实时情绪指数 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              <span>实时情绪指数</span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </h3>

            <div className="h-64 mb-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={sentimentData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                  <XAxis dataKey="time" stroke="rgba(255,255,255,0.7)" />
                  <YAxis domain={[0, 1]} stroke="rgba(255,255,255,0.7)" />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px'
                    }}
                  />
                  <Line type="monotone" dataKey="overall" stroke="#3B82F6" strokeWidth={3} name="整体情绪" />
                  <Line type="monotone" dataKey="politics" stroke="#EF4444" strokeWidth={2} name="政治" />
                  <Line type="monotone" dataKey="subculture" stroke="#10B981" strokeWidth={2} name="亚文化" />
                  <Line type="monotone" dataKey="technology" stroke="#F59E0B" strokeWidth={2} name="科技" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* 情绪指标卡片 */}
            <div className="grid grid-cols-2 gap-4">
              {[
                { name: '整体情绪', value: sentimentIndex.overall || 0.82, color: 'blue', trend: '+5.2%' },
                { name: '政治情绪', value: sentimentIndex.politics || 0.85, color: 'red', trend: '+8.1%' },
                { name: '亚文化情绪', value: sentimentIndex.subculture || 0.90, color: 'green', trend: '+12.3%' },
                { name: '科技情绪', value: sentimentIndex.technology || 0.78, color: 'yellow', trend: '+3.7%' }
              ].map((item, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70 text-sm">{item.name}</span>
                    <span className={`text-${item.color}-400 text-xs`}>{item.trend}</span>
                  </div>
                  <div className={`text-${item.color}-400 text-xl font-bold mb-2`}>
                    {(item.value * 100).toFixed(0)}%
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className={`bg-${item.color}-400 h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${item.value * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 情绪分析详情 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-400" />
              <span>情绪分析详情</span>
            </h3>

            <div className="space-y-4">
              {/* 情绪驱动因素 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">主要驱动因素</h4>
                <div className="space-y-2">
                  {[
                    { factor: 'Trump 2024竞选活动', impact: '+15%', sentiment: 'positive' },
                    { factor: 'XRP ETF审批传言', impact: '+12%', sentiment: 'positive' },
                    { factor: 'Musk AI言论', impact: '+8%', sentiment: 'mixed' },
                    { factor: '监管政策不确定性', impact: '-5%', sentiment: 'negative' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-white/70">{item.factor}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`${
                          item.sentiment === 'positive' ? 'text-green-400' :
                          item.sentiment === 'negative' ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {item.impact}
                        </span>
                        <div className={`w-2 h-2 rounded-full ${
                          item.sentiment === 'positive' ? 'bg-green-400' :
                          item.sentiment === 'negative' ? 'bg-red-400' : 'bg-yellow-400'
                        }`}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 地区情绪分布 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">地区情绪分布</h4>
                <div className="space-y-3">
                  {[
                    { region: '北美', sentiment: 0.85, flag: '🇺🇸', trend: 'up' },
                    { region: '欧洲', sentiment: 0.78, flag: '🇪🇺', trend: 'stable' },
                    { region: '亚太', sentiment: 0.82, flag: '🌏', trend: 'up' },
                    { region: '中国', sentiment: 0.75, flag: '🇨🇳', trend: 'down' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{item.flag}</span>
                        <span className="text-white/70 text-sm">{item.region}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-white text-sm">{(item.sentiment * 100).toFixed(0)}%</span>
                        <div className={`w-3 h-3 ${
                          item.trend === 'up' ? 'text-green-400' :
                          item.trend === 'down' ? 'text-red-400' : 'text-yellow-400'
                        }`}>
                          {item.trend === 'up' ? '↗' : item.trend === 'down' ? '↘' : '→'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 情绪预警 */}
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h4 className="text-yellow-400 font-medium mb-2 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>情绪预警</span>
                </h4>
                <div className="text-xs text-white/70 space-y-1">
                  <div>• 政治情绪波动加剧，建议关注选举相关热点</div>
                  <div>• 亚文化情绪持续高涨，NFT/GameFi领域机会增加</div>
                  <div>• 监管情绪谨慎，注意合规风险</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 全球监控标签内容 */}
      {activeTab === 'global' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 全球热点地图 */}
          <div className="lg:col-span-2 card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Globe className="w-5 h-5 text-blue-400" />
              <span>全球热点分布</span>
            </h3>

            {/* 模拟世界地图热点分布 */}
            <div className="bg-slate-800 rounded-lg p-6 h-64 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20"></div>

              {/* 地区热点标记 */}
              {[
                { region: '北美', x: '20%', y: '30%', intensity: 0.92, count: 156 },
                { region: '欧洲', x: '50%', y: '25%', intensity: 0.78, count: 89 },
                { region: '亚太', x: '75%', y: '45%', intensity: 0.85, count: 134 },
                { region: '中东', x: '55%', y: '50%', intensity: 0.65, count: 45 },
                { region: '南美', x: '25%', y: '70%', intensity: 0.58, count: 32 },
                { region: '非洲', x: '52%', y: '65%', intensity: 0.42, count: 18 }
              ].map((point, index) => (
                <div
                  key={index}
                  className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                  style={{ left: point.x, top: point.y }}
                >
                  <div className={`w-4 h-4 rounded-full animate-pulse ${
                    point.intensity > 0.8 ? 'bg-red-400' :
                    point.intensity > 0.6 ? 'bg-yellow-400' : 'bg-green-400'
                  }`}></div>
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="bg-black/80 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                      <div className="font-medium">{point.region}</div>
                      <div>热点: {point.count}</div>
                      <div>强度: {(point.intensity * 100).toFixed(0)}%</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* 连接线动画 */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                <defs>
                  <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="rgba(59, 130, 246, 0)" />
                    <stop offset="50%" stopColor="rgba(59, 130, 246, 0.5)" />
                    <stop offset="100%" stopColor="rgba(59, 130, 246, 0)" />
                  </linearGradient>
                </defs>
                <line x1="20%" y1="30%" x2="75%" y2="45%" stroke="url(#connectionGradient)" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3s" repeatCount="indefinite" />
                </line>
                <line x1="50%" y1="25%" x2="75%" y2="45%" stroke="url(#connectionGradient)" strokeWidth="1">
                  <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="4s" repeatCount="indefinite" />
                </line>
              </svg>
            </div>

            {/* 全球统计 */}
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-white/70 text-sm">活跃地区</div>
                <div className="text-white text-xl font-bold">6</div>
                <div className="text-green-400 text-xs">+1 (24h)</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-white/70 text-sm">总热点数</div>
                <div className="text-white text-xl font-bold">474</div>
                <div className="text-blue-400 text-xs">+23 (1h)</div>
              </div>
              <div className="bg-white/5 rounded-lg p-3 text-center">
                <div className="text-white/70 text-sm">平均强度</div>
                <div className="text-white text-xl font-bold">0.71</div>
                <div className="text-yellow-400 text-xs">+0.05</div>
              </div>
            </div>
          </div>

          {/* 地区详情 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Radar className="w-5 h-5 text-green-400" />
              <span>地区监控详情</span>
            </h3>

            <div className="space-y-4 max-h-96 overflow-y-auto">
              {[
                {
                  region: '北美地区',
                  flag: '🇺🇸',
                  status: 'active',
                  hotspots: 156,
                  languages: ['en'],
                  top_topics: ['Trump 2024', 'XRP ETF', 'AI Regulation'],
                  sentiment: 0.92,
                  sources: 12
                },
                {
                  region: '欧洲地区',
                  flag: '🇪🇺',
                  status: 'active',
                  hotspots: 89,
                  languages: ['en', 'es', 'ru'],
                  top_topics: ['MiCA Regulation', 'Digital Euro', 'Green Tech'],
                  sentiment: 0.78,
                  sources: 8
                },
                {
                  region: '亚太地区',
                  flag: '🌏',
                  status: 'active',
                  hotspots: 134,
                  languages: ['ja', 'ko', 'en'],
                  top_topics: ['K-pop NFT', 'Anime AI', 'GameFi'],
                  sentiment: 0.85,
                  sources: 10
                },
                {
                  region: '中国地区',
                  flag: '🇨🇳',
                  status: 'filtered',
                  hotspots: 67,
                  languages: ['zh'],
                  top_topics: ['数字人民币', 'AI生成', '元宇宙'],
                  sentiment: 0.75,
                  sources: 6
                }
              ].map((region, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{region.flag}</span>
                      <span className="text-white font-medium">{region.region}</span>
                    </div>
                    <div className={`w-2 h-2 rounded-full ${
                      region.status === 'active' ? 'bg-green-400' :
                      region.status === 'filtered' ? 'bg-yellow-400' : 'bg-red-400'
                    }`}></div>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-white/70">热点数量:</span>
                      <span className="text-white">{region.hotspots}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">情绪指数:</span>
                      <span className="text-green-400">{(region.sentiment * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">数据源:</span>
                      <span className="text-blue-400">{region.sources}个</span>
                    </div>
                    <div>
                      <span className="text-white/70 text-xs">语言支持:</span>
                      <div className="flex space-x-1 mt-1">
                        {region.languages.map((lang, i) => (
                          <span key={i} className="bg-purple-500/20 text-purple-300 px-1 py-0.5 rounded text-xs">
                            {lang.toUpperCase()}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div>
                      <span className="text-white/70 text-xs">热门话题:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {region.top_topics.map((topic, i) => (
                          <span key={i} className="bg-blue-500/20 text-blue-300 px-1 py-0.5 rounded text-xs">
                            {topic}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 暗网信号标签内容 */}
      {activeTab === 'darkweb' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 暗网信号监控 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Shield className="w-5 h-5 text-red-400" />
              <span>暗网信号监控</span>
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
            </h3>

            {/* 暗网监控状态 */}
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-red-400 font-medium">监控状态</span>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={settings.darkwebEnabled || false}
                    onChange={handleDarkwebToggle}
                    className="w-4 h-4 text-red-600 rounded"
                  />
                  <span className={`text-sm ${settings.darkwebEnabled ? 'text-green-400' : 'text-red-400'}`}>
                    {settings.darkwebEnabled ? '已启用' : '已禁用'}
                  </span>
                </div>
              </div>
              <div className="text-xs text-white/60">
                ⚠️ 暗网监控涉及敏感内容，请确保合规使用
              </div>
            </div>

            {/* 暗网信号列表 */}
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {settings.darkwebEnabled ? (
                mockDarkwebSignals && mockDarkwebSignals.length > 0 ? (
                  mockDarkwebSignals.map((signal, index) => (
                    <div key={index} className="bg-red-500/5 border border-red-500/20 rounded-lg p-3">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="text-white font-medium text-sm">{signal.signal}</h4>
                        <span className={`text-xs px-2 py-1 rounded ${
                          signal.confidence > 0.8 ? 'bg-red-500/20 text-red-400' :
                          signal.confidence > 0.6 ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {(signal.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <p className="text-white/60 text-xs mb-2">{signal.description}</p>
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center space-x-2">
                          <span className="text-white/50">来源:</span>
                          <span className="text-red-400">{signal.source}</span>
                        </div>
                        <span className="text-white/40">
                          {formatTimeAgo(signal.timestamp)}
                        </span>
                      </div>
                      <div className="mt-2 flex items-center space-x-2">
                        <span className={`text-xs px-2 py-1 rounded ${
                          signal.riskLevel === 'critical' ? 'bg-red-500/20 text-red-400' :
                          signal.riskLevel === 'high' ? 'bg-orange-500/20 text-orange-400' :
                          signal.riskLevel === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {signal.riskLevel === 'critical' ? '严重风险' :
                           signal.riskLevel === 'high' ? '高风险' :
                           signal.riskLevel === 'medium' ? '中等风险' : '低风险'}
                        </span>
                        <span className="text-xs bg-purple-500/20 text-purple-400 px-2 py-1 rounded">
                          {signal.category}
                        </span>
                      </div>
                      {signal.tags && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {signal.tags.map((tag, tagIndex) => (
                            <span key={tagIndex} className="text-xs bg-white/10 text-white/60 px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center text-white/60 py-8">
                    <Shield className="w-12 h-12 mx-auto mb-3 opacity-50" />
                    <p>暂无暗网信号</p>
                    <p className="text-sm">系统正在监控中...</p>
                  </div>
                )
              ) : (
                <div className="text-center text-white/60 py-8">
                  <Shield className="w-12 h-12 mx-auto mb-3 opacity-30" />
                  <p>暗网监控已禁用</p>
                  <p className="text-sm">启用监控以查看信号</p>
                </div>
              )}
            </div>
          </div>

          {/* 暗网分析统计 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Activity className="w-5 h-5 text-purple-400" />
              <span>暗网分析统计</span>
            </h3>

            <div className="space-y-4">
              {/* 监控范围 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">监控范围</h4>
                <div className="space-y-2 text-sm">
                  {[
                    { platform: 'Tor Forums', status: 'active', nodes: 12 },
                    { platform: 'Dark Markets', status: 'limited', nodes: 5 },
                    { platform: 'Encrypted Chats', status: 'active', nodes: 8 },
                    { platform: 'Hidden Services', status: 'monitoring', nodes: 15 }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-white/70">{item.platform}</span>
                      <div className="flex items-center space-x-2">
                        <span className="text-white text-xs">{item.nodes}节点</span>
                        <div className={`w-2 h-2 rounded-full ${
                          item.status === 'active' ? 'bg-green-400' :
                          item.status === 'limited' ? 'bg-yellow-400' : 'bg-blue-400'
                        }`}></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 信号统计 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">信号统计</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-white/70 text-sm">今日信号</div>
                    <div className="text-red-400 text-xl font-bold">23</div>
                    <div className="text-green-400 text-xs">+5</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white/70 text-sm">高风险</div>
                    <div className="text-red-400 text-xl font-bold">3</div>
                    <div className="text-red-400 text-xs">需关注</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white/70 text-sm">平均置信度</div>
                    <div className="text-yellow-400 text-xl font-bold">76%</div>
                    <div className="text-blue-400 text-xs">+2%</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white/70 text-sm">响应时间</div>
                    <div className="text-green-400 text-xl font-bold">8分钟</div>
                    <div className="text-green-400 text-xs">优秀</div>
                  </div>
                </div>
              </div>

              {/* 安全提醒 */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h4 className="text-red-400 font-medium mb-2 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>安全提醒</span>
                </h4>
                <div className="text-xs text-white/70 space-y-1">
                  <div>• 暗网监控数据仅用于早期信号检测</div>
                  <div>• 所有数据经过匿名化和合规处理</div>
                  <div>• 不涉及非法内容的直接访问</div>
                  <div>• 遵循当地法律法规和平台政策</div>
                </div>
              </div>

              {/* Tor网络状态 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">Tor网络状态</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">连接节点:</span>
                    <span className="text-green-400">40/40</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">网络延迟:</span>
                    <span className="text-yellow-400">2.3s</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">匿名等级:</span>
                    <span className="text-green-400">高</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">数据加密:</span>
                    <span className="text-green-400">AES-256</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  )
}

export default HotspotMonitor
