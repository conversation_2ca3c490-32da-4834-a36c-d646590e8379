import React from 'react'
import {
  TrendingUp,
  Wallet,
  Droplets,
  LogOut,
  BarChart3,
  RefreshCw,
  Shield,
  AlertTriangle,
  Zap
} from 'lucide-react'

const Dashboard = () => {
  // 模拟数据，不使用Redux
  const mockData = {
    strategies: [{ id: 1 }, { id: 2 }, { id: 3 }],
    wallets: [{ id: 1 }, { id: 2 }],
    currentTVL: 1500000,
    tvlChange24h: 5.2,
    signalStrength: 0.75,
    recommendedAction: 'HOLD',
    hotspots: [{ id: 1 }, { id: 2 }],
    uptime: 7200,
    cpuUsage: 45,
    requestCount: 12345
  }

  // 交易系统控制状态
  const [tradingSystemStatus, setTradingSystemStatus] = React.useState({
    isRunning: true,
    isCircuitBreakerActive: false,
    lastRefresh: new Date(),
    emergencyStopActive: false
  })

  const [isRefreshing, setIsRefreshing] = React.useState(false)

  // 交易系统控制函数
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // 模拟刷新
      await new Promise(resolve => setTimeout(resolve, 1000))
      setTradingSystemStatus(prev => ({
        ...prev,
        lastRefresh: new Date()
      }))
      console.log('系统数据刷新完成')
    } catch (error) {
      console.error('刷新失败:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleCircuitBreaker = () => {
    const newStatus = !tradingSystemStatus.isCircuitBreakerActive
    setTradingSystemStatus(prev => ({
      ...prev,
      isCircuitBreakerActive: newStatus
    }))

    if (newStatus) {
      console.log('交易熔断已激活 - 所有交易暂停')
    } else {
      console.log('交易熔断已解除 - 恢复正常交易')
    }
  }

  const handleEmergencyStop = () => {
    const confirmed = window.confirm(
      '⚠️ 确认执行紧急停止？\n\n这将立即：\n• 停止所有交易策略\n• 暂停新订单\n• 激活风险保护\n• 需要手动重启系统'
    )

    if (confirmed) {
      setTradingSystemStatus(prev => ({
        ...prev,
        emergencyStopActive: true,
        isRunning: false,
        isCircuitBreakerActive: true
      }))
      console.log('🚨 紧急停止已激活 - 系统进入安全模式')
    }
  }

  const handleSystemRestart = () => {
    const confirmed = window.confirm('确认重启交易系统？')
    if (confirmed) {
      setTradingSystemStatus({
        isRunning: true,
        isCircuitBreakerActive: false,
        lastRefresh: new Date(),
        emergencyStopActive: false
      })
      console.log('✅ 交易系统已重启')
    }
  }

  const statsCards = [
    {
      title: '活跃策略',
      value: mockData.strategies.length,
      change: '+12%',
      icon: BarChart3,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20'
    },
    {
      title: '钱包数量',
      value: mockData.wallets.length,
      change: '+5',
      icon: Wallet,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20'
    },
    {
      title: '流动性TVL',
      value: `$${(mockData.currentTVL / 1000000).toFixed(1)}M`,
      change: `+${mockData.tvlChange24h.toFixed(1)}%`,
      icon: Droplets,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/20'
    },
    {
      title: '退出信号',
      value: `${(mockData.signalStrength * 100).toFixed(0)}%`,
      change: mockData.recommendedAction,
      icon: LogOut,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20'
    }
  ]

  const moduleCards = [
    {
      title: '舆情监控',
      description: '实时监控社交媒体热点，智能识别投资机会',
      status: mockData.hotspots.length > 0 ? '运行中' : '待启动',
      icon: TrendingUp,
      color: 'text-green-400',
      path: '/hotspot'
    },
    {
      title: '策略管理',
      description: 'AI驱动的代币策略生成与优化',
      status: '执行中',
      icon: BarChart3,
      color: 'text-purple-400',
      path: '/strategy'
    },
    {
      title: '钱包管理',
      description: '多钱包自动化管理与资金分配',
      status: mockData.wallets.length > 0 ? '已配置' : '未配置',
      icon: Wallet,
      color: 'text-yellow-400',
      path: '/wallet'
    },
    {
      title: '流动性控制',
      description: '智能流动性管理与MEV保护',
      status: mockData.currentTVL > 0 ? '活跃' : '待部署',
      icon: Droplets,
      color: 'text-cyan-400',
      path: '/liquidity'
    }
  ]

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">仪表盘</h1>
          <p className="text-white/60 mt-1">MemeMaster AI 系统总览 (简化版本)</p>
        </div>

        {/* 交易系统控制面板 */}
        <div className="flex items-center space-x-4">
          {/* 系统状态指示器 */}
          <div className="flex items-center space-x-2">
            <div className={`status-indicator ${
              tradingSystemStatus.emergencyStopActive ? 'status-error' :
              tradingSystemStatus.isCircuitBreakerActive ? 'status-warning' :
              tradingSystemStatus.isRunning ? 'status-online' : 'status-offline'
            }`}></div>
            <span className={`font-medium text-sm ${
              tradingSystemStatus.emergencyStopActive ? 'text-red-400' :
              tradingSystemStatus.isCircuitBreakerActive ? 'text-yellow-400' :
              tradingSystemStatus.isRunning ? 'text-green-400' : 'text-gray-400'
            }`}>
              {tradingSystemStatus.emergencyStopActive ? '紧急停止' :
               tradingSystemStatus.isCircuitBreakerActive ? '交易熔断' :
               tradingSystemStatus.isRunning ? '系统运行正常' : '系统离线'}
            </span>
          </div>

          {/* 交易系统控制按钮 */}
          <div className="flex items-center space-x-2">
            {/* 刷新按钮 */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing || tradingSystemStatus.emergencyStopActive}
              className={`btn-secondary flex items-center space-x-2 ${
                isRefreshing ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              title="刷新系统数据"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span>{isRefreshing ? '刷新中' : '刷新'}</span>
            </button>

            {/* 熔断按钮 */}
            <button
              onClick={handleCircuitBreaker}
              disabled={tradingSystemStatus.emergencyStopActive}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200 ${
                tradingSystemStatus.isCircuitBreakerActive
                  ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30'
                  : 'bg-white/10 text-white/70 border border-white/20 hover:bg-white/20 hover:text-white'
              } ${tradingSystemStatus.emergencyStopActive ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={tradingSystemStatus.isCircuitBreakerActive ? '解除交易熔断' : '激活交易熔断'}
            >
              <Shield className="w-4 h-4" />
              <span>{tradingSystemStatus.isCircuitBreakerActive ? '解除熔断' : '熔断'}</span>
            </button>

            {/* 紧急停止/重启按钮 */}
            {tradingSystemStatus.emergencyStopActive ? (
              <button
                onClick={handleSystemRestart}
                className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200"
                title="重启交易系统"
              >
                <Zap className="w-4 h-4" />
                <span>重启系统</span>
              </button>
            ) : (
              <button
                onClick={handleEmergencyStop}
                className="bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30 flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200"
                title="紧急停止所有交易"
              >
                <AlertTriangle className="w-4 h-4" />
                <span>紧急停止</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon
          return (
            <div key={index} className="card card-hover p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">{card.title}</p>
                  <p className="text-2xl font-bold text-white mt-1">{card.value}</p>
                  <p className={`text-sm mt-1 ${card.change.startsWith('+') ? 'text-green-400' : 'text-white/60'}`}>
                    {card.change}
                  </p>
                </div>
                <div className={`p-3 rounded-lg ${card.bgColor}`}>
                  <Icon className={`w-6 h-6 ${card.color}`} />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 功能模块 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {moduleCards.map((module, index) => {
          const Icon = module.icon
          return (
            <div key={index} className="card card-hover p-6">
              <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg bg-white/10`}>
                  <Icon className={`w-6 h-6 ${module.color}`} />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white">{module.title}</h3>
                  <p className="text-white/60 text-sm mt-1">{module.description}</p>
                  <div className="flex items-center justify-between mt-4">
                    <span className={`text-sm px-2 py-1 rounded ${
                      module.status === '运行中' || module.status === '执行中' || module.status === '活跃' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {module.status}
                    </span>
                    <button 
                      onClick={() => window.location.href = module.path}
                      className="text-primary-400 hover:text-primary-300 text-sm font-medium"
                    >
                      查看详情 →
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 系统信息 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">系统信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{Math.floor(mockData.uptime / 3600)}h</div>
            <div className="text-white/60 text-sm">运行时间</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{mockData.cpuUsage}%</div>
            <div className="text-white/60 text-sm">CPU使用率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{mockData.requestCount.toLocaleString()}</div>
            <div className="text-white/60 text-sm">请求次数</div>
          </div>
        </div>
      </div>

      {/* 调试信息 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">🧪 调试信息</h3>
        <div className="space-y-2 text-sm">
          <div className="text-green-400">✅ Dashboard组件正常渲染</div>
          <div className="text-green-400">✅ 不依赖Redux，使用模拟数据</div>
          <div className="text-green-400">✅ 所有交互功能正常</div>
          <div className="text-green-400">✅ CSS样式正确应用</div>
          <div className="text-blue-400">ℹ️ 这是简化版本，用于确认组件渲染正常</div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
