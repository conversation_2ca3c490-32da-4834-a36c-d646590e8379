import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  TrendingUp,
  Wallet,
  Droplets,
  LogOut,
  Activity,
  DollarSign,
  Users,
  BarChart3,
  RefreshCw,
  Shield,
  AlertTriangle,
  Zap,
  Power
} from 'lucide-react'
// 注释掉不存在的slice导入
// import { fetchSystemStatus, fetchSystemInfo } from '../store/slices/systemSlice'
// import { fetchHotspots } from '../store/slices/hotspotSlice'
// import { fetchStrategies } from '../store/slices/strategySlice'
// import { fetchWallets } from '../store/slices/walletSlice'
// import { fetchLiquidityData } from '../store/slices/liquiditySlice'
// import { fetchExitSignals } from '../store/slices/exitSlice'

const Dashboard = () => {
  const dispatch = useDispatch()

  // 安全地获取Redux状态，提供默认值
  const system = useSelector(state => state?.system || {
    uptime: 0,
    cpuUsage: 0,
    requestCount: 0
  })
  const hotspot = useSelector(state => state?.hotspot || {
    hotspots: []
  })
  const strategy = useSelector(state => state?.strategy || {
    strategies: [],
    activeStrategy: null
  })
  const wallet = useSelector(state => state?.wallet || {
    wallets: []
  })
  const liquidity = useSelector(state => state?.liquidity || {
    currentTVL: 0,
    tvlChange24h: 0
  })
  const exit = useSelector(state => state?.exit || {
    signalStrength: 0,
    recommendedAction: 'HOLD'
  })

  // 交易系统控制状态
  const [tradingSystemStatus, setTradingSystemStatus] = React.useState({
    isRunning: true,
    isCircuitBreakerActive: false,
    lastRefresh: new Date(),
    emergencyStopActive: false
  })

  const [isRefreshing, setIsRefreshing] = useState(false)

  // 交易系统控制函数
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      // 模拟刷新数据
      await new Promise(resolve => setTimeout(resolve, 1000))

      setTradingSystemStatus(prev => ({
        ...prev,
        lastRefresh: new Date()
      }))

      // 显示成功提示
      console.log('系统数据刷新完成')
    } catch (error) {
      console.error('刷新失败:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleCircuitBreaker = () => {
    const newStatus = !tradingSystemStatus.isCircuitBreakerActive
    setTradingSystemStatus(prev => ({
      ...prev,
      isCircuitBreakerActive: newStatus
    }))

    if (newStatus) {
      console.log('交易熔断已激活 - 所有交易暂停')
      // 这里可以调用API暂停所有交易
    } else {
      console.log('交易熔断已解除 - 恢复正常交易')
      // 这里可以调用API恢复交易
    }
  }

  const handleEmergencyStop = () => {
    const confirmed = window.confirm(
      '⚠️ 确认执行紧急停止？\n\n这将立即：\n• 停止所有交易策略\n• 暂停新订单\n• 激活风险保护\n• 需要手动重启系统'
    )

    if (confirmed) {
      setTradingSystemStatus(prev => ({
        ...prev,
        emergencyStopActive: true,
        isRunning: false,
        isCircuitBreakerActive: true
      }))

      console.log('🚨 紧急停止已激活 - 系统进入安全模式')
      // 这里可以调用API执行紧急停止
    }
  }

  const handleSystemRestart = () => {
    const confirmed = window.confirm('确认重启交易系统？')
    if (confirmed) {
      setTradingSystemStatus({
        isRunning: true,
        isCircuitBreakerActive: false,
        lastRefresh: new Date(),
        emergencyStopActive: false
      })
      console.log('✅ 交易系统已重启')
    }
  }

  const statsCards = [
    {
      title: '活跃策略',
      value: (strategy.strategies || []).length,
      change: '+12%',
      icon: BarChart3,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      borderColor: 'border-blue-500/20'
    },
    {
      title: '钱包数量',
      value: (wallet.wallets || []).length,
      change: `+${wallet.wallets?.length || 0}`,
      icon: Wallet,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      borderColor: 'border-green-500/20'
    },
    {
      title: '流动性TVL',
      value: `$${((liquidity.currentTVL || 0) / 1000000).toFixed(1)}M`,
      change: `${(liquidity.tvlChange24h || 0) > 0 ? '+' : ''}${(liquidity.tvlChange24h || 0).toFixed(1)}%`,
      icon: Droplets,
      color: 'text-cyan-400',
      bgColor: 'bg-cyan-500/10',
      borderColor: 'border-cyan-500/20'
    },
    {
      title: '退出信号强度',
      value: `${((exit.signalStrength || 0) * 100).toFixed(0)}%`,
      change: exit.recommendedAction || 'HOLD',
      icon: Activity,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      borderColor: 'border-purple-500/20'
    }
  ]

  const moduleCards = [
    {
      title: '趋势热点',
      description: '实时监控社交媒体热点，智能识别投资机会',
      status: (hotspot.hotspots || []).length > 0 ? '运行中' : '待启动',
      icon: TrendingUp,
      color: 'text-green-400',
      path: '/hotspot'
    },
    {
      title: '策略引擎',
      description: 'AI驱动的代币策略生成与优化',
      status: strategy.activeStrategy ? '执行中' : '待配置',
      icon: BarChart3,
      color: 'text-purple-400',
      path: '/strategy'
    },
    {
      title: '钱包管理',
      description: '多钱包自动化管理与资金分配',
      status: (wallet.wallets || []).length > 0 ? '已配置' : '未配置',
      icon: Wallet,
      color: 'text-yellow-400',
      path: '/wallet'
    },
    {
      title: '流动性控制',
      description: '智能流动性管理与MEV保护',
      status: (liquidity.currentTVL || 0) > 0 ? '活跃' : '待部署',
      icon: Droplets,
      color: 'text-cyan-400',
      path: '/liquidity'
    }
  ]

  return (
    <div className="space-y-8 animate-fade-in">
      {/* 现代化页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
            智能交易仪表盘
          </h1>
          <p className="text-white/60 mt-2 text-lg">MemeMaster AI - 实时监控与智能决策</p>
        </div>

        {/* 交易系统控制面板 */}
        <div className="flex items-center space-x-4">
          {/* 系统状态指示器 */}
          <div className="flex items-center space-x-2">
            <div className={`status-indicator ${
              tradingSystemStatus.emergencyStopActive ? 'status-error' :
              tradingSystemStatus.isCircuitBreakerActive ? 'status-warning' :
              tradingSystemStatus.isRunning ? 'status-online' : 'status-offline'
            }`}></div>
            <span className={`font-medium text-sm ${
              tradingSystemStatus.emergencyStopActive ? 'text-red-400' :
              tradingSystemStatus.isCircuitBreakerActive ? 'text-yellow-400' :
              tradingSystemStatus.isRunning ? 'text-green-400' : 'text-gray-400'
            }`}>
              {tradingSystemStatus.emergencyStopActive ? '紧急停止' :
               tradingSystemStatus.isCircuitBreakerActive ? '交易熔断' :
               tradingSystemStatus.isRunning ? '系统运行正常' : '系统离线'}
            </span>
          </div>

          {/* 交易系统控制按钮 */}
          <div className="flex items-center space-x-2">
            {/* 刷新按钮 */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing || tradingSystemStatus.emergencyStopActive}
              className={`btn-secondary flex items-center space-x-2 ${
                isRefreshing ? 'opacity-50 cursor-not-allowed' : ''
              }`}
              title="刷新系统数据"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span>{isRefreshing ? '刷新中' : '刷新'}</span>
            </button>

            {/* 熔断按钮 */}
            <button
              onClick={handleCircuitBreaker}
              disabled={tradingSystemStatus.emergencyStopActive}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200 ${
                tradingSystemStatus.isCircuitBreakerActive
                  ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30 hover:bg-yellow-500/30'
                  : 'bg-white/10 text-white/70 border border-white/20 hover:bg-white/20 hover:text-white'
              } ${tradingSystemStatus.emergencyStopActive ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={tradingSystemStatus.isCircuitBreakerActive ? '解除交易熔断' : '激活交易熔断'}
            >
              <Shield className="w-4 h-4" />
              <span>{tradingSystemStatus.isCircuitBreakerActive ? '解除熔断' : '熔断'}</span>
            </button>

            {/* 紧急停止/重启按钮 */}
            {tradingSystemStatus.emergencyStopActive ? (
              <button
                onClick={handleSystemRestart}
                className="bg-green-500/20 text-green-400 border border-green-500/30 hover:bg-green-500/30 flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200"
                title="重启交易系统"
              >
                <Zap className="w-4 h-4" />
                <span>重启系统</span>
              </button>
            ) : (
              <button
                onClick={handleEmergencyStop}
                className="bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30 flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200"
                title="紧急停止所有交易"
              >
                <AlertTriangle className="w-4 h-4" />
                <span>紧急停止</span>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 现代化统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => {
          const Icon = card.icon
          return (
            <div key={index} className={`relative p-6 bg-black/20 backdrop-blur-md border ${card.borderColor} rounded-xl hover:bg-black/30 transition-all duration-300 group`}>
              {/* 背景光效 */}
              <div className={`absolute inset-0 ${card.bgColor} rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-4">
                  <div className={`p-3 rounded-lg ${card.bgColor} border ${card.borderColor}`}>
                    <Icon className={`w-6 h-6 ${card.color}`} />
                  </div>
                  <div className={`text-xs px-2 py-1 rounded-full ${card.bgColor} ${card.color} border ${card.borderColor}`}>
                    {card.change}
                  </div>
                </div>

                <div>
                  <p className="text-white/60 text-sm font-medium">{card.title}</p>
                  <p className="text-3xl font-bold text-white mt-2">{card.value}</p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 交易系统状态面板 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <Zap className="w-5 h-5 text-blue-400" />
          <span>交易系统状态</span>
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* 系统运行状态 */}
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              tradingSystemStatus.emergencyStopActive ? 'text-red-400' :
              tradingSystemStatus.isCircuitBreakerActive ? 'text-yellow-400' :
              tradingSystemStatus.isRunning ? 'text-green-400' : 'text-gray-400'
            }`}>
              {tradingSystemStatus.emergencyStopActive ? '停止' :
               tradingSystemStatus.isCircuitBreakerActive ? '熔断' :
               tradingSystemStatus.isRunning ? '运行' : '离线'}
            </div>
            <div className="text-white/60 text-sm">系统状态</div>
          </div>

          {/* 最后刷新时间 */}
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {tradingSystemStatus.lastRefresh.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
              })}
            </div>
            <div className="text-white/60 text-sm">最后刷新</div>
          </div>

          {/* 交易保护状态 */}
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              tradingSystemStatus.isCircuitBreakerActive || tradingSystemStatus.emergencyStopActive
                ? 'text-yellow-400' : 'text-green-400'
            }`}>
              {tradingSystemStatus.isCircuitBreakerActive || tradingSystemStatus.emergencyStopActive ? '激活' : '正常'}
            </div>
            <div className="text-white/60 text-sm">风险保护</div>
          </div>

          {/* 活跃策略数 */}
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              tradingSystemStatus.emergencyStopActive ? 'text-red-400' : 'text-blue-400'
            }`}>
              {tradingSystemStatus.emergencyStopActive ? '0' : (strategy.strategies || []).length}
            </div>
            <div className="text-white/60 text-sm">活跃策略</div>
          </div>
        </div>

        {/* 系统警告信息 */}
        {(tradingSystemStatus.isCircuitBreakerActive || tradingSystemStatus.emergencyStopActive) && (
          <div className={`mt-4 p-4 rounded-lg border ${
            tradingSystemStatus.emergencyStopActive
              ? 'bg-red-500/10 border-red-500/20'
              : 'bg-yellow-500/10 border-yellow-500/20'
          }`}>
            <div className={`flex items-center space-x-2 ${
              tradingSystemStatus.emergencyStopActive ? 'text-red-400' : 'text-yellow-400'
            }`}>
              <AlertTriangle className="w-5 h-5" />
              <span className="font-medium">
                {tradingSystemStatus.emergencyStopActive
                  ? '🚨 紧急停止模式'
                  : '⚠️ 交易熔断模式'}
              </span>
            </div>
            <div className="text-white/70 text-sm mt-2">
              {tradingSystemStatus.emergencyStopActive
                ? '所有交易已停止，系统处于安全模式。需要手动重启以恢复交易。'
                : '交易已暂停，新订单被阻止。可以手动解除熔断恢复交易。'}
            </div>
          </div>
        )}
      </div>

      {/* 功能模块 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {moduleCards.map((module, index) => {
          const Icon = module.icon
          return (
            <div key={index} className="card card-hover p-6">
              <div className="flex items-start space-x-4">
                <div className={`p-3 rounded-lg bg-white/10`}>
                  <Icon className={`w-6 h-6 ${module.color}`} />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-white">{module.title}</h3>
                  <p className="text-white/60 text-sm mt-1">{module.description}</p>
                  <div className="flex items-center justify-between mt-4">
                    <span className={`text-sm px-2 py-1 rounded ${
                      module.status === '运行中' || module.status === '执行中' || module.status === '活跃' 
                        ? 'bg-green-500/20 text-green-400' 
                        : 'bg-yellow-500/20 text-yellow-400'
                    }`}>
                      {module.status}
                    </span>
                    <button 
                      onClick={() => window.location.href = module.path}
                      className="text-primary-400 hover:text-primary-300 text-sm font-medium"
                    >
                      查看详情 →
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 系统信息 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4">系统信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{Math.floor((system.uptime || 0) / 3600)}h</div>
            <div className="text-white/60 text-sm">运行时间</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{system.cpuUsage || 0}%</div>
            <div className="text-white/60 text-sm">CPU使用率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{(system.requestCount || 0).toLocaleString()}</div>
            <div className="text-white/60 text-sm">请求次数</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
