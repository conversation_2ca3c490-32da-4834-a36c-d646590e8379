import React, { useState, useEffect } from 'react';
import { User, <PERSON>Card, Settings, BarChart3, Shield, Clock } from 'lucide-react';

const UserDashboard = () => {
  const [user, setUser] = useState(null);
  const [usageSummary, setUsageSummary] = useState(null);
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserData();
    fetchUsageSummary();
    fetchSubscriptionPlans();
  }, []);

  const fetchUserData = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  const fetchUsageSummary = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch('/api/usage/summary', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsageSummary(data);
      }
    } catch (error) {
      console.error('获取使用摘要失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSubscriptionPlans = async () => {
    try {
      const response = await fetch('/api/subscription/plans');
      if (response.ok) {
        const data = await response.json();
        setSubscriptionPlans(data.plans);
      }
    } catch (error) {
      console.error('获取订阅计划失败:', error);
    }
  };

  const handleUpgrade = async (targetTier) => {
    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/subscription/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          target_tier: targetTier,
          payment_method: 'solana'
        })
      });

      if (response.ok) {
        const data = await response.json();
        alert(`升级到 ${targetTier} 需要支付 ${data.payment_details.amount_sol} SOL`);
      }
    } catch (error) {
      console.error('升级订阅失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">请先登录</h2>
          <button 
            onClick={() => window.location.href = '/login'}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600"
          >
            前往登录
          </button>
        </div>
      </div>
    );
  }

  const getSubscriptionBadge = (tier) => {
    const badges = {
      free: { color: 'bg-gray-100 text-gray-800', label: '免费版' },
      pro: { color: 'bg-blue-100 text-blue-800', label: 'Pro版' },
      enterprise: { color: 'bg-purple-100 text-purple-800', label: '机构版' }
    };
    return badges[tier] || badges.free;
  };

  const badge = getSubscriptionBadge(user.role);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 用户信息卡片 */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-500 rounded-full p-3">
                <User className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{user.full_name || user.username}</h1>
                <p className="text-gray-600">{user.email}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
                    {badge.label}
                  </span>
                  {usageSummary?.trial_status?.is_trial && (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      试用中 ({usageSummary.trial_status.days_remaining}天剩余)
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">注册时间</p>
              <p className="text-lg font-semibold">{new Date(user.created_at).toLocaleDateString()}</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 使用统计 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                使用统计
              </h2>
              
              {usageSummary && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {usageSummary.current_usage.daily_hotspots_used}
                    </div>
                    <div className="text-sm text-gray-600">今日热点检测</div>
                    <div className="text-xs text-gray-500">
                      限制: {usageSummary.usage_limits.daily_hotspots === -1 ? '无限制' : usageSummary.usage_limits.daily_hotspots}
                    </div>
                  </div>
                  
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {usageSummary.current_usage.strategies_created}
                    </div>
                    <div className="text-sm text-gray-600">策略生成</div>
                    <div className="text-xs text-gray-500">
                      限制: {usageSummary.usage_limits.max_strategies === -1 ? '无限制' : usageSummary.usage_limits.max_strategies}
                    </div>
                  </div>
                  
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {usageSummary.current_usage.deployments}
                    </div>
                    <div className="text-sm text-gray-600">代币部署</div>
                  </div>
                  
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {usageSummary.current_usage.paid_features_used}
                    </div>
                    <div className="text-sm text-gray-600">付费功能使用</div>
                  </div>
                </div>
              )}
            </div>

            {/* 功能权限 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                功能权限
              </h2>
              
              {usageSummary && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span>热点监测</span>
                    <span className="text-green-600">✓ 可用</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span>策略管理</span>
                    <span className="text-green-600">✓ 可用</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span>一键部署</span>
                    <span className={usageSummary.features.one_click_deployment ? "text-green-600" : "text-red-600"}>
                      {usageSummary.features.one_click_deployment ? "✓ 可用" : "✗ 需升级"}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span>查重功能</span>
                    <span className={usageSummary.features.duplicate_checking ? "text-green-600" : "text-red-600"}>
                      {usageSummary.features.duplicate_checking ? "✓ 可用" : "✗ 需升级"}
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span>暗网监测</span>
                    <span className={usageSummary.features.darkweb_monitoring ? "text-green-600" : "text-red-600"}>
                      {usageSummary.features.darkweb_monitoring ? "✓ 可用" : "✗ 需升级"}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 订阅管理 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                订阅管理
              </h2>
              
              <div className="space-y-4">
                {subscriptionPlans.map((plan) => (
                  <div key={plan.tier} className={`p-4 border rounded-lg ${user.role === plan.tier ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-semibold">{plan.name}</h3>
                      {user.role === plan.tier && (
                        <span className="text-xs bg-blue-500 text-white px-2 py-1 rounded">当前</span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                    <div className="text-lg font-bold text-blue-600 mb-3">
                      {plan.price_sol > 0 ? `${plan.price_sol} SOL/月` : '免费'}
                    </div>
                    {user.role !== plan.tier && plan.tier !== 'free' && (
                      <button
                        onClick={() => handleUpgrade(plan.tier)}
                        className="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 text-sm"
                      >
                        升级到 {plan.name}
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 快速操作 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                快速操作
              </h2>
              
              <div className="space-y-3">
                <button className="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                  查看使用历史
                </button>
                <button className="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                  下载使用报告
                </button>
                <button className="w-full text-left p-3 bg-gray-50 rounded-lg hover:bg-gray-100">
                  账户设置
                </button>
                <button 
                  onClick={() => {
                    localStorage.removeItem('access_token');
                    window.location.href = '/';
                  }}
                  className="w-full text-left p-3 bg-red-50 text-red-600 rounded-lg hover:bg-red-100"
                >
                  退出登录
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
