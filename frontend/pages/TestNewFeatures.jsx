import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Wallet,
  Droplets,
  Shield,
  Activity,
  CheckCircle,
  ArrowRight,
  Zap,
  Target,
  Eye,
  Bot
} from 'lucide-react'

const TestNewFeatures = () => {
  const [testResults, setTestResults] = useState({})

  const testAPI = async (endpoint, name) => {
    try {
      const response = await fetch(endpoint)
      const data = await response.json()
      setTestResults(prev => ({
        ...prev,
        [name]: { success: true, data }
      }))
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [name]: { success: false, error: error.message }
      }))
    }
  }

  const features = [
    {
      title: '钱包管理（抗MEV版）',
      description: '批量钱包生成、防封禁系统、智能资金分配',
      icon: Wallet,
      color: 'blue',
      path: '/wallet',
      apis: [
        { name: 'batch-generate', endpoint: '/api/wallet/batch-generate' }
      ]
    },
    {
      title: '流动性管理（抗MEV版）',
      description: 'MEV防护、鲸鱼监控、滑点优化、自动补充',
      icon: Droplets,
      color: 'green',
      path: '/liquidity',
      apis: [
        { name: 'mev-protection', endpoint: '/api/liquidity/mev-protection' },
        { name: 'whale-activity', endpoint: '/api/liquidity/whale-activity' },
        { name: 'slippage-optimization', endpoint: '/api/liquidity/slippage-optimization' }
      ]
    }
  ]

  const subFeatures = [
    {
      title: 'MEV防护系统',
      description: 'Flashbots集成、私有内存池、前置交易检测',
      icon: Shield,
      color: 'purple'
    },
    {
      title: '鲸鱼监控',
      description: 'Nansen API、实时活动监控、风险评估',
      icon: Eye,
      color: 'yellow'
    },
    {
      title: '行为伪装',
      description: 'Puppeteer模拟、IP轮换、速率自适应',
      icon: Bot,
      color: 'pink'
    },
    {
      title: '滑点优化',
      description: '1inch/Paraswap聚合器、大额订单拆分',
      icon: Target,
      color: 'indigo'
    }
  ]

  return (
    <div className="space-y-8 fade-in">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">
          钱包管理 & 流动性控制
        </h1>
        <p className="text-white/60 text-lg">
          抗MEV版本 • 全新升级 • 功能测试
        </p>
      </div>

      {/* 主要功能卡片 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {features.map((feature, index) => (
          <div key={index} className="card p-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className={`flex items-center justify-center w-16 h-16 bg-${feature.color}-500/20 rounded-xl`}>
                <feature.icon className={`w-8 h-8 text-${feature.color}-400`} />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">{feature.title}</h3>
                <p className="text-white/60 mt-1">{feature.description}</p>
              </div>
            </div>

            {/* API测试 */}
            <div className="space-y-3 mb-6">
              <h4 className="text-white font-medium">API测试:</h4>
              {feature.apis.map((api, apiIndex) => (
                <div key={apiIndex} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                  <span className="text-white/70 font-mono text-sm">{api.endpoint}</span>
                  <div className="flex items-center space-x-2">
                    {testResults[api.name] && (
                      <span className={`text-xs px-2 py-1 rounded ${
                        testResults[api.name].success 
                          ? 'bg-green-500/20 text-green-400' 
                          : 'bg-red-500/20 text-red-400'
                      }`}>
                        {testResults[api.name].success ? '成功' : '失败'}
                      </span>
                    )}
                    <button
                      onClick={() => testAPI(api.endpoint, api.name)}
                      className="btn-secondary text-xs px-3 py-1"
                    >
                      测试
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* 访问按钮 */}
            <Link
              to={feature.path}
              className={`btn-primary w-full flex items-center justify-center space-x-2 bg-${feature.color}-500/20 border-${feature.color}-500/30 text-${feature.color}-400 hover:bg-${feature.color}-500/30`}
            >
              <span>访问 {feature.title}</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        ))}
      </div>

      {/* 子功能展示 */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6 text-center">核心功能特性</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {subFeatures.map((feature, index) => (
            <div key={index} className="card p-6 text-center">
              <div className={`flex items-center justify-center w-12 h-12 bg-${feature.color}-500/20 rounded-lg mx-auto mb-4`}>
                <feature.icon className={`w-6 h-6 text-${feature.color}-400`} />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-white/60 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* 性能指标 */}
      <div className="card p-8">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">性能目标</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-green-500/20 rounded-xl mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
            <div className="text-3xl font-bold text-green-400 mb-2">&gt;90%</div>
            <div className="text-white/60">封禁规避率</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-blue-500/20 rounded-xl mx-auto mb-4">
              <Shield className="w-8 h-8 text-blue-400" />
            </div>
            <div className="text-3xl font-bold text-blue-400 mb-2">&gt;80%</div>
            <div className="text-white/60">MEV损失减少</div>
          </div>
          
          <div className="text-center">
            <div className="flex items-center justify-center w-16 h-16 bg-purple-500/20 rounded-xl mx-auto mb-4">
              <Target className="w-8 h-8 text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-purple-400 mb-2">&lt;0.5%</div>
            <div className="text-white/60">滑点控制</div>
          </div>
        </div>
      </div>

      {/* 技术栈 */}
      <div className="card p-8">
        <h2 className="text-2xl font-bold text-white mb-6 text-center">技术栈</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            'Flashbots RPC',
            'Puppeteer',
            '1inch API',
            'Paraswap API',
            'Nansen API',
            'Web3.js',
            'Solana Web3',
            'React + Redux'
          ].map((tech, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-3 text-center">
              <span className="text-white/80 text-sm">{tech}</span>
            </div>
          ))}
        </div>
      </div>

      {/* 测试结果 */}
      {Object.keys(testResults).length > 0 && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4">API测试结果</h3>
          <div className="space-y-2">
            {Object.entries(testResults).map(([name, result]) => (
              <div key={name} className="flex items-center justify-between bg-white/5 rounded-lg p-3">
                <span className="text-white font-mono text-sm">{name}</span>
                <span className={`text-sm px-2 py-1 rounded ${
                  result.success 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {result.success ? '✓ 成功' : '✗ 失败'}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default TestNewFeatures
