import React, { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Wallet,
  Plus,
  Settings,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  Shield,
  Activity,
  AlertTriangle,
  CheckCircle,
  Globe,
  Zap,
  Server,
  Users,
  DollarSign,
  BarChart3,
  Target,
  Bot,
  Wifi,
  Database
} from 'lucide-react'

const WalletManagerNew = () => {
  const dispatch = useDispatch()
  const [activeTab, setActiveTab] = useState('overview')
  const [showSettings, setShowSettings] = useState(false)
  const [showPrivateKeys, setShowPrivateKeys] = useState({})
  const [isGenerating, setIsGenerating] = useState(false)
  const [wallets, setWallets] = useState([])
  const [antiBanMetrics, setAntiBanMetrics] = useState({
    evasionRate: 92,
    behaviorDisguise: true,
    currentRate: 2.5,
    platformResponse: '正常',
    adaptiveStatus: '监控中'
  })

  // 模拟数据
  const mockWallets = [
    {
      id: '1',
      address: '0x1234...5678',
      network: 'ethereum',
      balance: { eth: 0.1234, usdc: 150.50 },
      status: 'active',
      privateKey: '0xabcd...efgh'
    },
    {
      id: '2',
      address: 'Sol9876...4321',
      network: 'solana',
      balance: { sol: 1.2345, usdc: 200.75 },
      status: 'active',
      privateKey: 'Sol1234...5678'
    }
  ]

  const handleGenerateWallets = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/wallet/batch-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          count: 50,
          chains: ['ethereum', 'solana']
        })
      })
      const data = await response.json()
      if (data.success) {
        setWallets(data.wallets)
        setAntiBanMetrics(data.antiBanMetrics)
      }
    } catch (error) {
      console.error('生成钱包失败:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const togglePrivateKey = (walletId) => {
    setShowPrivateKeys(prev => ({
      ...prev,
      [walletId]: !prev[walletId]
    }))
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400'
      case 'inactive': return 'bg-gray-500/20 text-gray-400'
      default: return 'bg-blue-500/20 text-blue-400'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return '活跃'
      case 'inactive': return '非活跃'
      default: return '未知'
    }
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题和导航 */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">钱包管理（抗MEV版）</h1>
            <p className="text-white/60 mt-1">批量钱包生成 • 防封禁系统 • 智能资金分配</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>高级设置</span>
            </button>
            <button
              onClick={handleGenerateWallets}
              disabled={isGenerating}
              className="btn-primary flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>生成中...</span>
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  <span>批量生成钱包</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
          {[
            { id: 'overview', label: '总览', icon: BarChart3 },
            { id: 'wallets', label: '钱包列表', icon: Wallet },
            { id: 'antiMev', label: '防MEV系统', icon: Shield },
            { id: 'monitoring', label: '监控面板', icon: Activity }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 核心指标卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">{mockWallets.length}</div>
              <div className="text-white/60 text-sm">总钱包数</div>
              <div className="text-green-400 text-xs mt-1">+50 可生成</div>
            </div>
            
            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-lg mx-auto mb-4">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-green-400">
                {mockWallets.filter(w => w.status === 'active').length}
              </div>
              <div className="text-white/60 text-sm">活跃钱包</div>
              <div className="text-green-400 text-xs mt-1">100% 活跃率</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-500/20 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-yellow-400">
                {mockWallets.reduce((sum, w) => sum + (w.balance.sol || 0), 0).toFixed(1)}
              </div>
              <div className="text-white/60 text-sm">总SOL余额</div>
              <div className="text-yellow-400 text-xs mt-1">≈ $105</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mx-auto mb-4">
                <Shield className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-purple-400">
                {antiBanMetrics.evasionRate}%
              </div>
              <div className="text-white/60 text-sm">防封禁率</div>
              <div className="text-green-400 text-xs mt-1">优秀水平</div>
            </div>
          </div>

          {/* 防封禁系统状态 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Bot className="w-5 h-5 text-purple-400" />
                <span>行为伪装系统</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Puppeteer模拟</span>
                  <span className="text-green-400">运行中</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">人工操作模拟</span>
                  <span className="text-green-400">96.8%相似度</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">随机延迟</span>
                  <span className="text-blue-400">2-8秒</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">浏览器指纹</span>
                  <span className="text-yellow-400">已随机化</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Wifi className="w-5 h-5 text-blue-400" />
                <span>IP轮换系统</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">代理池状态</span>
                  <span className="text-green-400">100个可用</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">轮换频率</span>
                  <span className="text-blue-400">每15分钟</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">地理分布</span>
                  <span className="text-yellow-400">全球覆盖</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">连接质量</span>
                  <span className="text-green-400">优秀</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-yellow-400" />
                <span>速率自适应</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">当前频率</span>
                  <span className="text-yellow-400 font-bold">{antiBanMetrics.currentRate} req/s</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平台响应</span>
                  <span className="text-green-400">{antiBanMetrics.platformResponse}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">自适应状态</span>
                  <span className="text-blue-400">{antiBanMetrics.adaptiveStatus}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">优化建议</span>
                  <span className="text-green-400">无需调整</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 钱包列表标签页 */}
      {activeTab === 'wallets' && (
        <div className="space-y-6">
          <div className="card">
            <div className="p-6 border-b border-white/10">
              <h3 className="text-lg font-semibold text-white">钱包列表</h3>
            </div>
            <div className="divide-y divide-white/10">
              {mockWallets.map((wallet) => (
                <div key={wallet.id} className="p-6 hover:bg-white/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-lg">
                        <Wallet className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-medium">{wallet.address}</span>
                          <button
                            onClick={() => copyToClipboard(wallet.address)}
                            className="text-white/40 hover:text-white/60 transition-colors"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(wallet.status)}`}>
                            {getStatusText(wallet.status)}
                          </span>
                          <span className="text-white/60 text-sm capitalize">{wallet.network}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <div className="text-white font-medium">
                          {wallet.balance.eth && `${wallet.balance.eth} ETH`}
                          {wallet.balance.sol && `${wallet.balance.sol} SOL`}
                        </div>
                        <div className="text-white/60 text-sm">
                          ${wallet.balance.usdc} USDC
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => togglePrivateKey(wallet.id)}
                          className="btn-secondary p-2"
                        >
                          {showPrivateKeys[wallet.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  {showPrivateKeys[wallet.id] && (
                    <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-red-400 text-sm font-medium">私钥 (请妥善保管)</span>
                        <button
                          onClick={() => copyToClipboard(wallet.privateKey)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                      <div className="text-white/80 font-mono text-sm mt-2 break-all">
                        {wallet.privateKey}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 防MEV系统标签页 */}
      {activeTab === 'antiMev' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-400" />
                <span>MEV防护状态</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Flashbots集成</span>
                  <span className="text-green-400 flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>已启用</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">私有内存池</span>
                  <span className="text-green-400 flex items-center space-x-1">
                    <Shield className="w-4 h-4" />
                    <span>保护中</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">MEV损失减少</span>
                  <span className="text-green-400 font-bold">84.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">前置交易检测</span>
                  <span className="text-blue-400">12次拦截</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Bot className="w-5 h-5 text-purple-400" />
                <span>行为伪装系统</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Puppeteer模拟</span>
                  <span className="text-green-400">运行中</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">人工操作模拟</span>
                  <span className="text-green-400">96.8%相似度</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">随机延迟</span>
                  <span className="text-blue-400">2-8秒</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">浏览器指纹</span>
                  <span className="text-yellow-400">已随机化</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-yellow-400" />
                <span>性能统计</span>
              </h3>
              <div className="space-y-4">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="text-green-400 text-sm font-medium">总节省金额</div>
                  <div className="text-green-400 text-xl font-bold">$127,450</div>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="text-blue-400 text-sm font-medium">拦截攻击</div>
                  <div className="text-blue-400 text-xl font-bold">89次</div>
                </div>
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                  <div className="text-purple-400 text-sm font-medium">保护效率</div>
                  <div className="text-purple-400 text-xl font-bold">96.8%</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 监控面板标签页 */}
      {activeTab === 'monitoring' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-400" />
                <span>实时活动监控</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">活跃钱包数</span>
                  <span className="text-green-400 font-bold">{mockWallets.filter(w => w.status === 'active').length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">每分钟交易</span>
                  <span className="text-blue-400">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400">98.7%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均Gas费</span>
                  <span className="text-yellow-400">0.0023 ETH</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Wifi className="w-5 h-5 text-blue-400" />
                <span>网络状态</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">代理连接</span>
                  <span className="text-green-400">稳定</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">延迟</span>
                  <span className="text-green-400">45ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">带宽使用</span>
                  <span className="text-blue-400">2.3 MB/s</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">IP轮换</span>
                  <span className="text-yellow-400">每15分钟</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalletManagerNew
