import React, { useState, useEffect } from 'react'
import {
  Droplets,
  TrendingUp,
  Shield,
  Eye,
  Activity,
  Target,
  RefreshCw,
  Settings,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  BarChart3,
  Users,
  Globe
} from 'lucide-react'

const LiquidityControlNew = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [showSettings, setShowSettings] = useState(false)
  const [mevProtection, setMevProtection] = useState(null)
  const [whaleActivity, setWhaleActivity] = useState([])
  const [slippageOptimization, setSlippageOptimization] = useState(null)

  // 获取MEV防护数据
  const fetchMevProtection = async () => {
    try {
      const response = await fetch('/api/liquidity/mev-protection')
      const data = await response.json()
      if (data.success) {
        setMevProtection(data.mevProtection)
      }
    } catch (error) {
      console.error('获取MEV防护数据失败:', error)
    }
  }

  // 获取鲸鱼活动数据
  const fetchWhaleActivity = async () => {
    try {
      const response = await fetch('/api/liquidity/whale-activity')
      const data = await response.json()
      if (data.success) {
        setWhaleActivity(data.whaleActivity)
      }
    } catch (error) {
      console.error('获取鲸鱼活动数据失败:', error)
    }
  }

  // 获取滑点优化数据
  const fetchSlippageOptimization = async () => {
    try {
      const response = await fetch('/api/liquidity/slippage-optimization')
      const data = await response.json()
      if (data.success) {
        setSlippageOptimization(data.slippageOptimization)
      }
    } catch (error) {
      console.error('获取滑点优化数据失败:', error)
    }
  }

  useEffect(() => {
    fetchMevProtection()
    fetchWhaleActivity()
    fetchSlippageOptimization()
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题和导航 */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">流动性管理（抗MEV版）</h1>
            <p className="text-white/60 mt-1">MEV防护 • 鲸鱼监控 • 滑点优化 • 自动补充</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>高级设置</span>
            </button>
            <button
              onClick={() => {
                fetchMevProtection()
                fetchWhaleActivity()
                fetchSlippageOptimization()
              }}
              className="btn-primary flex items-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>刷新数据</span>
            </button>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
          {[
            { id: 'overview', label: '总览', icon: Activity },
            { id: 'mev', label: 'MEV防护', icon: Shield },
            { id: 'whale', label: '鲸鱼监控', icon: Eye },
            { id: 'optimization', label: '滑点优化', icon: Target }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 内容区域 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 关键指标 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mx-auto mb-4">
                <Droplets className="w-6 h-6 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">$1.2M</div>
              <div className="text-white/60 text-sm">当前TVL</div>
              <div className="text-green-400 text-xs mt-1">+3.2% (24h)</div>
            </div>
            
            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-lg mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-green-400">0.42%</div>
              <div className="text-white/60 text-sm">最优滑点</div>
              <div className="text-green-400 text-xs mt-1">实时计算</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-500/20 rounded-lg mx-auto mb-4">
                <Shield className="w-6 h-6 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-white">高</div>
              <div className="text-white/60 text-sm">MEV保护</div>
              <div className="text-green-400 text-xs mt-1">已启用</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">开启</div>
              <div className="text-white/60 text-sm">自动补充</div>
              <div className="text-green-400 text-xs mt-1">自动管理</div>
            </div>
          </div>

          {/* MEV防护和鲸鱼监控 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* MEV防护统计 */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-blue-400" />
                <span>MEV防护统计</span>
              </h3>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/60 text-sm">MEV损失减少</div>
                  <div className="text-green-400 text-2xl font-bold">
                    {mevProtection?.flashbotsIntegration?.lossReduction || 84.2}%
                  </div>
                  <div className="text-green-400 text-xs">↑ 2.1%</div>
                </div>
                <div className="bg-white/5 rounded-lg p-4 text-center">
                  <div className="text-white/60 text-sm">Flashbots使用率</div>
                  <div className="text-blue-400 text-2xl font-bold">
                    {mevProtection?.flashbotsIntegration?.bundleSuccessRate || 76}%
                  </div>
                  <div className="text-blue-400 text-xs">活跃</div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">私有内存池</span>
                  <span className="text-green-400 text-sm">
                    {mevProtection?.privateMempool?.enabled ? '启用' : '禁用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">交易随机化</span>
                  <span className="text-blue-400 text-sm">
                    {mevProtection?.privateMempool?.transactionRandomization ? '启用' : '禁用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">前置交易检测</span>
                  <span className="text-yellow-400 text-sm">
                    {mevProtection?.privateMempool?.frontrunInterceptions || 0} 次拦截
                  </span>
                </div>
              </div>
            </div>

            {/* 鲸鱼监控 */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Eye className="w-5 h-5 text-purple-400" />
                <span>鲸鱼活动监控</span>
              </h3>

              <div className="space-y-4">
                {whaleActivity.slice(0, 3).map((activity, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-3 border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-mono text-sm">{activity.address}</span>
                      <span className="text-white/50 text-xs">{activity.time}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-white/70 text-sm">{activity.action}</span>
                        <div className="text-blue-400 font-bold">{activity.amount}</div>
                      </div>
                      <div className={`text-sm font-bold ${
                        activity.impact?.startsWith('+') ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {activity.impact}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <button
                onClick={fetchWhaleActivity}
                className="btn-secondary w-full mt-4 flex items-center justify-center space-x-2"
              >
                <RefreshCw className="w-4 h-4" />
                <span>刷新监控</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* MEV防护标签页 */}
      {activeTab === 'mev' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-400" />
                <span>Flashbots集成</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">RPC端点</span>
                  <span className="text-green-400">已连接</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Bundle成功率</span>
                  <span className="text-blue-400 font-bold">
                    {mevProtection?.flashbotsIntegration?.bundleSuccessRate || 94.2}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均优先费</span>
                  <span className="text-yellow-400">
                    {mevProtection?.flashbotsIntegration?.averagePriorityFee || 3.2} Gwei
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">MEV损失减少</span>
                  <span className="text-green-400 font-bold">
                    {mevProtection?.flashbotsIntegration?.lossReduction || 84.2}%
                  </span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Clock className="w-5 h-5 text-purple-400" />
                <span>私有内存池</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">保护状态</span>
                  <span className="text-green-400">
                    {mevProtection?.privateMempool?.enabled ? '启用' : '禁用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">交易随机化</span>
                  <span className="text-blue-400">
                    {mevProtection?.privateMempool?.transactionRandomization ? '活跃' : '禁用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">延迟范围</span>
                  <span className="text-yellow-400">
                    {mevProtection?.privateMempool?.delayRange || '2-8秒'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">前置交易拦截</span>
                  <span className="text-green-400">
                    {mevProtection?.privateMempool?.frontrunInterceptions || 12}次
                  </span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-yellow-400" />
                <span>MEV统计</span>
              </h3>
              <div className="space-y-4">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="text-green-400 text-sm font-medium">总节省金额</div>
                  <div className="text-green-400 text-xl font-bold">
                    {formatCurrency(mevProtection?.statistics?.totalSavings || 127450)}
                  </div>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="text-blue-400 text-sm font-medium">拦截攻击</div>
                  <div className="text-blue-400 text-xl font-bold">
                    {mevProtection?.statistics?.attacksBlocked || 89}次
                  </div>
                </div>
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                  <div className="text-purple-400 text-sm font-medium">保护效率</div>
                  <div className="text-purple-400 text-xl font-bold">
                    {mevProtection?.statistics?.protectionEfficiency || 96.8}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 鲸鱼监控标签页 */}
      {activeTab === 'whale' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Eye className="w-5 h-5 text-purple-400" />
                <span>实时鲸鱼活动</span>
              </h3>
              <div className="space-y-4">
                {whaleActivity.map((activity, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white font-mono text-sm">{activity.address}</span>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs ${
                          activity.risk === 'high' ? 'bg-red-500/20 text-red-400' :
                          activity.risk === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {activity.risk === 'high' ? '高风险' : activity.risk === 'medium' ? '中风险' : '低风险'}
                        </span>
                        <span className="text-white/50 text-xs">{activity.time}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-white/70 text-sm">{activity.action}</span>
                        <div className="text-blue-400 font-bold">{activity.amount}</div>
                      </div>
                      <div className={`text-sm font-bold ${
                        activity.impact?.startsWith('+') ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {activity.impact}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-400" />
                <span>Nansen数据</span>
              </h3>
              <div className="space-y-4">
                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">Smart Money流入</span>
                    <span className="text-green-400 font-bold">$8.2M</span>
                  </div>
                  <div className="text-green-400 text-sm">↑ 15.3% (24h)</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">鲸鱼交易数</span>
                    <span className="text-blue-400 font-bold">23</span>
                  </div>
                  <div className="text-blue-400 text-sm">过去1小时</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">监控地址</span>
                    <span className="text-purple-400 font-bold">247</span>
                  </div>
                  <div className="text-purple-400 text-sm">活跃监控中</div>
                </div>

                <div className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white/70">预警触发</span>
                    <span className="text-yellow-400 font-bold">5</span>
                  </div>
                  <div className="text-yellow-400 text-sm">需要关注</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 滑点优化标签页 */}
      {activeTab === 'optimization' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Target className="w-5 h-5 text-green-400" />
                <span>1inch聚合器</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">连接状态</span>
                  <span className="text-green-400">
                    {slippageOptimization?.oneinch?.status === 'connected' ? '已连接' : '未连接'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">支持链</span>
                  <span className="text-blue-400">
                    {slippageOptimization?.oneinch?.supportedChains || 4}个
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400 font-bold">
                    {slippageOptimization?.oneinch?.successRate || 96.8}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均节省</span>
                  <span className="text-purple-400 font-bold">
                    {slippageOptimization?.oneinch?.averageSavings || 18.5}%
                  </span>
                </div>
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <div className="text-green-400 text-sm font-medium">今日节省</div>
                  <div className="text-green-400 text-xl font-bold">
                    {formatCurrency(slippageOptimization?.oneinch?.todaySavings || 12450)}
                  </div>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Zap className="w-5 h-5 text-blue-400" />
                <span>Paraswap聚合器</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">连接状态</span>
                  <span className="text-green-400">
                    {slippageOptimization?.paraswap?.status === 'connected' ? '已连接' : '未连接'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">支持链</span>
                  <span className="text-blue-400">
                    {slippageOptimization?.paraswap?.supportedChains || 5}个
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400 font-bold">
                    {slippageOptimization?.paraswap?.successRate || 94.2}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均节省</span>
                  <span className="text-purple-400 font-bold">
                    {slippageOptimization?.paraswap?.averageSavings || 16.3}%
                  </span>
                </div>
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                  <div className="text-blue-400 text-sm font-medium">今日节省</div>
                  <div className="text-blue-400 text-xl font-bold">
                    {formatCurrency(slippageOptimization?.paraswap?.todaySavings || 9820)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 大额订单拆分 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Droplets className="w-5 h-5 text-purple-400" />
              <span>大额订单拆分算法</span>
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">活跃拆分订单</div>
                <div className="text-purple-400 text-2xl font-bold">
                  {slippageOptimization?.orderSplitting?.activeSplitOrders || 7}
                </div>
                <div className="text-purple-400 text-xs mt-1">实时执行中</div>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">平均拆分数</div>
                <div className="text-blue-400 text-2xl font-bold">
                  {slippageOptimization?.orderSplitting?.averageSplits || 4.2}
                </div>
                <div className="text-blue-400 text-xs mt-1">每个大额订单</div>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <div className="text-white/70 text-sm mb-2">滑点减少</div>
                <div className="text-green-400 text-2xl font-bold">
                  {slippageOptimization?.orderSplitting?.slippageReduction || 67}%
                </div>
                <div className="text-green-400 text-xs mt-1">相比直接执行</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default LiquidityControlNew
