import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>dingUp,
  Zap,
  Shield,
  BarChart3,
  Settings,
  Plus,
  CheckCircle,
  AlertTriangle,
  Clock,
  Globe,
  Code,
  FileText,
  Activity,
  Target,
  Gauge,
  Cpu,
  Sparkles,
  Brain,
  Filter,
  Search,
  Eye,
  Play,
  Pause,
  Archive,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  RefreshCw,
  DollarSign,
  TrendingDown,
  Users,
  Layers,
  Database,
  Monitor,
  Info,
  XCircle,
  ExternalLink
} from 'lucide-react'
import { ResponsiveContainer, Pie<PERSON>hart as RechartsPieChart, Pie, Cell, Tooltip, AreaChart, Area, XAxis, YAxis, BarChart, Bar, LineChart, Line } from 'recharts'

const StrategyManager = () => {
  // 主要状态管理
  const [activeTab, setActiveTab] = useState('repository') // 默认显示策略仓库
  const [showDeployModal, setShowDeployModal] = useState(false)
  const [selectedChains, setSelectedChains] = useState(['ethereum', 'solana'])
  const [deploymentProgress, setDeploymentProgress] = useState(0)
  const [auditProgress, setAuditProgress] = useState(0)
  const [isDeploying, setIsDeploying] = useState(false)
  const [isAuditing, setIsAuditing] = useState(false)
  const [isGeneratingStrategy, setIsGeneratingStrategy] = useState(false)
  const [selectedHotspot, setSelectedHotspot] = useState(null)
  const [generatedStrategy, setGeneratedStrategy] = useState(null)
  const [showStrategyModal, setShowStrategyModal] = useState(false)

  // 新增状态管理 - 添加默认值保护
  const [strategies, setStrategies] = useState([])
  const [filteredStrategies, setFilteredStrategies] = useState([])
  const [selectedStrategies, setSelectedStrategies] = useState([])
  const [filterOptions, setFilterOptions] = useState({
    status: 'all',
    category: 'all',
    score: 'all',
    timeRange: 'all'
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [showStrategyDetails, setShowStrategyDetails] = useState(false)
  const [selectedStrategy, setSelectedStrategy] = useState(null)
  const [deployedTokens, setDeployedTokens] = useState([])
  const [monitoringData, setMonitoringData] = useState({})
  const [showBatchActions, setShowBatchActions] = useState(false)

  // 查重功能状态
  const [showDuplicateCheck, setShowDuplicateCheck] = useState(false)
  const [duplicateCheckResult, setDuplicateCheckResult] = useState(null)
  const [isCheckingDuplicates, setIsCheckingDuplicates] = useState(false)
  const [selectedStrategyForCheck, setSelectedStrategyForCheck] = useState(null)

  // 策略仓库模拟数据
  const mockStrategies = [
    {
      id: 'str_001',
      name: 'Trump 2024 Victory Token',
      symbol: 'TRUMP24',
      status: 'draft',
      category: 'politics',
      created: '2024-01-15T10:30:00Z',
      hotspot_source: 'Trump 2024 Campaign Launch',
      score: 92.5,
      estimated_roi: 245.8,
      viral_potential: 0.92,
      sentiment: 0.72,
      tokenomics: {
        total_supply: 1000000000,
        allocation: { airdrop: 50, liquidity: 30, marketing: 15, team: 5 },
        burn_rate: 0.02
      },
      deployment_config: {
        platforms: ['pump.fun', 'raydium'],
        chains: ['solana'],
        gas_optimized: true
      },
      risk_level: 'medium',
      compliance_status: 'passed'
    },
    {
      id: 'str_002',
      name: 'XRP ETF Celebration',
      symbol: 'XRPETF',
      status: 'pending',
      category: 'finance',
      created: '2024-01-15T09:45:00Z',
      hotspot_source: 'XRP ETF Approval Speculation',
      score: 89.3,
      estimated_roi: 189.4,
      viral_potential: 0.88,
      sentiment: 0.84,
      tokenomics: {
        total_supply: 500000000,
        allocation: { airdrop: 40, liquidity: 35, marketing: 20, team: 5 },
        burn_rate: 0.015
      },
      deployment_config: {
        platforms: ['uniswap', 'raydium'],
        chains: ['ethereum', 'solana'],
        gas_optimized: true
      },
      risk_level: 'low',
      compliance_status: 'passed'
    },
    {
      id: 'str_003',
      name: 'AI Musk Revolution',
      symbol: 'AIMUSK',
      status: 'deploying',
      category: 'technology',
      created: '2024-01-15T08:20:00Z',
      hotspot_source: 'Musk AI Commentary Storm',
      score: 86.7,
      estimated_roi: 167.3,
      viral_potential: 0.91,
      sentiment: 0.76,
      tokenomics: {
        total_supply: 2000000000,
        allocation: { airdrop: 45, liquidity: 30, marketing: 20, team: 5 },
        burn_rate: 0.025
      },
      deployment_config: {
        platforms: ['pump.fun'],
        chains: ['solana'],
        gas_optimized: true
      },
      risk_level: 'medium',
      compliance_status: 'passed',
      deployment_progress: 65
    },
    {
      id: 'str_004',
      name: 'Pepe Renaissance',
      symbol: 'PEPE2',
      status: 'active',
      category: 'subculture',
      created: '2024-01-14T16:15:00Z',
      deployed: '2024-01-15T02:30:00Z',
      hotspot_source: 'Pepe Community Revival',
      score: 78.9,
      estimated_roi: 134.7,
      viral_potential: 0.85,
      sentiment: 0.91,
      tokenomics: {
        total_supply: ************,
        allocation: { airdrop: 60, liquidity: 25, marketing: 10, team: 5 },
        burn_rate: 0.01
      },
      deployment_config: {
        platforms: ['pump.fun', 'raydium'],
        chains: ['solana'],
        gas_optimized: true
      },
      risk_level: 'low',
      compliance_status: 'passed',
      contract_address: 'So11111111111111111111111111111111111111112',
      current_price: 0.000045,
      market_cap: 18945000,
      holders: 2847,
      liquidity: 125000
    },
    {
      id: 'str_005',
      name: 'K-Pop NFT Mania',
      symbol: 'KPOP',
      status: 'active',
      category: 'subculture',
      created: '2024-01-14T14:20:00Z',
      deployed: '2024-01-14T22:45:00Z',
      hotspot_source: 'K-pop NFT Collection Drop',
      score: 82.1,
      estimated_roi: 156.2,
      viral_potential: 0.87,
      sentiment: 0.93,
      tokenomics: {
        total_supply: 777777777,
        allocation: { airdrop: 55, liquidity: 25, marketing: 15, team: 5 },
        burn_rate: 0.02
      },
      deployment_config: {
        platforms: ['uniswap'],
        chains: ['ethereum'],
        gas_optimized: true
      },
      risk_level: 'medium',
      compliance_status: 'passed',
      contract_address: '******************************************',
      current_price: 0.0012,
      market_cap: 933333,
      holders: 1456,
      liquidity: 89000
    }
  ]

  // 已部署代币监控数据
  const mockDeployedTokens = mockStrategies.filter(s => s.status === 'active')

  // 模拟数据
  const gasOptimizationData = [
    { time: '00:00', standard: 45, optimized: 27 },
    { time: '04:00', standard: 38, optimized: 23 },
    { time: '08:00', standard: 52, optimized: 31 },
    { time: '12:00', standard: 48, optimized: 29 },
    { time: '16:00', standard: 41, optimized: 25 },
    { time: '20:00', standard: 35, optimized: 21 }
  ]

  const deploymentStatsData = [
    { name: 'Ethereum', value: 35, color: '#627EEA' },
    { name: 'Solana', value: 25, color: '#9945FF' },
    { name: 'BSC', value: 20, color: '#F3BA2F' },
    { name: 'Polygon', value: 15, color: '#8247E5' },
    { name: 'Arbitrum', value: 5, color: '#28A0F0' }
  ]

  const securityMetrics = {
    vulnerabilities: 0,
    codeCoverage: 94.2,
    auditScore: 98.5,
    certikScore: 96.8
  }

  const deploymentHistory = [
    {
      id: 1,
      name: 'MemeCoin Alpha',
      symbol: 'MCA',
      status: 'success',
      auditScore: '98/100',
      deployedAt: '2024-01-15T10:30:00Z',
      chains: ['ethereum', 'polygon'],
      deployTime: 12,
      gasUsed: 2847392,
      addresses: {
        ethereum: '******************************************',
        polygon: '******************************************'
      }
    },
    {
      id: 2,
      name: 'DeFi Token Beta',
      symbol: 'DTB',
      status: 'success',
      auditScore: '96/100',
      deployedAt: '2024-01-14T15:45:00Z',
      chains: ['solana', 'bsc'],
      deployTime: 8,
      gasUsed: 1923847,
      addresses: {
        solana: 'So11111111111111111111111111111111111111112',
        bsc: '0xabcdefabcdefabcdefabcdefabcdefabcdefabcdef'
      }
    }
  ]

  // 初始化数据
  useEffect(() => {
    setStrategies(mockStrategies)
    setFilteredStrategies(mockStrategies)
    setDeployedTokens(mockDeployedTokens)
  }, [])

  // 筛选策略
  useEffect(() => {
    let filtered = strategies.filter(strategy => {
      // 搜索过滤
      if (searchQuery && !strategy.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !strategy.symbol.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !strategy.hotspot_source.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false
      }

      // 状态过滤
      if (filterOptions.status !== 'all' && strategy.status !== filterOptions.status) {
        return false
      }

      // 类别过滤
      if (filterOptions.category !== 'all' && strategy.category !== filterOptions.category) {
        return false
      }

      // 评分过滤
      if (filterOptions.score !== 'all') {
        if (filterOptions.score === 'high' && strategy.score < 85) return false
        if (filterOptions.score === 'medium' && (strategy.score < 70 || strategy.score >= 85)) return false
        if (filterOptions.score === 'low' && strategy.score >= 70) return false
      }

      return true
    })

    setFilteredStrategies(filtered)
  }, [strategies, searchQuery, filterOptions])

  const handleChainToggle = (chain) => {
    setSelectedChains(prev =>
      prev.includes(chain)
        ? prev.filter(c => c !== chain)
        : [...prev, chain]
    )
  }

  // 策略选择管理
  const handleStrategySelect = (strategyId) => {
    setSelectedStrategies(prev =>
      prev.includes(strategyId)
        ? prev.filter(id => id !== strategyId)
        : [...prev, strategyId]
    )
  }

  const handleSelectAll = () => {
    const safeFilteredStrategies = filteredStrategies || []
    const safeSelectedStrategies = selectedStrategies || []
    if (safeSelectedStrategies.length === safeFilteredStrategies.length) {
      setSelectedStrategies([])
    } else {
      setSelectedStrategies(safeFilteredStrategies.map(s => s.id))
    }
  }

  // 策略操作
  const handleViewStrategy = (strategy) => {
    setSelectedStrategy(strategy)
    setShowStrategyDetails(true)
  }

  const handleDeployStrategy = async (strategy) => {
    try {
      setIsDeploying(true)
      setDeploymentProgress(0)

      // 模拟部署过程
      const steps = [
        { progress: 20, message: '准备部署参数...' },
        { progress: 40, message: '生成智能合约...' },
        { progress: 60, message: '安全审计检查...' },
        { progress: 80, message: '部署到区块链...' },
        { progress: 100, message: '部署完成!' }
      ]

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 1000))
        setDeploymentProgress(step.progress)
      }

      // 更新策略状态
      setStrategies(prev => prev.map(s =>
        s.id === strategy.id
          ? { ...s, status: 'active', deployed: new Date().toISOString() }
          : s
      ))

      alert(`${strategy.name} 部署成功！`)
    } catch (error) {
      console.error('部署失败:', error)
      alert('部署失败，请重试')
    } finally {
      setIsDeploying(false)
      setDeploymentProgress(0)
    }
  }

  const handleBatchDeploy = async () => {
    const safeSelectedStrategies = selectedStrategies || []
    if (safeSelectedStrategies.length === 0) {
      alert('请先选择要部署的策略')
      return
    }

    try {
      setIsDeploying(true)
      setDeploymentProgress(0)

      for (let i = 0; i < safeSelectedStrategies.length; i++) {
        const strategyId = safeSelectedStrategies[i]
        const strategy = (strategies || []).find(s => s.id === strategyId)

        // 模拟每个策略的部署
        await new Promise(resolve => setTimeout(resolve, 2000))
        setDeploymentProgress(((i + 1) / safeSelectedStrategies.length) * 100)

        // 更新策略状态
        setStrategies(prev => (prev || []).map(s =>
          s.id === strategyId
            ? { ...s, status: 'active', deployed: new Date().toISOString() }
            : s
        ))
      }

      alert(`成功部署 ${safeSelectedStrategies.length} 个策略！`)
      setSelectedStrategies([])
    } catch (error) {
      console.error('批量部署失败:', error)
      alert('批量部署失败，请重试')
    } finally {
      setIsDeploying(false)
      setDeploymentProgress(0)
    }
  }

  const handleExportStrategies = () => {
    const selectedData = strategies.filter(s => selectedStrategies.includes(s.id))
    const dataStr = JSON.stringify(selectedData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `strategies_${new Date().toISOString().split('T')[0]}.json`
    link.click()
  }

  // 生成热点策略
  const generateHotspotStrategy = async (hotspot) => {
    setIsGeneratingStrategy(true)
    setSelectedHotspot(hotspot)

    try {
      const response = await fetch('http://localhost:8001/prediction/generate-hotspot-strategy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(hotspot)
      })
      const data = await response.json()

      if (data.status === 'success') {
        setGeneratedStrategy(data.strategy)
        setShowStrategyModal(true)
      } else {
        alert('策略生成失败：' + data.message)
      }
    } catch (error) {
      console.error('策略生成失败:', error)
      alert('策略生成失败，请重试')
    } finally {
      setIsGeneratingStrategy(false)
    }
  }

  // 查重功能
  const checkTokenDuplicates = async (strategy) => {
    setIsCheckingDuplicates(true)
    setSelectedStrategyForCheck(strategy)

    try {
      // 模拟查重API调用
      const response = await fetch('/api/strategy/check-duplicates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token_name: strategy.token_info?.name || strategy.name,
          platforms: ['pump.fun', 'raydium']
        })
      })

      if (response.ok) {
        const result = await response.json()
        setDuplicateCheckResult(result)
      } else {
        // 模拟查重结果
        const mockResult = generateMockDuplicateResult(strategy.token_info?.name || strategy.name)
        setDuplicateCheckResult(mockResult)
      }

      setShowDuplicateCheck(true)
    } catch (error) {
      console.error('查重检查失败:', error)
      // 生成模拟结果
      const mockResult = generateMockDuplicateResult(strategy.token_info?.name || strategy.name)
      setDuplicateCheckResult(mockResult)
      setShowDuplicateCheck(true)
    } finally {
      setIsCheckingDuplicates(false)
    }
  }

  // 生成模拟查重结果
  const generateMockDuplicateResult = (tokenName) => {
    const platforms = ['pump.fun', 'raydium']
    const riskScenarios = ['high', 'medium', 'low', 'none']
    const scenario = riskScenarios[Math.floor(Math.random() * riskScenarios.length)]

    const platformResults = platforms.map(platform => {
      if (scenario === 'none') {
        return {
          platform,
          exists: false,
          data: null
        }
      }

      const exists = Math.random() > 0.3 // 70% 概率存在同名代币
      if (!exists) {
        return {
          platform,
          exists: false,
          data: null
        }
      }

      const isDead = scenario === 'low' ? true : Math.random() > 0.6
      const createdDaysAgo = scenario === 'high' ? Math.floor(Math.random() * 7) + 1 : Math.floor(Math.random() * 60) + 7
      const liquidity = isDead ? Math.random() * 0.1 : Math.random() * 100 + 1

      return {
        platform,
        exists: true,
        data: {
          address: '0x' + Math.random().toString(16).substr(2, 40),
          created_at: new Date(Date.now() - createdDaysAgo * 24 * 60 * 60 * 1000).toISOString(),
          is_dead: isDead,
          liquidity: liquidity,
          max_liquidity: liquidity * (1 + Math.random() * 5),
          last_trade: isDead ?
            new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString() :
            new Date(Date.now() - Math.floor(Math.random() * 24) * 60 * 60 * 1000).toISOString(),
          trade_count_24h: isDead ? 0 : Math.floor(Math.random() * 1000),
          holder_count: isDead ? Math.floor(Math.random() * 50) : Math.floor(Math.random() * 2000) + 100,
          price_change_24h: (Math.random() - 0.5) * 100
        }
      }
    })

    // 计算风险评分
    let riskScore = 0
    const warnings = []
    const suggestions = []

    platformResults.forEach(result => {
      if (result.exists) {
        riskScore += 30 // 基础风险

        if (!result.data.is_dead) {
          riskScore += 50 // 活跃代币高风险
          warnings.push(`${result.platform}上存在活跃同名代币`)
          suggestions.push({
            action: 'rename',
            label: '修改代币名称',
            priority: 'high'
          })
        } else {
          riskScore += 10 // 死亡代币低风险
          warnings.push(`${result.platform}上存在已死亡同名代币`)
        }

        if (result.data.liquidity > 50) {
          riskScore += 40 // 高流动性风险
          warnings.push(`同名代币流动性较高 (${result.data.liquidity.toFixed(2)} SOL)`)
        }

        const createdDaysAgo = Math.floor((Date.now() - new Date(result.data.created_at).getTime()) / (1000 * 60 * 60 * 24))
        if (createdDaysAgo < 7) {
          riskScore += 30 // 近期创建风险
          warnings.push(`同名代币在${createdDaysAgo}天前创建`)
        }
      }
    })

    // 确定风险级别
    let riskLevel = 'LOW'
    if (riskScore >= 70) {
      riskLevel = 'HIGH'
      suggestions.push({
        action: 'cancel',
        label: '取消部署',
        priority: 'high'
      })
    } else if (riskScore >= 40) {
      riskLevel = 'MEDIUM'
      suggestions.push({
        action: 'modify',
        label: '添加版本后缀',
        priority: 'medium'
      })
    }

    if (suggestions.length === 0) {
      suggestions.push({
        action: 'proceed',
        label: '继续部署',
        priority: 'low'
      })
    }

    return {
      token_name: tokenName,
      risk_level: riskLevel,
      risk_score: riskScore,
      platform_results: platformResults,
      warnings,
      suggestions,
      checked_at: new Date().toISOString()
    }
  }

  // 一键部署策略
  const deployStrategy = async (strategy) => {
    try {
      // 先进行查重检查
      await checkTokenDuplicates(strategy)
    } catch (error) {
      console.error('部署前检查失败:', error)
      alert('部署前检查失败，请重试')
    }
  }

  // 强制部署（跳过查重）
  const forceDeployStrategy = (strategy) => {
    setActiveTab('deployment')
    setSelectedStrategy(strategy)
    setShowDuplicateCheck(false)
  }

  // 获取预测概率颜色
  const getProbabilityColor = (prob) => {
    if (prob >= 0.8) return 'text-red-400'
    if (prob >= 0.6) return 'text-yellow-400'
    return 'text-green-400'
  }

  // 获取风险级别颜色
  const getRiskLevelColor = (level) => {
    switch (level) {
      case 'HIGH': return 'bg-red-100 border-red-500 text-red-700'
      case 'MEDIUM': return 'bg-yellow-100 border-yellow-500 text-yellow-700'
      case 'LOW': return 'bg-green-100 border-green-500 text-green-700'
      default: return 'bg-gray-100 border-gray-500 text-gray-700'
    }
  }

  // 获取风险级别徽章颜色
  const getRiskBadgeColor = (level) => {
    switch (level) {
      case 'HIGH': return 'bg-red-500 text-white'
      case 'MEDIUM': return 'bg-yellow-500 text-black'
      case 'LOW': return 'bg-green-500 text-white'
      default: return 'bg-gray-500 text-white'
    }
  }

  // 处理建议操作
  const handleSuggestion = (action, strategy) => {
    switch (action) {
      case 'rename':
        const newName = prompt('请输入新的代币名称:', strategy.token_info?.name || strategy.name)
        if (newName && newName.trim()) {
          // 更新策略名称
          alert(`代币名称已修改为: ${newName}`)
          setShowDuplicateCheck(false)
        }
        break
      case 'modify':
        const suffix = prompt('请输入版本后缀 (如: V2, Pro, 2024):', 'V2')
        if (suffix && suffix.trim()) {
          const newNameWithSuffix = `${strategy.token_info?.name || strategy.name}${suffix}`
          alert(`代币名称已修改为: ${newNameWithSuffix}`)
          setShowDuplicateCheck(false)
        }
        break
      case 'proceed':
      case 'force':
        forceDeployStrategy(strategy)
        break
      case 'cancel':
        setShowDuplicateCheck(false)
        break
      default:
        break
    }
  }

  const handleDeployToken = async () => {
    setIsDeploying(true)
    setDeploymentProgress(0)

    // 模拟部署过程
    const steps = [
      { progress: 20, message: '生成智能合约...' },
      { progress: 40, message: '安全审计中...' },
      { progress: 60, message: 'Gas优化...' },
      { progress: 80, message: '多链部署...' },
      { progress: 100, message: '部署完成!' }
    ]

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setDeploymentProgress(step.progress)
    }

    setIsDeploying(false)
    setShowDeployModal(false)
  }

  const handleRunAudit = async () => {
    setIsAuditing(true)
    setAuditProgress(0)

    // 模拟审计过程
    const steps = [25, 50, 75, 100]
    for (const progress of steps) {
      await new Promise(resolve => setTimeout(resolve, 800))
      setAuditProgress(progress)
    }

    setIsAuditing(false)
  }

  const handlePumpFunDeploy = async () => {
    setIsDeploying(true)
    setDeploymentProgress(0)

    try {
      // 获取表单数据
      const config = {
        tokenName: "MemeMaster Token",
        tokenSymbol: "MMT",
        totalSupply: 1000000000,
        description: "AI驱动的下一代Meme代币",
        initialPrice: 0.0001,
        marketingBudget: 5,
        autoMarketing: true,
        communityGovernance: true
      }

      // 模拟部署进度
      const steps = [
        { progress: 15, message: '连接Pump.fun API...' },
        { progress: 30, message: '创建代币元数据...' },
        { progress: 50, message: '部署智能合约...' },
        { progress: 70, message: '设置营销参数...' },
        { progress: 85, message: '启动社区功能...' },
        { progress: 100, message: 'Pump.fun部署完成!' }
      ]

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800))
        setDeploymentProgress(step.progress)
      }

      // 模拟API调用成功
      const mockResponse = {
        data: {
          contract_address: '0x' + Math.random().toString(16).substr(2, 40),
          transaction_hash: '0x' + Math.random().toString(16).substr(2, 64)
        }
      }
      console.log('Pump.fun部署成功:', mockResponse.data)

      // 可以在这里添加成功通知
      alert(`部署成功！合约地址: ${mockResponse.data.contract_address}`)

    } catch (error) {
      console.error('Pump.fun部署失败:', error)
      alert('部署失败，请重试')
    } finally {
      setIsDeploying(false)
    }
  }

  const handleRaydiumDeploy = async () => {
    setIsDeploying(true)
    setDeploymentProgress(0)

    try {
      // 获取表单数据
      const config = {
        tokenName: "MemeMaster Token",
        tokenSymbol: "MMT",
        totalSupply: 1000000000,
        initialLiquidity: 10,
        tokenAllocation: 50,
        liquidityLock: true,
        tradingFeeShare: false
      }

      // 模拟部署进度
      const steps = [
        { progress: 20, message: '连接RAYDIUM协议...' },
        { progress: 40, message: '创建流动性池...' },
        { progress: 60, message: '注入初始流动性...' },
        { progress: 80, message: '配置交易参数...' },
        { progress: 100, message: 'RAYDIUM部署完成!' }
      ]

      for (const step of steps) {
        await new Promise(resolve => setTimeout(resolve, 800))
        setDeploymentProgress(step.progress)
      }

      // 模拟API调用成功
      const mockResponse = {
        data: {
          contract_address: '0x' + Math.random().toString(16).substr(2, 40),
          pool_address: '0x' + Math.random().toString(16).substr(2, 40),
          transaction_hash: '0x' + Math.random().toString(16).substr(2, 64)
        }
      }
      console.log('RAYDIUM部署成功:', mockResponse.data)

      // 可以在这里添加成功通知
      alert(`部署成功！合约地址: ${mockResponse.data.contract_address}\n流动性池: ${mockResponse.data.pool_address}`)

    } catch (error) {
      console.error('RAYDIUM部署失败:', error)
      alert('部署失败，请重试')
    } finally {
      setIsDeploying(false)
    }
  }

  return (
    <>
      <div className="min-h-screen relative">
        {/* 背景动画层 */}
        <div className="fixed inset-0 -z-10">
          <div className="absolute inset-0 background-animated"></div>
          <div className="absolute inset-0 bg-gradient-to-tr from-purple-900/30 via-transparent to-blue-900/30" style={{animation: 'subtlePulse 8s ease-in-out infinite'}}></div>
          {/* 浮动装饰元素 */}
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/10 rounded-full blur-xl" style={{animation: 'floatingOrbs 12s ease-in-out infinite'}}></div>
          <div className="absolute top-40 right-32 w-24 h-24 bg-purple-500/10 rounded-full blur-xl" style={{animation: 'floatingOrbs 15s ease-in-out infinite reverse'}}></div>
          <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-indigo-500/10 rounded-full blur-xl" style={{animation: 'floatingOrbs 18s ease-in-out infinite'}}></div>
        </div>

        {/* 主要内容区域 */}
        <div className="page-container p-6">
          <div className="max-w-7xl mx-auto space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center space-x-3">
                <Zap className="w-8 h-8 text-blue-400" />
                <span>策略引擎中心</span>
              </h1>
              <p className="text-white/60 mt-1">
                智能交易策略 • 安全发币操作 • 多链部署 • 自动化审计
              </p>
              <div className="flex items-center space-x-4 mt-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-sm">系统运行正常</span>
                </div>
                <div className="text-white/50 text-sm">
                  成功率: <span className="text-green-400">99.2%</span>
                </div>
                <div className="text-white/50 text-sm">
                  Gas节省: <span className="text-blue-400">42.8%</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <button className="btn-secondary flex items-center space-x-2">
                <Settings className="w-4 h-4" />
                <span>设置</span>
              </button>
              <button className="btn-success flex items-center space-x-2">
                <Zap className="w-4 h-4" />
                <span>安全发币</span>
              </button>
              <button className="btn-primary flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>新建策略</span>
              </button>
            </div>
          </div>

          {/* 导航标签 */}
          <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
            {[
              { id: 'repository', name: '策略仓库', icon: Database, description: '未部署策略管理' },
              { id: 'monitoring', name: '策略监控', icon: Monitor, description: '已部署策略监控' },
              { id: 'deployment', name: '一键部署', icon: Zap, description: '快速部署工作流' },
              { id: 'security', name: '安全审计', icon: Shield, description: '智能合约安全检查' },
              { id: 'optimization', name: 'Gas优化', icon: Zap, description: 'Gas费用优化' },
              { id: 'analytics', name: '数据分析', icon: BarChart3, description: '策略表现分析' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 group relative ${
                  activeTab === tab.id
                    ? 'bg-primary-500 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
                title={tab.description}
              >
                <tab.icon className="w-4 h-4" />
                <span className="hidden lg:inline">{tab.name}</span>

                {/* 工具提示 */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap lg:hidden">
                  {tab.name}
                </div>
              </button>
            ))}
          </div>

          {/* 标签内容 */}
          {/* 策略仓库标签页 */}
          {activeTab === 'repository' && (
            <div className="min-h-[800px] space-y-6">
              {/* 策略仓库头部 */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div>
                  <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
                    <Database className="w-6 h-6 text-blue-400" />
                    <span>策略仓库</span>
                    <span className="text-sm text-white/60">({(filteredStrategies || []).length} 个策略)</span>
                  </h2>
                  <p className="text-white/60 text-sm mt-1">
                    管理所有AI生成但尚未部署的策略，支持筛选、批量操作和一键部署
                  </p>
                </div>

                {/* 操作按钮组 */}
                <div className="flex items-center space-x-3">
                  {(selectedStrategies || []).length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-white/70 text-sm">已选择 {(selectedStrategies || []).length} 个</span>
                      <button
                        onClick={handleBatchDeploy}
                        disabled={isDeploying}
                        className="btn-primary flex items-center space-x-2"
                      >
                        <Zap className="w-4 h-4" />
                        <span>批量部署</span>
                      </button>
                      <button
                        onClick={handleExportStrategies}
                        className="btn-secondary flex items-center space-x-2"
                      >
                        <Download className="w-4 h-4" />
                        <span>导出</span>
                      </button>
                    </div>
                  )}
                  <button className="btn-success flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>新建策略</span>
                  </button>
                </div>
              </div>

              {/* 筛选和搜索栏 */}
              <div className="card p-4">
                <div className="flex flex-col lg:flex-row lg:items-center space-y-4 lg:space-y-0 lg:space-x-4">
                  {/* 搜索框 */}
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
                    <input
                      type="text"
                      placeholder="搜索策略名称、符号或热点来源..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="input-field pl-10 w-full"
                    />
                  </div>

                  {/* 筛选选项 */}
                  <div className="flex items-center space-x-3">
                    <select
                      value={filterOptions.status}
                      onChange={(e) => setFilterOptions(prev => ({ ...prev, status: e.target.value }))}
                      className="input-field"
                    >
                      <option value="all">所有状态</option>
                      <option value="draft">草稿</option>
                      <option value="pending">待部署</option>
                      <option value="deploying">部署中</option>
                    </select>

                    <select
                      value={filterOptions.category}
                      onChange={(e) => setFilterOptions(prev => ({ ...prev, category: e.target.value }))}
                      className="input-field"
                    >
                      <option value="all">所有类别</option>
                      <option value="politics">政治</option>
                      <option value="finance">金融</option>
                      <option value="technology">科技</option>
                      <option value="subculture">亚文化</option>
                    </select>

                    <select
                      value={filterOptions.score}
                      onChange={(e) => setFilterOptions(prev => ({ ...prev, score: e.target.value }))}
                      className="input-field"
                    >
                      <option value="all">所有评分</option>
                      <option value="high">高分 (85+)</option>
                      <option value="medium">中分 (70-84)</option>
                      <option value="low">低分 (&lt;70)</option>
                    </select>

                    <button
                      onClick={handleSelectAll}
                      className="btn-secondary flex items-center space-x-2"
                    >
                      <CheckCircle className="w-4 h-4" />
                      <span>{(selectedStrategies || []).length === (filteredStrategies || []).length ? '取消全选' : '全选'}</span>
                    </button>
                  </div>
                </div>
              </div>

              {/* 策略卡片网格 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {(filteredStrategies || []).map((strategy) => (
                  <div
                    key={strategy.id}
                    className={`card p-6 transition-all duration-200 cursor-pointer border-2 ${
                      selectedStrategies.includes(strategy.id)
                        ? 'border-primary-500 bg-primary-500/10'
                        : 'border-transparent hover:border-white/20'
                    }`}
                    onClick={() => handleStrategySelect(strategy.id)}
                  >
                    {/* 策略头部 */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-white font-semibold text-lg">{strategy.name}</h3>
                          <span className="text-white/60 text-sm">({strategy.symbol})</span>
                        </div>
                        <p className="text-white/70 text-sm line-clamp-2">{strategy.hotspot_source}</p>
                      </div>

                      {/* 选择复选框 */}
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={selectedStrategies.includes(strategy.id)}
                          onChange={() => handleStrategySelect(strategy.id)}
                          className="w-4 h-4"
                          onClick={(e) => e.stopPropagation()}
                        />
                      </div>
                    </div>

                    {/* 状态和评分 */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 rounded-full ${
                          strategy.status === 'draft' ? 'bg-gray-400' :
                          strategy.status === 'pending' ? 'bg-yellow-400' :
                          strategy.status === 'deploying' ? 'bg-blue-400' :
                          'bg-green-400'
                        }`}></div>
                        <span className="text-white/70 text-sm capitalize">{strategy.status}</span>
                      </div>

                      <div className="text-right">
                        <div className={`text-lg font-bold ${
                          strategy.score >= 85 ? 'text-red-400' :
                          strategy.score >= 70 ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {strategy.score}
                        </div>
                        <div className="text-white/50 text-xs">热度评分</div>
                      </div>
                    </div>

                    {/* 关键指标 */}
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center">
                        <div className="text-white/60 text-xs">预期ROI</div>
                        <div className="text-green-400 font-semibold">+{strategy.estimated_roi}%</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white/60 text-xs">病毒潜力</div>
                        <div className="text-purple-400 font-semibold">{(strategy.viral_potential * 100).toFixed(0)}%</div>
                      </div>
                      <div className="text-center">
                        <div className="text-white/60 text-xs">情绪指数</div>
                        <div className="text-blue-400 font-semibold">{(strategy.sentiment * 100).toFixed(0)}%</div>
                      </div>
                    </div>

                    {/* 类别标签 */}
                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-2 py-1 rounded text-xs border ${
                        strategy.category === 'politics' ? 'text-red-400 bg-red-500/20 border-red-500/30' :
                        strategy.category === 'finance' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                        strategy.category === 'technology' ? 'text-blue-400 bg-blue-500/20 border-blue-500/30' :
                        'text-purple-400 bg-purple-500/20 border-purple-500/30'
                      }`}>
                        {strategy.category}
                      </span>

                      <div className="text-white/50 text-xs">
                        {new Date(strategy.created).toLocaleDateString()}
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleViewStrategy(strategy)
                        }}
                        className="flex-1 btn-secondary text-sm py-2 flex items-center justify-center space-x-1"
                      >
                        <Eye className="w-3 h-3" />
                        <span>详情</span>
                      </button>

                      {strategy.status !== 'deploying' && strategy.status !== 'active' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deployStrategy(strategy)
                          }}
                          disabled={isDeploying || isCheckingDuplicates}
                          className="flex-1 btn-primary text-sm py-2 flex items-center justify-center space-x-1"
                        >
                          {isCheckingDuplicates && selectedStrategyForCheck?.id === strategy.id ? (
                            <>
                              <Activity className="w-3 h-3 animate-pulse" />
                              <span>查重中</span>
                            </>
                          ) : (
                            <>
                              <Zap className="w-3 h-3" />
                              <span>部署</span>
                            </>
                          )}
                        </button>
                      )}

                      {/* 查重按钮 */}
                      {strategy.status !== 'deploying' && strategy.status !== 'active' && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            checkTokenDuplicates(strategy)
                          }}
                          disabled={isCheckingDuplicates}
                          className="btn-secondary text-sm py-2 px-3 flex items-center justify-center"
                          title="查重检查"
                        >
                          <Shield className="w-3 h-3" />
                        </button>
                      )}

                      {strategy.status === 'deploying' && (
                        <div className="flex-1 bg-blue-500/20 text-blue-400 text-sm py-2 px-3 rounded text-center">
                          部署中 {strategy.deployment_progress}%
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* 空状态 */}
              {(filteredStrategies || []).length === 0 && (
                <div className="card p-12 text-center">
                  <Database className="w-16 h-16 text-white/30 mx-auto mb-4" />
                  <h3 className="text-white/70 text-lg mb-2">暂无策略</h3>
                  <p className="text-white/50 mb-6">
                    {searchQuery || Object.values(filterOptions).some(v => v !== 'all')
                      ? '没有找到符合条件的策略，请调整筛选条件'
                      : '还没有生成任何策略，点击上方按钮开始创建'}
                  </p>
                  <button className="btn-primary flex items-center space-x-2 mx-auto">
                    <Plus className="w-4 h-4" />
                    <span>创建第一个策略</span>
                  </button>
                </div>
              )}

              {/* 热点预测策略模块 */}
              <div className="card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h4 className="text-white font-medium flex items-center space-x-2">
                    <Brain className="w-5 h-5 text-purple-400" />
                    <span>热点预测策略</span>
                    <span className="text-sm text-white/60">(来自热点预测模块)</span>
                  </h4>
                  <div className="text-white/60 text-sm">
                    基于热点预测模块生成的AI策略
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                  {[
                    {
                      id: "hotspot_strategy_001",
                      hotspot_id: "hot_001",
                      hotspot_keyword: "Trump 2024",
                      token_info: {
                        name: "TRUMPWIN",
                        symbol: "TWIN",
                        description: "基于'Trump 2024'热点的政治改革主题代币",
                        total_supply: 1500000000,
                        initial_price: 0.0015
                      },
                      success_metrics: {
                        target_market_cap: "$1200K",
                        expected_roi: "500-1200%",
                        success_probability: "92%",
                        risk_level: "中等"
                      },
                      deployment: {
                        recommended_platform: "pump.fun",
                        estimated_time: "3分钟",
                        gas_optimization: "42% 节省"
                      },
                      generated_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
                      expires_at: new Date(Date.now() + 90 * 60 * 1000).toISOString(),
                      status: "ready",
                      category: "politics"
                    },
                    {
                      id: "hotspot_strategy_002",
                      hotspot_id: "hot_002",
                      hotspot_keyword: "AI Revolution",
                      token_info: {
                        name: "AIREVOLUTION",
                        symbol: "AIREV",
                        description: "基于'AI Revolution'热点的人工智能革命主题代币",
                        total_supply: 4500000000,
                        initial_price: 0.0006
                      },
                      success_metrics: {
                        target_market_cap: "$800K",
                        expected_roi: "300-800%",
                        success_probability: "87%",
                        risk_level: "低"
                      },
                      deployment: {
                        recommended_platform: "raydium",
                        estimated_time: "4分钟",
                        gas_optimization: "38% 节省"
                      },
                      generated_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
                      expires_at: new Date(Date.now() + 19 * 60 * 60 * 1000).toISOString(),
                      status: "ready",
                      category: "technology"
                    },
                    {
                      id: "hotspot_strategy_003",
                      hotspot_id: "hot_003",
                      hotspot_keyword: "Pepe Comeback",
                      token_info: {
                        name: "PEPEBACK",
                        symbol: "PEPEB",
                        description: "基于'Pepe Comeback'热点的网络文化主题代币",
                        total_supply: 8500000000,
                        initial_price: 0.00012
                      },
                      success_metrics: {
                        target_market_cap: "$1500K",
                        expected_roi: "400-1000%",
                        success_probability: "89%",
                        risk_level: "中等"
                      },
                      deployment: {
                        recommended_platform: "pump.fun",
                        estimated_time: "2分钟",
                        gas_optimization: "45% 节省"
                      },
                      generated_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
                      expires_at: new Date(Date.now() + 5.5 * 60 * 60 * 1000).toISOString(),
                      status: "ready",
                      category: "subculture"
                    },
                    {
                      id: "hotspot_strategy_004",
                      hotspot_id: "hot_004",
                      hotspot_keyword: "XRP ETF",
                      token_info: {
                        name: "ETFBULL",
                        symbol: "ETFB",
                        description: "基于'XRP ETF'热点的金融革命主题代币",
                        total_supply: 2200000000,
                        initial_price: 0.0009
                      },
                      success_metrics: {
                        target_market_cap: "$950K",
                        expected_roi: "350-750%",
                        success_probability: "84%",
                        risk_level: "中等"
                      },
                      deployment: {
                        recommended_platform: "raydium",
                        estimated_time: "3分钟",
                        gas_optimization: "40% 节省"
                      },
                      generated_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
                      expires_at: new Date(Date.now() + 60 * 60 * 1000).toISOString(),
                      status: "expiring",
                      category: "finance"
                    }
                  ].map((strategy, index) => {
                    const timeToExpiry = new Date(strategy.expires_at) - new Date();
                    const hoursToExpiry = Math.floor(timeToExpiry / (1000 * 60 * 60));
                    const minutesToExpiry = Math.floor((timeToExpiry % (1000 * 60 * 60)) / (1000 * 60));

                    return (
                      <div key={index} className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 border border-white/10">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h5 className="text-white font-medium text-sm">{strategy.token_info.name}</h5>
                              <span className="text-white/60 text-xs">({strategy.token_info.symbol})</span>
                              <span className={`px-2 py-0.5 rounded text-xs border ${
                                strategy.category === 'politics' ? 'text-red-400 bg-red-500/20 border-red-500/30' :
                                strategy.category === 'technology' ? 'text-blue-400 bg-blue-500/20 border-blue-500/30' :
                                strategy.category === 'finance' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                                strategy.category === 'subculture' ? 'text-purple-400 bg-purple-500/20 border-purple-500/30' :
                                'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
                              }`}>
                                {strategy.category}
                              </span>
                            </div>
                            <p className="text-white/60 text-xs line-clamp-2">{strategy.token_info.description}</p>
                            <div className="text-white/50 text-xs mt-1">
                              热点: {strategy.hotspot_keyword}
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded text-xs border ${
                            strategy.status === 'ready' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                            strategy.status === 'expiring' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                            'text-red-400 bg-red-500/20 border-red-500/30'
                          }`}>
                            {strategy.status === 'ready' ? '就绪' :
                             strategy.status === 'expiring' ? '即将过期' : '已过期'}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 mb-3">
                          <div className="text-center">
                            <div className="text-white/70 text-xs">成功率</div>
                            <div className="text-green-400 text-sm font-medium">
                              {strategy.success_metrics.success_probability}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-white/70 text-xs">预期ROI</div>
                            <div className="text-blue-400 text-sm font-medium">
                              {strategy.success_metrics.expected_roi}
                            </div>
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-white/70">目标市值</span>
                            <span className="text-yellow-400 font-medium">
                              {strategy.success_metrics.target_market_cap}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs mb-1">
                            <span className="text-white/70">推荐平台</span>
                            <span className="text-purple-400 font-medium">
                              {strategy.deployment.recommended_platform}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/70">剩余时间</span>
                            <span className={`font-medium ${
                              hoursToExpiry < 1 ? 'text-red-400' :
                              hoursToExpiry < 6 ? 'text-yellow-400' : 'text-green-400'
                            }`}>
                              {hoursToExpiry > 0 ? `${hoursToExpiry}h ${minutesToExpiry}m` : `${minutesToExpiry}m`}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => {
                              // 先进行查重检查
                              deployStrategy(strategy);
                            }}
                            disabled={isCheckingDuplicates}
                            className="flex-1 btn-primary text-xs py-2 flex items-center justify-center space-x-1"
                          >
                            {isCheckingDuplicates && selectedStrategyForCheck?.id === strategy.id ? (
                              <>
                                <Activity className="w-3 h-3 animate-pulse" />
                                <span>查重中</span>
                              </>
                            ) : (
                              <>
                                <Zap className="w-3 h-3" />
                                <span>一键部署</span>
                              </>
                            )}
                          </button>

                          {/* 查重按钮 */}
                          <button
                            onClick={() => {
                              checkTokenDuplicates(strategy);
                            }}
                            disabled={isCheckingDuplicates}
                            className="btn-secondary text-xs py-2 px-3 flex items-center justify-center"
                            title="查重检查"
                          >
                            <Shield className="w-3 h-3" />
                          </button>

                          <button
                            onClick={() => {
                              // 查看策略详情
                              alert(`策略详情:\n\n代币: ${strategy.token_info.name} (${strategy.token_info.symbol})\n总供应量: ${strategy.token_info.total_supply.toLocaleString()}\n初始价格: $${strategy.token_info.initial_price}\n\n成功指标:\n- 目标市值: ${strategy.success_metrics.target_market_cap}\n- 预期ROI: ${strategy.success_metrics.expected_roi}\n- 成功概率: ${strategy.success_metrics.success_probability}\n- 风险等级: ${strategy.success_metrics.risk_level}\n\n部署信息:\n- 推荐平台: ${strategy.deployment.recommended_platform}\n- 预计时间: ${strategy.deployment.estimated_time}\n- Gas优化: ${strategy.deployment.gas_optimization}`);
                            }}
                            className="btn-secondary text-xs py-2 px-3 flex items-center justify-center"
                          >
                            <Eye className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                  <div className="flex items-start space-x-2">
                    <Info className="w-4 h-4 text-blue-400 mt-0.5" />
                    <div className="flex-1">
                      <div className="text-blue-400 text-sm font-medium">热点预测策略说明</div>
                      <div className="text-white/70 text-xs mt-1">
                        这些策略由热点预测模块的AI引擎自动生成，基于实时热点数据和市场分析。策略具有时效性，建议在过期前及时部署。
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 策略监控标签页 */}
          {activeTab === 'monitoring' && (
            <div className="min-h-[800px] space-y-6">
              {/* 监控概览 */}
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="card p-6">
                  <div className="flex items-center space-x-3">
                    <Activity className="w-8 h-8 text-green-400" />
                    <div>
                      <div className="text-white font-medium">活跃策略</div>
                      <div className="text-green-400 text-2xl font-bold">{(deployedTokens || []).length}</div>
                    </div>
                  </div>
                </div>

                <div className="card p-6">
                  <div className="flex items-center space-x-3">
                    <DollarSign className="w-8 h-8 text-blue-400" />
                    <div>
                      <div className="text-white font-medium">总市值</div>
                      <div className="text-blue-400 text-2xl font-bold">
                        ${(deployedTokens.reduce((sum, token) => sum + token.market_cap, 0) / 1000000).toFixed(1)}M
                      </div>
                    </div>
                  </div>
                </div>

                <div className="card p-6">
                  <div className="flex items-center space-x-3">
                    <Users className="w-8 h-8 text-purple-400" />
                    <div>
                      <div className="text-white font-medium">总持币人</div>
                      <div className="text-purple-400 text-2xl font-bold">
                        {deployedTokens.reduce((sum, token) => sum + token.holders, 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="card p-6">
                  <div className="flex items-center space-x-3">
                    <Layers className="w-8 h-8 text-yellow-400" />
                    <div>
                      <div className="text-white font-medium">总流动性</div>
                      <div className="text-yellow-400 text-2xl font-bold">
                        ${(deployedTokens.reduce((sum, token) => sum + token.liquidity, 0) / 1000).toFixed(0)}K
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 已部署策略监控 */}
              <div className="card p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
                    <Monitor className="w-5 h-5 text-blue-400" />
                    <span>已部署策略实时监控</span>
                  </h3>
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400 text-sm">实时更新</span>
                    </div>
                    <button className="btn-secondary flex items-center space-x-2">
                      <RefreshCw className="w-4 h-4" />
                      <span>刷新</span>
                    </button>
                  </div>
                </div>

                <div className="space-y-4">
                  {(deployedTokens || []).map((token) => (
                    <div key={token.id} className="bg-white/5 rounded-lg p-6 border border-white/10">
                      {/* 代币头部信息 */}
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h4 className="text-white font-semibold text-lg flex items-center space-x-2">
                            <span>{token.name}</span>
                            <span className="text-white/60">({token.symbol})</span>
                          </h4>
                          <p className="text-white/60 text-sm">{token.hotspot_source}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="text-white/50 text-xs">
                              部署时间: {new Date(token.deployed).toLocaleString()}
                            </span>
                            <span className="text-white/50 text-xs">
                              合约: {token.contract_address.slice(0, 8)}...{token.contract_address.slice(-8)}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                          <span className="text-green-400 text-sm">运行中</span>
                        </div>
                      </div>

                      {/* 关键指标网格 */}
                      <div className="grid grid-cols-2 lg:grid-cols-6 gap-4 mb-4">
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">当前价格</div>
                          <div className="text-green-400 font-semibold">${token.current_price}</div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">市值</div>
                          <div className="text-blue-400 font-semibold">
                            ${(token.market_cap / 1000000).toFixed(1)}M
                          </div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">持币人数</div>
                          <div className="text-purple-400 font-semibold">{token.holders.toLocaleString()}</div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">流动性</div>
                          <div className="text-yellow-400 font-semibold">
                            ${(token.liquidity / 1000).toFixed(0)}K
                          </div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">24h涨幅</div>
                          <div className="text-green-400 font-semibold">+{(Math.random() * 50 + 10).toFixed(1)}%</div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-xs">交易量</div>
                          <div className="text-orange-400 font-semibold">
                            ${(Math.random() * 500 + 100).toFixed(0)}K
                          </div>
                        </div>
                      </div>

                      {/* 价格走势图 */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-white/70 text-sm">24小时价格走势</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-green-400 text-sm">+{(Math.random() * 50 + 10).toFixed(1)}%</span>
                          </div>
                        </div>
                        <div className="h-24 bg-white/5 rounded-lg flex items-end justify-between p-2">
                          {Array.from({ length: 24 }, (_, i) => (
                            <div
                              key={i}
                              className="bg-green-400/60 rounded-sm"
                              style={{
                                height: `${Math.random() * 80 + 20}%`,
                                width: '3px'
                              }}
                            ></div>
                          ))}
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleViewStrategy(token)}
                          className="btn-secondary flex items-center space-x-2"
                        >
                          <Eye className="w-4 h-4" />
                          <span>详细监控</span>
                        </button>
                        <button className="btn-primary flex items-center space-x-2">
                          <Plus className="w-4 h-4" />
                          <span>添加流动性</span>
                        </button>
                        <button className="btn-warning flex items-center space-x-2">
                          <TrendingDown className="w-4 h-4" />
                          <span>启动退出</span>
                        </button>
                        <button className="btn-secondary flex items-center space-x-2">
                          <Pause className="w-4 h-4" />
                          <span>暂停</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 空状态 */}
                {(deployedTokens || []).length === 0 && (
                  <div className="text-center py-12">
                    <Monitor className="w-16 h-16 text-white/30 mx-auto mb-4" />
                    <h3 className="text-white/70 text-lg mb-2">暂无已部署策略</h3>
                    <p className="text-white/50 mb-6">
                      还没有部署任何策略，请先从策略仓库部署策略
                    </p>
                    <button
                      onClick={() => setActiveTab('repository')}
                      className="btn-primary flex items-center space-x-2 mx-auto"
                    >
                      <Database className="w-4 h-4" />
                      <span>前往策略仓库</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'strategies' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {/* 策略概览卡片 */}
              <div className="lg:col-span-2 xl:col-span-2 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  <span>策略表现概览</span>
                </h3>

                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总收益率</div>
                    <div className="text-green-400 text-2xl font-bold">+148.6%</div>
                    <div className="text-green-400 text-xs">↑ 12.3%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">胜率</div>
                    <div className="text-blue-400 text-2xl font-bold">76.8%</div>
                    <div className="text-blue-400 text-xs">↑ 2.1%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">活跃策略</div>
                    <div className="text-purple-400 text-2xl font-bold">4</div>
                    <div className="text-white/50 text-xs">共5个</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总交易</div>
                    <div className="text-yellow-400 text-2xl font-bold">1,113</div>
                    <div className="text-yellow-400 text-xs">+47 今日</div>
                  </div>
                </div>

                {/* 策略列表 */}
                <div className="space-y-3">
                  {[
                    {
                      id: 1,
                      name: '动量追踪策略',
                      description: '基于价格动量和成交量的智能交易',
                      status: 'active',
                      allocation: 35,
                      performance: { roi: 24.8, winRate: 78.5, totalTrades: 342, avgHoldTime: '2.3小时' }
                    },
                    {
                      id: 2,
                      name: '情绪分析策略',
                      description: '社交媒体情绪和新闻分析驱动',
                      status: 'active',
                      allocation: 25,
                      performance: { roi: 18.2, winRate: 72.1, totalTrades: 198, avgHoldTime: '4.1小时' }
                    },
                    {
                      id: 3,
                      name: '鲸鱼跟踪策略',
                      description: '监控大额钱包交易行为',
                      status: 'testing',
                      allocation: 20,
                      performance: { roi: 31.5, winRate: 85.2, totalTrades: 89, avgHoldTime: '6.8小时' }
                    }
                  ].map((strategy) => (
                    <div
                      key={strategy.id}
                      className="bg-white/5 rounded-lg p-4 border border-white/10 transition-all duration-200 cursor-pointer hover:bg-white/10"
                    >
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            strategy.status === 'active' ? 'bg-green-400' :
                            strategy.status === 'paused' ? 'bg-yellow-400' :
                            strategy.status === 'testing' ? 'bg-blue-400' : 'bg-gray-400'
                          }`}></div>
                          <div>
                            <h4 className="text-white font-medium">{strategy.name}</h4>
                            <p className="text-white/60 text-sm">{strategy.description}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 text-lg font-bold">
                            +{strategy.performance.roi}%
                          </div>
                          <div className="text-white/50 text-sm">{strategy.allocation}% 分配</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <div className="text-white/60">胜率</div>
                          <div className="text-blue-400 font-medium">{strategy.performance.winRate}%</div>
                        </div>
                        <div>
                          <div className="text-white/60">交易次数</div>
                          <div className="text-purple-400 font-medium">{strategy.performance.totalTrades}</div>
                        </div>
                        <div>
                          <div className="text-white/60">平均持仓</div>
                          <div className="text-yellow-400 font-medium">{strategy.performance.avgHoldTime}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 侧边栏信息 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4">系统状态</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">系统状态</span>
                    <span className="text-green-400">正常运行</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">活跃策略</span>
                    <span className="text-blue-400">4/5</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">今日收益</span>
                    <span className="text-green-400">+12.3%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">风险等级</span>
                    <span className="text-yellow-400">中等</span>
                  </div>
                </div>
              </div>
            </div>
          )}



          {/* 发币部署标签 */}
          {activeTab === 'deployment' && (
            <div className="min-h-[800px] grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 基础部署配置 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-blue-400" />
                  <span>基础配置</span>
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-white/70 text-sm mb-2">代币名称</label>
                    <input
                      type="text"
                      className="input-field w-full"
                      placeholder="输入代币名称"
                      defaultValue="MemeMaster Token"
                    />
                  </div>

                  <div>
                    <label className="block text-white/70 text-sm mb-2">代币符号</label>
                    <input
                      type="text"
                      className="input-field w-full"
                      placeholder="输入代币符号"
                      defaultValue="MMT"
                    />
                  </div>

                  <div>
                    <label className="block text-white/70 text-sm mb-2">总供应量</label>
                    <input
                      type="number"
                      className="input-field w-full"
                      placeholder="输入总供应量"
                      defaultValue="1000000000"
                    />
                  </div>

                  <div>
                    <label className="block text-white/70 text-sm mb-2">代币描述</label>
                    <textarea
                      className="input-field w-full h-20 resize-none"
                      placeholder="输入代币描述"
                      defaultValue="AI驱动的下一代Meme代币"
                    />
                  </div>

                  <div>
                    <label className="block text-white/70 text-sm mb-2">代币图标URL</label>
                    <input
                      type="url"
                      className="input-field w-full"
                      placeholder="https://example.com/token-icon.png"
                    />
                  </div>
                </div>
              </div>

              {/* Pump.fun部署 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                  <span>Pump.fun部署</span>
                </h3>

                <div className="space-y-4">
                  <div className="bg-purple-500/20 rounded-lg p-4 border border-purple-500/30">
                    <h4 className="text-purple-300 font-medium mb-2">平台特色</h4>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• 无需初始流动性</li>
                      <li>• 自动价格发现</li>
                      <li>• 内置营销工具</li>
                      <li>• 社区驱动增长</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-white/70 text-sm mb-2">初始价格 (SOL)</label>
                      <input
                        type="number"
                        className="input-field w-full"
                        placeholder="0.0001"
                        step="0.0001"
                      />
                    </div>

                    <div>
                      <label className="block text-white/70 text-sm mb-2">营销预算 (SOL)</label>
                      <input
                        type="number"
                        className="input-field w-full"
                        placeholder="5"
                        step="0.1"
                      />
                    </div>

                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="w-4 h-4" defaultChecked />
                      <label className="text-white/70 text-sm">启用自动营销</label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="w-4 h-4" defaultChecked />
                      <label className="text-white/70 text-sm">社区治理模式</label>
                    </div>
                  </div>

                  <button
                    onClick={() => handlePumpFunDeploy()}
                    className="btn-gradient w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500"
                  >
                    <Zap className="w-4 h-4" />
                    <span>部署到Pump.fun</span>
                  </button>
                </div>
              </div>

              {/* RAYDIUM部署 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <div className="w-5 h-5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full"></div>
                  <span>RAYDIUM部署</span>
                </h3>

                <div className="space-y-4">
                  <div className="bg-blue-500/20 rounded-lg p-4 border border-blue-500/30">
                    <h4 className="text-blue-300 font-medium mb-2">平台优势</h4>
                    <ul className="text-white/70 text-sm space-y-1">
                      <li>• 专业DEX集成</li>
                      <li>• 高流动性支持</li>
                      <li>• 低滑点交易</li>
                      <li>• 机构级工具</li>
                    </ul>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <label className="block text-white/70 text-sm mb-2">初始流动性 (SOL)</label>
                      <input
                        type="number"
                        className="input-field w-full"
                        placeholder="10"
                        step="0.1"
                      />
                    </div>

                    <div>
                      <label className="block text-white/70 text-sm mb-2">代币分配比例</label>
                      <input
                        type="number"
                        className="input-field w-full"
                        placeholder="50"
                        step="1"
                        max="100"
                      />
                      <div className="text-white/50 text-xs mt-1">流动性池代币比例 (%)</div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="w-4 h-4" defaultChecked />
                      <label className="text-white/70 text-sm">启用流动性锁定</label>
                    </div>

                    <div className="flex items-center space-x-3">
                      <input type="checkbox" className="w-4 h-4" />
                      <label className="text-white/70 text-sm">开启交易费分成</label>
                    </div>
                  </div>

                  <button
                    onClick={() => handleRaydiumDeploy()}
                    className="btn-gradient w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-blue-500 to-cyan-500"
                  >
                    <Zap className="w-4 h-4" />
                    <span>部署到RAYDIUM</span>
                  </button>
                </div>
              </div>

              {/* 部署统计 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-green-400" />
                  <span>部署统计</span>
                </h3>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总部署数</div>
                    <div className="text-blue-400 text-2xl font-bold">156</div>
                    <div className="text-blue-400 text-xs">+12 本月</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">成功率</div>
                    <div className="text-green-400 text-2xl font-bold">99.2%</div>
                    <div className="text-green-400 text-xs">↑ 0.3%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">平均时间</div>
                    <div className="text-purple-400 text-2xl font-bold">8.5min</div>
                    <div className="text-purple-400 text-xs">↓ 2.1min</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">Gas节省</div>
                    <div className="text-yellow-400 text-2xl font-bold">42.8%</div>
                    <div className="text-yellow-400 text-xs">↑ 5.2%</div>
                  </div>
                </div>

                <div className="h-48">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsPieChart>
                      <Pie
                        data={deploymentStatsData}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}%`}
                      >
                        {(deploymentStatsData || []).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px'
                        }}
                      />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* 平台流动性策略 */}
              <div className="lg:col-span-3 grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Pump.fun流动性策略 */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                    <div className="w-5 h-5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full"></div>
                    <span>Pump.fun流动性策略</span>
                  </h3>

                  <div className="space-y-4">
                    <div className="bg-purple-500/10 rounded-lg p-4 border border-purple-500/20">
                      <h4 className="text-purple-300 font-medium mb-3">社区驱动策略</h4>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-sm">社区活跃度</div>
                          <div className="text-purple-400 text-xl font-bold">87%</div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-sm">病毒传播系数</div>
                          <div className="text-pink-400 text-xl font-bold">2.3x</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">自动营销推广</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                            <span className="text-green-400 text-sm">活跃</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">社交媒体集成</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                            <span className="text-blue-400 text-sm">已连接</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">影响者网络</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                            <span className="text-yellow-400 text-sm">扩展中</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h5 className="text-white font-medium">智能流动性管理</h5>
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white/70 text-sm">动态定价算法</span>
                          <span className="text-green-400 text-sm">优化中</span>
                        </div>
                        <div className="progress-bar">
                          <div className="progress-fill bg-purple-400" style={{width: '78%'}}></div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white/70 text-sm">社区激励池</span>
                          <span className="text-blue-400 text-sm">15.2K SOL</span>
                        </div>
                        <div className="progress-bar">
                          <div className="progress-fill bg-blue-400" style={{width: '65%'}}></div>
                        </div>
                      </div>
                    </div>

                    <button className="btn-gradient w-full bg-gradient-to-r from-purple-500 to-pink-500">
                      优化Pump.fun策略
                    </button>
                  </div>
                </div>

                {/* RAYDIUM流动性策略 */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                    <div className="w-5 h-5 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full"></div>
                    <span>RAYDIUM流动性策略</span>
                  </h3>

                  <div className="space-y-4">
                    <div className="bg-blue-500/10 rounded-lg p-4 border border-blue-500/20">
                      <h4 className="text-blue-300 font-medium mb-3">专业交易策略</h4>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-sm">流动性深度</div>
                          <div className="text-blue-400 text-xl font-bold">$2.4M</div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 text-center">
                          <div className="text-white/60 text-sm">24h交易量</div>
                          <div className="text-cyan-400 text-xl font-bold">$890K</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">自动做市商</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                            <span className="text-green-400 text-sm">运行中</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">套利机器人</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                            <span className="text-blue-400 text-sm">监控中</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-white/70 text-sm">流动性锁定</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-purple-400 rounded-full"></div>
                            <span className="text-purple-400 text-sm">365天</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h5 className="text-white font-medium">高级流动性工具</h5>
                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white/70 text-sm">无常损失保护</span>
                          <span className="text-green-400 text-sm">96.8%</span>
                        </div>
                        <div className="progress-bar">
                          <div className="progress-fill bg-blue-400" style={{width: '97%'}}></div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-3">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-white/70 text-sm">收益农场APY</span>
                          <span className="text-cyan-400 text-sm">234.5%</span>
                        </div>
                        <div className="progress-bar">
                          <div className="progress-fill bg-cyan-400" style={{width: '85%'}}></div>
                        </div>
                      </div>
                    </div>

                    <button className="btn-gradient w-full bg-gradient-to-r from-blue-500 to-cyan-500">
                      优化RAYDIUM策略
                    </button>
                  </div>
                </div>
              </div>

              {/* 平台对比分析 */}
              <div className="lg:col-span-3 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-green-400" />
                  <span>平台流动性对比分析</span>
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 对比表格 */}
                  <div className="lg:col-span-2">
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-white/20">
                            <th className="text-left text-white/70 py-3 px-4">指标</th>
                            <th className="text-center text-purple-400 py-3 px-4">Pump.fun</th>
                            <th className="text-center text-blue-400 py-3 px-4">RAYDIUM</th>
                          </tr>
                        </thead>
                        <tbody className="text-sm">
                          <tr className="border-b border-white/10">
                            <td className="text-white/70 py-3 px-4">初始资金要求</td>
                            <td className="text-center text-green-400 py-3 px-4">低 (0.1 SOL)</td>
                            <td className="text-center text-yellow-400 py-3 px-4">中 (10 SOL)</td>
                          </tr>
                          <tr className="border-b border-white/10">
                            <td className="text-white/70 py-3 px-4">流动性深度</td>
                            <td className="text-center text-yellow-400 py-3 px-4">中等</td>
                            <td className="text-center text-green-400 py-3 px-4">高</td>
                          </tr>
                          <tr className="border-b border-white/10">
                            <td className="text-white/70 py-3 px-4">营销支持</td>
                            <td className="text-center text-green-400 py-3 px-4">强</td>
                            <td className="text-center text-yellow-400 py-3 px-4">中</td>
                          </tr>
                          <tr className="border-b border-white/10">
                            <td className="text-white/70 py-3 px-4">技术门槛</td>
                            <td className="text-center text-green-400 py-3 px-4">低</td>
                            <td className="text-center text-red-400 py-3 px-4">高</td>
                          </tr>
                          <tr className="border-b border-white/10">
                            <td className="text-white/70 py-3 px-4">收益潜力</td>
                            <td className="text-center text-blue-400 py-3 px-4">高波动</td>
                            <td className="text-center text-green-400 py-3 px-4">稳定增长</td>
                          </tr>
                          <tr>
                            <td className="text-white/70 py-3 px-4">适合场景</td>
                            <td className="text-center text-purple-400 py-3 px-4">Meme币</td>
                            <td className="text-center text-blue-400 py-3 px-4">DeFi项目</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* 推荐策略 */}
                  <div className="space-y-4">
                    <h4 className="text-white font-medium">智能推荐</h4>

                    <div className="bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg p-4 border border-purple-500/30">
                      <h5 className="text-purple-300 font-medium mb-2">Pump.fun适合</h5>
                      <ul className="text-white/70 text-sm space-y-1">
                        <li>• 新手友好</li>
                        <li>• 病毒式传播</li>
                        <li>• 社区驱动</li>
                        <li>• 快速启动</li>
                      </ul>
                    </div>

                    <div className="bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-lg p-4 border border-blue-500/30">
                      <h5 className="text-blue-300 font-medium mb-2">RAYDIUM适合</h5>
                      <ul className="text-white/70 text-sm space-y-1">
                        <li>• 专业项目</li>
                        <li>• 长期发展</li>
                        <li>• 机构投资</li>
                        <li>• 稳定收益</li>
                      </ul>
                    </div>

                    <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg p-4 border border-green-500/30">
                      <h5 className="text-green-300 font-medium mb-2">混合策略</h5>
                      <p className="text-white/70 text-sm">
                        先在Pump.fun获得初始关注，再迁移到RAYDIUM获得专业流动性支持
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 部署历史 */}
              <div className="lg:col-span-3 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-yellow-400" />
                  <span>部署历史</span>
                </h3>

                <div className="space-y-3">
                  {(deploymentHistory || []).map((deployment) => (
                    <div key={deployment.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                          <div>
                            <h4 className="text-white font-medium">{deployment.name} ({deployment.symbol})</h4>
                            <p className="text-white/60 text-sm">
                              {new Date(deployment.deployedAt).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 font-bold">审计分数: {deployment.auditScore}</div>
                          <div className="text-white/50 text-sm">部署时间: {deployment.deployTime}分钟</div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className="text-white/60">部署链</div>
                          <div className="text-blue-400">{deployment.chains.join(', ')}</div>
                        </div>
                        <div>
                          <div className="text-white/60">Gas消耗</div>
                          <div className="text-purple-400">{deployment.gasUsed.toLocaleString()}</div>
                        </div>
                        <div>
                          <div className="text-white/60">状态</div>
                          <div className="text-green-400">成功</div>
                        </div>
                        <div>
                          <div className="text-white/60">合约地址</div>
                          <div className="text-yellow-400 truncate">
                            {Object.values(deployment.addresses)[0]}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 安全审计标签 */}
          {activeTab === 'security' && (
            <div className="min-h-[800px] grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 安全评分 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span>安全评分</span>
                </h3>

                <div className="space-y-6">
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-400 mb-2">98.5</div>
                    <div className="text-white/60">综合安全评分</div>
                    <div className="text-green-400 text-sm">优秀</div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white/70">代码覆盖率</span>
                        <span className="text-blue-400">{securityMetrics.codeCoverage}%</span>
                      </div>
                      <div className="progress-bar">
                        <div
                          className="progress-fill bg-blue-400"
                          style={{width: `${securityMetrics.codeCoverage}%`}}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white/70">CertiK评分</span>
                        <span className="text-purple-400">{securityMetrics.certikScore}%</span>
                      </div>
                      <div className="progress-bar">
                        <div
                          className="progress-fill bg-purple-400"
                          style={{width: `${securityMetrics.certikScore}%`}}
                        ></div>
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white/70">漏洞检测</span>
                        <span className="text-green-400">{securityMetrics.vulnerabilities} 个</span>
                      </div>
                      <div className="progress-bar">
                        <div className="progress-fill bg-green-400" style={{width: '100%'}}></div>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={handleRunAudit}
                    disabled={isAuditing}
                    className="btn-success w-full flex items-center justify-center space-x-2"
                  >
                    <Shield className="w-4 h-4" />
                    <span>{isAuditing ? '审计中...' : '运行安全审计'}</span>
                  </button>

                  {isAuditing && (
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-white/70">审计进度</span>
                        <span className="text-yellow-400">{auditProgress}%</span>
                      </div>
                      <div className="progress-bar">
                        <div
                          className="progress-fill bg-yellow-400"
                          style={{width: `${auditProgress}%`}}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 审计工具 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Code className="w-5 h-5 text-blue-400" />
                  <span>审计工具</span>
                </h3>

                <div className="space-y-4">
                  {[
                    { name: 'Slither', status: 'active', score: 96, icon: Code },
                    { name: 'Mythril', status: 'active', score: 94, icon: Shield },
                    { name: 'CertiK', status: 'pending', score: 97, icon: CheckCircle },
                    { name: 'Fuzzing Tests', status: 'active', score: 92, icon: Target }
                  ].map((tool, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <tool.icon className="w-5 h-5 text-blue-400" />
                          <div>
                            <h4 className="text-white font-medium">{tool.name}</h4>
                            <p className="text-white/60 text-sm">
                              {tool.status === 'active' ? '运行中' : '等待中'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-lg font-bold ${
                            tool.score >= 95 ? 'text-green-400' :
                            tool.score >= 90 ? 'text-yellow-400' : 'text-red-400'
                          }`}>
                            {tool.score}%
                          </div>
                          <div className={`w-3 h-3 rounded-full ${
                            tool.status === 'active' ? 'bg-green-400' : 'bg-yellow-400'
                          }`}></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 安全建议 */}
              <div className="lg:col-span-2 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  <span>安全建议</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    {
                      type: 'success',
                      title: '重入攻击防护',
                      description: '已实现ReentrancyGuard保护',
                      icon: CheckCircle,
                      color: 'text-green-400'
                    },
                    {
                      type: 'success',
                      title: '整数溢出检查',
                      description: '使用SafeMath库防止溢出',
                      icon: CheckCircle,
                      color: 'text-green-400'
                    },
                    {
                      type: 'warning',
                      title: '权限管理',
                      description: '建议实现多重签名管理',
                      icon: AlertTriangle,
                      color: 'text-yellow-400'
                    },
                    {
                      type: 'success',
                      title: 'Gas优化',
                      description: '代码已优化，Gas消耗降低42%',
                      icon: CheckCircle,
                      color: 'text-green-400'
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                      <div className="flex items-start space-x-3">
                        <item.icon className={`w-5 h-5 ${item.color} mt-0.5`} />
                        <div>
                          <h4 className="text-white font-medium mb-1">{item.title}</h4>
                          <p className="text-white/60 text-sm">{item.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Gas优化标签 */}
          {activeTab === 'optimization' && (
            <div className="min-h-[800px] space-y-6">
              {/* Gas优化概览统计 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-yellow-400 text-sm font-medium">Gas节省</p>
                      <p className="text-3xl font-bold text-white mt-1">42.8%</p>
                    </div>
                    <div className="p-3 bg-yellow-500/20 rounded-xl">
                      <Zap className="w-8 h-8 text-yellow-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-400 text-sm font-medium">优化次数</p>
                      <p className="text-3xl font-bold text-white mt-1">1,247</p>
                    </div>
                    <div className="p-3 bg-green-500/20 rounded-xl">
                      <Activity className="w-8 h-8 text-green-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-400 text-sm font-medium">节省ETH</p>
                      <p className="text-3xl font-bold text-white mt-1">127.5</p>
                    </div>
                    <div className="p-3 bg-blue-500/20 rounded-xl">
                      <Cpu className="w-8 h-8 text-blue-400" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500/10 to-indigo-500/10 border border-purple-500/20 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-400 text-sm font-medium">效率提升</p>
                      <p className="text-3xl font-bold text-white mt-1">+35%</p>
                    </div>
                    <div className="p-3 bg-purple-500/20 rounded-xl">
                      <TrendingUp className="w-8 h-8 text-purple-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 主要Gas优化内容 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Gas优化图表 */}
                <div className="lg:col-span-2 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span>Gas价格优化趋势</span>
                </h3>

                <div className="h-64 mb-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={gasOptimizationData}>
                      <defs>
                        <linearGradient id="standardGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#EF4444" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#EF4444" stopOpacity={0}/>
                        </linearGradient>
                        <linearGradient id="optimizedGradient" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10B981" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis dataKey="time" stroke="#ffffff60" />
                      <YAxis stroke="#ffffff60" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="standard"
                        stroke="#EF4444"
                        fillOpacity={1}
                        fill="url(#standardGradient)"
                        name="标准Gas"
                      />
                      <Area
                        type="monotone"
                        dataKey="optimized"
                        stroke="#10B981"
                        fillOpacity={1}
                        fill="url(#optimizedGradient)"
                        name="优化Gas"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>

                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">平均节省</div>
                    <div className="text-green-400 text-2xl font-bold">42.8%</div>
                    <div className="text-green-400 text-xs">↑ 5.2%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">最大节省</div>
                    <div className="text-blue-400 text-2xl font-bold">58.3%</div>
                    <div className="text-blue-400 text-xs">新记录</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总节省ETH</div>
                    <div className="text-purple-400 text-2xl font-bold">127.5</div>
                    <div className="text-purple-400 text-xs">本月</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">优化次数</div>
                    <div className="text-yellow-400 text-2xl font-bold">1,247</div>
                    <div className="text-yellow-400 text-xs">+89 今日</div>
                  </div>
                </div>
              </div>

              {/* 优化策略 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Gauge className="w-5 h-5 text-blue-400" />
                  <span>优化策略</span>
                </h3>

                <div className="space-y-4">
                  {[
                    { name: '智能批处理', enabled: true, savings: '35%' },
                    { name: '存储优化', enabled: true, savings: '28%' },
                    { name: '函数内联', enabled: false, savings: '15%' },
                    { name: '循环展开', enabled: true, savings: '22%' }
                  ].map((strategy, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-4 border border-white/10">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            strategy.enabled ? 'bg-green-400' : 'bg-gray-400'
                          }`}></div>
                          <div>
                            <h4 className="text-white font-medium">{strategy.name}</h4>
                            <p className="text-white/60 text-sm">
                              {strategy.enabled ? '已启用' : '已禁用'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-green-400 font-bold">-{strategy.savings}</div>
                          <button className={`text-xs px-2 py-1 rounded ${
                            strategy.enabled
                              ? 'bg-red-500/20 text-red-400'
                              : 'bg-green-500/20 text-green-400'
                          }`}>
                            {strategy.enabled ? '禁用' : '启用'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 实时监控 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Activity className="w-5 h-5 text-green-400" />
                  <span>实时监控</span>
                </h3>

                <div className="space-y-4">
                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70">当前Gas价格</span>
                      <span className="text-blue-400 font-bold">23 Gwei</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-blue-400" style={{width: '45%'}}></div>
                    </div>
                    <div className="text-xs text-white/50 mt-1">较昨日下降 12%</div>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70">网络拥堵度</span>
                      <span className="text-green-400 font-bold">低</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-green-400" style={{width: '25%'}}></div>
                    </div>
                    <div className="text-xs text-white/50 mt-1">最佳部署时机</div>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-white/70">预计等待时间</span>
                      <span className="text-purple-400 font-bold">2.3分钟</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-purple-400" style={{width: '30%'}}></div>
                    </div>
                    <div className="text-xs text-white/50 mt-1">快速确认</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 部署分析标签 */}
          {activeTab === 'analytics' && (
            <div className="min-h-[800px] grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {/* 总体统计 */}
              <div className="xl:col-span-3 card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <BarChart3 className="w-5 h-5 text-blue-400" />
                  <span>部署分析概览</span>
                </h3>

                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总部署数</div>
                    <div className="text-blue-400 text-3xl font-bold">1,247</div>
                    <div className="text-blue-400 text-xs">+89 本周</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">成功率</div>
                    <div className="text-green-400 text-3xl font-bold">99.2%</div>
                    <div className="text-green-400 text-xs">↑ 0.3%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">总价值锁定</div>
                    <div className="text-purple-400 text-3xl font-bold">$2.4M</div>
                    <div className="text-purple-400 text-xs">↑ 15.7%</div>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <div className="text-white/60 text-sm">活跃合约</div>
                    <div className="text-yellow-400 text-3xl font-bold">892</div>
                    <div className="text-yellow-400 text-xs">71.5%</div>
                  </div>
                </div>

                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={[
                      { month: '1月', deployments: 45, success: 44 },
                      { month: '2月', deployments: 52, success: 51 },
                      { month: '3月', deployments: 48, success: 47 },
                      { month: '4月', deployments: 61, success: 60 },
                      { month: '5月', deployments: 55, success: 55 },
                      { month: '6月', deployments: 67, success: 66 }
                    ]}>
                      <XAxis dataKey="month" stroke="#ffffff60" />
                      <YAxis stroke="#ffffff60" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0,0,0,0.8)',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '8px'
                        }}
                      />
                      <Bar dataKey="deployments" fill="#3B82F6" name="总部署" />
                      <Bar dataKey="success" fill="#10B981" name="成功部署" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* 链分布 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Globe className="w-5 h-5 text-green-400" />
                  <span>链分布</span>
                </h3>

                <div className="space-y-3">
                  {(deploymentStatsData || []).map((chain, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{backgroundColor: chain.color}}
                        ></div>
                        <span className="text-white">{chain.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-bold">{chain.value}%</div>
                        <div className="text-white/50 text-xs">
                          {Math.round(1247 * chain.value / 100)} 个
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 性能指标 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <Cpu className="w-5 h-5 text-purple-400" />
                  <span>性能指标</span>
                </h3>

                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-white/70">平均部署时间</span>
                      <span className="text-blue-400">8.5分钟</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-blue-400" style={{width: '75%'}}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-white/70">Gas效率</span>
                      <span className="text-green-400">92.3%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-green-400" style={{width: '92%'}}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-white/70">安全评分</span>
                      <span className="text-purple-400">98.5%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-purple-400" style={{width: '98%'}}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-white/70">用户满意度</span>
                      <span className="text-yellow-400">96.8%</span>
                    </div>
                    <div className="progress-bar">
                      <div className="progress-fill bg-yellow-400" style={{width: '97%'}}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 最新报告 */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-yellow-400" />
                  <span>最新报告</span>
                </h3>

                <div className="space-y-3">
                  {[
                    { name: '月度安全报告', date: '2024-01-15', status: '已完成' },
                    { name: 'Gas优化分析', date: '2024-01-14', status: '已完成' },
                    { name: '性能评估报告', date: '2024-01-13', status: '进行中' },
                    { name: '用户反馈汇总', date: '2024-01-12', status: '已完成' }
                  ].map((report, index) => (
                    <div key={index} className="bg-white/5 rounded-lg p-3 border border-white/10">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-white font-medium text-sm">{report.name}</h4>
                          <p className="text-white/60 text-xs">{report.date}</p>
                        </div>
                        <div className={`text-xs px-2 py-1 rounded ${
                          report.status === '已完成'
                            ? 'bg-green-500/20 text-green-400'
                            : 'bg-yellow-500/20 text-yellow-400'
                        }`}>
                          {report.status}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 部署模态框 */}
          {showDeployModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-xl font-bold text-white mb-4">部署确认</h3>

                {isDeploying ? (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div className="text-white/70 mb-2">部署进度</div>
                      <div className="text-2xl font-bold text-blue-400">{deploymentProgress}%</div>
                    </div>
                    <div className="progress-bar">
                      <div
                        className="progress-fill bg-blue-400"
                        style={{width: `${deploymentProgress}%`}}
                      ></div>
                    </div>
                    <div className="text-center text-white/60">
                      正在部署到 {selectedChains.join(', ')}...
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-white/70">
                      确认部署到以下链：
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {(selectedChains || []).map(chain => (
                        <span key={chain} className="bg-blue-500/20 text-blue-400 px-3 py-1 rounded-full text-sm">
                          {chain}
                        </span>
                      ))}
                    </div>
                    <div className="flex space-x-3">
                      <button
                        onClick={handleDeployToken}
                        className="btn-primary flex-1"
                      >
                        确认部署
                      </button>
                      <button
                        onClick={() => setShowDeployModal(false)}
                        className="btn-secondary flex-1"
                      >
                        取消
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          </div>
        </div>

        {/* 策略详情模态框 */}
        {showStrategyDetails && selectedStrategy && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* 模态框头部 */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <div>
                <h2 className="text-xl font-semibold text-white flex items-center space-x-2">
                  <Eye className="w-6 h-6 text-blue-400" />
                  <span>{selectedStrategy.name} 详情</span>
                </h2>
                <p className="text-white/60 text-sm mt-1">{selectedStrategy.hotspot_source}</p>
              </div>
              <button
                onClick={() => setShowStrategyDetails(false)}
                className="text-white/60 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* 模态框内容 */}
            <div className="p-6 space-y-6">
              {/* 基础信息 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="card p-4">
                  <h3 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <FileText className="w-5 h-5 text-blue-400" />
                    <span>基础信息</span>
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/70">代币名称:</span>
                      <span className="text-white">{selectedStrategy.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">代币符号:</span>
                      <span className="text-white">{selectedStrategy.symbol}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">状态:</span>
                      <span className={`capitalize ${
                        selectedStrategy.status === 'active' ? 'text-green-400' :
                        selectedStrategy.status === 'deploying' ? 'text-blue-400' :
                        selectedStrategy.status === 'pending' ? 'text-yellow-400' :
                        'text-gray-400'
                      }`}>
                        {selectedStrategy.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">类别:</span>
                      <span className="text-white capitalize">{selectedStrategy.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">创建时间:</span>
                      <span className="text-white">{new Date(selectedStrategy.created).toLocaleString()}</span>
                    </div>
                    {selectedStrategy.deployed && (
                      <div className="flex justify-between">
                        <span className="text-white/70">部署时间:</span>
                        <span className="text-white">{new Date(selectedStrategy.deployed).toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="card p-4">
                  <h3 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <Target className="w-5 h-5 text-green-400" />
                    <span>关键指标</span>
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-white/70">热度评分:</span>
                      <span className={`font-semibold ${
                        selectedStrategy.score >= 85 ? 'text-red-400' :
                        selectedStrategy.score >= 70 ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {selectedStrategy.score}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">预期ROI:</span>
                      <span className="text-green-400 font-semibold">+{selectedStrategy.estimated_roi}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">病毒潜力:</span>
                      <span className="text-purple-400 font-semibold">{(selectedStrategy.viral_potential * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">情绪指数:</span>
                      <span className="text-blue-400 font-semibold">{(selectedStrategy.sentiment * 100).toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">风险等级:</span>
                      <span className={`capitalize font-semibold ${
                        selectedStrategy.risk_level === 'low' ? 'text-green-400' :
                        selectedStrategy.risk_level === 'medium' ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {selectedStrategy.risk_level}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">合规状态:</span>
                      <span className="text-green-400 font-semibold capitalize">{selectedStrategy.compliance_status}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 经济模型 */}
              <div className="card p-4">
                <h3 className="text-white font-medium mb-4 flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-yellow-400" />
                  <span>经济模型</span>
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-white/80 text-sm mb-3">代币分配</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-white/70">空投:</span>
                        <span className="text-white">{selectedStrategy.tokenomics.allocation.airdrop}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">流动性:</span>
                        <span className="text-white">{selectedStrategy.tokenomics.allocation.liquidity}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">营销:</span>
                        <span className="text-white">{selectedStrategy.tokenomics.allocation.marketing}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">团队:</span>
                        <span className="text-white">{selectedStrategy.tokenomics.allocation.team}%</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-white/80 text-sm mb-3">供应参数</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-white/70">总供应量:</span>
                        <span className="text-white">{selectedStrategy.tokenomics.total_supply.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/70">燃烧率:</span>
                        <span className="text-white">{(selectedStrategy.tokenomics.burn_rate * 100).toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* 部署配置 */}
              <div className="card p-4">
                <h3 className="text-white font-medium mb-4 flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-blue-400" />
                  <span>部署配置</span>
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <h4 className="text-white/80 text-sm mb-2">支持平台</h4>
                    <div className="flex flex-wrap gap-2">
                      {(selectedStrategy?.deployment_config?.platforms || []).map((platform) => (
                        <span key={platform} className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">
                          {platform}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-white/80 text-sm mb-2">支持链</h4>
                    <div className="flex flex-wrap gap-2">
                      {(selectedStrategy?.deployment_config?.chains || []).map((chain) => (
                        <span key={chain} className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs capitalize">
                          {chain}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-white/80 text-sm mb-2">Gas优化</h4>
                    <span className={`px-2 py-1 rounded text-xs ${
                      selectedStrategy.deployment_config.gas_optimized
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}>
                      {selectedStrategy.deployment_config.gas_optimized ? '已启用' : '未启用'}
                    </span>
                  </div>
                </div>
              </div>

              {/* 如果是已部署的策略，显示实时数据 */}
              {selectedStrategy.status === 'active' && (
                <div className="card p-4">
                  <h3 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <Activity className="w-5 h-5 text-green-400" />
                    <span>实时数据</span>
                  </h3>
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <div className="text-white/60 text-xs">当前价格</div>
                      <div className="text-green-400 font-semibold">${selectedStrategy.current_price}</div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <div className="text-white/60 text-xs">市值</div>
                      <div className="text-blue-400 font-semibold">
                        ${(selectedStrategy.market_cap / 1000000).toFixed(1)}M
                      </div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <div className="text-white/60 text-xs">持币人数</div>
                      <div className="text-purple-400 font-semibold">{selectedStrategy.holders?.toLocaleString()}</div>
                    </div>
                    <div className="bg-white/5 rounded-lg p-3 text-center">
                      <div className="text-white/60 text-xs">流动性</div>
                      <div className="text-yellow-400 font-semibold">
                        ${(selectedStrategy.liquidity / 1000).toFixed(0)}K
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作按钮 */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t border-white/10">
                <button
                  onClick={() => setShowStrategyDetails(false)}
                  className="btn-secondary"
                >
                  关闭
                </button>
                {selectedStrategy.status !== 'active' && selectedStrategy.status !== 'deploying' && (
                  <button
                    onClick={() => {
                      setShowStrategyDetails(false)
                      handleDeployStrategy(selectedStrategy)
                    }}
                    className="btn-primary flex items-center space-x-2"
                  >
                    <Zap className="w-4 h-4" />
                    <span>立即部署</span>
                  </button>
                )}
                {selectedStrategy.status === 'active' && (
                  <button className="btn-success flex items-center space-x-2">
                    <Monitor className="w-4 h-4" />
                    <span>进入监控</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}

      {/* 部署进度模态框 */}
      {isDeploying && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-xl p-8 max-w-md w-full">
            <div className="text-center">
              <Zap className="w-16 h-16 text-blue-400 mx-auto mb-4 animate-pulse" />
              <h3 className="text-white text-lg font-semibold mb-2">正在部署策略</h3>
              <p className="text-white/60 text-sm mb-6">请稍候，部署过程可能需要几分钟...</p>

              <div className="w-full bg-white/20 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${deploymentProgress}%` }}
                ></div>
              </div>

              <div className="text-blue-400 text-sm font-medium">
                {deploymentProgress}% 完成
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 策略生成结果模态框 */}
      {showStrategyModal && generatedStrategy && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white">AI策略生成结果</h3>
                <button
                  onClick={() => setShowStrategyModal(false)}
                  className="text-white/60 hover:text-white"
                >
                  ✕
                </button>
              </div>

              <div className="space-y-6">
                {/* 代币信息 */}
                <div className="card p-6">
                  <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <Sparkles className="w-5 h-5 text-yellow-400" />
                    <span>代币信息</span>
                  </h4>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-white/70 text-sm">代币名称</div>
                      <div className="text-white font-medium">{generatedStrategy.token_info?.name}</div>
                    </div>
                    <div>
                      <div className="text-white/70 text-sm">代币符号</div>
                      <div className="text-white font-medium">{generatedStrategy.token_info?.symbol}</div>
                    </div>
                    <div>
                      <div className="text-white/70 text-sm">总供应量</div>
                      <div className="text-white font-medium">{generatedStrategy.token_info?.total_supply?.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-white/70 text-sm">初始价格</div>
                      <div className="text-white font-medium">${generatedStrategy.token_info?.initial_price}</div>
                    </div>
                  </div>
                </div>

                {/* 成功指标 */}
                <div className="card p-6">
                  <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                    <Target className="w-5 h-5 text-green-400" />
                    <span>成功指标</span>
                  </h4>

                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <div className="text-white/70 text-sm">预期ROI</div>
                      <div className="text-green-400 font-bold text-lg">{generatedStrategy.success_metrics?.expected_roi}</div>
                    </div>
                    <div>
                      <div className="text-white/70 text-sm">成功概率</div>
                      <div className="text-blue-400 font-bold text-lg">{generatedStrategy.success_metrics?.success_probability}</div>
                    </div>
                    <div>
                      <div className="text-white/70 text-sm">风险等级</div>
                      <div className="text-yellow-400 font-bold text-lg">{generatedStrategy.success_metrics?.risk_level}</div>
                    </div>
                  </div>
                </div>

                {/* 部署策略 */}
                {generatedStrategy.deployment_strategy && (
                  <div className="card p-6">
                    <h4 className="text-white font-medium mb-4 flex items-center space-x-2">
                      <Zap className="w-5 h-5 text-purple-400" />
                      <span>部署策略</span>
                    </h4>

                    <div className="space-y-3">
                      <div>
                        <div className="text-white/70 text-sm">推荐平台</div>
                        <div className="text-white">{generatedStrategy.deployment_strategy.platform}</div>
                      </div>
                      <div>
                        <div className="text-white/70 text-sm">初始流动性</div>
                        <div className="text-white">{generatedStrategy.deployment_strategy.initial_liquidity}</div>
                      </div>
                      <div>
                        <div className="text-white/70 text-sm">营销策略</div>
                        <div className="text-white">{generatedStrategy.deployment_strategy.marketing_approach}</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="flex space-x-4">
                  <button
                    onClick={() => deployStrategy(generatedStrategy)}
                    className="flex-1 btn-primary py-3 flex items-center justify-center space-x-2"
                  >
                    <Zap className="w-5 h-5" />
                    <span>一键部署</span>
                  </button>

                  <button
                    onClick={() => setShowStrategyModal(false)}
                    className="flex-1 btn-secondary py-3"
                  >
                    稍后处理
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 查重报告弹窗 */}
      {showDuplicateCheck && duplicateCheckResult && selectedStrategyForCheck && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-white flex items-center space-x-2">
                  <Shield className="w-6 h-6 text-blue-400" />
                  <span>代币查重报告</span>
                </h3>
                <button
                  onClick={() => setShowDuplicateCheck(false)}
                  className="text-white/60 hover:text-white"
                >
                  <XCircle className="w-6 h-6" />
                </button>
              </div>

              {/* 风险级别概览 */}
              <div className={`border rounded-lg p-4 mb-6 ${getRiskLevelColor(duplicateCheckResult?.risk_level || 'LOW')}`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h4 className="text-lg font-bold">
                      代币: {duplicateCheckResult?.token_name || '未知代币'}
                    </h4>
                    <span className={`px-3 py-1 rounded text-sm font-bold ${getRiskBadgeColor(duplicateCheckResult?.risk_level || 'LOW')}`}>
                      {(duplicateCheckResult?.risk_level || 'LOW') === 'HIGH' ? '高风险' :
                       (duplicateCheckResult?.risk_level || 'LOW') === 'MEDIUM' ? '中风险' : '低风险'}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm opacity-75">风险评分</div>
                    <div className="text-2xl font-bold">{duplicateCheckResult?.risk_score || 0}/100</div>
                  </div>
                </div>

                <div className="text-sm opacity-90">
                  检查时间: {new Date(duplicateCheckResult?.checked_at || new Date()).toLocaleString()}
                </div>
              </div>

              {/* 平台查重结果 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                {(duplicateCheckResult?.platform_results || []).map((platform, index) => (
                  <div key={index} className="border border-white/20 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium text-white">{platform.platform}</h5>
                      {platform.exists ? (
                        <span className="px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs">
                          发现同名
                        </span>
                      ) : (
                        <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">
                          无重名
                        </span>
                      )}
                    </div>

                    {platform.exists && platform.data ? (
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-white/70">状态:</span>
                          <span className={platform.data.is_dead ? 'text-red-400' : 'text-green-400'}>
                            {platform.data.is_dead ? '已死亡' : '活跃中'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">创建时间:</span>
                          <span className="text-white">
                            {new Date(platform.data.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">流动性:</span>
                          <span className="text-yellow-400">
                            {platform.data.liquidity.toFixed(2)} SOL
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">持币人数:</span>
                          <span className="text-purple-400">
                            {platform.data.holder_count.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">24h交易:</span>
                          <span className="text-blue-400">
                            {platform.data.trade_count_24h.toLocaleString()}笔
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-white/70">最后交易:</span>
                          <span className="text-white/60">
                            {new Date(platform.data.last_trade).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="mt-2 pt-2 border-t border-white/10">
                          <a
                            href={`https://solscan.io/token/${platform.data.address}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-400 text-xs hover:underline flex items-center space-x-1"
                          >
                            <ExternalLink className="w-3 h-3" />
                            <span>查看详情</span>
                          </a>
                        </div>
                      </div>
                    ) : (
                      <div className="text-white/60 text-sm">
                        未发现同名代币，可安全使用此名称
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* 风险警告 */}
              {(duplicateCheckResult?.warnings || []).length > 0 && (
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4 mb-6">
                  <div className="flex items-start space-x-2">
                    <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
                    <div className="flex-1">
                      <h4 className="text-yellow-400 font-medium mb-2">风险警告</h4>
                      <ul className="space-y-1 text-sm text-yellow-300">
                        {(duplicateCheckResult?.warnings || []).map((warning, i) => (
                          <li key={i} className="flex items-start space-x-2">
                            <span className="text-yellow-400 mt-1">•</span>
                            <span>{warning}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )}

              {/* 操作建议 */}
              <div className="mb-6">
                <h4 className="text-white font-medium mb-3">操作建议</h4>
                <div className="flex flex-wrap gap-3">
                  {(duplicateCheckResult?.suggestions || []).map((suggestion, i) => (
                    <button
                      key={i}
                      onClick={() => handleSuggestion(suggestion.action, selectedStrategyForCheck)}
                      className={`px-4 py-2 rounded text-sm font-medium transition-colors ${
                        suggestion.priority === 'high' ? 'bg-red-500 hover:bg-red-600 text-white' :
                        suggestion.priority === 'medium' ? 'bg-yellow-500 hover:bg-yellow-600 text-black' :
                        'bg-green-500 hover:bg-green-600 text-white'
                      }`}
                    >
                      {suggestion.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* 底部操作 */}
              <div className="flex items-center justify-between pt-4 border-t border-white/20">
                <div className="text-white/60 text-sm">
                  {(duplicateCheckResult?.risk_level || 'LOW') === 'HIGH' ?
                    '建议修改代币名称后再部署' :
                    (duplicateCheckResult?.risk_level || 'LOW') === 'MEDIUM' ?
                    '可以部署，但建议添加区分标识' :
                    '可以安全部署此策略'
                  }
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDuplicateCheck(false)}
                    className="btn-secondary"
                  >
                    取消
                  </button>
                  {(duplicateCheckResult?.risk_level || 'LOW') !== 'HIGH' && (
                    <button
                      onClick={() => handleSuggestion('force', selectedStrategyForCheck)}
                      className="btn-primary"
                    >
                      继续部署
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}

export default StrategyManager
