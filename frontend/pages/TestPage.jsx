import React from 'react';
import { CheckCircle, AlertCircle, Info } from 'lucide-react';

const TestPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 to-slate-800 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
            🎉 React 前端测试页面
          </h1>
          <p className="text-slate-300 text-lg">
            如果您能看到这个页面，说明React前端已经正常工作！
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-400 mr-3" />
              <h3 className="text-xl font-semibold">React 渲染</h3>
            </div>
            <p className="text-slate-300">
              React组件正常渲染，JSX语法工作正常
            </p>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-400 mr-3" />
              <h3 className="text-xl font-semibold">Lucide 图标</h3>
            </div>
            <p className="text-slate-300">
              图标库正常加载，UI组件可以使用
            </p>
          </div>

          <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="w-8 h-8 text-green-400 mr-3" />
              <h3 className="text-xl font-semibold">Tailwind CSS</h3>
            </div>
            <p className="text-slate-300">
              样式框架正常工作，响应式布局可用
            </p>
          </div>
        </div>

        <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 mb-8">
          <div className="flex items-center mb-4">
            <Info className="w-6 h-6 text-blue-400 mr-3" />
            <h3 className="text-xl font-semibold">系统信息</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-slate-400">当前时间:</span>
              <span className="ml-2 text-white">{new Date().toLocaleString('zh-CN')}</span>
            </div>
            <div>
              <span className="text-slate-400">用户代理:</span>
              <span className="ml-2 text-white break-all">{navigator.userAgent.slice(0, 50)}...</span>
            </div>
            <div>
              <span className="text-slate-400">屏幕分辨率:</span>
              <span className="ml-2 text-white">{screen.width} x {screen.height}</span>
            </div>
            <div>
              <span className="text-slate-400">视口大小:</span>
              <span className="ml-2 text-white">{window.innerWidth} x {window.innerHeight}</span>
            </div>
          </div>
        </div>

        <div className="text-center">
          <div className="bg-green-500/10 border border-green-500/30 rounded-xl p-6">
            <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-green-400 mb-2">✅ React 前端正常工作</h2>
            <p className="text-slate-300">
              所有基础功能都已正常加载，您可以继续使用MemeMaster AI的其他功能。
            </p>
          </div>
        </div>

        <div className="mt-8 text-center">
          <a 
            href="/"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 rounded-lg font-medium transition-all duration-200"
          >
            返回首页
          </a>
        </div>
      </div>
    </div>
  );
};

export default TestPage;
