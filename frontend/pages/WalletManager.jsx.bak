import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Wallet,
  Plus,
  Settings,
  Eye,
  EyeOff,
  Copy,
  RefreshCw,
  Shield,
  Globe,
  Zap,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Cpu,
  Server,
  Users,
  DollarSign,
  BarChart3,
  Layers,
  Lock,
  Unlock,
  Timer,
  Target,
  Gauge,
  Bot,
  Wifi,
  Database
} from 'lucide-react'
import { fetchWallets, updateSettings, updateInitialFunding, generateWallets, distributeRandomFunds } from '../store/slices/walletSlice'

const WalletManager = () => {
  const dispatch = useDispatch()
  const { wallets, settings, isLoading, generationProgress, proxyStatus, antiBanMetrics } = useSelector(state => state.wallet)
  const [showSettings, setShowSettings] = useState(false)
  const [showPrivateKeys, setShowPrivateKeys] = useState({})
  const [isGenerating, setIsGenerating] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [activeTab, setActiveTab] = useState('overview') // overview, wallets, antiMev, monitoring

  useEffect(() => {
    dispatch(fetchWallets())
  }, [dispatch])

  const mockWallets = [
    {
      id: 1,
      address: '0x1234...5678',
      privateKey: '0xabcd...efgh',
      balance: { eth: 0.15, sol: 2.3, usdc: 150 },
      status: 'active',
      network: 'ethereum'
    },
    {
      id: 2,
      address: 'ABC123...XYZ789',
      privateKey: 'DEF456...UVW012',
      balance: { eth: 0.08, sol: 1.8, usdc: 80 },
      status: 'active',
      network: 'solana'
    },
    {
      id: 3,
      address: '0x9876...4321',
      privateKey: '0x5432...1098',
      balance: { eth: 0.02, sol: 0.5, usdc: 25 },
      status: 'low_balance',
      network: 'ethereum'
    }
  ]

  const togglePrivateKey = (walletId) => {
    setShowPrivateKeys(prev => ({
      ...prev,
      [walletId]: !prev[walletId]
    }))
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
    // 这里可以添加一个toast通知
  }

  const handleSettingChange = (key, value) => {
    dispatch(updateSettings({ [key]: value }))
  }

  const handleFundingChange = (currency, value) => {
    dispatch(updateInitialFunding({ [currency]: parseFloat(value) }))
  }

  const handleGenerateWallets = async () => {
    setIsGenerating(true)
    try {
      await dispatch(generateWallets({
        count: settings.generateCount,
        chains: settings.supportedChains,
        fundingConfig: settings.initialFunding,
        randomVariation: settings.randomVariation
      }))

      // 生成完成后分配随机资金
      await dispatch(distributeRandomFunds())
    } catch (error) {
      console.error('钱包生成失败:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const getProxyStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20'
      case 'rotating': return 'text-yellow-400 bg-yellow-500/20'
      case 'error': return 'text-red-400 bg-red-500/20'
      default: return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getBanRiskLevel = (score) => {
    if (score >= 90) return { color: 'text-green-400', level: '低风险' }
    if (score >= 70) return { color: 'text-yellow-400', level: '中风险' }
    return { color: 'text-red-400', level: '高风险' }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'text-green-400 bg-green-500/20'
      case 'low_balance':
        return 'text-yellow-400 bg-yellow-500/20'
      case 'inactive':
        return 'text-red-400 bg-red-500/20'
      default:
        return 'text-gray-400 bg-gray-500/20'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return '活跃'
      case 'low_balance':
        return '余额不足'
      case 'inactive':
        return '未激活'
      default:
        return '未知'
    }
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题和导航 */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">钱包管理（抗MEV版）</h1>
            <p className="text-white/60 mt-1">批量钱包生成 • 防封禁系统 • 智能资金分配</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="btn-secondary flex items-center space-x-2"
            >
              <Settings className="w-4 h-4" />
              <span>高级设置</span>
            </button>
            <button
              onClick={handleGenerateWallets}
              disabled={isGenerating}
              className="btn-primary flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>生成中...</span>
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4" />
                  <span>批量生成钱包</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* 标签导航 */}
        <div className="flex space-x-1 bg-white/5 p-1 rounded-lg">
          {[
            { id: 'overview', label: '总览', icon: BarChart3 },
            { id: 'wallets', label: '钱包列表', icon: Wallet },
            { id: 'antiMev', label: '防MEV系统', icon: Shield },
            { id: 'monitoring', label: '监控面板', icon: Activity }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* 生成进度 */}
      {isGenerating && generationProgress && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />
            <span>钱包批量生成进度</span>
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span className="text-white/70">当前进度</span>
              <span className="text-blue-400 font-mono">{generationProgress.current}/{generationProgress.total}</span>
            </div>
            <div className="progress-bar">
              <div
                className="progress-fill bg-gradient-to-r from-blue-400 to-purple-400"
                style={{width: `${(generationProgress.current / generationProgress.total) * 100}%`}}
              ></div>
            </div>
            <div className="text-center text-white/60 text-sm">
              {generationProgress.status}
            </div>
            <div className="grid grid-cols-3 gap-4 text-center text-sm">
              <div>
                <div className="text-green-400 font-bold">{Math.floor((generationProgress.current / generationProgress.total) * 100)}%</div>
                <div className="text-white/60">完成率</div>
              </div>
              <div>
                <div className="text-blue-400 font-bold">{generationProgress.current}</div>
                <div className="text-white/60">已生成</div>
              </div>
              <div>
                <div className="text-yellow-400 font-bold">{generationProgress.total - generationProgress.current}</div>
                <div className="text-white/60">剩余</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 内容区域 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 核心指标卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">{mockWallets.length}</div>
              <div className="text-white/60 text-sm">总钱包数</div>
              <div className="text-green-400 text-xs mt-1">+{settings.generateCount} 可生成</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-lg mx-auto mb-4">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-green-400">
                {mockWallets.filter(w => w.status === 'active').length}
              </div>
              <div className="text-white/60 text-sm">活跃钱包</div>
              <div className="text-green-400 text-xs mt-1">{Math.round((mockWallets.filter(w => w.status === 'active').length / mockWallets.length) * 100)}% 活跃率</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-500/20 rounded-lg mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-yellow-400" />
              </div>
              <div className="text-2xl font-bold text-yellow-400">
                {mockWallets.reduce((sum, w) => sum + w.balance.sol, 0).toFixed(1)}
              </div>
              <div className="text-white/60 text-sm">总SOL余额</div>
              <div className="text-yellow-400 text-xs mt-1">≈ ${(mockWallets.reduce((sum, w) => sum + w.balance.sol, 0) * 85).toFixed(0)}</div>
            </div>

            <div className="card p-6 text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-500/20 rounded-lg mx-auto mb-4">
                <Shield className="w-6 h-6 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-purple-400">
                {antiBanMetrics?.evasionRate || 92}%
              </div>
              <div className="text-white/60 text-sm">防封禁率</div>
              <div className="text-green-400 text-xs mt-1">优秀水平</div>
            </div>
          </div>

          {/* 防封禁系统状态 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 代理状态 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Globe className="w-5 h-5 text-blue-400" />
            <span>代理状态</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">住宅代理</span>
              <span className={`px-2 py-1 rounded text-xs ${getProxyStatusColor(proxyStatus?.residential || 'active')}`}>
                {proxyStatus?.residential === 'active' ? '正常' : '轮换中'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">数据中心代理</span>
              <span className={`px-2 py-1 rounded text-xs ${getProxyStatusColor(proxyStatus?.datacenter || 'active')}`}>
                {proxyStatus?.datacenter === 'active' ? '正常' : '轮换中'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">当前IP</span>
              <span className="text-blue-400 text-sm font-mono">
                {proxyStatus?.currentIP || '*************'}
              </span>
            </div>
          </div>
        </div>

        {/* 防封禁指标 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Shield className="w-5 h-5 text-green-400" />
            <span>防封禁指标</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">规避率</span>
              <span className={`font-bold ${getBanRiskLevel(antiBanMetrics?.evasionRate || 92).color}`}>
                {antiBanMetrics?.evasionRate || 92}%
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">风险等级</span>
              <span className={`text-sm ${getBanRiskLevel(antiBanMetrics?.evasionRate || 92).color}`}>
                {getBanRiskLevel(antiBanMetrics?.evasionRate || 92).level}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">行为伪装</span>
              <span className="text-green-400 text-sm">
                {antiBanMetrics?.behaviorDisguise ? '启用' : '禁用'}
              </span>
            </div>
          </div>
        </div>

        {/* 速率自适应 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Zap className="w-5 h-5 text-yellow-400" />
            <span>速率自适应</span>
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-white/70">当前频率</span>
              <span className="text-yellow-400 font-bold">
                {antiBanMetrics?.currentRate || 2.5}req/s
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">平台响应</span>
              <span className="text-green-400 text-sm">
                {antiBanMetrics?.platformResponse || '正常'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/70">自适应状态</span>
              <span className="text-blue-400 text-sm">
                {antiBanMetrics?.adaptiveStatus || '监控中'}
              </span>
            </div>
          </div>
        </div>
        </div>
      )}

      {/* 钱包列表标签页 */}
      {activeTab === 'wallets' && (
        <div className="space-y-6">
          {/* 钱包统计 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="card p-6 text-center">
              <div className="text-2xl font-bold text-white">{mockWallets.length}</div>
              <div className="text-white/60 text-sm">总钱包数</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-2xl font-bold text-green-400">
                {mockWallets.filter(w => w.status === 'active').length}
              </div>
              <div className="text-white/60 text-sm">活跃钱包</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-2xl font-bold text-blue-400">
                {mockWallets.reduce((sum, w) => sum + w.balance.eth, 0).toFixed(2)}
              </div>
              <div className="text-white/60 text-sm">总ETH余额</div>
            </div>
            <div className="card p-6 text-center">
              <div className="text-2xl font-bold text-yellow-400">
                {mockWallets.reduce((sum, w) => sum + w.balance.usdc, 0).toFixed(0)}
              </div>
              <div className="text-white/60 text-sm">总USDC余额</div>
            </div>
          </div>

          {/* 钱包列表 */}
          <div className="card">
            <div className="p-6 border-b border-white/10">
              <h3 className="text-lg font-semibold text-white">钱包列表</h3>
            </div>
            <div className="divide-y divide-white/10">
              {mockWallets.map((wallet) => (
                <div key={wallet.id} className="p-6 hover:bg-white/5 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-lg">
                        <Wallet className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="text-white font-medium">{wallet.address}</span>
                          <button
                            onClick={() => copyToClipboard(wallet.address)}
                            className="text-white/40 hover:text-white/60 transition-colors"
                          >
                            <Copy className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 rounded text-xs ${getStatusColor(wallet.status)}`}>
                            {getStatusText(wallet.status)}
                          </span>
                          <span className="text-white/60 text-sm capitalize">{wallet.network}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <div className="text-white font-medium">
                          {wallet.balance.eth > 0 && `${wallet.balance.eth} ETH`}
                          {wallet.balance.sol > 0 && `${wallet.balance.sol} SOL`}
                        </div>
                        <div className="text-white/60 text-sm">
                          ${wallet.balance.usdc} USDC
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => togglePrivateKey(wallet.id)}
                          className="btn-secondary p-2"
                        >
                          {showPrivateKeys[wallet.id] ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  {showPrivateKeys[wallet.id] && (
                    <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-red-400 text-sm font-medium">私钥 (请妥善保管)</span>
                        <button
                          onClick={() => copyToClipboard(wallet.privateKey)}
                          className="text-red-400 hover:text-red-300 transition-colors"
                        >
                          <Copy className="w-4 h-4" />
                        </button>
                      </div>
                      <div className="text-white/80 font-mono text-sm mt-2 break-all">
                        {wallet.privateKey}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* 防MEV系统标签页 */}
      {activeTab === 'antiMev' && (
        <div className="space-y-6">
          {/* MEV防护状态 */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Shield className="w-5 h-5 text-green-400" />
                <span>MEV防护状态</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Flashbots集成</span>
                  <span className="text-green-400 flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>已启用</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">私有内存池</span>
                  <span className="text-green-400 flex items-center space-x-1">
                    <Lock className="w-4 h-4" />
                    <span>保护中</span>
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">MEV损失减少</span>
                  <span className="text-green-400 font-bold">84.2%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">前置交易检测</span>
                  <span className="text-blue-400">12次拦截</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Bot className="w-5 h-5 text-purple-400" />
                <span>行为伪装系统</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Puppeteer模拟</span>
                  <span className="text-green-400">运行中</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">人工操作模拟</span>
                  <span className="text-green-400">96.8%相似度</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">随机延迟</span>
                  <span className="text-blue-400">2-8秒</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">浏览器指纹</span>
                  <span className="text-yellow-400">已随机化</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Gauge className="w-5 h-5 text-yellow-400" />
                <span>速率自适应</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">当前频率</span>
                  <span className="text-yellow-400 font-bold">2.5 req/s</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平台响应</span>
                  <span className="text-green-400">正常</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">自适应状态</span>
                  <span className="text-blue-400">监控中</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">优化建议</span>
                  <span className="text-green-400">无需调整</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 监控面板标签页 */}
      {activeTab === 'monitoring' && (
        <div className="space-y-6">
          {/* 实时监控指标 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-400" />
                <span>实时活动监控</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">活跃钱包数</span>
                  <span className="text-green-400 font-bold">{mockWallets.filter(w => w.status === 'active').length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">每分钟交易</span>
                  <span className="text-blue-400">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">成功率</span>
                  <span className="text-green-400">98.7%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">平均Gas费</span>
                  <span className="text-yellow-400">0.0023 ETH</span>
                </div>
              </div>
            </div>

            <div className="card p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <Wifi className="w-5 h-5 text-blue-400" />
                <span>网络状态</span>
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">代理连接</span>
                  <span className="text-green-400">稳定</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">延迟</span>
                  <span className="text-green-400">45ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">带宽使用</span>
                  <span className="text-blue-400">2.3 MB/s</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">IP轮换</span>
                  <span className="text-yellow-400">每15分钟</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 设置面板 */}
      {showSettings && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-6">钱包设置</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 生成设置 */}
            <div>
              <h4 className="text-white font-medium mb-4">生成设置</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    生成数量: {settings.generateCount}
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="100"
                    step="5"
                    value={settings.generateCount}
                    onChange={(e) => handleSettingChange('generateCount', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    资金随机变化: ±{settings.randomVariation}%
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="15"
                    step="1"
                    value={settings.randomVariation}
                    onChange={(e) => handleSettingChange('randomVariation', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">支持的区块链</label>
                  <div className="grid grid-cols-2 gap-2">
                    {['ethereum', 'solana', 'bsc', 'polygon'].map((chain) => (
                      <label key={chain} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={settings.supportedChains?.includes(chain)}
                          onChange={(e) => {
                            const chains = settings.supportedChains || []
                            if (e.target.checked) {
                              handleSettingChange('supportedChains', [...chains, chain])
                            } else {
                              handleSettingChange('supportedChains', chains.filter(c => c !== chain))
                            }
                          }}
                          className="w-4 h-4"
                        />
                        <span className="text-white/70 text-sm capitalize">{chain}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    租金回收阈值: {settings.rentRecoveryThreshold} SOL
                  </label>
                  <input
                    type="range"
                    min="0.001"
                    max="0.01"
                    step="0.001"
                    value={settings.rentRecoveryThreshold}
                    onChange={(e) => handleSettingChange('rentRecoveryThreshold', parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* 防封禁设置 */}
            <div>
              <h4 className="text-white font-medium mb-4">防封禁增强</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">防封禁策略</label>
                  <select
                    value={settings.antiBanStrategy}
                    onChange={(e) => handleSettingChange('antiBanStrategy', e.target.value)}
                    className="input-field w-full"
                  >
                    <option value="conservative">保守 - 低频率</option>
                    <option value="standard">标准 - 平衡模式</option>
                    <option value="aggressive">激进 - 高效率</option>
                  </select>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.enableIPRotation}
                    onChange={(e) => handleSettingChange('enableIPRotation', e.target.checked)}
                    className="w-4 h-4"
                  />
                  <label className="text-white/70">启用IP轮换</label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.enableBehaviorDisguise}
                    onChange={(e) => handleSettingChange('enableBehaviorDisguise', e.target.checked)}
                    className="w-4 h-4"
                  />
                  <label className="text-white/70">启用行为伪装</label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.enableRateAdaptive}
                    onChange={(e) => handleSettingChange('enableRateAdaptive', e.target.checked)}
                    className="w-4 h-4"
                  />
                  <label className="text-white/70">启用速率自适应</label>
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    代理轮换间隔: {settings.proxyRotationInterval}分钟
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="60"
                    step="5"
                    value={settings.proxyRotationInterval}
                    onChange={(e) => handleSettingChange('proxyRotationInterval', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* 初始资金分配 */}
            <div>
              <h4 className="text-white font-medium mb-4">初始资金分配</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">ETH</label>
                  <input
                    type="number"
                    step="0.01"
                    value={settings.initialFunding.eth}
                    onChange={(e) => handleFundingChange('eth', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">SOL</label>
                  <input
                    type="number"
                    step="0.1"
                    value={settings.initialFunding.sol}
                    onChange={(e) => handleFundingChange('sol', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">USDC</label>
                  <input
                    type="number"
                    step="10"
                    value={settings.initialFunding.usdc}
                    onChange={(e) => handleFundingChange('usdc', e.target.value)}
                    className="input-field w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalletManager
