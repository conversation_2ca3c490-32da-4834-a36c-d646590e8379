import React, { useState, useEffect } from 'react';
import { Check, X, Star, Zap, Shield, Globe, Crown } from 'lucide-react';

const SubscriptionPage = () => {
  const [plans, setPlans] = useState([]);
  const [payPerUse, setPayPerUse] = useState({});
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(null);

  useEffect(() => {
    fetchSubscriptionPlans();
    fetchCurrentUser();
  }, []);

  const fetchSubscriptionPlans = async () => {
    try {
      const response = await fetch('/api/subscription/plans');
      if (response.ok) {
        const data = await response.json();
        setPlans(data.plans);
        setPayPerUse(data.pay_per_use);
      }
    } catch (error) {
      console.error('获取订阅计划失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentUser = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) return;

      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentUser(data.user);
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  };

  const handleUpgrade = async (targetTier) => {
    if (!currentUser) {
      alert('请先登录');
      return;
    }

    setUpgrading(targetTier);

    try {
      const token = localStorage.getItem('access_token');
      const response = await fetch('/api/subscription/upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          target_tier: targetTier,
          payment_method: 'solana'
        })
      });

      if (response.ok) {
        const data = await response.json();
        
        // 显示支付信息
        const paymentInfo = `
升级到 ${data.plan_details.name}

支付信息：
• 金额：${data.payment_details.amount_sol} SOL (约 $${data.payment_details.amount_usd})
• 收款地址：${data.payment_details.recipient_address}
• 备注：${data.payment_details.memo}

请使用您的Solana钱包完成支付，支付完成后系统将自动激活您的订阅。
        `;
        
        alert(paymentInfo);
      } else {
        const errorData = await response.json();
        alert(`升级失败：${errorData.detail}`);
      }
    } catch (error) {
      alert('升级失败，请重试');
    } finally {
      setUpgrading(null);
    }
  };

  const getPlanIcon = (tier) => {
    switch (tier) {
      case 'free':
        return <Star className="h-8 w-8 text-gray-500" />;
      case 'pro':
        return <Zap className="h-8 w-8 text-blue-500" />;
      case 'enterprise':
        return <Crown className="h-8 w-8 text-purple-500" />;
      default:
        return <Star className="h-8 w-8 text-gray-500" />;
    }
  };

  const getPlanColor = (tier) => {
    switch (tier) {
      case 'free':
        return 'border-gray-200 bg-white';
      case 'pro':
        return 'border-blue-500 bg-blue-50 ring-2 ring-blue-500';
      case 'enterprise':
        return 'border-purple-500 bg-purple-50 ring-2 ring-purple-500';
      default:
        return 'border-gray-200 bg-white';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            选择适合您的订阅计划
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            从免费试用到企业级解决方案，我们为每个用户提供最适合的功能和服务
          </p>
        </div>

        {/* 订阅计划 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => {
            const isCurrentPlan = currentUser?.role === plan.tier;
            const isPopular = plan.tier === 'pro';
            
            return (
              <div
                key={plan.tier}
                className={`relative rounded-2xl p-8 ${getPlanColor(plan.tier)} ${
                  isPopular ? 'transform scale-105' : ''
                }`}
              >
                {isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                      最受欢迎
                    </span>
                  </div>
                )}

                {isCurrentPlan && (
                  <div className="absolute -top-4 right-4">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      当前计划
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <div className="flex justify-center mb-4">
                    {getPlanIcon(plan.tier)}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-4">{plan.description}</p>
                  <div className="text-4xl font-bold text-gray-900 mb-2">
                    {plan.price_sol > 0 ? (
                      <>
                        <span className="text-2xl">$</span>
                        {plan.price_usd}
                        <span className="text-lg text-gray-600">/月</span>
                      </>
                    ) : (
                      '免费'
                    )}
                  </div>
                  {plan.price_sol > 0 && (
                    <p className="text-sm text-gray-600">
                      或 {plan.price_sol} SOL/月
                    </p>
                  )}
                </div>

                {/* 功能列表 */}
                <div className="space-y-4 mb-8">
                  {plan.highlights.map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                  
                  {plan.restrictions.map((restriction, index) => (
                    <div key={index} className="flex items-center">
                      <X className="h-5 w-5 text-red-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-500">{restriction}</span>
                    </div>
                  ))}
                </div>

                {/* 操作按钮 */}
                <div className="text-center">
                  {isCurrentPlan ? (
                    <button
                      disabled
                      className="w-full bg-gray-300 text-gray-500 py-3 px-6 rounded-lg font-medium cursor-not-allowed"
                    >
                      当前计划
                    </button>
                  ) : plan.tier === 'free' ? (
                    <button
                      disabled
                      className="w-full bg-gray-100 text-gray-500 py-3 px-6 rounded-lg font-medium cursor-not-allowed"
                    >
                      免费注册
                    </button>
                  ) : (
                    <button
                      onClick={() => handleUpgrade(plan.tier)}
                      disabled={upgrading === plan.tier}
                      className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                        plan.tier === 'pro'
                          ? 'bg-blue-500 text-white hover:bg-blue-600'
                          : 'bg-purple-500 text-white hover:bg-purple-600'
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      {upgrading === plan.tier ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          处理中...
                        </div>
                      ) : (
                        `升级到 ${plan.name}`
                      )}
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* 按次付费功能 */}
        <div className="bg-white rounded-2xl p-8 shadow-sm">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">按次付费功能</h2>
            <p className="text-gray-600">
              免费用户也可以按需使用高级功能，灵活付费
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(payPerUse).map(([type, info]) => (
              <div key={type} className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {info.description}
                </h3>
                <div className="text-2xl font-bold text-blue-600 mb-4">
                  {info.price_sol} SOL
                </div>
                <p className="text-sm text-gray-600 mb-4">
                  {type === 'exit_execution' && info.profit_share && 
                    `+ ${(info.profit_share * 100)}% 利润分成`
                  }
                </p>
                <button className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded hover:bg-gray-200 text-sm">
                  立即使用
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* 常见问题 */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">常见问题</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-left">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">如何支付订阅费用？</h3>
              <p className="text-gray-600">
                我们支持Solana (SOL) 加密货币支付，系统会自动转换为稳定币以避免价格波动。
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">可以随时取消订阅吗？</h3>
              <p className="text-gray-600">
                是的，您可以随时取消订阅。取消后您仍可使用至当前计费周期结束。
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">免费试用包含哪些功能？</h3>
              <p className="text-gray-600">
                免费试用14天，包含基础热点监测、策略管理等核心功能，每日3次热点检测。
              </p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h3 className="font-semibold text-gray-900 mb-2">企业版有什么特殊服务？</h3>
              <p className="text-gray-600">
                企业版包含暗网监测、自定义AI模型、专属客户经理和私有部署选项。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPage;
