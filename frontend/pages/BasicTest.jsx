import React from 'react';

const BasicTest = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
      color: 'white',
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        textAlign: 'center'
      }}>
        <h1 style={{
          fontSize: '3rem',
          marginBottom: '20px',
          background: 'linear-gradient(135deg, #a78bfa 0%, #60a5fa 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}>
          🎉 React 前端测试成功！
        </h1>
        
        <div style={{
          background: 'rgba(30, 41, 59, 0.6)',
          border: '1px solid rgba(71, 85, 105, 0.5)',
          borderRadius: '12px',
          padding: '30px',
          marginBottom: '30px'
        }}>
          <h2 style={{ marginBottom: '20px', color: '#10b981' }}>✅ 系统状态检查</h2>
          
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '20px',
            marginBottom: '20px'
          }}>
            <div style={{
              background: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px',
              padding: '15px'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '5px' }}>React 渲染</div>
              <div style={{ color: '#10b981' }}>✅ 正常</div>
            </div>
            
            <div style={{
              background: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px',
              padding: '15px'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '5px' }}>JSX 语法</div>
              <div style={{ color: '#10b981' }}>✅ 正常</div>
            </div>
            
            <div style={{
              background: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px',
              padding: '15px'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '5px' }}>CSS 样式</div>
              <div style={{ color: '#10b981' }}>✅ 正常</div>
            </div>
            
            <div style={{
              background: 'rgba(16, 185, 129, 0.1)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '8px',
              padding: '15px'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '5px' }}>JavaScript</div>
              <div style={{ color: '#10b981' }}>✅ 正常</div>
            </div>
          </div>
          
          <div style={{
            background: 'rgba(59, 130, 246, 0.1)',
            border: '1px solid rgba(59, 130, 246, 0.3)',
            borderRadius: '8px',
            padding: '20px',
            marginTop: '20px'
          }}>
            <h3 style={{ marginBottom: '15px', color: '#60a5fa' }}>📊 系统信息</h3>
            <div style={{ textAlign: 'left', fontSize: '0.9rem' }}>
              <div style={{ marginBottom: '8px' }}>
                <strong>当前时间:</strong> {new Date().toLocaleString('zh-CN')}
              </div>
              <div style={{ marginBottom: '8px' }}>
                <strong>用户代理:</strong> {navigator.userAgent.slice(0, 80)}...
              </div>
              <div style={{ marginBottom: '8px' }}>
                <strong>屏幕分辨率:</strong> {screen.width} x {screen.height}
              </div>
              <div style={{ marginBottom: '8px' }}>
                <strong>视口大小:</strong> {window.innerWidth} x {window.innerHeight}
              </div>
              <div>
                <strong>React 版本:</strong> 18.x
              </div>
            </div>
          </div>
        </div>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <a 
            href="/"
            style={{
              display: 'block',
              background: 'rgba(99, 102, 241, 0.2)',
              border: '1px solid rgba(99, 102, 241, 0.3)',
              borderRadius: '12px',
              padding: '20px',
              textDecoration: 'none',
              color: 'white',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.background = 'rgba(99, 102, 241, 0.3)';
              e.target.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.target.style.background = 'rgba(99, 102, 241, 0.2)';
              e.target.style.transform = 'translateY(0)';
            }}
          >
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '8px', color: '#a78bfa' }}>
              🏠 主页
            </div>
            <div style={{ fontSize: '0.9rem', color: '#cbd5e1' }}>
              返回主仪表盘
            </div>
          </a>
          
          <a 
            href="/simple"
            style={{
              display: 'block',
              background: 'rgba(16, 185, 129, 0.2)',
              border: '1px solid rgba(16, 185, 129, 0.3)',
              borderRadius: '12px',
              padding: '20px',
              textDecoration: 'none',
              color: 'white',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.background = 'rgba(16, 185, 129, 0.3)';
              e.target.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.target.style.background = 'rgba(16, 185, 129, 0.2)';
              e.target.style.transform = 'translateY(0)';
            }}
          >
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '8px', color: '#34d399' }}>
              📊 简化仪表盘
            </div>
            <div style={{ fontSize: '0.9rem', color: '#cbd5e1' }}>
              查看简化版仪表盘
            </div>
          </a>
          
          <a 
            href="/dashboard"
            style={{
              display: 'block',
              background: 'rgba(245, 158, 11, 0.2)',
              border: '1px solid rgba(245, 158, 11, 0.3)',
              borderRadius: '12px',
              padding: '20px',
              textDecoration: 'none',
              color: 'white',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.background = 'rgba(245, 158, 11, 0.3)';
              e.target.style.transform = 'translateY(-2px)';
            }}
            onMouseOut={(e) => {
              e.target.style.background = 'rgba(245, 158, 11, 0.2)';
              e.target.style.transform = 'translateY(0)';
            }}
          >
            <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '8px', color: '#fbbf24' }}>
              🎯 完整仪表盘
            </div>
            <div style={{ fontSize: '0.9rem', color: '#cbd5e1' }}>
              查看完整功能版本
            </div>
          </a>
        </div>
        
        <div style={{
          background: 'rgba(16, 185, 129, 0.1)',
          border: '1px solid rgba(16, 185, 129, 0.3)',
          borderRadius: '12px',
          padding: '20px'
        }}>
          <h2 style={{ color: '#10b981', marginBottom: '10px' }}>🎉 恭喜！React前端已成功修复</h2>
          <p style={{ color: '#cbd5e1', lineHeight: '1.6' }}>
            如果您能看到这个页面，说明React前端已经完全正常工作。
            所有组件都能正确渲染，您可以继续使用MemeMaster AI的所有功能。
          </p>
        </div>
      </div>
    </div>
  );
};

export default BasicTest;
