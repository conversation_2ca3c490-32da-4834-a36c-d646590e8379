import React, { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  AlertTriangle,
  TrendingDown,
  Shield,
  Settings,
  Zap,
  Activity,
  Brain,
  BarChart3,
  Target,
  Clock,
  DollarSign,
  TrendingUp,
  Eye,
  Layers,
  GitBranch,
  Gauge
} from 'lucide-react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts'
import {
  fetchExitSignals,
  updateSettings,
  updateVolatilityParams,
  fetchMultiIndicators,
  updateXGBoostModel,
  executeEmergencyExit,
  activateHedging
} from '../store/slices/exitSlice'
import { exitAPI } from '../services/api'

const ExitSystem = () => {
  const dispatch = useDispatch()
  const {
    signalStrength,
    recommendedAction,
    estimatedROI,
    settings,
    isLoading,
    multiIndicators,
    xgboostPrediction,
    hedgingStatus,
    emergencyExitStatus,
    volatilityLevel,
    sellBatches,
    optionPositions
  } = useSelector(state => state?.exit || {
    signalStrength: 0.65,
    recommendedAction: 'hold',
    estimatedROI: 0.15,
    settings: {},
    isLoading: false,
    multiIndicators: null,
    xgboostPrediction: null,
    hedgingStatus: {},
    emergencyExitStatus: {},
    volatilityLevel: 'medium',
    sellBatches: null,
    optionPositions: null
  })

  // 获取已发行代币列表
  const { deployedTokens } = useSelector(state => state?.businessFlow || { deployedTokens: [] })

  const [showSettings, setShowSettings] = useState(false)
  const [emergencyConfirm, setEmergencyConfirm] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedToken, setSelectedToken] = useState('MEME')
  const updateInterval = useRef(null)

  useEffect(() => {
    // 初始化加载所有数据
    dispatch(fetchExitSignals())
    dispatch(fetchMultiIndicators())

    // 每15秒更新一次退出信号和多指标数据
    updateInterval.current = setInterval(() => {
      dispatch(fetchExitSignals())
      dispatch(fetchMultiIndicators())
    }, 15000)

    return () => {
      if (updateInterval.current) {
        clearInterval(updateInterval.current)
      }
    }
  }, [dispatch])

  // 安全的多指标数据，确保所有属性都有默认值
  const defaultIndicatorData = {
    technical: {
      rsi: 68.5,
      macd: 0.12,
      bollinger: 'upper',
      volume_spike: 2.3
    },
    onchain: {
      whale_activity: 0.78,
      transaction_volume: 1.45,
      holder_distribution: 0.65,
      smart_money_flow: 0.82
    },
    sentiment: {
      social_score: 0.73,
      news_sentiment: 0.68,
      fear_greed: 0.45,
      polymarket_prob: 0.67
    },
    events: {
      regulatory_risk: 0.23,
      market_correlation: 0.89,
      volatility_forecast: 0.72,
      liquidity_depth: 0.91
    }
  }

  const indicatorData = {
    technical: {
      rsi: multiIndicators?.technical?.rsi || defaultIndicatorData.technical.rsi,
      macd: multiIndicators?.technical?.macd || defaultIndicatorData.technical.macd,
      bollinger: multiIndicators?.technical?.bollinger || defaultIndicatorData.technical.bollinger,
      volume_spike: multiIndicators?.technical?.volume_spike || defaultIndicatorData.technical.volume_spike
    },
    onchain: {
      whale_activity: multiIndicators?.onchain?.whale_activity || defaultIndicatorData.onchain.whale_activity,
      transaction_volume: multiIndicators?.onchain?.transaction_volume || defaultIndicatorData.onchain.transaction_volume,
      holder_distribution: multiIndicators?.onchain?.holder_distribution || defaultIndicatorData.onchain.holder_distribution,
      smart_money_flow: multiIndicators?.onchain?.smart_money_flow || defaultIndicatorData.onchain.smart_money_flow
    },
    sentiment: {
      social_score: multiIndicators?.sentiment?.social_score || defaultIndicatorData.sentiment.social_score,
      news_sentiment: multiIndicators?.sentiment?.news_sentiment || defaultIndicatorData.sentiment.news_sentiment,
      fear_greed: multiIndicators?.sentiment?.fear_greed || defaultIndicatorData.sentiment.fear_greed,
      polymarket_prob: multiIndicators?.sentiment?.polymarket_prob || defaultIndicatorData.sentiment.polymarket_prob
    },
    events: {
      regulatory_risk: multiIndicators?.events?.regulatory_risk || defaultIndicatorData.events.regulatory_risk,
      market_correlation: multiIndicators?.events?.market_correlation || defaultIndicatorData.events.market_correlation,
      volatility_forecast: multiIndicators?.events?.volatility_forecast || defaultIndicatorData.events.volatility_forecast,
      liquidity_depth: multiIndicators?.events?.liquidity_depth || defaultIndicatorData.events.liquidity_depth
    }
  }

  // XGBoost模型预测数据
  const xgboostData = {
    prediction: xgboostPrediction?.prediction || 0.78,
    confidence: xgboostPrediction?.confidence || 0.85,
    feature_importance: xgboostPrediction?.feature_importance || {
      'RSI': 0.18,
      'Whale Activity': 0.22,
      'Sentiment': 0.15,
      'Volume': 0.12,
      'MACD': 0.10,
      'Polymarket': 0.23
    },
    model_accuracy: xgboostPrediction?.model_accuracy || 0.847,
    last_retrain: xgboostPrediction?.last_retrain || '2024-01-15T10:30:00Z'
  }

  // 动态卖出批次数据
  const batchData = sellBatches || [
    { batch: 1, amount: 7, price: 0.0245, status: 'completed', time: '10:15' },
    { batch: 2, amount: 7, price: 0.0238, status: 'completed', time: '10:35' },
    { batch: 3, amount: 9, price: 0.0232, status: 'pending', time: '10:55' },
    { batch: 4, amount: 9, price: 0.0225, status: 'queued', time: '11:15' },
    { batch: 5, amount: 12, price: 0.0218, status: 'queued', time: '11:35' }
  ]

  // 期权对冲数据
  const hedgingData = optionPositions || [
    { type: 'PUT', strike: 0.0208, expiry: '7d', amount: 30, premium: 0.0012, status: 'active' },
    { type: 'PUT', strike: 0.0195, expiry: '14d', amount: 20, premium: 0.0018, status: 'active' }
  ]

  const handleEmergencyExit = async () => {
    if (!emergencyConfirm) {
      setEmergencyConfirm(true)
      setTimeout(() => setEmergencyConfirm(false), 5000)
      return
    }

    try {
      await dispatch(executeEmergencyExit({ token: selectedToken }))
      setEmergencyConfirm(false)
    } catch (error) {
      console.error('紧急退出失败:', error)
    }
  }

  const handleActivateHedging = async () => {
    try {
      await dispatch(activateHedging({
        token: selectedToken,
        volatility: volatilityLevel || 'medium',
        hedgeRatio: settings?.hedgeRatio || 0.3
      }))
    } catch (error) {
      console.error('期权对冲激活失败:', error)
    }
  }

  const handleXGBoostRetrain = async () => {
    try {
      await dispatch(updateXGBoostModel())
    } catch (error) {
      console.error('模型重训练失败:', error)
    }
  }

  const handleSettingChange = (key, value) => {
    dispatch(updateSettings({ [key]: value }))
  }

  const handleVolatilityChange = (level, param, value) => {
    dispatch(updateVolatilityParams({
      [level]: {
        ...(settings?.volatilityParams?.[level] || {}),
        [param]: parseInt(value)
      }
    }))
  }

  const getSignalColor = () => {
    const safeSignalStrength = signalStrength || 0
    if (safeSignalStrength >= 0.8) return 'text-red-400'
    if (safeSignalStrength >= 0.6) return 'text-yellow-400'
    return 'text-green-400'
  }

  const getSignalBgColor = () => {
    const safeSignalStrength = signalStrength || 0
    if (safeSignalStrength >= 0.8) return 'bg-red-500/20'
    if (safeSignalStrength >= 0.6) return 'bg-yellow-500/20'
    return 'bg-green-500/20'
  }

  const getActionText = () => {
    switch (recommendedAction || 'hold') {
      case 'sell':
        return '立即卖出'
      case 'partial_sell':
        return '分批卖出'
      case 'hold':
        return '继续持有'
      default:
        return '等待信号'
    }
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 增强版页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white flex items-center space-x-3">
            <Activity className="w-8 h-8 text-red-400" />
            <span>动态策略中心</span>
          </h1>
          <p className="text-white/60 mt-1">
            XGBoost驱动 • 多指标融合 • 期权对冲 • 智能分批退出 • 实时策略调整
          </p>
          <div className="flex items-center space-x-4 mt-2">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm">实时监控中</span>
            </div>
            <div className="text-white/50 text-sm">
              模型准确率: <span className="text-green-400">{(xgboostData.model_accuracy * 100).toFixed(1)}%</span>
            </div>
            <div className="text-white/50 text-sm">
              预测置信度: <span className="text-blue-400">{(xgboostData.confidence * 100).toFixed(0)}%</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* 代币选择器 */}
          <select
            value={selectedToken}
            onChange={(e) => setSelectedToken(e.target.value)}
            className="input-field min-w-48"
          >
            <option value="">选择代币</option>
            {deployedTokens && deployedTokens.length > 0 ? (
              deployedTokens.map((token) => (
                <option key={token.id} value={token.token_symbol || token.tokenSymbol}>
                  {token.token_name || token.tokenName} ({token.token_symbol || token.tokenSymbol})
                </option>
              ))
            ) : (
              <>
                <option value="MEME">MEME Token (示例)</option>
                <option value="PEPE">PEPE (示例)</option>
                <option value="DOGE">DOGE (示例)</option>
                <option value="SHIB">SHIB (示例)</option>
              </>
            )}
          </select>

          {/* 期权对冲按钮 */}
          <button
            onClick={handleActivateHedging}
            className="btn-secondary flex items-center space-x-2"
          >
            <Shield className="w-4 h-4" />
            <span>激活对冲</span>
          </button>

          {/* 设置按钮 */}
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Settings className="w-4 h-4" />
            <span>设置</span>
          </button>

          {/* 紧急退出按钮 */}
          <button
            onClick={handleEmergencyExit}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 ${
              emergencyConfirm
                ? 'bg-red-600 hover:bg-red-700 text-white animate-pulse'
                : 'bg-red-500/20 hover:bg-red-500/30 text-red-400'
            }`}
          >
            <Zap className="w-4 h-4" />
            <span>{emergencyConfirm ? '确认紧急退出' : '紧急退出'}</span>
          </button>
        </div>
      </div>

      {/* 导航标签 */}
      <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
        {[
          { id: 'overview', name: '总览', icon: BarChart3 },
          { id: 'indicators', name: '多指标分析', icon: Layers },
          { id: 'xgboost', name: 'XGBoost模型', icon: Brain },
          { id: 'batches', name: '分批退出', icon: GitBranch },
          { id: 'hedging', name: '期权对冲', icon: Shield },
          { id: 'emergency', name: '紧急退出', icon: AlertTriangle }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
              activeTab === tab.id
                ? 'bg-primary-500 text-white'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span>{tab.name}</span>
          </button>
        ))}
      </div>

      {/* 已发行代币管理面板 */}
      {deployedTokens && deployedTokens.length > 0 && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Target className="w-5 h-5 text-green-400" />
            <span>已发行代币管理</span>
            <div className="ml-auto text-sm text-white/60">
              共 {deployedTokens.length} 个代币
            </div>
          </h3>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {deployedTokens.map((token) => (
              <div key={token.id} className="bg-white/5 rounded-lg p-4 border border-white/10">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h4 className="text-white font-medium">{token.token_name || token.tokenName}</h4>
                    <div className="text-white/60 text-sm">{token.token_symbol || token.tokenSymbol}</div>
                  </div>
                  <div className={`px-2 py-1 rounded text-xs ${
                    token.status === 'active' ? 'bg-green-500/20 text-green-400' :
                    token.status === 'exiting' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-gray-500/20 text-gray-400'
                  }`}>
                    {token.status === 'active' ? '活跃' :
                     token.status === 'exiting' ? '退出中' : '已退出'}
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">平台</span>
                    <span className="text-blue-400">{token.platform || 'pump.fun'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">部署时间</span>
                    <span className="text-white/60">
                      {token.deployedAt ? new Date(token.deployedAt).toLocaleDateString() : '今天'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">合约地址</span>
                    <span className="text-purple-400 font-mono text-xs">
                      {token.contract_address ?
                        `${token.contract_address.slice(0, 6)}...${token.contract_address.slice(-4)}` :
                        'N/A'
                      }
                    </span>
                  </div>
                </div>

                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => setSelectedToken(token.token_symbol || token.tokenSymbol)}
                    className="flex-1 btn-secondary text-xs py-1"
                  >
                    选择分析
                  </button>
                  <button
                    onClick={() => {/* 创建退出策略 */}}
                    className="flex-1 btn-primary text-xs py-1"
                  >
                    创建策略
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 总览标签内容 */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* XGBoost预测仪表 */}
          <div className="card p-6 text-center">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center justify-center space-x-2">
              <Brain className="w-5 h-5 text-purple-400" />
              <span>XGBoost预测</span>
            </h3>

            <div className="relative w-32 h-32 mx-auto mb-4">
              <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke="rgba(255,255,255,0.1)"
                  strokeWidth="8"
                  fill="none"
                />
                <circle
                  cx="60"
                  cy="60"
                  r="50"
                  stroke={xgboostData.prediction >= 0.8 ? '#EF4444' : xgboostData.prediction >= 0.6 ? '#F59E0B' : '#10B981'}
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={`${xgboostData.prediction * 314} 314`}
                  strokeLinecap="round"
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className={`text-2xl font-bold ${
                  xgboostData.prediction >= 0.8 ? 'text-red-400' :
                  xgboostData.prediction >= 0.6 ? 'text-yellow-400' : 'text-green-400'
                }`}>
                  {(xgboostData.prediction * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
              xgboostData.prediction >= 0.8 ? 'bg-red-500/20 text-red-400' :
              xgboostData.prediction >= 0.6 ? 'bg-yellow-500/20 text-yellow-400' : 'bg-green-500/20 text-green-400'
            }`}>
              {xgboostData.prediction >= 0.8 ? '强烈卖出' :
               xgboostData.prediction >= 0.6 ? '分批卖出' : '继续持有'}
            </div>
          </div>

          {/* 多指标融合 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Layers className="w-5 h-5 text-blue-400" />
              <span>多指标融合</span>
            </h3>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">技术指标</span>
                <span className="text-blue-400 font-medium">{((indicatorData.technical.rsi + indicatorData.technical.macd * 100) / 2).toFixed(0)}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">链上指标</span>
                <span className="text-green-400 font-medium">{(indicatorData.onchain.whale_activity * 100).toFixed(0)}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">情绪指标</span>
                <span className="text-yellow-400 font-medium">{(indicatorData.sentiment.social_score * 100).toFixed(0)}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">事件概率</span>
                <span className="text-purple-400 font-medium">{(indicatorData.sentiment.polymarket_prob * 100).toFixed(0)}%</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-white/10">
              <div className="text-xs text-white/50 text-center">
                <p>综合评分: <span className="text-white font-medium">{((xgboostData.prediction * 100)).toFixed(0)}/100</span></p>
              </div>
            </div>
          </div>

          {/* 动态卖出状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <GitBranch className="w-5 h-5 text-green-400" />
              <span>分批退出状态</span>
            </h3>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">已完成批次</span>
                <span className="text-green-400 font-medium">{batchData.filter(b => b.status === 'completed').length}/5</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">累计卖出</span>
                <span className="text-blue-400 font-medium">{batchData.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.amount, 0)}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">平均价格</span>
                <span className="text-yellow-400 font-medium">$0.0241</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">波动率级别</span>
                <span className={`font-medium ${
                  volatilityLevel === 'high' ? 'text-red-400' :
                  volatilityLevel === 'medium' ? 'text-yellow-400' : 'text-green-400'
                }`}>
                  {volatilityLevel === 'high' ? '高' : volatilityLevel === 'medium' ? '中' : '低'}
                </span>
              </div>
            </div>

            <div className="mt-4">
              <div className="w-full bg-white/20 rounded-full h-2">
                <div
                  className="bg-green-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${batchData.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.amount, 0)}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* 期权对冲状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Shield className="w-5 h-5 text-red-400" />
              <span>期权对冲</span>
            </h3>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">对冲比例</span>
                <span className="text-blue-400 font-medium">{hedgingData.reduce((sum, h) => sum + h.amount, 0)}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">期权数量</span>
                <span className="text-green-400 font-medium">{hedgingData.length}个</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">总权利金</span>
                <span className="text-yellow-400 font-medium">${hedgingData.reduce((sum, h) => sum + h.premium, 0).toFixed(4)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">保护水平</span>
                <span className="text-purple-400 font-medium">85%</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-white/10">
              <div className="text-xs text-white/50 text-center">
                <p>Deribit期权 • 自动执行</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 多指标分析标签内容 */}
      {activeTab === 'indicators' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 技术指标雷达图 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Gauge className="w-5 h-5 text-blue-400" />
              <span>技术指标雷达</span>
            </h3>

            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart data={[
                  { indicator: 'RSI', value: indicatorData.technical.rsi, fullMark: 100 },
                  { indicator: 'MACD', value: Math.abs(indicatorData.technical.macd) * 100, fullMark: 100 },
                  { indicator: '成交量', value: indicatorData.technical.volume_spike * 30, fullMark: 100 },
                  { indicator: '鲸鱼活动', value: indicatorData.onchain.whale_activity * 100, fullMark: 100 },
                  { indicator: '情绪指数', value: indicatorData.sentiment.social_score * 100, fullMark: 100 },
                  { indicator: '事件概率', value: indicatorData.sentiment.polymarket_prob * 100, fullMark: 100 }
                ]}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="indicator" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar name="指标值" dataKey="value" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.3} />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* 指标详细数据 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-green-400" />
              <span>指标详细分析</span>
            </h3>

            <div className="space-y-4">
              {/* 技术指标 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3 flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-blue-400" />
                  <span>技术指标</span>
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">RSI:</span>
                    <span className={`font-medium ${
                      indicatorData.technical.rsi > 70 ? 'text-red-400' :
                      indicatorData.technical.rsi < 30 ? 'text-green-400' : 'text-yellow-400'
                    }`}>{indicatorData.technical.rsi.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">MACD:</span>
                    <span className={`font-medium ${indicatorData.technical.macd > 0 ? 'text-green-400' : 'text-red-400'}`}>
                      {indicatorData.technical.macd.toFixed(3)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">布林带:</span>
                    <span className="text-blue-400 font-medium">{indicatorData.technical.bollinger}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">成交量激增:</span>
                    <span className="text-purple-400 font-medium">{indicatorData.technical.volume_spike.toFixed(1)}x</span>
                  </div>
                </div>
              </div>

              {/* 链上指标 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3 flex items-center space-x-2">
                  <Activity className="w-4 h-4 text-green-400" />
                  <span>链上指标</span>
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">鲸鱼活动:</span>
                    <span className="text-green-400 font-medium">{(indicatorData.onchain.whale_activity * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">交易量:</span>
                    <span className="text-blue-400 font-medium">{indicatorData.onchain.transaction_volume.toFixed(2)}M</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">持有者分布:</span>
                    <span className="text-yellow-400 font-medium">{(indicatorData.onchain.holder_distribution * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">聪明钱流向:</span>
                    <span className="text-purple-400 font-medium">{(indicatorData.onchain.smart_money_flow * 100).toFixed(0)}%</span>
                  </div>
                </div>
              </div>

              {/* 情绪和事件指标 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3 flex items-center space-x-2">
                  <Eye className="w-4 h-4 text-yellow-400" />
                  <span>情绪与事件</span>
                </h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">社交情绪:</span>
                    <span className="text-yellow-400 font-medium">{(indicatorData.sentiment.social_score * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">新闻情绪:</span>
                    <span className="text-blue-400 font-medium">{(indicatorData.sentiment.news_sentiment * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">恐慌贪婪:</span>
                    <span className="text-green-400 font-medium">{(indicatorData.sentiment.fear_greed * 100).toFixed(0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">Polymarket:</span>
                    <span className="text-purple-400 font-medium">{(indicatorData.sentiment.polymarket_prob * 100).toFixed(0)}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* XGBoost模型标签内容 */}
      {activeTab === 'xgboost' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 模型预测结果 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-400" />
              <span>XGBoost预测模型</span>
            </h3>

            <div className="space-y-4">
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">卖出概率预测</span>
                  <span className={`text-2xl font-bold ${
                    xgboostData.prediction >= 0.8 ? 'text-red-400' :
                    xgboostData.prediction >= 0.6 ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    {(xgboostData.prediction * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      xgboostData.prediction >= 0.8 ? 'bg-red-400' :
                      xgboostData.prediction >= 0.6 ? 'bg-yellow-400' : 'bg-green-400'
                    }`}
                    style={{ width: `${xgboostData.prediction * 100}%` }}
                  ></div>
                </div>
              </div>

              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70">模型置信度</span>
                  <span className="text-blue-400 font-medium">{(xgboostData.confidence * 100).toFixed(1)}%</span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white/70">模型准确率</span>
                  <span className="text-green-400 font-medium">{(xgboostData.model_accuracy * 100).toFixed(1)}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">最后训练</span>
                  <span className="text-white/50 text-sm">2小时前</span>
                </div>
              </div>

              <button
                onClick={handleXGBoostRetrain}
                className="btn-primary w-full flex items-center justify-center space-x-2"
              >
                <Brain className="w-4 h-4" />
                <span>重新训练模型</span>
              </button>
            </div>
          </div>

          {/* 特征重要性 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-400" />
              <span>特征重要性分析</span>
            </h3>

            <div className="space-y-3">
              {Object.entries(xgboostData.feature_importance)
                .sort(([,a], [,b]) => b - a)
                .map(([feature, importance], index) => (
                <div key={feature} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white text-sm">{feature}</span>
                    <span className="text-blue-400 font-medium">{(importance * 100).toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${importance * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 pt-4 border-t border-white/10">
              <div className="text-xs text-white/50">
                <p>基于最近1000个交易样本训练</p>
                <p>使用梯度提升决策树算法</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 分批退出标签内容 */}
      {activeTab === 'batches' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 分批执行状态 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <GitBranch className="w-5 h-5 text-green-400" />
              <span>分批执行状态</span>
            </h3>

            <div className="space-y-4">
              {batchData.map((batch, index) => (
                <div key={batch.batch} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-white font-medium">批次 {batch.batch}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        batch.status === 'completed' ? 'bg-green-500/20 text-green-400' :
                        batch.status === 'pending' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}>
                        {batch.status === 'completed' ? '已完成' :
                         batch.status === 'pending' ? '执行中' : '排队中'}
                      </span>
                    </div>
                    <span className="text-white/70 text-sm">{batch.time}</span>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-white/60">卖出比例</div>
                      <div className="text-white font-medium">{batch.amount}%</div>
                    </div>
                    <div>
                      <div className="text-white/60">执行价格</div>
                      <div className="text-green-400 font-medium">${batch.price}</div>
                    </div>
                    <div>
                      <div className="text-white/60">状态</div>
                      <div className={`font-medium ${
                        batch.status === 'completed' ? 'text-green-400' :
                        batch.status === 'pending' ? 'text-yellow-400' : 'text-gray-400'
                      }`}>
                        {batch.status === 'completed' ? '✓ 完成' :
                         batch.status === 'pending' ? '⏳ 执行中' : '⏸ 等待'}
                      </div>
                    </div>
                  </div>

                  {batch.status === 'completed' && (
                    <div className="mt-3 pt-3 border-t border-white/10">
                      <div className="text-xs text-white/50">
                        执行完成 • 滑点: 0.2% • Gas费: $2.3
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* 总体进度 */}
            <div className="mt-6 pt-6 border-t border-white/10">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/70">总体进度</span>
                <span className="text-white font-medium">
                  {batchData.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.amount, 0)}% / 100%
                </span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${batchData.filter(b => b.status === 'completed').reduce((sum, b) => sum + b.amount, 0)}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* 动态参数调整 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Gauge className="w-5 h-5 text-blue-400" />
              <span>动态参数调整</span>
            </h3>

            <div className="space-y-4">
              {/* 当前波动率 */}
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">当前波动率</span>
                  <span className={`text-xl font-bold ${
                    volatilityLevel === 'high' ? 'text-red-400' :
                    volatilityLevel === 'medium' ? 'text-yellow-400' : 'text-green-400'
                  }`}>
                    {volatilityLevel === 'high' ? '高' : volatilityLevel === 'medium' ? '中' : '低'}
                  </span>
                </div>
                <div className="text-sm text-white/60">
                  基于24小时价格波动和成交量分析
                </div>
              </div>

              {/* 当前策略参数 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">当前策略参数</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-white/60">批次大小</div>
                    <div className="text-blue-400 font-medium">
                      {settings.volatilityParams[volatilityLevel]?.batchSize || 9}%
                    </div>
                  </div>
                  <div>
                    <div className="text-white/60">执行间隔</div>
                    <div className="text-green-400 font-medium">
                      {settings.volatilityParams[volatilityLevel]?.interval || 40}分钟
                    </div>
                  </div>
                  <div>
                    <div className="text-white/60">滑点容忍</div>
                    <div className="text-yellow-400 font-medium">
                      {volatilityLevel === 'high' ? '3%' : volatilityLevel === 'medium' ? '2%' : '1%'}
                    </div>
                  </div>
                  <div>
                    <div className="text-white/60">Gas优化</div>
                    <div className="text-purple-400 font-medium">
                      {volatilityLevel === 'high' ? '快速' : '标准'}
                    </div>
                  </div>
                </div>
              </div>

              {/* 执行统计 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">执行统计</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/60">已执行金额</span>
                    <span className="text-green-400">$12,450</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">平均执行价格</span>
                    <span className="text-blue-400">$0.0241</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">总手续费</span>
                    <span className="text-yellow-400">$18.7</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/60">平均滑点</span>
                    <span className="text-purple-400">0.18%</span>
                  </div>
                </div>
              </div>

              {/* 下一批次预览 */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                <h4 className="text-blue-400 font-medium mb-2">下一批次预览</h4>
                <div className="text-sm text-white/70">
                  <div>预计执行时间: 11:15</div>
                  <div>预计卖出比例: 9%</div>
                  <div>目标价格: $0.0225</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 期权对冲标签内容 */}
      {activeTab === 'hedging' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 当前对冲持仓 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Shield className="w-5 h-5 text-red-400" />
              <span>期权对冲持仓</span>
            </h3>

            <div className="space-y-4">
              {hedgingData.map((position, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        position.type === 'PUT' ? 'bg-red-500/20 text-red-400' : 'bg-green-500/20 text-green-400'
                      }`}>
                        {position.type}
                      </span>
                      <span className="text-white font-medium">行权价 ${position.strike}</span>
                    </div>
                    <span className={`text-sm font-medium ${
                      position.status === 'active' ? 'text-green-400' : 'text-gray-400'
                    }`}>
                      {position.status === 'active' ? '活跃' : '已平仓'}
                    </span>
                  </div>

                  <div className="grid grid-cols-4 gap-3 text-sm">
                    <div>
                      <div className="text-white/60">到期时间</div>
                      <div className="text-white font-medium">{position.expiry}</div>
                    </div>
                    <div>
                      <div className="text-white/60">对冲比例</div>
                      <div className="text-blue-400 font-medium">{position.amount}%</div>
                    </div>
                    <div>
                      <div className="text-white/60">权利金</div>
                      <div className="text-yellow-400 font-medium">${position.premium}</div>
                    </div>
                    <div>
                      <div className="text-white/60">当前P&L</div>
                      <div className="text-green-400 font-medium">+$0.0008</div>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-white/10">
                    <div className="text-xs text-white/50">
                      Deribit交易所 • 自动执行 • Delta: -0.45
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 对冲总览 */}
            <div className="mt-6 pt-6 border-t border-white/10">
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-white/60">总对冲比例</div>
                  <div className="text-blue-400 text-xl font-bold">
                    {hedgingData.reduce((sum, h) => sum + h.amount, 0)}%
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-white/60">总权利金</div>
                  <div className="text-yellow-400 text-xl font-bold">
                    ${hedgingData.reduce((sum, h) => sum + h.premium, 0).toFixed(4)}
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-white/60">对冲效率</div>
                  <div className="text-green-400 text-xl font-bold">87%</div>
                </div>
              </div>
            </div>
          </div>

          {/* 对冲策略配置 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Target className="w-5 h-5 text-purple-400" />
              <span>对冲策略配置</span>
            </h3>

            <div className="space-y-4">
              {/* 保护水平设置 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">保护水平设置</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-white/70 text-sm">下跌保护</span>
                      <span className="text-green-400 font-medium">85%</span>
                    </div>
                    <input
                      type="range"
                      min="70"
                      max="95"
                      value="85"
                      className="w-full"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-white/70 text-sm">对冲比例</span>
                      <span className="text-blue-400 font-medium">50%</span>
                    </div>
                    <input
                      type="range"
                      min="20"
                      max="80"
                      value="50"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              {/* 期权策略选择 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">期权策略</h4>
                <div className="space-y-2">
                  {[
                    { name: 'PUT保护', desc: '买入看跌期权', active: true },
                    { name: 'PUT价差', desc: '买入+卖出看跌期权', active: false },
                    { name: '领口策略', desc: 'PUT+CALL组合', active: false }
                  ].map((strategy, index) => (
                    <label key={index} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="radio"
                        name="hedging-strategy"
                        defaultChecked={strategy.active}
                        className="w-4 h-4 text-blue-600"
                      />
                      <div>
                        <div className="text-white text-sm">{strategy.name}</div>
                        <div className="text-white/50 text-xs">{strategy.desc}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* 自动调整设置 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">自动调整</h4>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">Delta中性调整</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-blue-600 rounded" />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">波动率调整</span>
                    <input type="checkbox" defaultChecked className="w-4 h-4 text-blue-600 rounded" />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-white/70 text-sm">到期自动展期</span>
                    <input type="checkbox" className="w-4 h-4 text-blue-600 rounded" />
                  </label>
                </div>
              </div>

              {/* 风险指标 */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h4 className="text-red-400 font-medium mb-3">风险指标</h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <div className="text-white/60">最大损失</div>
                    <div className="text-red-400 font-medium">-15%</div>
                  </div>
                  <div>
                    <div className="text-white/60">时间衰减</div>
                    <div className="text-yellow-400 font-medium">-$0.0002/天</div>
                  </div>
                  <div>
                    <div className="text-white/60">隐含波动率</div>
                    <div className="text-blue-400 font-medium">45%</div>
                  </div>
                  <div>
                    <div className="text-white/60">Gamma风险</div>
                    <div className="text-purple-400 font-medium">低</div>
                  </div>
                </div>
              </div>

              <button
                onClick={handleActivateHedging}
                className="btn-primary w-full flex items-center justify-center space-x-2"
              >
                <Shield className="w-4 h-4" />
                <span>更新对冲策略</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 紧急退出标签内容 */}
      {activeTab === 'emergency' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 紧急退出控制 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              <span>紧急退出控制</span>
            </h3>

            <div className="space-y-4">
              {/* 紧急退出状态 */}
              <div className={`rounded-lg p-4 border ${
                emergencyExitStatus?.active
                  ? 'bg-red-500/10 border-red-500/20'
                  : 'bg-white/5 border-white/20'
              }`}>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-white font-medium">系统状态</span>
                  <span className={`px-3 py-1 rounded text-sm font-medium ${
                    emergencyExitStatus?.active
                      ? 'bg-red-500/20 text-red-400'
                      : 'bg-green-500/20 text-green-400'
                  }`}>
                    {emergencyExitStatus?.active ? '紧急模式' : '正常运行'}
                  </span>
                </div>
                <div className="text-sm text-white/60">
                  {emergencyExitStatus?.active
                    ? '系统处于紧急退出模式，所有交易已暂停'
                    : '系统正常运行，可执行紧急退出操作'}
                </div>
              </div>

              {/* 预签名交易状态 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">预签名交易</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-white/60">准备就绪</div>
                    <div className="text-green-400 font-medium">5笔交易</div>
                  </div>
                  <div>
                    <div className="text-white/60">预计执行时间</div>
                    <div className="text-blue-400 font-medium">&lt;30秒</div>
                  </div>
                  <div>
                    <div className="text-white/60">最大滑点</div>
                    <div className="text-yellow-400 font-medium">5%</div>
                  </div>
                  <div>
                    <div className="text-white/60">Gas费预估</div>
                    <div className="text-purple-400 font-medium">$25</div>
                  </div>
                </div>
              </div>

              {/* 执行配置 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">执行配置</h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-white/70 text-sm">清仓比例</span>
                    <span className="text-white font-medium">100%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/70 text-sm">执行方式</span>
                    <span className="text-blue-400 font-medium">并行执行</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/70 text-sm">DEX路由</span>
                    <span className="text-green-400 font-medium">1inch优化</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/70 text-sm">MEV保护</span>
                    <span className="text-purple-400 font-medium">已启用</span>
                  </div>
                </div>
              </div>

              {/* 紧急退出按钮 */}
              <div className="pt-4">
                {!emergencyExitStatus?.active ? (
                  <button
                    onClick={handleEmergencyExit}
                    className={`w-full px-6 py-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center space-x-3 ${
                      emergencyConfirm
                        ? 'bg-red-600 hover:bg-red-700 text-white animate-pulse'
                        : 'bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30'
                    }`}
                  >
                    <AlertTriangle className="w-5 h-5" />
                    <span className="text-lg">
                      {emergencyConfirm ? '确认执行紧急退出' : '紧急退出'}
                    </span>
                  </button>
                ) : (
                  <button
                    className="w-full px-6 py-4 rounded-lg font-medium bg-green-500/20 hover:bg-green-500/30 text-green-400 border border-green-500/30 flex items-center justify-center space-x-3"
                  >
                    <Zap className="w-5 h-5" />
                    <span className="text-lg">重启系统</span>
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* 执行历史和风险提示 */}
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-400" />
              <span>执行历史</span>
            </h3>

            <div className="space-y-4">
              {/* 最近执行记录 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">最近执行记录</h4>
                <div className="space-y-3">
                  {emergencyExitStatus?.lastExecution ? (
                    <div className="text-sm">
                      <div className="flex justify-between mb-1">
                        <span className="text-white/60">执行时间</span>
                        <span className="text-white">
                          {new Date(emergencyExitStatus.lastExecution).toLocaleString('zh-CN')}
                        </span>
                      </div>
                      <div className="flex justify-between mb-1">
                        <span className="text-white/60">执行数量</span>
                        <span className="text-green-400">{emergencyExitStatus.preSignedTxs}笔交易</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/60">执行结果</span>
                        <span className="text-green-400">成功</span>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-white/50 py-4">
                      <AlertTriangle className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p>暂无执行记录</p>
                    </div>
                  )}
                </div>
              </div>

              {/* 风险提示 */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h4 className="text-red-400 font-medium mb-3 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>重要风险提示</span>
                </h4>
                <div className="text-sm text-white/70 space-y-2">
                  <div>• 紧急退出将立即清仓所有持仓</div>
                  <div>• 可能产生较大滑点和MEV损失</div>
                  <div>• 执行后无法撤销，请谨慎操作</div>
                  <div>• 建议仅在极端市场情况下使用</div>
                </div>
              </div>

              {/* 技术细节 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">技术细节</h4>
                <div className="text-sm text-white/60 space-y-1">
                  <div>• 使用ThreadPoolExecutor并发执行</div>
                  <div>• 1inch API优化交易路径</div>
                  <div>• 预签名交易减少执行延迟</div>
                  <div>• 智能Gas费用优化</div>
                  <div>• 实时滑点监控和保护</div>
                </div>
              </div>

              {/* 联系支持 */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                <h4 className="text-blue-400 font-medium mb-2">需要帮助？</h4>
                <div className="text-sm text-white/70">
                  如遇到紧急情况或技术问题，请立即联系技术支持团队
                </div>
                <button className="mt-2 text-blue-400 text-sm hover:text-blue-300">
                  联系技术支持 →
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 设置面板 */}
      {showSettings && (
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-6">动态策略设置</h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 基础设置 */}
            <div>
              <h4 className="text-white font-medium mb-4">基础参数</h4>
              <div className="space-y-4">
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    退出信号阈值: {(settings.exitThreshold * 100).toFixed(0)}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={settings.exitThreshold}
                    onChange={(e) => handleSettingChange('exitThreshold', parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    止损线: {settings.stopLoss}%
                  </label>
                  <input
                    type="range"
                    min="5"
                    max="30"
                    step="1"
                    value={settings.stopLoss}
                    onChange={(e) => handleSettingChange('stopLoss', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    期权对冲比例: {settings.hedgeRatio}%
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    value={settings.hedgeRatio}
                    onChange={(e) => handleSettingChange('hedgeRatio', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
            </div>

            {/* 动态卖出参数 */}
            <div>
              <h4 className="text-white font-medium mb-4">动态卖出参数</h4>
              <div className="space-y-4">
                {Object.entries(settings.volatilityParams).map(([level, params]) => (
                  <div key={level} className="bg-white/5 rounded-lg p-4">
                    <h5 className="text-white text-sm font-medium mb-3 capitalize">
                      {level === 'high' ? '高波动 (>70%)' : 
                       level === 'medium' ? '中波动 (40-70%)' : 
                       '低波动 (<40%)'}
                    </h5>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-white/60 text-xs mb-1">批次大小 (%)</label>
                        <input
                          type="number"
                          min="1"
                          max="20"
                          value={params.batchSize}
                          onChange={(e) => handleVolatilityChange(level, 'batchSize', e.target.value)}
                          className="input-field w-full text-sm"
                        />
                      </div>
                      <div>
                        <label className="block text-white/60 text-xs mb-1">间隔 (分钟)</label>
                        <input
                          type="number"
                          min="1"
                          max="120"
                          value={params.interval}
                          onChange={(e) => handleVolatilityChange(level, 'interval', e.target.value)}
                          className="input-field w-full text-sm"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 警告提示 */}
      {signalStrength >= 0.8 && (
        <div className="card p-4 bg-red-500/10 border border-red-500/20">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-6 h-6 text-red-400 animate-pulse" />
            <div>
              <h4 className="text-red-400 font-medium">高风险警告</h4>
              <p className="text-white/70 text-sm">
                策略信号强度已达到 {(signalStrength * 100).toFixed(0)}%，建议立即关注市场动态并考虑执行动态策略调整。
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExitSystem
