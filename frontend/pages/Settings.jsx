import React, { useState } from 'react'
import { 
  Settings as SettingsIcon, 
  Globe, 
  Shield, 
  Database, 
  Key,
  Bell,
  Palette,
  Save
} from 'lucide-react'

const Settings = () => {
  const [settings, setSettings] = useState({
    // 全局设置
    geofence: ['US', 'EU', 'JP'],
    complianceLevel: 3,
    dataRetention: '30days',
    
    // API设置
    apiKeys: {
      twitter: '••••••••••••••••',
      reddit: '••••••••••••••••',
      polymarket: '••••••••••••••••',
      coingecko: '••••••••••••••••'
    },
    
    // 通知设置
    notifications: {
      hotspotAlerts: true,
      strategyUpdates: true,
      walletAlerts: true,
      liquidityWarnings: true,
      exitSignals: true
    },
    
    // 界面设置
    theme: 'dark',
    language: 'zh-CN',
    autoRefresh: true,
    refreshInterval: 5
  })

  const [showApiKeys, setShowApiKeys] = useState({})

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const handleGlobalSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const toggleApiKeyVisibility = (service) => {
    setShowApiKeys(prev => ({
      ...prev,
      [service]: !prev[service]
    }))
  }

  const handleGeofenceChange = (region, checked) => {
    const newGeofence = checked 
      ? [...settings.geofence, region]
      : settings.geofence.filter(r => r !== region)
    
    handleGlobalSettingChange('geofence', newGeofence)
  }

  const saveSettings = () => {
    // 这里应该调用API保存设置
    console.log('Saving settings:', settings)
    alert('设置已保存')
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">系统设置</h1>
          <p className="text-white/60 mt-1">配置系统参数和个人偏好</p>
        </div>
        <button
          onClick={saveSettings}
          className="btn-primary flex items-center space-x-2"
        >
          <Save className="w-4 h-4" />
          <span>保存设置</span>
        </button>
      </div>

      {/* 全局参数 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Globe className="w-5 h-5 text-blue-400" />
          <span>全局参数</span>
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h4 className="text-white font-medium mb-4">地理围栏</h4>
            <div className="space-y-3">
              {[
                { code: 'US', name: '美国' },
                { code: 'EU', name: '欧盟' },
                { code: 'JP', name: '日本' },
                { code: 'KR', name: '韩国' },
                { code: 'SG', name: '新加坡' },
                { code: 'AU', name: '澳大利亚' }
              ].map((region) => (
                <div key={region.code} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={settings.geofence.includes(region.code)}
                    onChange={(e) => handleGeofenceChange(region.code, e.target.checked)}
                    className="w-4 h-4 text-primary-600 rounded"
                  />
                  <span className="text-white">{region.name} ({region.code})</span>
                </div>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-white font-medium mb-4">合规与数据</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">
                  合规检查严格度: {settings.complianceLevel}/5
                </label>
                <input
                  type="range"
                  min="1"
                  max="5"
                  value={settings.complianceLevel}
                  onChange={(e) => handleGlobalSettingChange('complianceLevel', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">数据保留策略</label>
                <select
                  value={settings.dataRetention}
                  onChange={(e) => handleGlobalSettingChange('dataRetention', e.target.value)}
                  className="input-field w-full"
                >
                  <option value="7days">7天</option>
                  <option value="30days">30天</option>
                  <option value="90days">90天</option>
                  <option value="permanent">永久</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* API密钥管理 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Key className="w-5 h-5 text-yellow-400" />
          <span>API密钥管理</span>
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(settings.apiKeys).map(([service, key]) => (
            <div key={service} className="bg-white/5 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-white font-medium capitalize">{service}</span>
                <button
                  onClick={() => toggleApiKeyVisibility(service)}
                  className="text-blue-400 hover:text-blue-300 text-sm"
                >
                  {showApiKeys[service] ? '隐藏' : '显示'}
                </button>
              </div>
              <input
                type={showApiKeys[service] ? 'text' : 'password'}
                value={showApiKeys[service] ? key : '••••••••••••••••'}
                onChange={(e) => handleSettingChange('apiKeys', service, e.target.value)}
                className="input-field w-full"
                placeholder={`输入${service} API密钥`}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 通知设置 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Bell className="w-5 h-5 text-green-400" />
          <span>通知设置</span>
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(settings.notifications).map(([key, enabled]) => (
            <div key={key} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
              <span className="text-white">
                {key === 'hotspotAlerts' ? '热点警报' :
                 key === 'strategyUpdates' ? '策略更新' :
                 key === 'walletAlerts' ? '钱包警报' :
                 key === 'liquidityWarnings' ? '流动性警告' :
                 key === 'exitSignals' ? '退出信号' : key}
              </span>
              <input
                type="checkbox"
                checked={enabled}
                onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
                className="w-4 h-4 text-primary-600 rounded"
              />
            </div>
          ))}
        </div>
      </div>

      {/* 界面设置 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Palette className="w-5 h-5 text-purple-400" />
          <span>界面设置</span>
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h4 className="text-white font-medium mb-4">外观</h4>
            <div className="space-y-4">
              <div>
                <label className="block text-white/70 text-sm mb-2">主题</label>
                <select
                  value={settings.theme}
                  onChange={(e) => handleGlobalSettingChange('theme', e.target.value)}
                  className="input-field w-full"
                >
                  <option value="dark">暗黑模式</option>
                  <option value="light">明亮模式</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>

              <div>
                <label className="block text-white/70 text-sm mb-2">语言</label>
                <select
                  value={settings.language}
                  onChange={(e) => handleGlobalSettingChange('language', e.target.value)}
                  className="input-field w-full"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="en-US">English</option>
                  <option value="ja-JP">日本語</option>
                </select>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-white font-medium mb-4">数据刷新</h4>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.autoRefresh}
                  onChange={(e) => handleGlobalSettingChange('autoRefresh', e.target.checked)}
                  className="w-4 h-4 text-primary-600 rounded"
                />
                <span className="text-white">自动刷新数据</span>
              </div>

              {settings.autoRefresh && (
                <div>
                  <label className="block text-white/70 text-sm mb-2">
                    刷新间隔: {settings.refreshInterval}秒
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="60"
                    value={settings.refreshInterval}
                    onChange={(e) => handleGlobalSettingChange('refreshInterval', parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 安全设置 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Shield className="w-5 h-5 text-red-400" />
          <span>安全设置</span>
        </h3>
        
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Shield className="w-5 h-5 text-red-400" />
            <span className="text-red-400 font-medium">安全提醒</span>
          </div>
          <ul className="text-white/70 text-sm space-y-1">
            <li>• 定期更换API密钥以确保安全</li>
            <li>• 不要在公共网络环境下使用系统</li>
            <li>• 启用所有安全通知以及时了解风险</li>
            <li>• 定期备份重要配置和数据</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default Settings
