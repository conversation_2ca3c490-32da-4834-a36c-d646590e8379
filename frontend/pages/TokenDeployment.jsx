import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import {
  Zap,
  Brain,
  TrendingUp,
  Target,
  Shield,
  Clock,
  DollarSign,
  Users,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Copy,
  ExternalLink,
  RefreshCw,
  <PERSON>rkles
} from 'lucide-react'

const TokenDeployment = () => {
  const { hotspots } = useSelector(state => state.hotspot)
  const [selectedHotspot, setSelectedHotspot] = useState(null)
  const [generatedStrategy, setGeneratedStrategy] = useState(null)
  const [isGenerating, setIsGenerating] = useState(false)
  const [isDeploying, setIsDeploying] = useState(false)
  const [deploymentResult, setDeploymentResult] = useState(null)
  const [trendingStrategies, setTrendingStrategies] = useState([])

  // 获取推荐策略和检查预选热点
  useEffect(() => {
    fetchTrendingStrategies()

    // 检查是否有从热点页面传来的数据
    const savedHotspot = sessionStorage.getItem('selectedHotspot')
    if (savedHotspot) {
      try {
        const hotspotData = JSON.parse(savedHotspot)
        generateStrategy(hotspotData)
        sessionStorage.removeItem('selectedHotspot') // 清除数据
      } catch (error) {
        console.error('解析热点数据失败:', error)
      }
    }
  }, [])

  const fetchTrendingStrategies = async () => {
    try {
      const response = await fetch('http://localhost:8001/ai/token-strategies/trending')
      const data = await response.json()
      if (data.status === 'success') {
        setTrendingStrategies(data.trending_strategies)
      }
    } catch (error) {
      console.error('获取推荐策略失败:', error)
    }
  }

  // 生成AI策略
  const generateStrategy = async (hotspot) => {
    setIsGenerating(true)
    setSelectedHotspot(hotspot)
    
    try {
      const response = await fetch('http://localhost:8001/ai/generate-token-strategy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          keyword: hotspot.keyword || hotspot.title,
          category: hotspot.category,
          score: hotspot.score,
          sentiment: hotspot.sentiment || 0.75,
          volume: hotspot.volume || 1000
        })
      })
      
      const data = await response.json()
      if (data.status === 'success') {
        setGeneratedStrategy(data.strategy)
      }
    } catch (error) {
      console.error('生成策略失败:', error)
      alert('策略生成失败，请重试')
    } finally {
      setIsGenerating(false)
    }
  }

  // 一键部署
  const deployToken = async () => {
    if (!generatedStrategy) return
    
    setIsDeploying(true)
    
    try {
      const response = await fetch('http://localhost:8001/ai/deploy-token-strategy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(generatedStrategy)
      })
      
      const data = await response.json()
      if (data.status === 'success') {
        setDeploymentResult(data.deployment)
        alert('🎉 代币部署成功！')
      }
    } catch (error) {
      console.error('部署失败:', error)
      alert('部署失败，请重试')
    } finally {
      setIsDeploying(false)
    }
  }

  // 获取高分热点
  const getHighScoreHotspots = () => {
    return hotspots.filter(h => h.score >= 0.7).sort((a, b) => b.score - a.score).slice(0, 10)
  }

  const getRiskColor = (risk) => {
    switch (risk) {
      case '低': return 'text-green-400'
      case '中等': return 'text-yellow-400'
      case '高': return 'text-red-400'
      default: return 'text-white'
    }
  }

  return (
    <div className="space-y-6 fade-in">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white flex items-center space-x-3">
            <Zap className="w-8 h-8 text-purple-400" />
            <span>AI发币部署中心</span>
          </h1>
          <p className="text-white/60 mt-1">
            基于实时热点 • AI策略生成 • 一键智能部署
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchTrendingStrategies}
            className="btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>刷新策略</span>
          </button>
        </div>
      </div>

      {/* 推荐策略卡片 */}
      <div className="card p-6">
        <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
          <Sparkles className="w-5 h-5 text-yellow-400" />
          <span>AI推荐策略</span>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
        </h3>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {trendingStrategies.map((strategy, index) => (
            <div key={strategy.id} className="bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-200 border border-white/10">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="text-white font-medium">{strategy.token_name}</h4>
                  <p className="text-white/60 text-sm">{strategy.hotspot_keyword}</p>
                </div>
                <div className="text-right">
                  <div className="text-green-400 font-bold text-sm">{strategy.estimated_roi}</div>
                  <div className={`text-xs ${getRiskColor(strategy.risk_level)}`}>{strategy.risk_level}风险</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between mb-3">
                <span className="text-xs bg-primary-500/20 text-primary-300 px-2 py-1 rounded">
                  {strategy.category}
                </span>
                <span className="text-white font-bold">{(strategy.score * 100).toFixed(0)}%</span>
              </div>
              
              {strategy.time_sensitive && (
                <div className="flex items-center space-x-1 mb-3 text-orange-400 text-xs">
                  <Clock className="w-3 h-3" />
                  <span>时效性: {strategy.expires_in}</span>
                </div>
              )}
              
              <p className="text-white/70 text-xs mb-3">{strategy.reason}</p>
              
              <button
                onClick={() => generateStrategy({
                  keyword: strategy.hotspot_keyword,
                  category: strategy.category,
                  score: strategy.score,
                  title: strategy.token_name
                })}
                className="w-full btn-primary text-sm py-2"
              >
                生成详细策略
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* 热点选择区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 高分热点列表 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            <span>高分热点推荐</span>
          </h3>
          
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {getHighScoreHotspots().map((hotspot, index) => (
              <div 
                key={index} 
                className={`p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                  selectedHotspot?.title === hotspot.title
                    ? 'border-primary-400 bg-primary-500/20'
                    : 'border-white/10 bg-white/5 hover:border-white/30'
                }`}
                onClick={() => generateStrategy(hotspot)}
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium flex-1">{hotspot.title}</h4>
                  <span className="text-green-400 font-bold text-sm">
                    {(hotspot.score * 100).toFixed(0)}%
                  </span>
                </div>
                
                <p className="text-white/60 text-sm mb-2">{hotspot.description}</p>
                
                <div className="flex items-center justify-between">
                  <span className="text-xs bg-primary-500/20 text-primary-300 px-2 py-1 rounded">
                    {hotspot.category}
                  </span>
                  <span className="text-xs text-white/50">{hotspot.source}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 策略生成结果 */}
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Brain className="w-5 h-5 text-purple-400" />
            <span>AI策略生成</span>
          </h3>
          
          {isGenerating ? (
            <div className="text-center py-12">
              <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-white/60">AI正在分析热点并生成最优策略...</p>
            </div>
          ) : generatedStrategy ? (
            <div className="space-y-4">
              {/* 代币基本信息 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">代币信息</h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-white/70">名称:</span>
                    <span className="text-white ml-2">{generatedStrategy.token_info.name}</span>
                  </div>
                  <div>
                    <span className="text-white/70">符号:</span>
                    <span className="text-white ml-2">{generatedStrategy.token_info.symbol}</span>
                  </div>
                  <div>
                    <span className="text-white/70">总供应量:</span>
                    <span className="text-white ml-2">{generatedStrategy.token_info.total_supply.toLocaleString()}</span>
                  </div>
                  <div>
                    <span className="text-white/70">初始价格:</span>
                    <span className="text-white ml-2">${generatedStrategy.token_info.initial_price}</span>
                  </div>
                </div>
                <p className="text-white/60 text-sm mt-3">{generatedStrategy.token_info.description}</p>
              </div>

              {/* 分配策略 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">代币分配</h4>
                <div className="space-y-2">
                  {Object.entries(generatedStrategy.allocation).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <span className="text-white/70 capitalize">{key}:</span>
                      <span className="text-white">{(value * 100).toFixed(1)}%</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 部署信息 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">部署配置</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white/70">推荐平台:</span>
                    <span className="text-blue-400">{generatedStrategy.deployment.recommended_platform}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">预估成本:</span>
                    <span className="text-green-400">{generatedStrategy.deployment.estimated_cost.solana}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white/70">预估时间:</span>
                    <span className="text-yellow-400">{generatedStrategy.deployment.estimated_time}</span>
                  </div>
                </div>
              </div>

              {/* 成功指标 */}
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-3">成功预期</h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div className="text-center">
                    <div className="text-white/70">目标市值</div>
                    <div className="text-green-400 font-bold">{generatedStrategy.success_metrics.target_market_cap}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-white/70">成功概率</div>
                    <div className="text-blue-400 font-bold">{generatedStrategy.success_metrics.success_probability}</div>
                  </div>
                </div>
              </div>

              {/* 一键部署按钮 */}
              <button
                onClick={deployToken}
                disabled={isDeploying}
                className="w-full btn-gradient bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center space-x-2 py-3"
              >
                {isDeploying ? (
                  <>
                    <RefreshCw className="w-5 h-5 animate-spin" />
                    <span>部署中...</span>
                  </>
                ) : (
                  <>
                    <Zap className="w-5 h-5" />
                    <span>一键部署代币</span>
                    <Zap className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          ) : (
            <div className="text-center py-12">
              <Brain className="w-12 h-12 mx-auto mb-4 text-white/30" />
              <p className="text-white/60">选择热点生成AI策略</p>
              <p className="text-white/40 text-sm">点击左侧热点或推荐策略开始</p>
            </div>
          )}
        </div>
      </div>

      {/* 部署结果 */}
      {deploymentResult && (
        <div className="card p-6 border-l-4 border-green-500">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <span>部署成功</span>
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 合约信息 */}
            <div className="space-y-4">
              <div className="bg-green-500/10 rounded-lg p-4 border border-green-500/30">
                <h4 className="text-green-400 font-medium mb-3">合约信息</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">合约地址:</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-green-400 font-mono">{deploymentResult.contract_address}</span>
                      <button className="text-white/50 hover:text-white">
                        <Copy className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">区块链浏览器:</span>
                    <a 
                      href={deploymentResult.explorer_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-400 hover:text-blue-300 flex items-center space-x-1"
                    >
                      <span>查看</span>
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-white/70">审计评分:</span>
                    <span className="text-green-400">{deploymentResult.token_info.audit_score}/100</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 下一步操作 */}
            <div className="space-y-4">
              <div className="bg-blue-500/10 rounded-lg p-4 border border-blue-500/30">
                <h4 className="text-blue-400 font-medium mb-3">下一步操作</h4>
                <div className="space-y-2">
                  {deploymentResult.next_steps.slice(0, 3).map((step, index) => (
                    <div key={index} className="flex items-start space-x-2 text-sm">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        step.priority === '高' ? 'bg-red-400' : 
                        step.priority === '中' ? 'bg-yellow-400' : 'bg-green-400'
                      }`}></div>
                      <div>
                        <div className="text-white font-medium">{step.action}</div>
                        <div className="text-white/60 text-xs">{step.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TokenDeployment
