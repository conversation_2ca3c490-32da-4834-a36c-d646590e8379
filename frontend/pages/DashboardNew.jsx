import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  <PERSON>,
  Bot,
  Crown,
  ArrowUp,
  Calendar,
  RefreshCw,
  Flame,
  <PERSON>Chart,
  Zap,
  Wallet
} from 'lucide-react';

const DashboardNew = () => {
  const navigate = useNavigate();
  const [activeTimeframe, setActiveTimeframe] = useState('30天');

  // 模拟数据
  const user = { username: 'John', subscription_tier: 'PRO' };
  const hotspot = { hotspots: [1, 2, 3] };

  const marketData = {
    sentiment: 78.2,
    activeStrategies: 7,
    todayProfit: 3.2,
    walletBalance: 48.75,
    usdValue: 1250
  };

  // 热门代币数据
  const hotCoins = [
    { symbol: 'DOGE', name: '<PERSON><PERSON><PERSON><PERSON>', change: 12.4, positive: true },
    { symbol: 'SHIB', name: '<PERSON><PERSON> Inu', change: 8.2, positive: true },
    { symbol: 'PEPE', name: '<PERSON><PERSON><PERSON> Coin', change: -3.1, positive: false },
    { symbol: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>', change: 5.7, positive: true },
    { symbol: '<PERSON>ON<PERSON>', name: '<PERSON><PERSON>', change: 21.8, positive: true }
  ];

  // 暂时移除图表数据

  // 处理功能卡片点击
  const handleFeatureClick = (feature) => {
    switch (feature) {
      case '热点检测':
        navigate('/hotspot');
        break;
      case '策略生成':
        navigate('/strategy');
        break;
      case '一键部署':
        navigate('/business-flow');
        break;
      case '流动性管理':
        navigate('/liquidity');
        break;
      default:
        console.log('点击功能:', feature);
    }
  };

  return (
    <div className="space-y-6 fade-in">
      {/* 欢迎横幅 */}
      <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border border-purple-500/30 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">👋 欢迎回来，{user.username || 'John'}!</h2>
            <p className="text-slate-300">
              系统检测到{hotspot.hotspots?.length || 3}个新的交易机会，你的策略正在执行中。今日市场情绪指数为
              <span className="text-green-400 font-semibold"> +{marketData.sentiment}</span>
              ，适合积极交易。
            </p>
          </div>
          <button
            onClick={() => navigate('/business-flow')}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2"
          >
            <Zap className="w-5 h-5" />
            <span>查看交易机会</span>
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* AI分析使用 */}
        <div
          onClick={() => navigate('/hotspot')}
          className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-slate-400 text-sm mb-1">AI分析使用</div>
              <div className="text-2xl font-bold">42/∞</div>
            </div>
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center">
              <Brain className="w-6 h-6 text-purple-400" />
            </div>
          </div>
          <div className="flex items-center text-green-400 text-sm">
            <ArrowUp className="w-4 h-4 mr-1" />
            <span>+12% 本周使用率</span>
          </div>
        </div>

        {/* 活跃策略 */}
        <div
          onClick={() => navigate('/strategy')}
          className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-slate-400 text-sm mb-1">活跃策略</div>
              <div className="text-2xl font-bold">{marketData.activeStrategies}</div>
            </div>
            <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
              <Bot className="w-6 h-6 text-blue-400" />
            </div>
          </div>
          <div className="flex items-center text-green-400 text-sm">
            <ArrowUp className="w-4 h-4 mr-1" />
            <span>今日收益: +{marketData.todayProfit} SOL</span>
          </div>
        </div>

        {/* 订阅状态 */}
        <div
          onClick={() => navigate('/subscription')}
          className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-slate-400 text-sm mb-1">订阅状态</div>
              <div className="text-2xl font-bold">{user.subscription_tier || 'PRO'} 会员</div>
            </div>
            <div className="w-12 h-12 bg-yellow-600/20 rounded-lg flex items-center justify-center">
              <Crown className="w-6 h-6 text-yellow-400" />
            </div>
          </div>
          <div className="flex items-center text-slate-400 text-sm">
            <Calendar className="w-4 h-4 mr-1" />
            <span>到期日: 2023-12-15</span>
          </div>
        </div>

        {/* 钱包余额 */}
        <div
          onClick={() => navigate('/wallet')}
          className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-slate-400 text-sm mb-1">钱包余额</div>
              <div className="text-2xl font-bold">{marketData.walletBalance} SOL</div>
            </div>
            <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
              <Wallet className="w-6 h-6 text-green-400" />
            </div>
          </div>
          <div className="flex items-center text-slate-400 text-sm">
            <RefreshCw className="w-4 h-4 mr-1" />
            <span>≈ ${marketData.usdValue} USD</span>
          </div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* 市场趋势图表 */}
              <div className="lg:col-span-2 bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">市场情绪与交易趋势</h3>
                  <div className="flex space-x-2">
                    {['7天', '30天', '90天'].map((timeframe) => (
                      <button
                        key={timeframe}
                        onClick={() => setActiveTimeframe(timeframe)}
                        className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                          activeTimeframe === timeframe
                            ? 'bg-purple-600 text-white'
                            : 'bg-slate-700/50 text-slate-300 hover:bg-slate-600/50'
                        }`}
                      >
                        {timeframe}
                      </button>
                    ))}
                  </div>
                </div>
                <div className="h-80 bg-slate-700/30 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-4">📊</div>
                    <h3 className="text-lg font-semibold mb-2">市场趋势图表</h3>
                    <p className="text-slate-400 text-sm">图表功能即将上线</p>
                  </div>
                </div>
              </div>

              {/* 热门代币 */}
              <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold">热门Meme币</h3>
                  <button className="text-purple-400 hover:text-purple-300 text-sm font-medium">
                    查看更多
                  </button>
                </div>
                <div className="space-y-4">
                  {hotCoins.map((coin, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-all duration-200 cursor-pointer">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                          {coin.symbol.slice(0, 2)}
                        </div>
                        <div>
                          <div className="font-medium">{coin.name}</div>
                          <div className="text-slate-400 text-sm">{coin.symbol}/USD</div>
                        </div>
                      </div>
                      <div className={`font-semibold ${coin.positive ? 'text-green-400' : 'text-red-400'}`}>
                        {coin.positive ? '+' : ''}{coin.change}%
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

      {/* 核心功能 */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold">核心功能</h3>
          <button className="text-purple-400 hover:text-purple-300 text-sm font-medium">
            所有功能
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 热点检测 */}
          <div
            onClick={() => handleFeatureClick('热点检测')}
            className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
          >
            <div className="w-12 h-12 bg-red-600/20 rounded-lg flex items-center justify-center mb-4">
              <Flame className="w-6 h-6 text-red-400" />
            </div>
            <h4 className="text-lg font-semibold mb-2">热点检测</h4>
            <p className="text-slate-400 text-sm mb-4">实时监测社交媒体趋势，识别新兴Meme币机会</p>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleFeatureClick('热点检测');
              }}
              className="w-full bg-slate-700/50 hover:bg-slate-600/50 text-white py-2 rounded-lg transition-all duration-200"
            >
              立即扫描
            </button>
          </div>

          {/* 策略生成 */}
          <div
            onClick={() => handleFeatureClick('策略生成')}
            className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
          >
            <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mb-4">
              <Bot className="w-6 h-6 text-blue-400" />
            </div>
            <h4 className="text-lg font-semibold mb-2">策略生成</h4>
            <p className="text-slate-400 text-sm mb-4">AI驱动的交易策略，自动优化风险回报比</p>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleFeatureClick('策略生成');
              }}
              className="w-full bg-slate-700/50 hover:bg-slate-600/50 text-white py-2 rounded-lg transition-all duration-200"
            >
              创建策略
            </button>
          </div>

          {/* 一键部署 */}
          <div
            onClick={() => handleFeatureClick('一键部署')}
            className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
          >
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mb-4">
              <Zap className="w-6 h-6 text-purple-400" />
            </div>
            <h4 className="text-lg font-semibold mb-2">一键部署</h4>
            <p className="text-slate-400 text-sm mb-4">自动化部署代币到多个区块链网络</p>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleFeatureClick('一键部署');
              }}
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2 rounded-lg transition-all duration-200"
            >
              立即部署
            </button>
          </div>

          {/* 流动性管理 */}
          <div
            onClick={() => handleFeatureClick('流动性管理')}
            className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
          >
            <div className="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center mb-4">
              <PieChart className="w-6 h-6 text-green-400" />
            </div>
            <h4 className="text-lg font-semibold mb-2">流动性管理</h4>
            <p className="text-slate-400 text-sm mb-4">智能管理资金池，优化交易执行</p>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleFeatureClick('流动性管理');
              }}
              className="w-full bg-slate-700/50 hover:bg-slate-600/50 text-white py-2 rounded-lg transition-all duration-200"
            >
              管理流动性
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardNew;
