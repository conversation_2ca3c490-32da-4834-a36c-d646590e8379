import React from 'react'
import {
  Activity,
  TrendingUp,
  Users,
  DollarSign,
  CheckCircle,
  Zap,
  Target,
  BarChart3,
  Brain,
  Bot,
  Crown,
  Wallet
} from 'lucide-react'

const SimpleDashboard = () => {
  // 模拟数据
  const stats = {
    totalUsers: 156,
    activeStrategies: 23,
    totalProfit: 12450.67,
    successRate: 87.5
  }

  const quickStats = [
    {
      title: 'AI分析使用',
      value: '42/∞',
      icon: Brain,
      color: 'text-purple-400',
      bgColor: 'bg-purple-600/20',
      trend: '+12% 本周'
    },
    {
      title: '活跃策略',
      value: stats.activeStrategies,
      icon: Bot,
      color: 'text-blue-400',
      bgColor: 'bg-blue-600/20',
      trend: '今日收益: +3.2 SOL'
    },
    {
      title: '订阅状态',
      value: 'PRO 会员',
      icon: Crown,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-600/20',
      trend: '到期: 2023-12-15'
    },
    {
      title: '钱包余额',
      value: '48.75 SOL',
      icon: Wallet,
      color: 'text-green-400',
      bgColor: 'bg-green-600/20',
      trend: '≈ $1,250 USD'
    }
  ]

  const systemMetrics = [
    {
      title: '总用户数',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-400'
    },
    {
      title: '活跃策略',
      value: stats.activeStrategies,
      icon: BarChart3,
      color: 'text-green-400'
    },
    {
      title: '总收益',
      value: `$${stats.totalProfit.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-yellow-400'
    },
    {
      title: '成功率',
      value: `${stats.successRate}%`,
      icon: Target,
      color: 'text-purple-400'
    }
  ]

  return (
    <div className="space-y-6">
      {/* 欢迎横幅 */}
      <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border border-purple-500/30 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">👋 欢迎使用 MemeMaster AI</h1>
            <p className="text-slate-300">
              智能交易系统正在为您监控市场机会。系统检测到 3 个新的交易机会，
              <span className="text-green-400 font-semibold"> 市场情绪指数 +78.2</span>
              ，适合积极交易。
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-6 h-6 text-green-400" />
            <span className="text-green-400 font-medium">系统在线</span>
          </div>
        </div>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => (
          <div
            key={index}
            className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 hover:transform hover:-translate-y-1 cursor-pointer"
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <div className="text-slate-400 text-sm mb-1">{stat.title}</div>
                <div className="text-2xl font-bold">{stat.value}</div>
              </div>
              <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
            <div className="text-sm text-slate-400">
              {stat.trend}
            </div>
          </div>
        ))}
      </div>

      {/* 系统指标 */}
      <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
        <h2 className="text-xl font-semibold mb-6">系统指标</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {systemMetrics.map((metric, index) => (
            <div key={index} className="text-center">
              <div className={`w-16 h-16 ${metric.color.replace('text-', 'bg-').replace('-400', '-600/20')} rounded-lg flex items-center justify-center mx-auto mb-3`}>
                <metric.icon className={`w-8 h-8 ${metric.color}`} />
              </div>
              <div className="text-2xl font-bold mb-1">{metric.value}</div>
              <div className="text-slate-400 text-sm">{metric.title}</div>
            </div>
          ))}
        </div>
      </div>

      {/* 快速操作 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 cursor-pointer">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-red-600/20 rounded-lg flex items-center justify-center mr-4">
              <TrendingUp className="w-6 h-6 text-red-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">热点检测</h3>
              <p className="text-slate-400 text-sm">监测社交媒体趋势</p>
            </div>
          </div>
          <button className="w-full bg-slate-700/50 hover:bg-slate-600/50 text-white py-2 rounded-lg transition-all duration-200">
            立即扫描
          </button>
        </div>

        <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 cursor-pointer">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mr-4">
              <BarChart3 className="w-6 h-6 text-blue-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">策略生成</h3>
              <p className="text-slate-400 text-sm">AI驱动的交易策略</p>
            </div>
          </div>
          <button className="w-full bg-slate-700/50 hover:bg-slate-600/50 text-white py-2 rounded-lg transition-all duration-200">
            创建策略
          </button>
        </div>

        <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-200 cursor-pointer">
          <div className="flex items-center mb-4">
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mr-4">
              <Zap className="w-6 h-6 text-purple-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">一键部署</h3>
              <p className="text-slate-400 text-sm">自动化部署代币</p>
            </div>
          </div>
          <button className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2 rounded-lg transition-all duration-200">
            立即部署
          </button>
        </div>
      </div>

      {/* 状态信息 */}
      <div className="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-6">
        <h2 className="text-xl font-semibold mb-4">系统状态</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-slate-300">前端服务: 正常</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-slate-300">后端API: 正常</span>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-slate-300">数据库: 正常</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SimpleDashboard
