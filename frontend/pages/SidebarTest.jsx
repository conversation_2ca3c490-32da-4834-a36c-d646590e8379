import React from 'react'
import Sidebar from '../components/layout/Sidebar'

const SidebarTest = () => {
  return (
    <div className="h-screen flex bg-gradient-to-br from-gray-900 to-gray-800">
      {/* 侧边栏测试 */}
      <Sidebar
        collapsed={false}
        onToggle={() => console.log('Toggle sidebar')}
        isMobile={false}
        isOpen={true}
      />
      
      {/* 主内容区 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="flex-1 p-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-bold text-white mb-6">侧边栏图标测试页面</h1>
            
            <div className="bg-white/10 rounded-lg p-6 backdrop-blur-sm">
              <h2 className="text-xl font-semibold text-white mb-4">测试要点</h2>
              <ul className="space-y-2 text-white/80">
                <li>✅ 检查侧边栏品牌区域的机器人图标</li>
                <li>✅ 验证图标尺寸为 28px</li>
                <li>✅ 确认图标颜色为紫色主题色</li>
                <li>✅ 验证图标与文字间距为 10px</li>
                <li>✅ 确认副标题已移除</li>
                <li>✅ 测试侧边栏折叠/展开功能</li>
                <li>✅ 验证移动端响应式效果</li>
              </ul>
            </div>
            
            <div className="mt-8 bg-white/10 rounded-lg p-6 backdrop-blur-sm">
              <h2 className="text-xl font-semibold text-white mb-4">当前配置</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-white/60">品牌名称:</span>
                  <span className="text-white ml-2">MemeX</span>
                </div>
                <div>
                  <span className="text-white/60">图标:</span>
                  <span className="text-white ml-2">FaRobot (机器人)</span>
                </div>
                <div>
                  <span className="text-white/60">图标尺寸:</span>
                  <span className="text-white ml-2">28px</span>
                </div>
                <div>
                  <span className="text-white/60">图标间距:</span>
                  <span className="text-white ml-2">10px (右边距)</span>
                </div>
                <div>
                  <span className="text-white/60">副标题:</span>
                  <span className="text-white ml-2">已移除</span>
                </div>
                <div>
                  <span className="text-white/60">主题色:</span>
                  <span className="text-white ml-2">紫色渐变</span>
                </div>
              </div>
            </div>
            
            <div className="mt-8 bg-white/10 rounded-lg p-6 backdrop-blur-sm">
              <h2 className="text-xl font-semibold text-white mb-4">菜单结构</h2>
              <div className="space-y-2 text-sm text-white/80">
                <div>🏠 仪表盘</div>
                <div>🔥 趋势热点</div>
                <div>⚙️ 策略引擎</div>
                <div>🚀 一键部署</div>
                <div>📊 流动性管理</div>
                <div>🎯 动态退出</div>
                <div>🤖 自动化工具</div>
                <hr className="border-white/20 my-2" />
                <div>👤 账户管理</div>
                <div>⚙️ 系统设置</div>
                <div>❓ 帮助中心</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SidebarTest
