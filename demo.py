#!/usr/bin/env python3
"""
MemeMaster AI 演示脚本
展示系统的核心功能
"""

import sys
import os
import asyncio
import time
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append('.')

from src.utils.logger import LoggerManager
from src.hotspot.detector import ViralityCalculator
from src.strategy.generator import StrategyGenerator, EconomicModelCalculator, FearGreedIndex
from src.compliance.geofence import GeoFence
from src.compliance.trademark import TrademarkAPI

logger = LoggerManager.get_logger('demo')


def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                     MemeMaster AI Demo                       ║
    ║              AI-Powered Meme Token Generator                 ║
    ║                        Version 2.0                          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def demo_viral_calculation():
    """演示病毒性计算"""
    print("\n🔥 病毒性分析演示")
    print("=" * 50)
    
    calculator = ViralityCalculator()
    
    # 测试不同的数据集
    test_cases = [
        {
            'name': '高病毒性内容',
            'data': {
                'likes': 50000,
                'shares': 10000,
                'comments': 5000,
                'views': 200000,
                'timestamp': time.time() - 3600,  # 1小时前
                'growth_rate': 0.9
            }
        },
        {
            'name': '中等病毒性内容',
            'data': {
                'likes': 5000,
                'shares': 1000,
                'comments': 500,
                'views': 50000,
                'timestamp': time.time() - 7200,  # 2小时前
                'growth_rate': 0.5
            }
        },
        {
            'name': '低病毒性内容',
            'data': {
                'likes': 100,
                'shares': 20,
                'comments': 10,
                'views': 2000,
                'timestamp': time.time() - 86400,  # 1天前
                'growth_rate': 0.1
            }
        }
    ]
    
    for case in test_cases:
        score = calculator.calculate_viral_score(case['data'])
        print(f"📊 {case['name']}: {score:.3f}")
        print(f"   - 点赞: {case['data']['likes']:,}")
        print(f"   - 分享: {case['data']['shares']:,}")
        print(f"   - 评论: {case['data']['comments']:,}")
        print(f"   - 浏览: {case['data']['views']:,}")
        print()


def demo_economic_model():
    """演示经济模型计算"""
    print("\n💰 经济模型演示")
    print("=" * 50)
    
    calculator = EconomicModelCalculator()
    
    # 测试不同市场条件下的燃烧率
    market_conditions = [
        {'name': '极度贪婪市场', 'greed_index': 90, 'volatility': 0.3},
        {'name': '贪婪市场', 'greed_index': 75, 'volatility': 0.5},
        {'name': '中性市场', 'greed_index': 50, 'volatility': 0.4},
        {'name': '恐惧市场', 'greed_index': 25, 'volatility': 0.7},
        {'name': '极度恐惧市场', 'greed_index': 10, 'volatility': 0.9}
    ]
    
    for condition in market_conditions:
        burn_rate = calculator.calculate_burn_rate(
            condition['greed_index'], 
            condition['volatility']
        )
        print(f"🔥 {condition['name']}: {burn_rate:.1%}")
        print(f"   - 恐惧贪婪指数: {condition['greed_index']}")
        print(f"   - 波动率: {condition['volatility']:.1%}")
        print()
    
    # 演示供应分配
    print("📈 代币供应分配 (10亿总量):")
    total_supply = 1_000_000_000
    distribution = calculator.calculate_supply_distribution(total_supply)
    
    for category, amount in distribution.items():
        percentage = (amount / total_supply) * 100
        print(f"   - {category.replace('_', ' ').title()}: {amount:,} ({percentage:.0f}%)")


def demo_compliance_check():
    """演示合规检查"""
    print("\n🛡️ 合规检查演示")
    print("=" * 50)
    
    # 地理围栏演示
    geofence = GeoFence()
    print("🌍 地理围栏检查:")
    print(f"   - 受限地区: {', '.join(geofence.restricted_countries)}")
    print(f"   - 合规模式: {geofence.compliance_mode}")
    print(f"   - 地理围栏启用: {geofence.geofence_enabled}")
    print()
    
    # 商标检查演示
    trademark_api = TrademarkAPI()
    print("📝 商标检查演示:")
    
    test_names = [
        'TestCoin',
        'Bitcoin',  # 应该冲突
        'Apple',    # 应该冲突
        'MyAwesomeCoin',
        'SafeMoon'  # 应该冲突
    ]
    
    for name in test_names:
        has_conflict = trademark_api.check_trademark(name)
        status = "❌ 冲突" if has_conflict else "✅ 可用"
        print(f"   - {name}: {status}")


def demo_strategy_generation():
    """演示策略生成"""
    print("\n🧠 策略生成演示")
    print("=" * 50)
    
    # 模拟热点数据
    hotspot_data = {
        'content': 'Amazing new DeFi protocol with revolutionary features!',
        'sentiment_score': 0.8,
        'viral_score': 0.75,
        'source': 'twitter',
        'timestamp': time.time()
    }
    
    print("📊 输入热点数据:")
    print(f"   - 内容: {hotspot_data['content'][:50]}...")
    print(f"   - 情感分数: {hotspot_data['sentiment_score']}")
    print(f"   - 病毒性分数: {hotspot_data['viral_score']}")
    print(f"   - 来源: {hotspot_data['source']}")
    print()
    
    try:
        # 获取当前恐惧贪婪指数
        greed_index = FearGreedIndex.fetch()
        print(f"📈 当前恐惧贪婪指数: {greed_index}")
    except:
        greed_index = 50.0
        print(f"📈 恐惧贪婪指数 (模拟): {greed_index}")
    
    # 生成策略
    generator = StrategyGenerator()
    strategy = generator.generate_token_strategy(hotspot_data)
    
    if strategy:
        print("\n🎯 生成的代币策略:")
        print(f"   - 代币名称: {strategy.token_name}")
        print(f"   - 符号: {strategy.symbol}")
        print(f"   - 总供应量: {strategy.total_supply:,}")
        print(f"   - 燃烧率: {strategy.burn_rate:.1%}")
        print(f"   - 预估ROI: {strategy.estimated_roi:.1f}x")
        print(f"   - 合规状态: {strategy.compliance_status}")
        
        # 显示发布时间
        launch_times = strategy.launch_schedule.get('launch_times', {})
        if launch_times:
            print("\n⏰ 最佳发布时间:")
            for region, time_obj in launch_times.items():
                print(f"   - {region}: {time_obj.strftime('%Y-%m-%d %H:%M UTC')}")
    else:
        print("❌ 策略生成失败")


def demo_system_monitoring():
    """演示系统监控"""
    print("\n📊 系统监控演示")
    print("=" * 50)
    
    try:
        from src.utils.circuit_breaker import global_circuit_breaker
        
        # 获取系统指标
        metrics = global_circuit_breaker.get_metrics()
        state = global_circuit_breaker.get_state()
        
        print("🖥️ 系统指标:")
        print(f"   - CPU使用率: {metrics.cpu_percent:.1f}%")
        print(f"   - 内存使用率: {metrics.memory_percent:.1f}%")
        print(f"   - 磁盘使用率: {metrics.disk_percent:.1f}%")
        print(f"   - 活跃连接: {metrics.active_connections}")
        print(f"   - 平均延迟: {metrics.avg_latency:.3f}s")
        print(f"   - 错误率: {metrics.error_rate:.1f}%")
        print()
        
        print("🔧 熔断器状态:")
        print(f"   - 当前状态: {state.value}")
        
        # 显示阈值
        thresholds = global_circuit_breaker.TRIGGER_THRESHOLDS
        print("   - 触发阈值:")
        for metric, threshold in thresholds.items():
            print(f"     * {metric}: {threshold}")
            
    except Exception as e:
        print(f"❌ 监控数据获取失败: {str(e)}")


def main():
    """主演示函数"""
    print_banner()
    
    print("🚀 开始 MemeMaster AI 功能演示...")
    print()
    
    try:
        # 运行各个演示模块
        demo_viral_calculation()
        demo_economic_model()
        demo_compliance_check()
        demo_strategy_generation()
        demo_system_monitoring()
        
        print("\n" + "=" * 60)
        print("🎉 演示完成！MemeMaster AI 系统运行正常")
        print("=" * 60)
        
        print("\n📋 下一步操作:")
        print("1. 运行 './start.sh dev' 启动开发服务器")
        print("2. 访问 http://localhost:3000/docs 查看API文档")
        print("3. 运行 './start.sh test' 执行完整测试")
        print("4. 运行 './start.sh prod' 启动生产环境")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {str(e)}")
        print(f"\n❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
