/**
 * MemeMaster AI 高级筛选功能验证测试
 * 验证热点监控页面的高级筛选功能是否正常工作
 */

const puppeteer = require('puppeteer');

class AdvancedFiltersVerifier {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
  }

  async init() {
    console.log('🚀 启动高级筛选功能验证测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async navigateToHotspotPage() {
    console.log('📍 导航到热点监控页面...');
    
    try {
      await this.page.goto('http://localhost:3002/hotspot', {
        waitUntil: 'networkidle2',
        timeout: 30000
      });
      
      await this.page.waitForSelector('h1', { timeout: 10000 });
      const title = await this.page.$eval('h1', el => el.textContent);
      
      this.addResult('页面导航', title.includes('实时热点监控'), `页面标题: ${title}`);
      
    } catch (error) {
      this.addResult('页面导航', false, `导航失败: ${error.message}`);
    }
  }

  async verifyAdvancedFiltersButton() {
    console.log('🔍 验证高级筛选按钮...');
    
    try {
      // 确保在实时热点标签页
      const hotspotTab = await this.page.$('button:has-text("实时热点")');
      if (hotspotTab) {
        await hotspotTab.click();
        await this.page.waitForTimeout(1000);
      }
      
      // 检查高级筛选按钮是否存在
      const filtersButton = await this.page.$('button:has-text("高级筛选")');
      this.addResult('高级筛选按钮存在', filtersButton !== null, '高级筛选按钮存在');
      
      if (filtersButton) {
        // 点击高级筛选按钮
        await filtersButton.click();
        await this.page.waitForTimeout(1000);
        
        // 验证按钮激活状态
        const isActive = await this.page.$eval('button:has-text("高级筛选")', 
          el => el.classList.contains('bg-blue-500/20')
        );
        
        this.addResult('高级筛选按钮激活', isActive, '高级筛选按钮成功激活');
      }
      
    } catch (error) {
      this.addResult('高级筛选按钮验证', false, `按钮验证失败: ${error.message}`);
    }
  }

  async verifyFiltersPanel() {
    console.log('📊 验证筛选面板...');
    
    try {
      // 确保筛选面板已打开
      await this.page.click('button:has-text("高级筛选")');
      await this.page.waitForTimeout(1000);
      
      // 验证筛选面板标题
      const panelTitle = await this.page.$('h3:has-text("高级筛选器")');
      this.addResult('筛选面板标题', panelTitle !== null, '高级筛选器标题显示正确');
      
      // 验证操作按钮
      const resetButton = await this.page.$('button:has-text("重置")');
      const applyButton = await this.page.$('button:has-text("应用筛选")');
      const closeButton = await this.page.$('button svg'); // X按钮
      
      this.addResult('筛选操作按钮', resetButton && applyButton && closeButton, '重置、应用和关闭按钮存在');
      
    } catch (error) {
      this.addResult('筛选面板验证', false, `筛选面板验证失败: ${error.message}`);
    }
  }

  async verifyCategoryFilters() {
    console.log('🏷️ 验证分类筛选...');
    
    try {
      // 验证分类筛选标题
      const categoryTitle = await this.page.$('label:has-text("分类筛选")');
      this.addResult('分类筛选标题', categoryTitle !== null, '分类筛选标题存在');
      
      // 验证分类选项
      const categories = ['政治', '亚文化', '科技', '元宇宙', 'GameFi', 'NFT', '体育'];
      let categoryCount = 0;
      
      for (const category of categories) {
        const categoryElement = await this.page.$(`span:has-text("${category}")`);
        if (categoryElement) categoryCount++;
      }
      
      this.addResult('分类选项', categoryCount >= 5, `找到 ${categoryCount}/${categories.length} 个分类选项`);
      
      // 验证分类复选框
      const categoryCheckboxes = await this.page.$$('input[type="checkbox"]');
      this.addResult('分类复选框', categoryCheckboxes.length > 0, `找到 ${categoryCheckboxes.length} 个复选框`);
      
    } catch (error) {
      this.addResult('分类筛选验证', false, `分类筛选验证失败: ${error.message}`);
    }
  }

  async verifySourceFilters() {
    console.log('🌐 验证数据源筛选...');
    
    try {
      // 验证数据源筛选标题
      const sourceTitle = await this.page.$('label:has-text("数据源")');
      this.addResult('数据源筛选标题', sourceTitle !== null, '数据源筛选标题存在');
      
      // 验证数据源选项
      const sources = ['Twitter', 'Reddit', 'TikTok', 'YouTube', 'Discord', 'Darkweb'];
      let sourceCount = 0;
      
      for (const source of sources) {
        const sourceElement = await this.page.$(`span:has-text("${source}")`);
        if (sourceElement) sourceCount++;
      }
      
      this.addResult('数据源选项', sourceCount >= 4, `找到 ${sourceCount}/${sources.length} 个数据源选项`);
      
      // 验证置信度滑块
      const confidenceSlider = await this.page.$('input[type="range"]');
      this.addResult('置信度滑块', confidenceSlider !== null, '置信度滑块存在');
      
      // 验证置信度标签
      const confidenceLabel = await this.page.$('label:has-text("最小置信度")');
      this.addResult('置信度标签', confidenceLabel !== null, '置信度标签存在');
      
    } catch (error) {
      this.addResult('数据源筛选验证', false, `数据源筛选验证失败: ${error.message}`);
    }
  }

  async verifyRiskAndLanguageFilters() {
    console.log('⚠️ 验证风险等级和语言筛选...');
    
    try {
      // 验证风险等级筛选
      const riskTitle = await this.page.$('label:has-text("风险等级")');
      this.addResult('风险等级标题', riskTitle !== null, '风险等级标题存在');
      
      const riskLevels = ['低风险', '中风险', '高风险'];
      let riskCount = 0;
      
      for (const risk of riskLevels) {
        const riskElement = await this.page.$(`span:has-text("${risk}")`);
        if (riskElement) riskCount++;
      }
      
      this.addResult('风险等级选项', riskCount === 3, `找到 ${riskCount}/${riskLevels.length} 个风险等级选项`);
      
      // 验证语言筛选
      const languageTitle = await this.page.$('label:has-text("语言")');
      this.addResult('语言筛选标题', languageTitle !== null, '语言筛选标题存在');
      
      const languages = ['英语', '中文', '韩语', '日语'];
      let languageCount = 0;
      
      for (const lang of languages) {
        const langElement = await this.page.$(`span:has-text("${lang}")`);
        if (langElement) languageCount++;
      }
      
      this.addResult('语言选项', languageCount >= 3, `找到 ${languageCount}/${languages.length} 个语言选项`);
      
      // 验证地区筛选
      const regionTitle = await this.page.$('label:has-text("地区")');
      this.addResult('地区筛选标题', regionTitle !== null, '地区筛选标题存在');
      
      const regions = ['北美', '欧洲', '亚太', '中国'];
      let regionCount = 0;
      
      for (const region of regions) {
        const regionElement = await this.page.$(`span:has-text("${region}")`);
        if (regionElement) regionCount++;
      }
      
      this.addResult('地区选项', regionCount === 4, `找到 ${regionCount}/${regions.length} 个地区选项`);
      
    } catch (error) {
      this.addResult('风险和语言筛选验证', false, `风险和语言筛选验证失败: ${error.message}`);
    }
  }

  async verifyFilterInteractivity() {
    console.log('🎯 验证筛选交互功能...');
    
    try {
      // 测试复选框交互
      const firstCheckbox = await this.page.$('input[type="checkbox"]');
      if (firstCheckbox) {
        await firstCheckbox.click();
        await this.page.waitForTimeout(300);
        this.addResult('复选框交互', true, '复选框点击交互正常');
      }
      
      // 测试置信度滑块
      const slider = await this.page.$('input[type="range"]');
      if (slider) {
        await slider.click();
        await this.page.waitForTimeout(300);
        this.addResult('滑块交互', true, '置信度滑块交互正常');
      }
      
      // 测试应用筛选按钮
      const applyButton = await this.page.$('button:has-text("应用筛选")');
      if (applyButton) {
        await applyButton.click();
        await this.page.waitForTimeout(500);
        this.addResult('应用筛选按钮', true, '应用筛选按钮交互正常');
      }
      
      // 测试重置按钮
      const resetButton = await this.page.$('button:has-text("重置")');
      if (resetButton) {
        await resetButton.click();
        await this.page.waitForTimeout(500);
        this.addResult('重置按钮', true, '重置按钮交互正常');
      }
      
    } catch (error) {
      this.addResult('筛选交互验证', false, `筛选交互验证失败: ${error.message}`);
    }
  }

  async verifyUIConsistency() {
    console.log('🎨 验证UI一致性...');
    
    try {
      // 验证筛选面板设计
      const filterPanel = await this.page.$('.border-l-4.border-blue-500');
      this.addResult('筛选面板设计', filterPanel !== null, '筛选面板使用蓝色左边框设计');
      
      // 验证卡片设计
      const filterCards = await this.page.$$('.bg-white\\/5.rounded-lg');
      this.addResult('筛选卡片设计', filterCards.length >= 3, `找到 ${filterCards.length} 个筛选卡片`);
      
      // 验证按钮设计
      const buttons = await this.page.$$('button');
      this.addResult('按钮设计', buttons.length > 5, `找到 ${buttons.length} 个按钮元素`);
      
      // 验证图标使用
      const icons = await this.page.$$('svg');
      this.addResult('图标系统', icons.length > 10, `图标数量: ${icons.length}`);
      
    } catch (error) {
      this.addResult('UI一致性验证', false, `UI一致性验证失败: ${error.message}`);
    }
  }

  addResult(testName, passed, details) {
    this.testResults.push({
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details}`);
  }

  async generateReport() {
    console.log('\n📋 生成验证报告...');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 MemeMaster AI 高级筛选功能验证报告');
    console.log('='.repeat(60));
    console.log(`📊 总体结果: ${passedTests}/${totalTests} 项测试通过 (${successRate}%)`);
    console.log(`⏰ 测试时间: ${new Date().toLocaleString()}`);
    console.log('\n📝 详细结果:');
    
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.details}`);
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (successRate >= 90) {
      console.log('🎉 验证结果: 优秀! 高级筛选功能运行良好');
    } else if (successRate >= 75) {
      console.log('👍 验证结果: 良好! 大部分功能正常，建议优化部分细节');
    } else {
      console.log('⚠️  验证结果: 需要改进! 存在多个问题需要修复');
    }
    
    return {
      totalTests,
      passedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    };
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullVerification() {
    try {
      await this.init();
      await this.navigateToHotspotPage();
      await this.verifyAdvancedFiltersButton();
      await this.verifyFiltersPanel();
      await this.verifyCategoryFilters();
      await this.verifySourceFilters();
      await this.verifyRiskAndLanguageFilters();
      await this.verifyFilterInteractivity();
      await this.verifyUIConsistency();
      
      return await this.generateReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      this.addResult('整体验证', false, `验证失败: ${error.message}`);
      return await this.generateReport();
    } finally {
      await this.cleanup();
    }
  }
}

// 运行验证
async function main() {
  const verifier = new AdvancedFiltersVerifier();
  const report = await verifier.runFullVerification();
  
  // 保存报告到文件
  const fs = require('fs');
  const reportPath = 'advanced_filters_verification_report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = AdvancedFiltersVerifier;
