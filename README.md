# MemeMaster AI

一个基于AI的智能Meme代币发行和交易系统，支持多链部署和自动化策略执行。

## 功能特性

- 🔍 **智能热点捕捉** - 多源数据分析，包括社交媒体、暗网和视频内容
- 🧠 **AI策略生成** - 基于机器学习的代币发行策略
- 🔒 **合规检查** - 地理围栏和商标筛查
- ⚡ **多链支持** - 以太坊和Solana网络
- 🛡️ **防封禁系统** - 代理轮换和人类行为模拟
- 📊 **动态退出** - 基于XGBoost的智能退出策略
- 🔧 **监控告警** - 实时系统监控和熔断机制

## 快速开始

### 环境要求

- Python 3.10+
- Node.js 16+
- Docker & Docker Compose
- Redis
- 16GB+ RAM (推荐 32GB)

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd MemeMasterAI

# 安装依赖
pip install -r requirements.txt
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入必要的API密钥

# 启动服务
docker-compose up -d
```

### 配置

在 `.env` 文件中配置以下必要参数：

```ini
# 区块链节点
ETH_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
SOL_RPC_URL=https://api.mainnet-beta.solana.com

# API密钥
POLITICO_API_KEY=your_politico_key
TRADEMARKNOW_API_KEY=your_trademark_key
BRIGHTDATA_API_KEY=your_brightdata_key

# 安全设置
ENCRYPTION_KEY=secure_encryption_key_32bytes
```

## 项目结构

```
MemeMasterAI/
├── src/
│   ├── hotspot/          # 热点捕捉模块
│   ├── strategy/         # 策略生成模块
│   ├── blockchain/       # 区块链交互
│   ├── liquidity/        # 流动性管理
│   ├── exit/            # 获利退出
│   ├── compliance/      # 合规检查
│   └── utils/           # 工具类
├── contracts/           # 智能合约
├── tests/              # 测试文件
├── scripts/            # 部署脚本
├── docker/             # Docker配置
└── docs/               # 文档
```

## 开发指南

请参考 [开发者文档](docs/developer-guide.md) 获取详细的开发指南。

## 许可证

MIT License
