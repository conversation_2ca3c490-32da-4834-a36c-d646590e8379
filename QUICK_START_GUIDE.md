# 🚀 MemeMaster AI 仪表盘快速启动指南

## 🎯 概述

MemeMaster AI 仪表盘是一个现代化的 React 前端界面，为您的 MemeMaster AI 系统提供直观、专业的监控和控制功能。

## 📋 环境要求

- **Node.js** >= 16.0.0
- **Python** >= 3.8
- **npm** >= 8.0.0

## ⚡ 快速启动

### 方式一：一键启动（推荐）
```bash
python3 start_dashboard.py
```

### 方式二：Bash 脚本启动
```bash
chmod +x quick_start.sh
./quick_start.sh
```

### 方式三：手动启动
```bash
# 1. 安装依赖
npm install
pip3 install fastapi uvicorn psutil

# 2. 启动后端（新终端）
python3 -m uvicorn web_app:app --host 0.0.0.0 --port 8000 --reload

# 3. 启动前端（新终端）
npm run frontend:dev
```

## 🌐 访问地址

启动成功后，您可以通过以下地址访问：

- **🎨 前端界面**: http://localhost:3000
- **🔧 后端API**: http://localhost:8000
- **📚 API文档**: http://localhost:8000/docs

## 🏠 仪表盘功能

### 1. 主仪表盘
- 系统总览和关键指标
- 各模块状态监控
- 快速导航入口

### 2. 舆情监控
- 实时热点分布饼图
- 数据源权重配置
- 关键词管理
- 监控频率设置

### 3. 策略管理
- 代币参数配置
- 分配比例调整
- 策略执行控制
- 适配度监控

### 4. 钱包管理
- 多钱包统一管理
- 余额实时监控
- 安全显示控制
- 防封禁策略

### 5. 流动性控制
- TVL 监控和趋势
- 滑点优化
- MEV 保护
- 风险预警

### 6. 退出系统
- 信号强度仪表盘
- 动态卖出参数
- 紧急退出功能
- 风险管理

### 7. 系统设置
- 全局参数配置
- API 密钥管理
- 通知设置
- 界面个性化

## 🎨 界面特色

- **现代化暗黑主题** - 专业的渐变背景
- **响应式设计** - 适配各种设备
- **实时数据更新** - WebSocket 连接
- **交互式图表** - 数据可视化
- **模块化布局** - 清晰的功能分区

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :8000
   
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules
   npm install
   ```

3. **Python 模块缺失**
   ```bash
   pip3 install fastapi uvicorn psutil requests
   ```

### 调试模式

启用详细日志：
```bash
# 后端调试
uvicorn web_app:app --log-level debug --reload

# 前端调试
npm run frontend:dev
```

## 📱 使用技巧

1. **快速导航** - 使用左侧菜单快速切换功能模块
2. **实时监控** - 关注顶部状态栏的系统状态指示器
3. **参数调整** - 使用滑块和输入框精确控制参数
4. **数据刷新** - 点击顶部刷新按钮手动更新数据
5. **紧急操作** - 使用顶部快速操作按钮进行紧急控制

## 🎯 下一步

1. **探索功能** - 逐一体验各个功能模块
2. **配置参数** - 根据需求调整系统参数
3. **监控数据** - 观察实时数据变化
4. **集成数据** - 连接真实的 MemeMaster AI 数据源

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查后端服务日志
3. 确认网络连接正常
4. 验证依赖版本兼容性

---

**🎊 享受您的 MemeMaster AI 仪表盘体验！**
