#!/usr/bin/env python3
"""
MemeMaster AI 依赖安装脚本
"""

import subprocess
import sys
import os

def print_banner():
    print("""
╔══════════════════════════════════════════════════════════════╗
║                MemeMaster AI 依赖安装工具                    ║
╚══════════════════════════════════════════════════════════════╝
    """)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package):
    """安装单个包"""
    try:
        print(f"📦 安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 基础依赖包
    basic_packages = [
        "fastapi",
        "uvicorn[standard]",
        "python-multipart"
    ]
    
    # 用户管理系统依赖
    user_management_packages = [
        "pyjwt",
        "bcrypt", 
        "passlib[bcrypt]",
        "aiosqlite",
        "python-jose[cryptography]"
    ]
    
    # 可选依赖
    optional_packages = [
        "psutil",
        "requests",
        "aiofiles"
    ]
    
    print("\n🚀 开始安装依赖包...")
    
    # 安装基础依赖
    print("\n📦 安装基础依赖...")
    basic_success = True
    for package in basic_packages:
        if not install_package(package):
            basic_success = False
    
    # 安装用户管理系统依赖
    print("\n👤 安装用户管理系统依赖...")
    user_mgmt_success = True
    for package in user_management_packages:
        if not install_package(package):
            user_mgmt_success = False
    
    # 安装可选依赖
    print("\n🔧 安装可选依赖...")
    for package in optional_packages:
        install_package(package)  # 可选依赖失败不影响整体
    
    # 检查安装结果
    print("\n" + "="*60)
    print("📋 安装结果检查:")
    
    # 检查基础功能
    print("\n🔧 基础功能:")
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI 和 Uvicorn - Web服务器")
    except ImportError:
        print("❌ FastAPI 和 Uvicorn - Web服务器")
    
    # 检查用户管理功能
    print("\n👤 用户管理功能:")
    user_mgmt_available = True
    
    try:
        import jwt
        print("✅ PyJWT - JWT Token认证")
    except ImportError:
        print("❌ PyJWT - JWT Token认证")
        user_mgmt_available = False
    
    try:
        import bcrypt
        print("✅ bcrypt - 密码加密")
    except ImportError:
        print("❌ bcrypt - 密码加密")
        user_mgmt_available = False
    
    try:
        import aiosqlite
        print("✅ aiosqlite - 数据库支持")
    except ImportError:
        print("❌ aiosqlite - 数据库支持")
        user_mgmt_available = False
    
    # 检查可选功能
    print("\n🔧 可选功能:")
    try:
        import psutil
        print("✅ psutil - 系统监控")
    except ImportError:
        print("⚠️ psutil - 系统监控 (可选)")
    
    try:
        import requests
        print("✅ requests - HTTP客户端")
    except ImportError:
        print("⚠️ requests - HTTP客户端 (可选)")
    
    # 总结
    print("\n" + "="*60)
    if basic_success:
        print("🎉 基础功能安装成功！")
        print("   可以运行: python3 working_server.py")
    else:
        print("❌ 基础功能安装失败")
        return False
    
    if user_mgmt_available:
        print("🎉 用户管理系统可用！")
        print("   支持分层收费、Solana支付等功能")
    else:
        print("⚠️ 用户管理系统不可用")
        print("   部分依赖安装失败，将以基础模式运行")
    
    print("\n🚀 下一步:")
    print("1. 启动服务器: python3 working_server.py")
    print("2. 访问主页: http://localhost:8001/")
    print("3. 查看API文档: http://localhost:8001/docs")
    
    if user_mgmt_available:
        print("4. 初始化用户系统: python3 init_user_system.py")
        print("5. 启动完整系统: python3 run_with_user_management.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 安装被用户取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程出错: {e}")
        sys.exit(1)
