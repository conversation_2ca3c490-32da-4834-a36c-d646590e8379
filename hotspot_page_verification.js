/**
 * MemeMaster AI 热点页面验证脚本
 * 验证页面标题修改和热点数据显示效果
 */

const puppeteer = require('puppeteer');

class HotspotPageVerifier {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
  }

  async init() {
    console.log('🚀 启动热点页面验证测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
  }

  async navigateToPage() {
    console.log('📍 导航到热点监控页面...');
    
    try {
      await this.page.goto('http://localhost:3002/hotspot', {
        waitUntil: 'networkidle2',
        timeout: 30000
      });
      
      await this.page.waitForSelector('h1', { timeout: 10000 });
      this.addResult('页面导航', true, '页面成功加载');
      
    } catch (error) {
      this.addResult('页面导航', false, `导航失败: ${error.message}`);
    }
  }

  async verifyPageTitle() {
    console.log('📝 验证页面标题...');
    
    try {
      const title = await this.page.$eval('h1', el => el.textContent);
      const expectedTitle = '趋势热点监控';
      
      if (title.includes(expectedTitle)) {
        this.addResult('页面标题', true, `页面标题正确: ${title}`);
      } else {
        this.addResult('页面标题', false, `页面标题错误: 期望"${expectedTitle}"，实际"${title}"`);
      }
      
    } catch (error) {
      this.addResult('页面标题', false, `标题验证失败: ${error.message}`);
    }
  }

  async verifySidebarMenu() {
    console.log('🔍 验证侧边栏菜单...');
    
    try {
      // 检查侧边栏是否存在"趋势热点"菜单项
      const menuItems = await this.page.$$eval('nav button span, nav a span', 
        elements => elements.map(el => el.textContent)
      );
      
      const hasTrendHotspot = menuItems.some(item => item.includes('趋势热点'));
      
      if (hasTrendHotspot) {
        this.addResult('侧边栏菜单', true, '侧边栏包含"趋势热点"菜单项');
      } else {
        this.addResult('侧边栏菜单', false, `侧边栏菜单项: ${menuItems.join(', ')}`);
      }
      
    } catch (error) {
      this.addResult('侧边栏菜单', false, `侧边栏验证失败: ${error.message}`);
    }
  }

  async verifyHotspotData() {
    console.log('📊 验证热点数据显示...');
    
    try {
      // 等待热点数据加载
      await this.page.waitForSelector('.grid.grid-cols-1.lg\\:grid-cols-2', { timeout: 10000 });
      
      // 检查热点卡片数量
      const hotspotCards = await this.page.$$('.group.bg-gradient-to-br');
      const cardCount = hotspotCards.length;
      
      if (cardCount > 0) {
        this.addResult('热点数据', true, `找到 ${cardCount} 个热点卡片`);
      } else {
        this.addResult('热点数据', false, '未找到热点卡片');
      }
      
      // 验证热点卡片内容
      if (cardCount > 0) {
        const firstCard = hotspotCards[0];
        const cardTitle = await firstCard.$eval('h3', el => el.textContent);
        const cardDescription = await firstCard.$eval('p', el => el.textContent);
        
        this.addResult('热点内容', true, `第一个热点: ${cardTitle.substring(0, 30)}...`);
      }
      
    } catch (error) {
      this.addResult('热点数据', false, `热点数据验证失败: ${error.message}`);
    }
  }

  async verifyStatisticsCards() {
    console.log('📈 验证统计卡片...');
    
    try {
      // 检查统计卡片
      const statCards = await this.page.$$('.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 > div');
      const statCount = statCards.length;
      
      if (statCount >= 4) {
        this.addResult('统计卡片', true, `找到 ${statCount} 个统计卡片`);
        
        // 验证统计卡片内容
        const statTitles = await this.page.$$eval(
          '.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4 p.text-sm',
          elements => elements.map(el => el.textContent)
        );
        
        const expectedStats = ['超高热度', '高热度', '总讨论量', '平均置信度'];
        const hasAllStats = expectedStats.every(stat => 
          statTitles.some(title => title.includes(stat))
        );
        
        if (hasAllStats) {
          this.addResult('统计内容', true, '所有统计卡片内容正确');
        } else {
          this.addResult('统计内容', false, `统计卡片内容: ${statTitles.join(', ')}`);
        }
        
      } else {
        this.addResult('统计卡片', false, `统计卡片数量不足: ${statCount}`);
      }
      
    } catch (error) {
      this.addResult('统计卡片', false, `统计卡片验证失败: ${error.message}`);
    }
  }

  async verifyAdvancedFilters() {
    console.log('🔧 验证高级筛选功能...');
    
    try {
      // 检查高级筛选按钮
      const filtersButton = await this.page.$('button:has-text("高级筛选")');
      
      if (filtersButton) {
        this.addResult('高级筛选按钮', true, '高级筛选按钮存在');
        
        // 点击高级筛选按钮
        await filtersButton.click();
        await this.page.waitForTimeout(1000);
        
        // 检查筛选面板是否显示
        const filterPanel = await this.page.$('h3:has-text("高级筛选器")');
        
        if (filterPanel) {
          this.addResult('高级筛选面板', true, '高级筛选面板正常显示');
        } else {
          this.addResult('高级筛选面板', false, '高级筛选面板未显示');
        }
        
      } else {
        this.addResult('高级筛选按钮', false, '高级筛选按钮不存在');
      }
      
    } catch (error) {
      this.addResult('高级筛选功能', false, `高级筛选验证失败: ${error.message}`);
    }
  }

  async verifyTabNavigation() {
    console.log('🔄 验证标签导航...');
    
    try {
      // 检查标签按钮
      const tabs = await this.page.$$eval('button span', 
        elements => elements.map(el => el.textContent).filter(text => 
          ['实时热点', '热点预测', '设置'].includes(text)
        )
      );
      
      if (tabs.length >= 3) {
        this.addResult('标签导航', true, `找到标签: ${tabs.join(', ')}`);
        
        // 测试标签切换
        const predictionTab = await this.page.$('button:has-text("热点预测")');
        if (predictionTab) {
          await predictionTab.click();
          await this.page.waitForTimeout(1000);
          
          const predictionContent = await this.page.$('h2:has-text("热点预测与策略自动化")');
          if (predictionContent) {
            this.addResult('标签切换', true, '热点预测标签切换正常');
          } else {
            this.addResult('标签切换', false, '热点预测标签切换失败');
          }
        }
        
      } else {
        this.addResult('标签导航', false, `标签数量不足: ${tabs.length}`);
      }
      
    } catch (error) {
      this.addResult('标签导航', false, `标签导航验证失败: ${error.message}`);
    }
  }

  async verifyResponsiveDesign() {
    console.log('📱 验证响应式设计...');
    
    try {
      // 测试不同屏幕尺寸
      const viewports = [
        { width: 1920, height: 1080, name: '桌面端' },
        { width: 768, height: 1024, name: '平板端' },
        { width: 375, height: 667, name: '移动端' }
      ];
      
      for (const viewport of viewports) {
        await this.page.setViewport(viewport);
        await this.page.waitForTimeout(500);
        
        // 检查页面是否正常显示
        const isVisible = await this.page.$eval('h1', el => 
          window.getComputedStyle(el).display !== 'none'
        );
        
        if (isVisible) {
          this.addResult(`响应式-${viewport.name}`, true, `${viewport.name}显示正常`);
        } else {
          this.addResult(`响应式-${viewport.name}`, false, `${viewport.name}显示异常`);
        }
      }
      
      // 恢复桌面端视图
      await this.page.setViewport({ width: 1920, height: 1080 });
      
    } catch (error) {
      this.addResult('响应式设计', false, `响应式验证失败: ${error.message}`);
    }
  }

  async takeScreenshot() {
    console.log('📸 保存验证截图...');
    
    try {
      await this.page.screenshot({ 
        path: 'hotspot_page_verification.png',
        fullPage: true 
      });
      this.addResult('截图保存', true, '验证截图已保存');
    } catch (error) {
      this.addResult('截图保存', false, `截图保存失败: ${error.message}`);
    }
  }

  addResult(testName, passed, details) {
    this.testResults.push({
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details}`);
  }

  async generateReport() {
    console.log('\n📋 生成验证报告...');
    
    const passedTests = this.testResults.filter(r => r.passed).length;
    const totalTests = this.testResults.length;
    const successRate = ((passedTests / totalTests) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 MemeMaster AI 热点页面验证报告');
    console.log('='.repeat(60));
    console.log(`📊 总体结果: ${passedTests}/${totalTests} 项测试通过 (${successRate}%)`);
    console.log(`⏰ 测试时间: ${new Date().toLocaleString()}`);
    console.log('\n📝 详细结果:');
    
    this.testResults.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      console.log(`   ${result.details}`);
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (successRate >= 90) {
      console.log('🎉 验证结果: 优秀! 页面功能运行良好');
    } else if (successRate >= 75) {
      console.log('👍 验证结果: 良好! 大部分功能正常');
    } else {
      console.log('⚠️  验证结果: 需要改进! 存在问题需要修复');
    }
    
    return {
      totalTests,
      passedTests,
      successRate: parseFloat(successRate),
      results: this.testResults
    };
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullVerification() {
    try {
      await this.init();
      await this.navigateToPage();
      await this.verifyPageTitle();
      await this.verifySidebarMenu();
      await this.verifyHotspotData();
      await this.verifyStatisticsCards();
      await this.verifyAdvancedFilters();
      await this.verifyTabNavigation();
      await this.verifyResponsiveDesign();
      await this.takeScreenshot();
      
      return await this.generateReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error);
      this.addResult('整体验证', false, `验证失败: ${error.message}`);
      return await this.generateReport();
    } finally {
      await this.cleanup();
    }
  }
}

// 运行验证
async function main() {
  const verifier = new HotspotPageVerifier();
  const report = await verifier.runFullVerification();
  
  // 保存报告到文件
  const fs = require('fs');
  const reportPath = 'hotspot_page_verification_report.json';
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = HotspotPageVerifier;
