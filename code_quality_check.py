#!/usr/bin/env python3
"""
MemeMaster AI 代码质量检查工具
检查代码质量、性能问题和最佳实践
"""

import os
import ast
import sys
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Any
import re

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.issues = []
        self.stats = {
            "files_checked": 0,
            "lines_of_code": 0,
            "issues_found": 0,
            "warnings": 0,
            "errors": 0
        }
    
    def add_issue(self, file_path: str, line: int, issue_type: str, message: str, severity: str = "warning"):
        """添加问题"""
        self.issues.append({
            "file": file_path,
            "line": line,
            "type": issue_type,
            "message": message,
            "severity": severity
        })
        self.stats["issues_found"] += 1
        if severity == "error":
            self.stats["errors"] += 1
        else:
            self.stats["warnings"] += 1
    
    def check_python_file(self, file_path: Path):
        """检查Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                self.stats["lines_of_code"] += len(lines)
            
            # 解析AST
            try:
                tree = ast.parse(content)
            except SyntaxError as e:
                self.add_issue(str(file_path), e.lineno or 0, "syntax_error", 
                             f"语法错误: {e.msg}", "error")
                return
            
            # 检查导入
            self.check_imports(file_path, tree, lines)
            
            # 检查函数和类
            self.check_functions_and_classes(file_path, tree, lines)
            
            # 检查代码风格
            self.check_code_style(file_path, lines)
            
            # 检查安全问题
            self.check_security_issues(file_path, content, lines)
            
        except Exception as e:
            self.add_issue(str(file_path), 0, "file_error", 
                         f"文件读取错误: {e}", "error")
    
    def check_imports(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查导入语句"""
        imports = []
        used_names = set()
        
        # 收集所有导入
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append((alias.name, alias.asname, node.lineno))
            elif isinstance(node, ast.ImportFrom):
                for alias in node.names:
                    imports.append((f"{node.module}.{alias.name}" if node.module else alias.name, 
                                  alias.asname, node.lineno))
            elif isinstance(node, ast.Name):
                used_names.add(node.id)
        
        # 检查未使用的导入
        for import_name, alias, line_no in imports:
            name_to_check = alias if alias else import_name.split('.')[-1]
            if name_to_check not in used_names and name_to_check not in ['__future__']:
                self.add_issue(str(file_path), line_no, "unused_import", 
                             f"未使用的导入: {import_name}")
    
    def check_functions_and_classes(self, file_path: Path, tree: ast.AST, lines: List[str]):
        """检查函数和类"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # 检查函数长度
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    func_length = node.end_lineno - node.lineno
                    if func_length > 50:
                        self.add_issue(str(file_path), node.lineno, "long_function", 
                                     f"函数 '{node.name}' 过长 ({func_length} 行)")
                
                # 检查参数数量
                if len(node.args.args) > 7:
                    self.add_issue(str(file_path), node.lineno, "too_many_params", 
                                 f"函数 '{node.name}' 参数过多 ({len(node.args.args)} 个)")
                
                # 检查是否有文档字符串
                if not ast.get_docstring(node):
                    self.add_issue(str(file_path), node.lineno, "missing_docstring", 
                                 f"函数 '{node.name}' 缺少文档字符串")
            
            elif isinstance(node, ast.ClassDef):
                # 检查类是否有文档字符串
                if not ast.get_docstring(node):
                    self.add_issue(str(file_path), node.lineno, "missing_docstring", 
                                 f"类 '{node.name}' 缺少文档字符串")
    
    def check_code_style(self, file_path: Path, lines: List[str]):
        """检查代码风格"""
        for i, line in enumerate(lines, 1):
            # 检查行长度
            if len(line) > 120:
                self.add_issue(str(file_path), i, "long_line", 
                             f"行过长 ({len(line)} 字符)")
            
            # 检查尾随空格
            if line.rstrip() != line and line.strip():
                self.add_issue(str(file_path), i, "trailing_whitespace", 
                             "行尾有多余空格")
            
            # 检查TODO注释
            if "TODO" in line or "FIXME" in line:
                self.add_issue(str(file_path), i, "todo_comment", 
                             "发现TODO/FIXME注释")
    
    def check_security_issues(self, file_path: Path, content: str, lines: List[str]):
        """检查安全问题"""
        security_patterns = [
            (r'eval\s*\(', "使用eval()可能存在安全风险"),
            (r'exec\s*\(', "使用exec()可能存在安全风险"),
            (r'subprocess\.call\s*\(.*shell\s*=\s*True', "shell=True可能存在命令注入风险"),
            (r'password\s*=\s*["\'][^"\']*["\']', "硬编码密码"),
            (r'secret\s*=\s*["\'][^"\']*["\']', "硬编码密钥"),
        ]
        
        for i, line in enumerate(lines, 1):
            for pattern, message in security_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    self.add_issue(str(file_path), i, "security_issue", message, "error")
    
    def check_javascript_file(self, file_path: Path):
        """检查JavaScript/JSX文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                self.stats["lines_of_code"] += len(lines)
            
            # 基本的JavaScript检查
            for i, line in enumerate(lines, 1):
                # 检查console.log
                if 'console.log' in line and not line.strip().startswith('//'):
                    self.add_issue(str(file_path), i, "debug_code", 
                                 "发现console.log调试代码")
                
                # 检查var使用
                if re.search(r'\bvar\s+', line):
                    self.add_issue(str(file_path), i, "deprecated_var", 
                                 "建议使用let/const替代var")
                
                # 检查行长度
                if len(line) > 120:
                    self.add_issue(str(file_path), i, "long_line", 
                                 f"行过长 ({len(line)} 字符)")
        
        except Exception as e:
            self.add_issue(str(file_path), 0, "file_error", 
                         f"文件读取错误: {e}", "error")
    
    def check_project(self):
        """检查整个项目"""
        print("🔍 开始代码质量检查...")
        
        # 检查Python文件
        python_files = list(self.project_root.glob("**/*.py"))
        for file_path in python_files:
            if "node_modules" not in str(file_path) and "__pycache__" not in str(file_path):
                self.stats["files_checked"] += 1
                self.check_python_file(file_path)
        
        # 检查JavaScript/JSX文件
        js_files = list(self.project_root.glob("**/*.js")) + list(self.project_root.glob("**/*.jsx"))
        for file_path in js_files:
            if "node_modules" not in str(file_path) and "dist" not in str(file_path):
                self.stats["files_checked"] += 1
                self.check_javascript_file(file_path)
    
    def generate_report(self):
        """生成报告"""
        print("\n📊 代码质量检查报告")
        print("=" * 50)
        print(f"检查文件数: {self.stats['files_checked']}")
        print(f"代码行数: {self.stats['lines_of_code']}")
        print(f"发现问题: {self.stats['issues_found']}")
        print(f"错误: {self.stats['errors']}")
        print(f"警告: {self.stats['warnings']}")
        
        if self.issues:
            print("\n🔍 问题详情:")
            
            # 按文件分组
            issues_by_file = {}
            for issue in self.issues:
                file_path = issue["file"]
                if file_path not in issues_by_file:
                    issues_by_file[file_path] = []
                issues_by_file[file_path].append(issue)
            
            for file_path, file_issues in issues_by_file.items():
                print(f"\n📁 {file_path}")
                for issue in file_issues[:10]:  # 限制显示数量
                    severity_icon = "❌" if issue["severity"] == "error" else "⚠️"
                    print(f"  {severity_icon} 行 {issue['line']}: {issue['message']} ({issue['type']})")
                
                if len(file_issues) > 10:
                    print(f"  ... 还有 {len(file_issues) - 10} 个问题")
        
        # 保存详细报告
        report_file = self.project_root / "logs" / "code_quality_report.json"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "stats": self.stats,
                "issues": self.issues
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        # 返回质量评分
        if self.stats["lines_of_code"] > 0:
            issue_rate = self.stats["issues_found"] / self.stats["lines_of_code"]
            if issue_rate < 0.01:
                quality_score = "优秀"
            elif issue_rate < 0.05:
                quality_score = "良好"
            elif issue_rate < 0.1:
                quality_score = "一般"
            else:
                quality_score = "需要改进"
            
            print(f"\n🎯 代码质量评分: {quality_score}")
            print(f"   问题率: {issue_rate:.2%}")

def main():
    """主函数"""
    checker = CodeQualityChecker()
    checker.check_project()
    checker.generate_report()

if __name__ == "__main__":
    main()
