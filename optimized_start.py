#!/usr/bin/env python3
"""
MemeMaster AI 优化启动脚本
提供智能的服务管理和监控功能
"""

import os
import sys
import time
import subprocess
import signal
import psutil
from pathlib import Path
import json
import logging
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/startup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MemeMasterManager:
    """MemeMaster AI 服务管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = {}
        self.config = self.load_config()
        
    def load_config(self) -> Dict:
        """加载配置"""
        config_file = self.project_root / "config" / "startup.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # 默认配置
        return {
            "backend": {
                "script": "web_app.py",
                "port": 8001,
                "host": "0.0.0.0"
            },
            "frontend": {
                "command": "npm run frontend:dev",
                "port": 3000
            },
            "dependencies": {
                "python": ["fastapi", "uvicorn", "pydantic"],
                "node": ["react", "vite", "@vitejs/plugin-react"]
            }
        }
    
    def check_dependencies(self) -> bool:
        """检查依赖是否安装"""
        logger.info("🔍 检查依赖...")
        
        # 检查Python依赖
        missing_python = []
        for dep in self.config["dependencies"]["python"]:
            try:
                __import__(dep)
            except ImportError:
                missing_python.append(dep)
        
        if missing_python:
            logger.warning(f"缺少Python依赖: {missing_python}")
            return False
        
        # 检查Node.js依赖
        package_json = self.project_root / "package.json"
        if package_json.exists():
            node_modules = self.project_root / "node_modules"
            if not node_modules.exists():
                logger.warning("Node.js依赖未安装")
                return False
        
        logger.info("✅ 依赖检查通过")
        return True
    
    def install_dependencies(self):
        """安装依赖"""
        logger.info("📦 安装依赖...")
        
        # 安装Python依赖
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "fastapi", "uvicorn[standard]", "pydantic", "python-dotenv"
            ], check=True)
            logger.info("✅ Python依赖安装完成")
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Python依赖安装失败: {e}")
            return False
        
        # 安装Node.js依赖
        if (self.project_root / "package.json").exists():
            try:
                subprocess.run(["npm", "install"], cwd=self.project_root, check=True)
                logger.info("✅ Node.js依赖安装完成")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Node.js依赖安装失败: {e}")
                return False
        
        return True
    
    def check_port(self, port: int) -> bool:
        """检查端口是否被占用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result == 0
        except Exception:
            return False
    
    def start_backend(self) -> bool:
        """启动后端服务"""
        backend_config = self.config["backend"]
        port = backend_config["port"]
        
        if self.check_port(port):
            logger.info(f"✅ 后端服务已在端口 {port} 运行")
            return True
        
        logger.info("🚀 启动后端服务...")
        script_path = self.project_root / backend_config["script"]
        
        if not script_path.exists():
            logger.error(f"❌ 后端脚本不存在: {script_path}")
            return False
        
        try:
            process = subprocess.Popen([
                sys.executable, str(script_path)
            ], cwd=self.project_root)
            
            self.processes["backend"] = process
            
            # 等待服务启动
            for i in range(30):
                time.sleep(1)
                if self.check_port(port):
                    logger.info(f"✅ 后端服务启动成功 (PID: {process.pid})")
                    return True
            
            logger.error("❌ 后端服务启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 后端服务启动失败: {e}")
            return False
    
    def start_frontend(self) -> bool:
        """启动前端服务"""
        frontend_config = self.config["frontend"]
        port = frontend_config["port"]
        
        if self.check_port(port):
            logger.info(f"✅ 前端服务已在端口 {port} 运行")
            return True
        
        logger.info("🚀 启动前端服务...")
        
        try:
            process = subprocess.Popen(
                frontend_config["command"].split(),
                cwd=self.project_root
            )
            
            self.processes["frontend"] = process
            
            # 等待服务启动
            for i in range(60):  # 前端启动较慢
                time.sleep(1)
                if self.check_port(port):
                    logger.info(f"✅ 前端服务启动成功 (PID: {process.pid})")
                    return True
            
            logger.error("❌ 前端服务启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 前端服务启动失败: {e}")
            return False
    
    def stop_services(self):
        """停止所有服务"""
        logger.info("🛑 停止服务...")
        
        for name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"✅ {name} 服务已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                logger.warning(f"⚠️ 强制终止 {name} 服务")
            except Exception as e:
                logger.error(f"❌ 停止 {name} 服务失败: {e}")
    
    def start_all(self):
        """启动所有服务"""
        logger.info("🚀 MemeMaster AI 启动中...")
        
        # 检查并安装依赖
        if not self.check_dependencies():
            if not self.install_dependencies():
                logger.error("❌ 依赖安装失败，无法启动")
                return False
        
        # 启动后端
        if not self.start_backend():
            logger.error("❌ 后端启动失败")
            return False
        
        # 启动前端
        if not self.start_frontend():
            logger.error("❌ 前端启动失败")
            self.stop_services()
            return False
        
        logger.info("🎉 MemeMaster AI 启动完成!")
        logger.info(f"📊 前端界面: http://localhost:{self.config['frontend']['port']}")
        logger.info(f"📡 后端API: http://localhost:{self.config['backend']['port']}")
        
        return True
    
    def status(self):
        """显示服务状态"""
        backend_port = self.config["backend"]["port"]
        frontend_port = self.config["frontend"]["port"]
        
        backend_running = self.check_port(backend_port)
        frontend_running = self.check_port(frontend_port)
        
        print("📊 MemeMaster AI 服务状态")
        print("=" * 40)
        print(f"后端服务 (:{backend_port}): {'🟢 运行中' if backend_running else '🔴 未运行'}")
        print(f"前端服务 (:{frontend_port}): {'🟢 运行中' if frontend_running else '🔴 未运行'}")
        
        if backend_running and frontend_running:
            print("\n✅ 所有服务正常运行")
            print(f"🌐 访问地址: http://localhost:{frontend_port}")
        else:
            print("\n⚠️ 部分服务未运行")

def main():
    """主函数"""
    manager = MemeMasterManager()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到停止信号，正在关闭服务...")
        manager.stop_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "status":
            manager.status()
            return
        elif command == "stop":
            manager.stop_services()
            return
        elif command == "start":
            pass  # 继续执行启动逻辑
        else:
            print("用法: python optimized_start.py [start|stop|status]")
            return
    
    # 启动服务
    if manager.start_all():
        try:
            # 保持运行
            while True:
                time.sleep(10)
        except KeyboardInterrupt:
            pass
        finally:
            manager.stop_services()

if __name__ == "__main__":
    main()
