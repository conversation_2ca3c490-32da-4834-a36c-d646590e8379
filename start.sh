#!/bin/bash

# MemeMaster AI 启动脚本
# 用于快速启动开发环境或生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is not installed"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    # 检查Docker (可选)
    if command -v docker &> /dev/null; then
        log_info "Docker is available"
        DOCKER_AVAILABLE=true
    else
        log_warn "Docker is not available"
        DOCKER_AVAILABLE=false
    fi
    
    log_info "Dependencies check completed"
}

# 设置环境
setup_environment() {
    log_info "Setting up environment..."
    
    # 创建必要的目录
    mkdir -p logs
    mkdir -p data
    mkdir -p deployments
    mkdir -p /var/log/mememaster 2>/dev/null || mkdir -p logs/mememaster
    
    # 检查.env文件
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            log_warn ".env file not found, copying from .env.example"
            cp .env.example .env
            log_warn "Please edit .env file with your configuration"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    fi
    
    log_info "Environment setup completed"
}

# 安装依赖
install_dependencies() {
    log_info "Installing dependencies..."
    
    # 安装Python依赖
    if [ -f requirements.txt ]; then
        log_info "Installing Python dependencies..."
        pip install -r requirements.txt
    fi
    
    # 安装Node.js依赖
    if [ -f package.json ]; then
        log_info "Installing Node.js dependencies..."
        npm install
    fi
    
    log_info "Dependencies installation completed"
}

# 运行测试
run_tests() {
    log_info "Running tests..."
    
    # Python测试
    if command -v pytest &> /dev/null; then
        log_info "Running Python tests..."
        pytest tests/ -v --tb=short
    else
        log_warn "pytest not found, skipping Python tests"
    fi
    
    # Node.js测试
    if [ -f package.json ] && grep -q "test" package.json; then
        log_info "Running Node.js tests..."
        npm test
    fi
    
    log_info "Tests completed"
}

# 启动开发服务器
start_dev() {
    log_info "Starting development server..."
    
    # 设置开发环境变量
    export DEBUG=true
    export ENVIRONMENT=development
    
    # 启动Python应用
    python -m src.app
}

# 启动生产服务器
start_prod() {
    log_info "Starting production server..."
    
    # 设置生产环境变量
    export DEBUG=false
    export ENVIRONMENT=production
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        log_info "Starting with Docker Compose..."
        docker-compose up -d
        
        # 显示服务状态
        sleep 5
        docker-compose ps
        
        log_info "Services started. Access the application at:"
        log_info "  - Main API: http://localhost:3000"
        log_info "  - Metrics: http://localhost:8000"
        log_info "  - Grafana: http://localhost:3001"
        
    else
        log_info "Starting without Docker..."
        # 启动Redis (如果可用)
        if command -v redis-server &> /dev/null; then
            redis-server --daemonize yes
        fi
        
        # 启动主应用
        python -m src.app
    fi
}

# 停止服务
stop_services() {
    log_info "Stopping services..."
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        docker-compose down
    else
        # 停止Python进程
        pkill -f "python -m src.app" || true
        
        # 停止Redis
        pkill redis-server || true
    fi
    
    log_info "Services stopped"
}

# 查看日志
view_logs() {
    log_info "Viewing logs..."
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        docker-compose logs -f
    else
        tail -f logs/*.log 2>/dev/null || echo "No log files found"
    fi
}

# 清理
cleanup() {
    log_info "Cleaning up..."
    
    # 清理Python缓存
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
    find . -name "*.pyc" -delete 2>/dev/null || true
    
    # 清理Node.js缓存
    rm -rf node_modules/.cache 2>/dev/null || true
    
    # 清理日志文件
    rm -rf logs/*.log 2>/dev/null || true
    
    log_info "Cleanup completed"
}

# 显示帮助
show_help() {
    echo "MemeMaster AI Startup Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development server"
    echo "  prod        Start production server"
    echo "  test        Run tests"
    echo "  install     Install dependencies"
    echo "  stop        Stop all services"
    echo "  logs        View logs"
    echo "  cleanup     Clean up temporary files"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev      # Start development server"
    echo "  $0 prod     # Start production server with Docker"
    echo "  $0 test     # Run all tests"
}

# 主函数
main() {
    case "${1:-help}" in
        "dev")
            check_dependencies
            setup_environment
            install_dependencies
            start_dev
            ;;
        "prod")
            check_dependencies
            setup_environment
            install_dependencies
            start_prod
            ;;
        "test")
            check_dependencies
            setup_environment
            install_dependencies
            run_tests
            ;;
        "install")
            check_dependencies
            setup_environment
            install_dependencies
            ;;
        "stop")
            check_dependencies
            stop_services
            ;;
        "logs")
            view_logs
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
