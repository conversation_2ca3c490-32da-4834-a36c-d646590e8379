// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

/**
 * @title SecureMemeToken
 * @dev 安全的Meme代币合约，具有动态燃烧机制和防护功能
 */
contract SecureMemeToken is ERC20, ERC20Burnable, ReentrancyGuard, Pausable, Ownable {
    using SafeMath for uint256;

    // 事件定义
    event BurnRateUpdated(uint256 oldRate, uint256 newRate);
    event TaxCollected(address indexed from, uint256 amount);
    event LiquidityAdded(uint256 tokenAmount, uint256 ethAmount);
    event AntiWhaleTriggered(address indexed whale, uint256 amount);
    event TradingEnabled();
    event TradingDisabled();

    // 状态变量
    uint256 public burnRate;                    // 燃烧率 (基点，10000 = 100%)
    uint256 public maxBurnRate = 1500;          // 最大燃烧率 15%
    uint256 public taxRate;                     // 税率
    uint256 public maxTaxRate = 1000;           // 最大税率 10%
    
    uint256 public maxTransactionAmount;        // 最大交易金额
    uint256 public maxWalletAmount;             // 最大钱包持有量
    
    bool public tradingEnabled = false;         // 交易是否启用
    bool public antiWhaleEnabled = true;        // 反鲸鱼机制
    bool public autoBurnEnabled = true;         // 自动燃烧
    
    address public liquidityPool;               // 流动性池地址
    address public taxWallet;                   // 税收钱包
    address public marketingWallet;             // 营销钱包
    
    mapping(address => bool) public isExcludedFromFees;     // 免费地址
    mapping(address => bool) public isExcludedFromLimits;   // 免限制地址
    mapping(address => bool) public isBlacklisted;          // 黑名单
    mapping(address => uint256) public lastTransactionTime; // 最后交易时间
    
    uint256 public constant COOLDOWN_PERIOD = 60;           // 冷却期60秒
    uint256 public launchTime;                              // 启动时间
    uint256 public constant LAUNCH_PROTECTION_PERIOD = 300; // 启动保护期5分钟

    modifier onlyWhenTradingEnabled() {
        require(tradingEnabled || isExcludedFromLimits[msg.sender], "Trading not enabled");
        _;
    }

    modifier notBlacklisted(address account) {
        require(!isBlacklisted[account], "Address is blacklisted");
        _;
    }

    modifier respectsCooldown(address account) {
        if (!isExcludedFromLimits[account]) {
            require(
                block.timestamp >= lastTransactionTime[account] + COOLDOWN_PERIOD,
                "Cooldown period not met"
            );
        }
        _;
    }

    constructor(
        string memory name,
        string memory symbol,
        uint256 totalSupply,
        uint256 _burnRate,
        address _taxWallet,
        address _marketingWallet
    ) ERC20(name, symbol) {
        require(_burnRate <= maxBurnRate, "Burn rate too high");
        require(_taxWallet != address(0), "Invalid tax wallet");
        require(_marketingWallet != address(0), "Invalid marketing wallet");

        burnRate = _burnRate;
        taxRate = 500; // 5% 默认税率
        taxWallet = _taxWallet;
        marketingWallet = _marketingWallet;
        
        // 设置限制
        maxTransactionAmount = totalSupply.mul(2).div(100); // 2% 最大交易
        maxWalletAmount = totalSupply.mul(3).div(100);      // 3% 最大持有
        
        // 排除合约创建者和合约地址
        isExcludedFromFees[owner()] = true;
        isExcludedFromFees[address(this)] = true;
        isExcludedFromFees[_taxWallet] = true;
        isExcludedFromFees[_marketingWallet] = true;
        
        isExcludedFromLimits[owner()] = true;
        isExcludedFromLimits[address(this)] = true;
        
        _mint(owner(), totalSupply);
        
        emit BurnRateUpdated(0, _burnRate);
    }

    /**
     * @dev 更新燃烧率
     */
    function updateBurnRate(uint256 newRate) external onlyOwner {
        require(newRate <= maxBurnRate, "Rate exceeds maximum");
        uint256 oldRate = burnRate;
        burnRate = newRate;
        emit BurnRateUpdated(oldRate, newRate);
    }

    /**
     * @dev 更新税率
     */
    function updateTaxRate(uint256 newRate) external onlyOwner {
        require(newRate <= maxTaxRate, "Rate exceeds maximum");
        taxRate = newRate;
    }

    /**
     * @dev 启用交易
     */
    function enableTrading() external onlyOwner {
        require(!tradingEnabled, "Trading already enabled");
        tradingEnabled = true;
        launchTime = block.timestamp;
        emit TradingEnabled();
    }

    /**
     * @dev 禁用交易（紧急情况）
     */
    function disableTrading() external onlyOwner {
        tradingEnabled = false;
        emit TradingDisabled();
    }

    /**
     * @dev 设置流动性池地址
     */
    function setLiquidityPool(address _liquidityPool) external onlyOwner {
        require(_liquidityPool != address(0), "Invalid address");
        liquidityPool = _liquidityPool;
        isExcludedFromLimits[_liquidityPool] = true;
    }

    /**
     * @dev 添加/移除黑名单
     */
    function setBlacklist(address account, bool blacklisted) external onlyOwner {
        isBlacklisted[account] = blacklisted;
    }

    /**
     * @dev 批量黑名单操作
     */
    function batchBlacklist(address[] calldata accounts, bool blacklisted) external onlyOwner {
        for (uint256 i = 0; i < accounts.length; i++) {
            isBlacklisted[accounts[i]] = blacklisted;
        }
    }

    /**
     * @dev 设置费用豁免
     */
    function setExcludedFromFees(address account, bool excluded) external onlyOwner {
        isExcludedFromFees[account] = excluded;
    }

    /**
     * @dev 设置限制豁免
     */
    function setExcludedFromLimits(address account, bool excluded) external onlyOwner {
        isExcludedFromLimits[account] = excluded;
    }

    /**
     * @dev 更新交易限制
     */
    function updateLimits(uint256 _maxTransactionAmount, uint256 _maxWalletAmount) external onlyOwner {
        require(_maxTransactionAmount >= totalSupply().div(1000), "Max transaction too low"); // 至少0.1%
        require(_maxWalletAmount >= totalSupply().div(1000), "Max wallet too low");
        
        maxTransactionAmount = _maxTransactionAmount;
        maxWalletAmount = _maxWalletAmount;
    }

    /**
     * @dev 重写转账函数，添加燃烧和税收机制
     */
    function _transfer(
        address from,
        address to,
        uint256 amount
    ) internal override nonReentrant whenNotPaused 
      onlyWhenTradingEnabled 
      notBlacklisted(from) 
      notBlacklisted(to)
      respectsCooldown(from) {
        
        require(from != address(0), "Transfer from zero address");
        require(to != address(0), "Transfer to zero address");
        require(amount > 0, "Transfer amount must be greater than zero");

        // 检查交易限制
        if (!isExcludedFromLimits[from] && !isExcludedFromLimits[to]) {
            require(amount <= maxTransactionAmount, "Transfer amount exceeds limit");
            
            // 检查接收方钱包限制
            if (to != liquidityPool) {
                require(
                    balanceOf(to).add(amount) <= maxWalletAmount,
                    "Recipient wallet exceeds limit"
                );
            }
        }

        // 启动保护期内的额外检查
        if (block.timestamp < launchTime + LAUNCH_PROTECTION_PERIOD) {
            _launchProtection(from, to, amount);
        }

        uint256 transferAmount = amount;
        
        // 计算费用（如果不在豁免列表中）
        if (!isExcludedFromFees[from] && !isExcludedFromFees[to]) {
            uint256 fees = _calculateFees(amount);
            if (fees > 0) {
                transferAmount = amount.sub(fees);
                _processFees(from, fees);
            }
        }

        // 执行转账
        super._transfer(from, to, transferAmount);
        
        // 更新最后交易时间
        lastTransactionTime[from] = block.timestamp;
        
        // 反鲸鱼检查
        if (antiWhaleEnabled && !isExcludedFromLimits[to]) {
            _checkAntiWhale(to, amount);
        }
    }

    /**
     * @dev 启动保护机制
     */
    function _launchProtection(address from, address to, uint256 amount) internal {
        // 限制大额交易
        if (!isExcludedFromLimits[from]) {
            require(
                amount <= totalSupply().div(200), // 启动期最大0.5%
                "Launch protection: amount too large"
            );
        }
    }

    /**
     * @dev 计算费用
     */
    function _calculateFees(uint256 amount) internal view returns (uint256) {
        uint256 totalFeeRate = burnRate.add(taxRate);
        return amount.mul(totalFeeRate).div(10000);
    }

    /**
     * @dev 处理费用
     */
    function _processFees(address from, uint256 totalFees) internal {
        uint256 burnAmount = totalFees.mul(burnRate).div(burnRate.add(taxRate));
        uint256 taxAmount = totalFees.sub(burnAmount);

        // 燃烧代币
        if (burnAmount > 0 && autoBurnEnabled) {
            super._transfer(from, address(0), burnAmount);
        }

        // 收取税费
        if (taxAmount > 0) {
            super._transfer(from, taxWallet, taxAmount);
            emit TaxCollected(from, taxAmount);
        }
    }

    /**
     * @dev 反鲸鱼检查
     */
    function _checkAntiWhale(address account, uint256 amount) internal {
        uint256 balance = balanceOf(account);
        uint256 totalSupplyAmount = totalSupply();
        
        // 如果持有量超过总供应量的5%，触发反鲸鱼机制
        if (balance > totalSupplyAmount.mul(5).div(100)) {
            emit AntiWhaleTriggered(account, balance);
            // 可以在这里添加额外的反鲸鱼措施
        }
    }

    /**
     * @dev 紧急暂停
     */
    function emergencyPause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 取消暂停
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急提取ETH
     */
    function emergencyWithdrawETH() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    /**
     * @dev 紧急提取代币
     */
    function emergencyWithdrawTokens(address token, uint256 amount) external onlyOwner {
        require(token != address(this), "Cannot withdraw own tokens");
        IERC20(token).transfer(owner(), amount);
    }

    /**
     * @dev 获取合约信息
     */
    function getContractInfo() external view returns (
        uint256 _totalSupply,
        uint256 _burnRate,
        uint256 _taxRate,
        bool _tradingEnabled,
        uint256 _maxTransactionAmount,
        uint256 _maxWalletAmount
    ) {
        return (
            totalSupply(),
            burnRate,
            taxRate,
            tradingEnabled,
            maxTransactionAmount,
            maxWalletAmount
        );
    }

    // 接收ETH
    receive() external payable {}
}
