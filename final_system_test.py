#!/usr/bin/env python3
"""
MemeMaster AI 参数优化系统最终测试
验证前端和后端的完整集成
"""

import requests
import json
import time
import subprocess
import sys
from urllib.parse import urljoin

# 测试配置
FRONTEND_URL = "http://localhost:3000"
BACKEND_URL = "http://localhost:8001"

def test_frontend_accessibility():
    """测试前端可访问性"""
    print("🌐 测试前端可访问性...")
    try:
        response = requests.get(FRONTEND_URL, timeout=10)
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {str(e)}")
        return False

def test_backend_health():
    """测试后端健康状态"""
    print("🔧 测试后端健康状态...")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端API服务正常运行")
            print(f"   服务版本: {data.get('version', 'N/A')}")
            return True
        else:
            print(f"❌ 后端API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端API连接失败: {str(e)}")
        return False

def test_optimization_workflow():
    """测试完整的参数优化工作流"""
    print("⚙️ 测试参数优化工作流...")
    
    # 1. 测试热点分析
    print("  📊 测试热点参数分析...")
    hotspot_data = {
        "keyword": "AI革命",
        "category": "technology",
        "score": 0.92,
        "trend": "rising",
        "regions": ["US", "EU", "ASIA"]
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/optimization/analyze-hotspot",
            json=hotspot_data,
            timeout=10
        )
        if response.status_code == 200:
            print("    ✅ 热点分析成功")
        else:
            print(f"    ❌ 热点分析失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ 热点分析异常: {str(e)}")
        return False
    
    # 2. 测试策略优化
    print("  🎯 测试策略参数优化...")
    try:
        response = requests.post(
            f"{BACKEND_URL}/optimization/strategy-parameters",
            json=hotspot_data,
            timeout=10
        )
        if response.status_code == 200:
            print("    ✅ 策略优化成功")
        else:
            print(f"    ❌ 策略优化失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ 策略优化异常: {str(e)}")
        return False
    
    # 3. 测试参数验证
    print("  🔍 测试参数验证...")
    custom_params = {
        "hotspot": {
            "min_virality": 0.85,
            "cultural_fit_threshold": 0.75,
            "response_timeout": 300
        },
        "strategy": {
            "airdrop_ratio": 0.50,
            "liquidity_ratio": 0.30,
            "marketing_ratio": 0.15,
            "team_ratio": 0.05
        },
        "liquidity": {
            "slippage_tolerance": 0.005,
            "whale_alert_threshold": 0.05
        },
        "exit": {
            "stop_loss_default": 0.15,
            "signal_threshold": 0.75
        }
    }
    
    try:
        response = requests.post(
            f"{BACKEND_URL}/optimization/validate-parameters",
            json=custom_params,
            timeout=10
        )
        if response.status_code == 200:
            print("    ✅ 参数验证成功")
        else:
            print(f"    ❌ 参数验证失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ 参数验证异常: {str(e)}")
        return False
    
    print("✅ 参数优化工作流测试完成")
    return True

def test_market_sensitivity():
    """测试市场灵敏度配置"""
    print("📈 测试市场灵敏度配置...")
    
    levels = ["conservative", "balanced", "aggressive"]
    for level in levels:
        try:
            response = requests.get(
                f"{BACKEND_URL}/optimization/market-sensitivity/{level}",
                timeout=10
            )
            if response.status_code == 200:
                print(f"    ✅ {level} 配置正常")
            else:
                print(f"    ❌ {level} 配置失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"    ❌ {level} 配置异常: {str(e)}")
            return False
    
    return True

def test_parameter_presets():
    """测试参数预设功能"""
    print("📋 测试参数预设功能...")
    
    try:
        response = requests.get(
            f"{BACKEND_URL}/optimization/custom-parameters/presets",
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            presets = data.get('presets', {})
            print(f"    ✅ 获取到 {len(presets)} 个预设配置")
            for preset_name in presets.keys():
                print(f"      - {preset_name}")
            return True
        else:
            print(f"    ❌ 预设获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"    ❌ 预设获取异常: {str(e)}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 MemeMaster AI 参数优化系统测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"总测试项目: {total_tests}")
    print(f"通过测试: {passed_tests} ✅")
    print(f"失败测试: {failed_tests} ❌")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if failed_tests == 0:
        print("\n🎉 所有测试通过！系统运行正常。")
        print("\n📝 系统访问信息:")
        print(f"  前端地址: {FRONTEND_URL}")
        print(f"  后端API: {BACKEND_URL}")
        print("\n🚀 您可以开始使用MemeMaster AI参数优化功能了！")
    else:
        print(f"\n⚠️ 有 {failed_tests} 项测试失败，请检查系统配置。")
    
    print("="*60)

def main():
    """主测试函数"""
    print("🚀 开始MemeMaster AI参数优化系统最终测试")
    print("="*60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(3)
    
    # 执行测试
    test_results = {}
    
    test_results["前端可访问性"] = test_frontend_accessibility()
    test_results["后端健康检查"] = test_backend_health()
    test_results["参数优化工作流"] = test_optimization_workflow()
    test_results["市场灵敏度配置"] = test_market_sensitivity()
    test_results["参数预设功能"] = test_parameter_presets()
    
    # 生成报告
    generate_test_report(test_results)
    
    # 返回退出码
    all_passed = all(test_results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
