# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
dist_simple/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Test files (keep only essential ones)
test_*.html
test_*.py
demo_*.py
debug_*.js

# Documentation (keep main docs, ignore detailed reports)
*_REPORT.md
*_SUMMARY.md
*_STATUS.md
*_GUIDE.md
*_README.md
*_DOCUMENTATION.md
*_COMPLETION_REPORT.md
*_FIX_REPORT.md
*_IMPLEMENTATION_REPORT.md
*_CHECK_REPORT.md

# Keep essential documentation
!README.md
!PROJECT_OVERVIEW.md
!QUICK_START_GUIDE.md
!DEPLOYMENT_GUIDE.md
!VERSION_CONTROL_GUIDE.md
