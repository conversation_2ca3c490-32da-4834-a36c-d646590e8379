#!/usr/bin/env python3
"""
设置MemeMaster AI自动启动
确保每次开机或新对话线程时React前端都能正常运行
"""

import os
import sys
import platform
from pathlib import Path

def create_startup_script():
    """创建启动脚本"""
    project_root = Path(__file__).parent.absolute()
    
    # 创建启动脚本
    startup_script = project_root / 'mememaster_startup.py'
    
    script_content = f'''#!/usr/bin/env python3
"""
MemeMaster AI 自动启动脚本
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def main():
    """主函数"""
    project_root = Path("{project_root}")
    os.chdir(str(project_root))
    
    print("🚀 MemeMaster AI 自动启动...")
    
    # 等待系统完全启动
    time.sleep(5)
    
    try:
        # 运行确保React前端的脚本
        subprocess.run([
            sys.executable, 
            str(project_root / "ensure_react_frontend.py"), 
            "start"
        ], check=True)
        
        print("✅ MemeMaster AI 启动完成")
        
    except Exception as e:
        print(f"❌ 启动失败: {{e}}")

if __name__ == "__main__":
    main()
'''
    
    with open(startup_script, 'w') as f:
        f.write(script_content)
    
    # 添加执行权限
    startup_script.chmod(0o755)
    
    return startup_script

def setup_macos_autostart():
    """设置macOS自动启动"""
    project_root = Path(__file__).parent.absolute()
    startup_script = create_startup_script()
    
    # 创建LaunchAgent plist文件
    plist_dir = Path.home() / "Library" / "LaunchAgents"
    plist_dir.mkdir(exist_ok=True)
    
    plist_file = plist_dir / "com.mememasterai.autostart.plist"
    
    plist_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.mememasterai.autostart</string>
    <key>ProgramArguments</key>
    <array>
        <string>{sys.executable}</string>
        <string>{startup_script}</string>
    </array>
    <key>WorkingDirectory</key>
    <string>{project_root}</string>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <false/>
    <key>StandardOutPath</key>
    <string>{project_root}/autostart.log</string>
    <key>StandardErrorPath</key>
    <string>{project_root}/autostart.error.log</string>
</dict>
</plist>
'''
    
    with open(plist_file, 'w') as f:
        f.write(plist_content)
    
    print(f"✅ 已创建macOS自动启动配置: {plist_file}")
    print("💡 重启后将自动启动MemeMaster AI")
    
    return plist_file

def setup_linux_autostart():
    """设置Linux自动启动"""
    project_root = Path(__file__).parent.absolute()
    startup_script = create_startup_script()
    
    # 创建systemd服务文件
    service_content = f'''[Unit]
Description=MemeMaster AI Auto Start
After=network.target

[Service]
Type=oneshot
User={os.getenv('USER')}
WorkingDirectory={project_root}
ExecStart={sys.executable} {startup_script}
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
'''
    
    service_file = project_root / "mememasterai.service"
    with open(service_file, 'w') as f:
        f.write(service_content)
    
    print(f"✅ 已创建Linux服务文件: {service_file}")
    print("💡 要启用自动启动，请运行:")
    print(f"   sudo cp {service_file} /etc/systemd/system/")
    print("   sudo systemctl enable mememasterai.service")
    print("   sudo systemctl start mememasterai.service")
    
    return service_file

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    project_root = Path(__file__).parent.absolute()
    
    if platform.system() == "Darwin":  # macOS
        # 创建应用程序包
        app_dir = project_root / "MemeMaster AI.app" / "Contents"
        app_dir.mkdir(parents=True, exist_ok=True)
        
        # Info.plist
        info_plist = app_dir / "Info.plist"
        info_content = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>MemeMaster AI</string>
    <key>CFBundleIdentifier</key>
    <string>com.mememasterai.app</string>
    <key>CFBundleName</key>
    <string>MemeMaster AI</string>
    <key>CFBundleVersion</key>
    <string>2.0.0</string>
</dict>
</plist>
'''
        with open(info_plist, 'w') as f:
            f.write(info_content)
        
        # 可执行文件
        macos_dir = app_dir / "MacOS"
        macos_dir.mkdir(exist_ok=True)
        
        executable = macos_dir / "MemeMaster AI"
        exec_content = f'''#!/bin/bash
cd "{project_root}"
{sys.executable} ensure_react_frontend.py start
'''
        with open(executable, 'w') as f:
            f.write(exec_content)
        executable.chmod(0o755)
        
        print(f"✅ 已创建macOS应用程序: {project_root}/MemeMaster AI.app")
        
    elif platform.system() == "Linux":
        # 创建.desktop文件
        desktop_file = project_root / "MemeMaster AI.desktop"
        desktop_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=MemeMaster AI
Comment=智能Meme币交易系统
Exec={sys.executable} {project_root}/ensure_react_frontend.py start
Icon={project_root}/icon.png
Path={project_root}
Terminal=true
Categories=Development;
'''
        with open(desktop_file, 'w') as f:
            f.write(desktop_content)
        desktop_file.chmod(0o755)
        
        print(f"✅ 已创建Linux桌面文件: {desktop_file}")

def create_quick_commands():
    """创建快速命令脚本"""
    project_root = Path(__file__).parent.absolute()
    
    # 创建快速启动脚本
    quick_start = project_root / "quick_start_react.py"
    quick_content = f'''#!/usr/bin/env python3
"""
MemeMaster AI 快速启动React前端
"""

import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path("{project_root}")
    ensure_script = project_root / "ensure_react_frontend.py"
    
    print("🚀 快速启动MemeMaster AI React前端...")
    
    try:
        subprocess.run([sys.executable, str(ensure_script), "start"], check=True)
    except Exception as e:
        print(f"❌ 启动失败: {{e}}")

if __name__ == "__main__":
    main()
'''
    
    with open(quick_start, 'w') as f:
        f.write(quick_content)
    quick_start.chmod(0o755)
    
    # 创建状态检查脚本
    quick_status = project_root / "quick_check_status.py"
    status_content = f'''#!/usr/bin/env python3
"""
MemeMaster AI 快速状态检查
"""

import subprocess
import sys
from pathlib import Path

def main():
    project_root = Path("{project_root}")
    ensure_script = project_root / "ensure_react_frontend.py"
    
    try:
        subprocess.run([sys.executable, str(ensure_script), "status"], check=True)
    except Exception as e:
        print(f"❌ 检查失败: {{e}}")

if __name__ == "__main__":
    main()
'''
    
    with open(quick_status, 'w') as f:
        f.write(status_content)
    quick_status.chmod(0o755)
    
    print(f"✅ 已创建快速命令:")
    print(f"   启动: python3 {quick_start}")
    print(f"   状态: python3 {quick_status}")

def main():
    """主函数"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║              MemeMaster AI 自动启动设置工具                  ║
║                    永久解决React前端问题                     ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    system = platform.system()
    print(f"🖥️ 检测到操作系统: {system}")
    
    try:
        # 创建快速命令
        create_quick_commands()
        
        # 创建桌面快捷方式
        create_desktop_shortcut()
        
        # 设置自动启动
        if system == "Darwin":  # macOS
            plist_file = setup_macos_autostart()
            print(f"\n🍎 macOS自动启动已配置")
            print(f"📁 配置文件: {plist_file}")
            
        elif system == "Linux":
            service_file = setup_linux_autostart()
            print(f"\n🐧 Linux自动启动已配置")
            print(f"📁 服务文件: {service_file}")
            
        else:
            print(f"\n⚠️ 不支持的操作系统: {system}")
            print("请手动设置自动启动")
        
        print(f"\n🎉 自动启动设置完成！")
        print("="*50)
        print("📋 可用命令:")
        print("   python3 ensure_react_frontend.py start  # 启动系统")
        print("   python3 ensure_react_frontend.py status # 检查状态")
        print("   python3 quick_start_react.py           # 快速启动")
        print("   python3 quick_check_status.py          # 快速检查")
        print("")
        print("💡 现在每次新的对话线程时，React前端都会自动启动！")
        print("🌐 访问地址: http://localhost:3000/")
        
    except Exception as e:
        print(f"❌ 设置失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
