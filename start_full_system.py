#!/usr/bin/env python3
"""
MemeMaster AI 完整系统启动脚本
同时启动后端API服务器和前端React应用
"""

import os
import sys
import time
import signal
import subprocess
import threading
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    MemeMaster AI                             ║
║                  完整系统启动器 v2.0                         ║
║                                                              ║
║  🚀 后端API服务器 | 🎨 React前端界面 | 💰 用户管理系统      ║
║  📊 实时数据监控  | 🤖 AI智能分析   | ⚡ 自动化交易         ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查系统依赖"""
    print("🔍 检查系统依赖...")
    
    # 检查Python
    try:
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
            print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
            return False
        print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    except Exception as e:
        print(f"❌ Python检查失败: {e}")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, check=True)
        npm_version = result.stdout.strip()
        print(f"✅ npm {npm_version}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ npm未安装或不可用")
        return False
    
    # 检查Python依赖
    python_deps = ['fastapi', 'uvicorn', 'pyjwt', 'bcrypt', 'aiosqlite']
    missing_python_deps = []
    
    for dep in python_deps:
        try:
            __import__(dep)
        except ImportError:
            missing_python_deps.append(dep)
    
    if missing_python_deps:
        print(f"⚠️ 缺少Python依赖: {', '.join(missing_python_deps)}")
        print("运行: pip install " + " ".join(missing_python_deps))
    else:
        print("✅ Python依赖完整")
    
    # 检查Node.js依赖
    if not os.path.exists('node_modules'):
        print("⚠️ Node.js依赖未安装")
        print("运行: npm install")
        return False
    else:
        print("✅ Node.js依赖已安装")
    
    return len(missing_python_deps) == 0

class ProcessManager:
    """进程管理器"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
    
    def start_backend(self):
        """启动后端服务器"""
        print("🚀 启动后端API服务器...")
        try:
            process = subprocess.Popen([
                sys.executable, 'working_server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes['backend'] = process
            
            # 等待服务器启动
            time.sleep(3)
            
            if process.poll() is None:
                print("✅ 后端服务器启动成功 (PID: {})".format(process.pid))
                print("   📡 API地址: http://localhost:8001/")
                print("   📚 API文档: http://localhost:8001/docs")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ 后端服务器启动失败")
                print(f"错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 后端服务器启动异常: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务器"""
        print("🎨 启动React前端应用...")
        try:
            process = subprocess.Popen([
                'npm', 'run', 'frontend:dev'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            self.processes['frontend'] = process
            
            # 等待前端服务器启动
            time.sleep(5)
            
            if process.poll() is None:
                print("✅ 前端应用启动成功 (PID: {})".format(process.pid))
                print("   🌐 前端地址: http://localhost:3000/")
                print("   🎯 用户界面: React + Vite")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ 前端应用启动失败")
                print(f"错误: {stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 前端应用启动异常: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            try:
                for name, process in self.processes.items():
                    if process.poll() is not None:
                        print(f"⚠️ {name} 进程已退出 (返回码: {process.returncode})")
                        if name == 'backend':
                            print("尝试重启后端服务器...")
                            self.start_backend()
                        elif name == 'frontend':
                            print("尝试重启前端应用...")
                            self.start_frontend()
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                print(f"进程监控错误: {e}")
                time.sleep(5)
    
    def stop_all(self):
        """停止所有进程"""
        print("\n🛑 正在停止所有服务...")
        self.running = False
        
        for name, process in self.processes.items():
            try:
                print(f"停止 {name} 服务...")
                process.terminate()
                
                # 等待进程优雅退出
                try:
                    process.wait(timeout=5)
                    print(f"✅ {name} 服务已停止")
                except subprocess.TimeoutExpired:
                    print(f"⚠️ {name} 服务未响应，强制终止...")
                    process.kill()
                    process.wait()
                    print(f"✅ {name} 服务已强制停止")
                    
            except Exception as e:
                print(f"停止 {name} 服务时出错: {e}")

def setup_signal_handlers(manager):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在关闭系统...")
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def print_system_info():
    """打印系统信息"""
    print("\n" + "="*60)
    print("🎉 MemeMaster AI 系统启动完成！")
    print("="*60)
    
    print("\n🌐 访问地址:")
    print("   • 前端界面: http://localhost:3000/")
    print("   • 后端API: http://localhost:8001/")
    print("   • API文档: http://localhost:8001/docs")
    print("   • 健康检查: http://localhost:8001/health")
    
    print("\n📱 功能模块:")
    print("   • 🤖 AI智能分析 - 实时热点监测")
    print("   • ⚡ 自动化交易 - 策略管理系统")
    print("   • 💰 用户管理 - 分层收费模式")
    print("   • 📊 数据可视化 - React仪表盘")
    
    print("\n🔧 系统架构:")
    print("   • 前端: React 18 + Vite + Tailwind CSS")
    print("   • 后端: FastAPI + Python 3.8+")
    print("   • 数据库: SQLite + aiosqlite")
    print("   • 认证: JWT Token + bcrypt")
    
    print("\n💡 使用提示:")
    print("   • 前端开发: 修改frontend/目录下的文件")
    print("   • 后端开发: 修改src/目录下的文件")
    print("   • 用户管理: 访问 /login 或 /user-dashboard")
    print("   • 管理员: 访问 /admin (需要企业版权限)")
    
    print("\n⚠️ 注意事项:")
    print("   • 使用 Ctrl+C 停止所有服务")
    print("   • 前端代理配置指向后端8001端口")
    print("   • 生产环境请修改默认密钥和配置")
    
    print("\n" + "="*60)

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺失的依赖")
        print("Python依赖: pip install fastapi uvicorn pyjwt bcrypt aiosqlite")
        print("Node.js依赖: npm install")
        sys.exit(1)
    
    # 创建进程管理器
    manager = ProcessManager()
    
    # 设置信号处理器
    setup_signal_handlers(manager)
    
    try:
        # 启动后端服务器
        if not manager.start_backend():
            print("❌ 后端服务器启动失败")
            sys.exit(1)
        
        # 启动前端应用
        if not manager.start_frontend():
            print("❌ 前端应用启动失败")
            manager.stop_all()
            sys.exit(1)
        
        # 打印系统信息
        print_system_info()
        
        # 启动进程监控
        monitor_thread = threading.Thread(target=manager.monitor_processes, daemon=True)
        monitor_thread.start()
        
        # 主线程等待
        print("系统运行中... 按 Ctrl+C 停止")
        while manager.running:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n用户中断，正在关闭系统...")
        manager.stop_all()
    except Exception as e:
        print(f"\n系统运行错误: {e}")
        manager.stop_all()
        sys.exit(1)

if __name__ == "__main__":
    main()
