# 多阶段构建的Dockerfile
FROM python:3.10-slim as python-base

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libssl-dev \
    libffi-dev \
    python3-dev \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Node.js阶段
FROM node:18-slim as node-base

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装Node.js依赖
RUN npm ci --only=production

# Python依赖阶段
FROM python-base as python-deps

# 复制requirements文件
COPY requirements.txt ./

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 最终阶段
FROM python-base as final

# 创建非root用户
RUN groupadd -r mememaster && useradd -r -g mememaster mememaster

# 从Node.js阶段复制依赖
COPY --from=node-base /app/node_modules ./node_modules

# 从Python依赖阶段复制依赖
COPY --from=python-deps /usr/local/lib/python3.10/site-packages /usr/local/lib/python3.10/site-packages
COPY --from=python-deps /usr/local/bin /usr/local/bin

# 安装Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /var/log/mememaster \
    && mkdir -p /app/data \
    && mkdir -p /app/deployments

# 设置权限
RUN chown -R mememaster:mememaster /app \
    && chown -R mememaster:mememaster /var/log/mememaster

# 切换到非root用户
USER mememaster

# 暴露端口
EXPOSE 3000 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# 启动命令
CMD ["python", "-m", "src.app"]
