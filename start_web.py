#!/usr/bin/env python3
"""
MemeMaster AI Web服务启动脚本
简化版本，直接启动Web服务
"""

import os
import sys
import subprocess
import time

def main():
    print("🚀 MemeMaster AI Web服务启动器")
    print("=" * 50)
    
    # 检查Python环境
    print("📋 检查Python环境...")
    python_version = sys.version_info
    print(f"   Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = ['fastapi', 'uvicorn', 'psutil']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} (缺失)")
            missing_packages.append(package)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("   ✓ 包安装完成")
        except subprocess.CalledProcessError as e:
            print(f"   ✗ 包安装失败: {e}")
            return False
    
    # 启动Web服务
    print("\n🌐 启动Web服务...")
    print("   地址: http://0.0.0.0:8000")
    print("   外部访问: http://*************:8000")
    print("   按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 导入并启动应用
        from web_app import app
        import uvicorn
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 服务已停止")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
