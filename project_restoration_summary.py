#!/usr/bin/env python3
"""
项目恢复到原始状态总结
"""

import os
import requests
import webbrowser

def check_file_status():
    """检查关键文件状态"""
    print("🔍 检查关键文件状态...")
    
    key_files = [
        "frontend/main.jsx",
        "frontend/App.jsx", 
        "frontend/index.css",
        "frontend/layouts/MainLayout.jsx",
        "frontend/pages/Dashboard.jsx",
        "frontend/store/index.js"
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: 存在")
        else:
            print(f"❌ {file_path}: 缺失")

def check_removed_files():
    """检查已删除的新增文件"""
    print("\n🗑️ 检查已删除的新增文件...")
    
    removed_files = [
        "frontend/main-simple.jsx",
        "frontend/main-progressive.jsx", 
        "frontend/components/ModernSidebar.jsx",
        "frontend/components/ModernTopBar.jsx",
        "hotspot_dashboard.html",
        "fix_all_icons.py",
        "final_project_check.py"
    ]
    
    for file_path in removed_files:
        if not os.path.exists(file_path):
            print(f"✅ {file_path}: 已删除")
        else:
            print(f"⚠️ {file_path}: 仍存在")

def test_react_frontend():
    """测试React前端"""
    print("\n🔍 测试React前端...")
    
    try:
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ React前端响应正常")
            return True
        else:
            print(f"❌ React前端响应异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ React前端连接失败: {e}")
        return False

def test_backend_api():
    """测试后端API"""
    print("\n🔍 测试后端API...")
    
    try:
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API响应正常")
            return True
        else:
            print(f"❌ 后端API响应异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端API连接失败: {e}")
        return False

def main():
    print("🔄 MemeMaster AI 项目恢复状态检查")
    print("=" * 60)
    
    # 检查文件状态
    check_file_status()
    
    # 检查删除的文件
    check_removed_files()
    
    # 测试前端和后端
    react_ok = test_react_frontend()
    backend_ok = test_backend_api()
    
    print("\n" + "=" * 60)
    print("📋 恢复结果总结")
    print("=" * 60)
    
    print("\n✅ 已完成的恢复操作:")
    print("   • 🔄 恢复 main.jsx 到原始状态")
    print("   • 🔄 恢复 Redux store 配置")
    print("   • 🔄 恢复 MainLayout 组件")
    print("   • 🔄 恢复 Dashboard 组件")
    print("   • 🧹 清理 index.css 样式")
    print("   • 🗑️ 删除新增的测试文件")
    print("   • 🗑️ 删除现代化UI组件")
    
    print("\n📁 当前项目结构:")
    print("   • frontend/main.jsx - 标准React入口")
    print("   • frontend/App.jsx - 原始路由配置")
    print("   • frontend/index.css - 基础Tailwind样式")
    print("   • frontend/layouts/MainLayout.jsx - 原始布局")
    print("   • frontend/pages/ - 原始页面组件")
    print("   • frontend/store/ - 原始Redux配置")
    
    print("\n🎯 项目当前状态:")
    if react_ok:
        print("   ✅ React前端: 正常运行")
    else:
        print("   ❌ React前端: 需要检查")
        
    if backend_ok:
        print("   ✅ 后端API: 正常运行")
    else:
        print("   ❌ 后端API: 需要检查")
    
    print("\n🌐 可用访问地址:")
    print("   • React应用: http://localhost:3000/")
    print("   • HTML备用版: http://localhost:8090/simple_dashboard.html")
    print("   • API文档: http://localhost:8001/docs")
    print("   • 系统状态: http://localhost:8001/health")
    
    print("\n💡 下一步建议:")
    print("   1. 🎨 现在可以重新应用您的style和body参考设计")
    print("   2. 🔧 基于原始项目结构进行UI改进")
    print("   3. 📝 保持现有功能的同时更新视觉设计")
    print("   4. 🧪 逐步测试每个修改")
    
    print("\n🎊 项目已成功恢复到原始状态！")
    print("现在可以重新开始应用新的UI设计了。")
    
    # 自动打开React应用
    if react_ok:
        print("\n🌐 自动打开React应用...")
        try:
            webbrowser.open("http://localhost:3000/")
            print("✅ 已在浏览器中打开React应用")
        except:
            print("⚠️ 无法自动打开浏览器")

if __name__ == "__main__":
    main()
