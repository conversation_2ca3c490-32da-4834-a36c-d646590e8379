#!/usr/bin/env python3
"""
React前端启动问题诊断工具
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_node_npm():
    """检查Node.js和npm"""
    print("🔍 检查Node.js和npm...")
    
    # 检查Node.js
    success, stdout, stderr = run_command("node --version")
    if success:
        print(f"✅ Node.js: {stdout.strip()}")
    else:
        print(f"❌ Node.js未安装或不可用: {stderr}")
        return False
    
    # 检查npm
    success, stdout, stderr = run_command("npm --version")
    if success:
        print(f"✅ npm: {stdout.strip()}")
    else:
        print(f"❌ npm未安装或不可用: {stderr}")
        return False
    
    return True

def check_package_json():
    """检查package.json"""
    print("\n🔍 检查package.json...")
    
    package_json = Path("package.json")
    if not package_json.exists():
        print("❌ package.json不存在")
        return False
    
    try:
        with open(package_json, 'r') as f:
            data = json.load(f)
        
        print("✅ package.json存在")
        
        # 检查scripts
        scripts = data.get('scripts', {})
        if 'frontend:dev' in scripts:
            print(f"✅ frontend:dev脚本: {scripts['frontend:dev']}")
        else:
            print("❌ 缺少frontend:dev脚本")
            return False
        
        # 检查依赖
        deps = data.get('dependencies', {})
        dev_deps = data.get('devDependencies', {})
        
        required_deps = ['react', 'react-dom', 'vite']
        missing_deps = []
        
        for dep in required_deps:
            if dep not in deps and dep not in dev_deps:
                missing_deps.append(dep)
        
        if missing_deps:
            print(f"❌ 缺少依赖: {missing_deps}")
            return False
        else:
            print("✅ 必要依赖存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 解析package.json失败: {e}")
        return False

def check_node_modules():
    """检查node_modules"""
    print("\n🔍 检查node_modules...")
    
    node_modules = Path("node_modules")
    if not node_modules.exists():
        print("❌ node_modules不存在")
        return False
    
    # 检查关键模块
    key_modules = ['react', 'react-dom', 'vite', '@vitejs/plugin-react']
    missing_modules = []
    
    for module in key_modules:
        module_path = node_modules / module
        if not module_path.exists():
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ 缺少模块: {missing_modules}")
        return False
    else:
        print("✅ 关键模块存在")
        return True

def check_vite_config():
    """检查Vite配置"""
    print("\n🔍 检查Vite配置...")
    
    vite_config = Path("vite.config.js")
    if not vite_config.exists():
        print("❌ vite.config.js不存在")
        return False
    
    try:
        with open(vite_config, 'r') as f:
            content = f.read()
        
        print("✅ vite.config.js存在")
        
        # 检查关键配置
        if 'port: 3000' in content:
            print("✅ 端口配置正确")
        else:
            print("⚠️ 端口配置可能有问题")
        
        if 'localhost:8001' in content:
            print("✅ 代理配置指向8001端口")
        else:
            print("⚠️ 代理配置可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取vite.config.js失败: {e}")
        return False

def check_frontend_files():
    """检查前端文件"""
    print("\n🔍 检查前端文件...")
    
    # 检查关键文件
    key_files = [
        "index.html",
        "frontend/main.jsx",
        "frontend/App.jsx",
        "frontend/index.css"
    ]
    
    missing_files = []
    for file_path in key_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 关键前端文件存在")
        return True

def try_npm_install():
    """尝试重新安装依赖"""
    print("\n🔧 尝试重新安装依赖...")
    
    # 删除node_modules和package-lock.json
    print("清理旧依赖...")
    run_command("rm -rf node_modules package-lock.json")
    
    # 重新安装
    print("重新安装依赖...")
    success, stdout, stderr = run_command("npm install")
    
    if success:
        print("✅ 依赖安装成功")
        return True
    else:
        print(f"❌ 依赖安装失败: {stderr}")
        return False

def try_start_frontend():
    """尝试启动前端"""
    print("\n🚀 尝试启动前端...")
    
    success, stdout, stderr = run_command("npm run frontend:dev", cwd=".")
    
    if success:
        print("✅ 前端启动命令执行成功")
        print(f"输出: {stdout}")
        return True
    else:
        print(f"❌ 前端启动失败")
        print(f"错误: {stderr}")
        return False

def main():
    """主函数"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║              React前端启动问题诊断工具                       ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    # 切换到项目目录
    project_root = Path(__file__).parent.absolute()
    os.chdir(project_root)
    print(f"📁 项目目录: {project_root}")
    
    issues = []
    
    # 检查Node.js和npm
    if not check_node_npm():
        issues.append("Node.js或npm问题")
    
    # 检查package.json
    if not check_package_json():
        issues.append("package.json问题")
    
    # 检查node_modules
    if not check_node_modules():
        issues.append("node_modules问题")
        # 尝试重新安装
        if try_npm_install():
            print("✅ 依赖重新安装成功，重新检查...")
            if not check_node_modules():
                issues.append("依赖安装后仍有问题")
    
    # 检查Vite配置
    if not check_vite_config():
        issues.append("Vite配置问题")
    
    # 检查前端文件
    if not check_frontend_files():
        issues.append("前端文件缺失")
    
    # 总结
    print("\n" + "="*60)
    print("📋 诊断结果")
    print("="*60)
    
    if not issues:
        print("✅ 所有检查通过，尝试启动前端...")
        try_start_frontend()
    else:
        print("❌ 发现以下问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        
        print("\n💡 建议解决方案:")
        print("1. 确保Node.js >= 16.0.0")
        print("2. 运行: rm -rf node_modules package-lock.json")
        print("3. 运行: npm install")
        print("4. 检查vite.config.js配置")
        print("5. 确保所有前端文件存在")

if __name__ == "__main__":
    main()
