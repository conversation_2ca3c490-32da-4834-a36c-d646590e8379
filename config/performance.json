{"performance": {"lazy_imports": true, "asset_compression": true, "cache_cleanup": true, "memory_optimization": true}, "monitoring": {"startup_time_threshold": 5.0, "memory_threshold_mb": 500, "cpu_threshold_percent": 80}, "optimizations": [{"category": "导入优化", "action": "优化文件 detector.py", "result": "延迟导入 1 个模块", "timestamp": 1749741340.6727328}, {"category": "前端优化", "action": "压缩 index-1e7e5deb.js", "result": "节省 535 字节", "timestamp": 1749741340.890257}, {"category": "前端优化", "action": "压缩 index-ddaf4e5e.css", "result": "节省 1 字节", "timestamp": 1749741340.89981}, {"category": "缓存清理", "action": "清理 9 个缓存项", "result": "释放 0.20 MB", "timestamp": 1749741344.784965}, {"category": "数据库优化", "action": "检查查询性能", "result": "暂无数据库连接", "timestamp": 1749741344.785192}, {"category": "内存优化", "action": "检查内存使用", "result": "当前使用 30.22 MB，正常", "timestamp": 1749741344.7859552}, {"category": "文件结构", "action": "检查文件大小", "result": "无异常大文件", "timestamp": 1749741347.148698}], "metrics": {"startup_time": 0, "memory_usage": 30.22265625, "cpu_usage": 0, "disk_usage": 0, "file_count": 243}}