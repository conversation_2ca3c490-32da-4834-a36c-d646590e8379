{"backend": {"script": "web_app.py", "port": 8001, "host": "0.0.0.0", "workers": 1, "reload": true}, "frontend": {"command": "npm run frontend:dev", "port": 3000, "build_command": "npm run frontend:build"}, "dependencies": {"python": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "pydantic", "python-dotenv", "psutil"], "node": ["react", "react-dom", "vite", "@vitejs/plugin-react", "tailwindcss"]}, "monitoring": {"health_check_interval": 30, "restart_on_failure": true, "max_restart_attempts": 3}, "logging": {"level": "INFO", "file": "logs/mememaster.log", "max_size": "10MB", "backup_count": 5}, "security": {"cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:3001", "http://127.0.0.1:3001"], "api_rate_limit": "100/minute"}, "features": {"user_management": false, "ai_optimization": false, "blockchain_integration": false, "real_time_monitoring": true}}