# MemeMaster AI 参数优化配置
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any
import numpy as np

class OptimizationConfig:
    """参数优化配置类"""
    
    # 舆情监测参数优化
    HOTSPOT_CONFIG = {
        "min_virality": 0.85,         # 最低病毒性评分阈值
        "cultural_fit_threshold": 0.75, # 文化适配度阈值
        "response_timeout": 300,       # 响应超时(秒)
        "source_weights": {            # 来源权重
            "twitter": 0.35,
            "reddit": 0.25,
            "tiktok": 0.20,
            "darkweb": 0.15,
            "news": 0.05
        },
        "keyword_alert": [             # 关键词警报
            "election", "trump", "elon", 
            "kpop", "nft", "gamefi", "ai",
            "meme", "viral", "trending"
        ],
        "min_volume": {                # 最低传播量
            "twitter": 1000,           # 转发量
            "reddit": 500,             # 点赞量
            "tiktok": 100000           # 播放量
        }
    }

    # 策略生成参数优化
    STRATEGY_CONFIG = {
        "supply_range": [1e9, 5e10],      # 代币总量范围
        "base_allocation": {              # 基础分配比例
            "airdrop": 0.50,
            "liquidity": 0.30,
            "marketing": 0.10,
            "team": 0.10
        },
        "burn_rate_formula": "0.05 + (greed_index - 50)*0.001",  # 燃烧率公式
        "timezone_optimization": {        # 时区优化
            "US": "20:00 ET",
            "EU": "18:00 CET",
            "ASIA": "09:00 CST"
        },
        "compliance_checks": {            # 合规设置
            "trademark_check": True,
            "geo_fencing": True,
            "restricted_regions": ["US", "CN", "KR"]
        }
    }

    # 发币部署参数优化
    DEPLOY_CONFIG = {
        "platform_priority": ["pump.fun", "raydium", "uniswap"],  # 平台优先级
        "gas_optimization": {
            "max_gas_price": 50,          # Gwei
            "time_window": "01:00-04:00 UTC"  # 低Gas时段
        },
        "auto_audit": {
            "enabled": True,
            "tools": ["slither", "mythril"],
            "risk_threshold": 0.7         # 风险阈值
        },
        "deployment_timeout": 1800,       # 部署超时(秒)
        "multi_chain_support": ["solana", "ethereum", "bsc"]
    }

    # 流动性控制参数优化
    LIQUIDITY_CONFIG = {
        "wallet_management": {
            "min_wallets": 20,            # 最小钱包数
            "max_wallets": 100,           # 最大钱包数
            "initial_funding": 0.5,       # 初始资金(SOL/ETH)
            "rent_recovery_threshold": 0.001  # SOL租金回收阈值
        },
        "anti_ban": {
            "ip_rotation": True,
            "action_delay": [5, 30],      # 操作延迟范围(秒)
            "amount_variation": 0.1,      # 金额浮动±10%
            "account_tiering": {
                "master": 1,
                "operator": 5,
                "backup": 10
            }
        },
        "liquidity_management": {
            "min_tvl_ratio": 0.5,         # 最低TVL比例
            "slippage_tolerance": 0.005,  # 滑点容忍度0.5%
            "whale_alert_threshold": 0.05 # 鲸鱼活动警报阈值(5%)
        }
    }

    # 退出策略参数优化（核心）
    EXIT_STRATEGY_TEMPLATE = {
        "signal_threshold": 0.75,         # 触发退出信号阈值
        "indicators": {                   # 指标权重
            "price_volatility": 0.30,
            "rsi": 0.25,
            "sentiment": 0.20,
            "volume_spike": 0.15,
            "whale_activity": 0.10
        },
        "dynamic_selling": {
            "high_volatility": {          # 波动率>70%
                "batch_size": 0.08,       # 每批8%
                "interval": 1200          # 间隔20分钟
            },
            "medium_volatility": {        # 波动率40-70%
                "batch_size": 0.10,       # 每批10%
                "interval": 2400          # 间隔40分钟
            },
            "low_volatility": {           # 波动率<40%
                "batch_size": 0.12,       # 每批12%
                "interval": 3600          # 间隔60分钟
            }
        },
        "stop_loss": {
            "default": 0.15,              # 默认止损线15%
            "custom_rules": {             # 自定义规则
                "political": 0.10,        # 政治类止损10%
                "entertainment": 0.20     # 娱乐类止损20%
            }
        },
        "hedging": {
            "enabled": True,
            "min_volatility": 0.60,       # 启用对冲的最小波动率
            "position_ratio": 0.30        # 对冲头寸比例30%
        }
    }

    @staticmethod
    def customize_exit_strategy(token_type: str, initial_performance: float) -> Dict[str, Any]:
        """
        根据代币类型和初期表现定制退出策略
        """
        import copy
        strategy = copy.deepcopy(OptimizationConfig.EXIT_STRATEGY_TEMPLATE)
        
        if token_type.upper() == "POLITICAL":
            strategy["indicators"]["sentiment"] = 0.35  # 提高情绪权重
            strategy["stop_loss"]["default"] = 0.10
        elif token_type.upper() == "ENTERTAINMENT":
            strategy["dynamic_selling"]["high_volatility"]["batch_size"] = 0.10
            strategy["stop_loss"]["default"] = 0.20
        elif token_type.upper() == "TECHNOLOGY":
            strategy["indicators"]["volume_spike"] = 0.25  # 提高交易量权重
            strategy["signal_threshold"] = 0.80
        
        if initial_performance > 2.0:  # 初期表现>2倍
            strategy["signal_threshold"] = 0.80  # 提高退出门槛
        elif initial_performance < 0.5:  # 初期表现不佳
            strategy["stop_loss"]["default"] = 0.08  # 降低止损线
        
        return strategy

    @staticmethod
    def adjust_strategy_midway(current_performance: float, volatility: float) -> Dict[str, Any]:
        """
        根据中期表现调整策略
        """
        adjustments = {}
        
        if current_performance > 5.0 and volatility < 0.3:
            # 超高收益低波动：加快退出速度
            adjustments = {
                "dynamic_selling.high_volatility.batch_size": 0.12,
                "dynamic_selling.high_volatility.interval": 900  # 15分钟
            }
        elif current_performance < 1.2 and volatility > 0.6:
            # 表现不佳高波动：提前止损
            adjustments = {
                "stop_loss.default": 0.08,
                "hedging.enabled": True
            }
        elif current_performance > 3.0 and volatility > 0.7:
            # 高收益高波动：启用对冲
            adjustments = {
                "hedging.enabled": True,
                "hedging.position_ratio": 0.40
            }
            
        return adjustments

    @staticmethod
    def calculate_initial_liquidity(strategy: Dict[str, Any]) -> float:
        """根据策略计算初始流动性"""
        base = 0.5  # SOL/ETH
        
        # 基于代币总量调整
        supply_factor = min(1.0, strategy.get('total_supply', 1e9) / 1e10)
        
        # 基于热点类型调整
        hotspot_type = strategy.get('hotspot_type', 'GENERAL')
        if hotspot_type == "POLITICAL":
            return base * 1.5 * supply_factor
        elif hotspot_type == "SUBCULTURE":
            return base * 0.8 * supply_factor
        elif hotspot_type == "TECHNOLOGY":
            return base * 1.2 * supply_factor
        else:
            return base * supply_factor

    @staticmethod
    def validate_parameters(params: Dict[str, Any]) -> Dict[str, Any]:
        """参数安全验证"""
        def clamp(value, min_val, max_val):
            return max(min_val, min(value, max_val))
        
        # 确保分配比例总和为1
        if 'base_allocation' in params:
            allocation_sum = sum(params['base_allocation'].values())
            if abs(allocation_sum - 1.0) > 0.01:
                # 归一化分配比例
                for key in params['base_allocation']:
                    params['base_allocation'][key] /= allocation_sum
        
        # 波动率参数边界
        if 'dynamic_selling' in params:
            for volatility_level in params['dynamic_selling']:
                if 'batch_size' in params['dynamic_selling'][volatility_level]:
                    params['dynamic_selling'][volatility_level]['batch_size'] = clamp(
                        params['dynamic_selling'][volatility_level]['batch_size'], 0.05, 0.15
                    )
        
        # 止损安全范围
        if 'stop_loss' in params and 'default' in params['stop_loss']:
            params['stop_loss']['default'] = clamp(
                params['stop_loss']['default'], 0.05, 0.25
            )
        
        return params

    @staticmethod
    def get_market_sensitivity_config(sensitivity_level: str) -> Dict[str, Any]:
        """
        获取市场灵敏度配置
        sensitivity_level: 'conservative', 'balanced', 'aggressive'
        """
        configs = {
            'conservative': {
                'hotspot_threshold': 0.90,
                'exit_threshold': 0.65,
                'stop_loss_multiplier': 0.8,
                'batch_size_multiplier': 0.8
            },
            'balanced': {
                'hotspot_threshold': 0.75,
                'exit_threshold': 0.75,
                'stop_loss_multiplier': 1.0,
                'batch_size_multiplier': 1.0
            },
            'aggressive': {
                'hotspot_threshold': 0.60,
                'exit_threshold': 0.85,
                'stop_loss_multiplier': 1.2,
                'batch_size_multiplier': 1.2
            }
        }
        
        return configs.get(sensitivity_level, configs['balanced'])
