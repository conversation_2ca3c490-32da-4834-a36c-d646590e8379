#!/usr/bin/env python3
"""
MemeMaster AI 简化仪表盘
使用纯 HTML/CSS/JS 实现，无需复杂依赖
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import json
import time
import random

app = FastAPI(title="MemeMaster AI Dashboard", version="2.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 简化的HTML仪表盘
DASHBOARD_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MemeMaster AI 仪表盘</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e293b, #7c3aed, #1e293b);
            color: white;
            min-height: 100vh;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .card:hover { transform: translateY(-5px); }
        .card h3 {
            margin-bottom: 15px;
            color: #60a5fa;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
        }
        .metric-value {
            font-weight: bold;
            color: #34d399;
        }
        .chart-container {
            position: relative;
            height: 200px;
            margin: 15px 0;
        }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        .btn:hover { background: #2563eb; }
        .btn-danger { background: #ef4444; }
        .btn-danger:hover { background: #dc2626; }
        .settings-panel {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        .slider {
            width: 100%;
            margin: 10px 0;
        }
        .nav-tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 5px;
        }
        .nav-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.3s ease;
        }
        .nav-tab.active { background: #3b82f6; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MemeMaster AI 仪表盘</h1>
            <p>智能Meme币交易系统 - 实时监控与控制</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator"></span>
                <span>系统运行正常</span>
                <span style="margin-left: 20px;">最后更新: <span id="lastUpdate"></span></span>
            </div>
        </div>

        <div class="nav-tabs">
            <div class="nav-tab active" onclick="showTab('overview')">总览</div>
            <div class="nav-tab" onclick="showTab('hotspot')">舆情监控</div>
            <div class="nav-tab" onclick="showTab('strategy')">策略管理</div>
            <div class="nav-tab" onclick="showTab('wallet')">钱包管理</div>
            <div class="nav-tab" onclick="showTab('liquidity')">流动性</div>
            <div class="nav-tab" onclick="showTab('exit')">退出系统</div>
        </div>

        <!-- 总览标签页 -->
        <div id="overview" class="tab-content active">
            <div class="grid">
                <div class="card">
                    <h3>📊 系统状态</h3>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="uptime">12h 34m</span>
                    </div>
                    <div class="metric">
                        <span>活跃策略</span>
                        <span class="metric-value" id="activeStrategies">3</span>
                    </div>
                    <div class="metric">
                        <span>钱包数量</span>
                        <span class="metric-value" id="walletCount">25</span>
                    </div>
                    <div class="metric">
                        <span>总TVL</span>
                        <span class="metric-value" id="totalTVL">$1.2M</span>
                    </div>
                </div>

                <div class="card">
                    <h3>📈 实时数据</h3>
                    <div class="chart-container">
                        <canvas id="overviewChart"></canvas>
                    </div>
                </div>

                <div class="card">
                    <h3>🎯 快速操作</h3>
                    <button class="btn" onclick="refreshData()">刷新数据</button>
                    <button class="btn" onclick="showSettings()">系统设置</button>
                    <button class="btn btn-danger" onclick="emergencyStop()">紧急停止</button>
                </div>
            </div>
        </div>

        <!-- 舆情监控标签页 -->
        <div id="hotspot" class="tab-content">
            <div class="grid">
                <div class="card">
                    <h3>📊 热点分布</h3>
                    <div class="chart-container">
                        <canvas id="hotspotChart"></canvas>
                    </div>
                </div>

                <div class="card">
                    <h3>⚙️ 监控设置</h3>
                    <div class="settings-panel">
                        <label>最低适配度: <span id="adaptationValue">0.7</span></label>
                        <input type="range" class="slider" min="0" max="1" step="0.01" value="0.7" 
                               oninput="updateAdaptation(this.value)">
                        
                        <label>监控频率:</label>
                        <select onchange="updateFrequency(this.value)" style="width: 100%; padding: 8px; margin: 5px 0;">
                            <option value="realtime">实时</option>
                            <option value="5min">5分钟</option>
                            <option value="30min">30分钟</option>
                        </select>
                    </div>
                </div>

                <div class="card">
                    <h3>🔥 实时热点</h3>
                    <div id="hotspotList">
                        <div class="metric">
                            <span>Trump 2024 Campaign</span>
                            <span class="metric-value">92%</span>
                        </div>
                        <div class="metric">
                            <span>Viral Cat Meme</span>
                            <span class="metric-value">78%</span>
                        </div>
                        <div class="metric">
                            <span>AI Technology Trend</span>
                            <span class="metric-value">65%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 其他标签页内容 -->
        <div id="strategy" class="tab-content">
            <div class="card">
                <h3>🎯 策略管理</h3>
                <p>策略生成和管理功能正在开发中...</p>
            </div>
        </div>

        <div id="wallet" class="tab-content">
            <div class="card">
                <h3>💰 钱包管理</h3>
                <p>钱包管理功能正在开发中...</p>
            </div>
        </div>

        <div id="liquidity" class="tab-content">
            <div class="card">
                <h3>💧 流动性控制</h3>
                <p>流动性控制功能正在开发中...</p>
            </div>
        </div>

        <div id="exit" class="tab-content">
            <div class="card">
                <h3>🚪 退出系统</h3>
                <div class="chart-container">
                    <canvas id="exitChart"></canvas>
                </div>
                <button class="btn btn-danger" onclick="emergencyExit()">紧急退出</button>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 初始化图表
        function initCharts() {
            // 总览图表
            const overviewCtx = document.getElementById('overviewChart').getContext('2d');
            new Chart(overviewCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: 'TVL',
                        data: [800000, 950000, 1100000, 1200000, 1350000, 1200000],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: { grid: { color: 'rgba(255,255,255,0.1)' } },
                        x: { grid: { color: 'rgba(255,255,255,0.1)' } }
                    }
                }
            });

            // 热点分布图表
            const hotspotCtx = document.getElementById('hotspotChart').getContext('2d');
            new Chart(hotspotCtx, {
                type: 'doughnut',
                data: {
                    labels: ['政治', '亚文化', '科技', '体育'],
                    datasets: [{
                        data: [45, 30, 15, 10],
                        backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 退出信号图表
            const exitCtx = document.getElementById('exitChart').getContext('2d');
            new Chart(exitCtx, {
                type: 'doughnut',
                data: {
                    labels: ['信号强度', '剩余'],
                    datasets: [{
                        data: [78, 22],
                        backgroundColor: ['#f59e0b', 'rgba(255,255,255,0.1)']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: { legend: { display: false } }
                }
            });
        }

        // 更新函数
        function updateAdaptation(value) {
            document.getElementById('adaptationValue').textContent = value;
        }

        function updateFrequency(value) {
            console.log('监控频率更新为:', value);
        }

        function refreshData() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            // 模拟数据更新
            document.getElementById('uptime').textContent = Math.floor(Math.random() * 24) + 'h ' + Math.floor(Math.random() * 60) + 'm';
        }

        function showSettings() {
            alert('系统设置功能正在开发中...');
        }

        function emergencyStop() {
            if (confirm('确定要执行紧急停止吗？')) {
                alert('紧急停止已执行');
            }
        }

        function emergencyExit() {
            if (confirm('确定要执行紧急退出吗？')) {
                alert('紧急退出已执行');
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            refreshData();
            
            // 定时更新
            setInterval(refreshData, 30000);
        });
    </script>
</body>
</html>
"""

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """返回仪表盘HTML页面"""
    return DASHBOARD_HTML

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "service": "MemeMaster AI Dashboard",
        "version": "2.0.0"
    }

@app.get("/api/data")
async def get_dashboard_data():
    """获取仪表盘数据"""
    return {
        "system": {
            "uptime": f"{random.randint(10, 24)}h {random.randint(10, 59)}m",
            "active_strategies": random.randint(2, 5),
            "wallet_count": random.randint(20, 50),
            "total_tvl": f"${random.uniform(1.0, 2.0):.1f}M"
        },
        "hotspots": [
            {"name": "Trump 2024 Campaign", "score": 0.92, "category": "politics"},
            {"name": "Viral Cat Meme", "score": 0.78, "category": "subculture"},
            {"name": "AI Technology Trend", "score": 0.65, "category": "technology"}
        ],
        "distribution": {
            "politics": 45,
            "subculture": 30,
            "technology": 15,
            "sports": 10
        },
        "exit_signal": {
            "strength": random.uniform(0.6, 0.9),
            "action": "partial_sell",
            "roi": random.uniform(150, 250)
        }
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动 MemeMaster AI 简化仪表盘...")
    print("📊 访问地址: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
