#!/usr/bin/env python3
"""
MemeMaster AI 快速启动和状态检查脚本
"""

import os
import sys
import time
import subprocess
import requests
import json
from datetime import datetime
from pathlib import Path

class MemeMasterChecker:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_url = "http://localhost:8001"
        self.frontend_url = "http://localhost:3000"
        
    def print_header(self, title):
        """打印标题"""
        print(f"\n{'='*60}")
        print(f"🔍 {title}")
        print(f"{'='*60}")
    
    def print_status(self, item, status, details=""):
        """打印状态"""
        icon = "✅" if status else "❌"
        print(f"{icon} {item}")
        if details:
            print(f"   {details}")
    
    def check_dependencies(self):
        """检查依赖"""
        self.print_header("依赖检查")
        
        # 检查Python依赖
        try:
            import fastapi, uvicorn, jwt, bcrypt, aiosqlite
            self.print_status("Python核心依赖", True, "fastapi, uvicorn, jwt, bcrypt, aiosqlite")
        except ImportError as e:
            self.print_status("Python核心依赖", False, f"缺失: {e}")
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.print_status("Node.js", True, result.stdout.strip())
            else:
                self.print_status("Node.js", False, "未安装")
        except FileNotFoundError:
            self.print_status("Node.js", False, "未找到")
        
        # 检查npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.print_status("npm", True, result.stdout.strip())
            else:
                self.print_status("npm", False, "未安装")
        except FileNotFoundError:
            self.print_status("npm", False, "未找到")
    
    def check_files(self):
        """检查文件"""
        self.print_header("文件检查")
        
        required_files = [
            "package.json",
            "requirements.txt", 
            "vite.config.js",
            "working_server.py",
            "frontend/App.jsx",
            "frontend/main.jsx",
            "src/app.py"
        ]
        
        for file_path in required_files:
            full_path = self.project_root / file_path
            exists = full_path.exists()
            self.print_status(file_path, exists, f"大小: {full_path.stat().st_size if exists else 0} bytes")
    
    def check_database(self):
        """检查数据库"""
        self.print_header("数据库检查")
        
        db_files = list(self.project_root.glob("*.db"))
        if db_files:
            for db_file in db_files:
                self.print_status(f"数据库文件: {db_file.name}", True, f"大小: {db_file.stat().st_size} bytes")
        else:
            self.print_status("数据库文件", False, "未找到 *.db 文件")
            print("   💡 提示: 运行 'python3 init_user_system.py init' 初始化数据库")
    
    def check_services(self):
        """检查服务状态"""
        self.print_header("服务状态检查")
        
        # 检查后端服务
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                self.print_status("后端服务", True, f"版本: {data.get('version', 'unknown')}")
            else:
                self.print_status("后端服务", False, f"HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            self.print_status("后端服务", False, f"连接失败: {str(e)[:50]}...")
        
        # 检查前端服务
        try:
            response = requests.get(self.frontend_url, timeout=5)
            if response.status_code == 200:
                self.print_status("前端服务", True, "React应用正常")
            else:
                self.print_status("前端服务", False, f"HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            self.print_status("前端服务", False, f"连接失败: {str(e)[:50]}...")
    
    def test_api_endpoints(self):
        """测试API端点"""
        self.print_header("API端点测试")
        
        endpoints = [
            ("/health", "健康检查"),
            ("/api/system/info", "系统信息"),
            ("/api/hotspot/current", "热点数据"),
            ("/api/subscription/plans", "订阅计划"),
            ("/api/strategy/demo", "策略演示")
        ]
        
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.backend_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    self.print_status(description, True, f"GET {endpoint}")
                else:
                    self.print_status(description, False, f"HTTP {response.status_code}")
            except requests.exceptions.RequestException as e:
                self.print_status(description, False, f"请求失败")
    
    def start_backend(self):
        """启动后端服务"""
        self.print_header("启动后端服务")
        
        try:
            # 检查是否已经运行
            response = requests.get(f"{self.backend_url}/health", timeout=2)
            if response.status_code == 200:
                self.print_status("后端服务", True, "已在运行")
                return True
        except:
            pass
        
        # 启动后端
        print("🚀 启动后端服务...")
        try:
            process = subprocess.Popen(
                [sys.executable, "working_server.py"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            for i in range(10):
                time.sleep(1)
                try:
                    response = requests.get(f"{self.backend_url}/health", timeout=2)
                    if response.status_code == 200:
                        self.print_status("后端服务启动", True, f"PID: {process.pid}")
                        return True
                except:
                    continue
            
            self.print_status("后端服务启动", False, "超时")
            return False
            
        except Exception as e:
            self.print_status("后端服务启动", False, str(e))
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        self.print_header("启动前端服务")
        
        try:
            # 检查是否已经运行
            response = requests.get(self.frontend_url, timeout=2)
            if response.status_code == 200:
                self.print_status("前端服务", True, "已在运行")
                return True
        except:
            pass
        
        # 启动前端
        print("🚀 启动前端服务...")
        try:
            process = subprocess.Popen(
                ["npm", "run", "frontend:dev"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            for i in range(30):  # 前端启动较慢
                time.sleep(1)
                try:
                    response = requests.get(self.frontend_url, timeout=2)
                    if response.status_code == 200:
                        self.print_status("前端服务启动", True, f"PID: {process.pid}")
                        return True
                except:
                    continue
            
            self.print_status("前端服务启动", False, "超时")
            return False
            
        except Exception as e:
            self.print_status("前端服务启动", False, str(e))
            return False
    
    def generate_report(self):
        """生成状态报告"""
        self.print_header("系统状态报告")
        
        print(f"📅 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 项目路径: {self.project_root}")
        print(f"🌐 后端地址: {self.backend_url}")
        print(f"🌐 前端地址: {self.frontend_url}")
        
        # 生成访问链接
        print(f"\n🔗 快速访问链接:")
        print(f"   • 前端主页: {self.frontend_url}/")
        print(f"   • API文档: {self.backend_url}/docs")
        print(f"   • 健康检查: {self.backend_url}/health")
        print(f"   • 系统信息: {self.backend_url}/api/system/info")
    
    def run_full_check(self):
        """运行完整检查"""
        print("🎯 MemeMaster AI 系统检查开始...")
        
        self.check_dependencies()
        self.check_files()
        self.check_database()
        self.check_services()
        self.test_api_endpoints()
        self.generate_report()
        
        print(f"\n{'='*60}")
        print("✅ 系统检查完成！")
        print("💡 如需启动服务，请运行: python3 quick_start_and_check.py start")
    
    def start_services(self):
        """启动所有服务"""
        print("🚀 MemeMaster AI 服务启动...")
        
        # 启动后端
        backend_ok = self.start_backend()
        
        # 启动前端
        frontend_ok = self.start_frontend()
        
        if backend_ok and frontend_ok:
            print("\n🎉 所有服务启动成功！")
            self.generate_report()
        else:
            print("\n⚠️ 部分服务启动失败，请检查错误信息")

def main():
    """主函数"""
    checker = MemeMasterChecker()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        if command == "start":
            checker.start_services()
        elif command == "check":
            checker.run_full_check()
        else:
            print("用法: python3 quick_start_and_check.py [start|check]")
    else:
        checker.run_full_check()

if __name__ == "__main__":
    main()
