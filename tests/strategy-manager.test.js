/**
 * 策略管理模块测试
 * 测试策略仓库、部署、监控等核心功能
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import StrategyManager from '../frontend/pages/StrategyManager'
import { strategyAPI } from '../frontend/services/api'

// Mock API
jest.mock('../frontend/services/api', () => ({
  strategyAPI: {
    getRepository: jest.fn(),
    getDeployedStrategies: jest.fn(),
    deployStrategy: jest.fn(),
    batchDeployStrategies: jest.fn(),
    getStrategyDetails: jest.fn(),
  }
}))

// Mock store
const mockStore = configureStore({
  reducer: {
    strategy: (state = { strategies: [], isLoading: false }, action) => state
  }
})

// Mock data
const mockStrategies = [
  {
    id: 'str_001',
    name: 'Trump 2024 Victory Token',
    symbol: 'TRUMP24',
    status: 'draft',
    category: 'politics',
    score: 92.5,
    estimated_roi: 245.8,
    viral_potential: 0.92,
    sentiment: 0.72,
    created: '2024-01-15T10:30:00Z',
    hotspot_source: 'Trump 2024 Campaign Launch'
  },
  {
    id: 'str_002',
    name: 'XRP ETF Celebration',
    symbol: 'XRPETF',
    status: 'pending',
    category: 'finance',
    score: 89.3,
    estimated_roi: 189.4,
    viral_potential: 0.88,
    sentiment: 0.84,
    created: '2024-01-15T09:45:00Z',
    hotspot_source: 'XRP ETF Approval Speculation'
  }
]

const mockDeployedTokens = [
  {
    id: 'str_006',
    name: 'Bitcoin ETF Surge',
    symbol: 'BTCETF',
    status: 'active',
    contract_address: '******************************************',
    current_price: 0.0023,
    market_cap: 2300000,
    holders: 3456,
    liquidity: 145000,
    volume_24h: 567000,
    price_change_24h: 23.4
  }
]

describe('StrategyManager', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Setup default API responses
    strategyAPI.getRepository.mockResolvedValue({
      data: {
        strategies: mockStrategies,
        total_count: mockStrategies.length,
        summary: {
          draft_count: 1,
          pending_count: 1,
          deploying_count: 0,
          avg_score: 90.9,
          high_potential_count: 2
        }
      }
    })
    
    strategyAPI.getDeployedStrategies.mockResolvedValue({
      data: {
        deployed_tokens: mockDeployedTokens,
        total_count: mockDeployedTokens.length,
        summary: {
          total_market_cap: 2300000,
          total_holders: 3456,
          total_liquidity: 145000,
          avg_performance: 23.4
        }
      }
    })
  })

  const renderComponent = () => {
    return render(
      <Provider store={mockStore}>
        <StrategyManager />
      </Provider>
    )
  }

  describe('策略仓库功能', () => {
    test('应该显示策略仓库标签页', async () => {
      renderComponent()
      
      expect(screen.getByText('策略仓库')).toBeInTheDocument()
      expect(screen.getByText('未部署策略管理')).toBeInTheDocument()
    })

    test('应该加载并显示策略列表', async () => {
      renderComponent()
      
      await waitFor(() => {
        expect(strategyAPI.getRepository).toHaveBeenCalled()
      })
      
      expect(screen.getByText('Trump 2024 Victory Token')).toBeInTheDocument()
      expect(screen.getByText('XRP ETF Celebration')).toBeInTheDocument()
    })

    test('应该支持策略搜索', async () => {
      renderComponent()
      
      const searchInput = screen.getByPlaceholderText('搜索策略名称、符号或热点来源...')
      fireEvent.change(searchInput, { target: { value: 'Trump' } })
      
      await waitFor(() => {
        expect(screen.getByText('Trump 2024 Victory Token')).toBeInTheDocument()
        expect(screen.queryByText('XRP ETF Celebration')).not.toBeInTheDocument()
      })
    })

    test('应该支持按状态筛选', async () => {
      renderComponent()
      
      const statusFilter = screen.getByDisplayValue('所有状态')
      fireEvent.change(statusFilter, { target: { value: 'draft' } })
      
      await waitFor(() => {
        expect(screen.getByText('Trump 2024 Victory Token')).toBeInTheDocument()
        expect(screen.queryByText('XRP ETF Celebration')).not.toBeInTheDocument()
      })
    })

    test('应该支持策略选择', async () => {
      renderComponent()
      
      const checkboxes = screen.getAllByRole('checkbox')
      fireEvent.click(checkboxes[1]) // 第一个策略的复选框
      
      expect(screen.getByText('已选择 1 个')).toBeInTheDocument()
      expect(screen.getByText('批量部署')).toBeInTheDocument()
    })

    test('应该支持单个策略部署', async () => {
      strategyAPI.deployStrategy.mockResolvedValue({
        data: {
          status: 'success',
          strategy_id: 'str_001',
          contract_address: '******************************************'
        }
      })
      
      renderComponent()
      
      const deployButtons = screen.getAllByText('部署')
      fireEvent.click(deployButtons[0])
      
      await waitFor(() => {
        expect(strategyAPI.deployStrategy).toHaveBeenCalledWith({
          strategy_id: 'str_001'
        })
      })
    })

    test('应该支持批量部署', async () => {
      strategyAPI.batchDeployStrategies.mockResolvedValue({
        data: {
          status: 'success',
          deployed_count: 2,
          deployed_strategies: []
        }
      })
      
      renderComponent()
      
      // 选择多个策略
      const checkboxes = screen.getAllByRole('checkbox')
      fireEvent.click(checkboxes[1])
      fireEvent.click(checkboxes[2])
      
      // 点击批量部署
      const batchDeployButton = screen.getByText('批量部署')
      fireEvent.click(batchDeployButton)
      
      await waitFor(() => {
        expect(strategyAPI.batchDeployStrategies).toHaveBeenCalledWith({
          strategy_ids: ['str_001', 'str_002']
        })
      })
    })
  })

  describe('策略监控功能', () => {
    test('应该显示策略监控标签页', async () => {
      renderComponent()
      
      const monitoringTab = screen.getByText('策略监控')
      fireEvent.click(monitoringTab)
      
      expect(screen.getByText('已部署策略监控')).toBeInTheDocument()
    })

    test('应该加载并显示已部署策略', async () => {
      renderComponent()
      
      const monitoringTab = screen.getByText('策略监控')
      fireEvent.click(monitoringTab)
      
      await waitFor(() => {
        expect(strategyAPI.getDeployedStrategies).toHaveBeenCalled()
      })
      
      expect(screen.getByText('Bitcoin ETF Surge')).toBeInTheDocument()
      expect(screen.getByText('$2.3M')).toBeInTheDocument() // 市值
      expect(screen.getByText('3,456')).toBeInTheDocument() // 持币人数
    })

    test('应该显示监控概览数据', async () => {
      renderComponent()
      
      const monitoringTab = screen.getByText('策略监控')
      fireEvent.click(monitoringTab)
      
      await waitFor(() => {
        expect(screen.getByText('活跃策略')).toBeInTheDocument()
        expect(screen.getByText('总市值')).toBeInTheDocument()
        expect(screen.getByText('总持币人')).toBeInTheDocument()
        expect(screen.getByText('总流动性')).toBeInTheDocument()
      })
    })
  })

  describe('策略详情功能', () => {
    test('应该显示策略详情模态框', async () => {
      strategyAPI.getStrategyDetails.mockResolvedValue({
        data: {
          strategy: mockStrategies[0]
        }
      })
      
      renderComponent()
      
      const detailButtons = screen.getAllByText('详情')
      fireEvent.click(detailButtons[0])
      
      await waitFor(() => {
        expect(screen.getByText('Trump 2024 Victory Token 详情')).toBeInTheDocument()
        expect(screen.getByText('基础信息')).toBeInTheDocument()
        expect(screen.getByText('关键指标')).toBeInTheDocument()
        expect(screen.getByText('经济模型')).toBeInTheDocument()
      })
    })

    test('应该能够关闭策略详情模态框', async () => {
      renderComponent()
      
      const detailButtons = screen.getAllByText('详情')
      fireEvent.click(detailButtons[0])
      
      await waitFor(() => {
        expect(screen.getByText('Trump 2024 Victory Token 详情')).toBeInTheDocument()
      })
      
      const closeButton = screen.getByText('关闭')
      fireEvent.click(closeButton)
      
      await waitFor(() => {
        expect(screen.queryByText('Trump 2024 Victory Token 详情')).not.toBeInTheDocument()
      })
    })
  })

  describe('错误处理', () => {
    test('应该处理API错误', async () => {
      strategyAPI.getRepository.mockRejectedValue(new Error('API Error'))
      
      renderComponent()
      
      await waitFor(() => {
        expect(screen.getByText('暂无策略')).toBeInTheDocument()
      })
    })

    test('应该处理部署失败', async () => {
      strategyAPI.deployStrategy.mockRejectedValue(new Error('Deployment failed'))
      
      renderComponent()
      
      const deployButtons = screen.getAllByText('部署')
      fireEvent.click(deployButtons[0])
      
      // 这里应该显示错误提示，具体实现取决于错误处理逻辑
    })
  })

  describe('响应式设计', () => {
    test('应该在移动设备上正确显示', () => {
      // 模拟移动设备屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      })
      
      renderComponent()
      
      // 检查移动端特定的UI元素
      expect(screen.getByText('策略仓库')).toBeInTheDocument()
    })
  })
})

// 集成测试
describe('策略管理集成测试', () => {
  test('完整的策略部署流程', async () => {
    // 模拟完整的部署流程
    strategyAPI.deployStrategy.mockResolvedValue({
      data: {
        status: 'success',
        strategy_id: 'str_001',
        contract_address: '******************************************',
        deployment_time: 12,
        gas_used: 2847392
      }
    })
    
    renderComponent()
    
    // 1. 选择策略
    const deployButtons = screen.getAllByText('部署')
    fireEvent.click(deployButtons[0])
    
    // 2. 等待部署完成
    await waitFor(() => {
      expect(strategyAPI.deployStrategy).toHaveBeenCalled()
    })
    
    // 3. 验证部署结果
    // 这里应该检查策略状态是否更新为"已部署"
  })

  test('策略从仓库到监控的完整流程', async () => {
    // 测试策略从创建、部署到监控的完整生命周期
    renderComponent()
    
    // 1. 在策略仓库中部署策略
    const deployButtons = screen.getAllByText('部署')
    fireEvent.click(deployButtons[0])
    
    // 2. 切换到监控页面
    const monitoringTab = screen.getByText('策略监控')
    fireEvent.click(monitoringTab)
    
    // 3. 验证策略出现在监控列表中
    await waitFor(() => {
      expect(strategyAPI.getDeployedStrategies).toHaveBeenCalled()
    })
  })
})
